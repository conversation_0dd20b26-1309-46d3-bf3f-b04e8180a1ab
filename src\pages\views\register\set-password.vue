<template>
  <view class="set-password-container">
    <!-- 状态栏适配 -->
    <view
      class="status-bar"
      :style="{
        height: statusBarHeight
          ? `${statusBarHeight}px`
          : 'var(--status-bar-height)'
      }"
    ></view>

    <!-- 顶部导航栏 -->
    <view class="header">
      <view class="back-button" @tap="backToLogin">
        <van-icon name="arrow-left" size="16" color="#333" />
      </view>
      <view class="header-title">设置密码</view>
    </view>

    <!-- 主体内容 -->
    <view class="main-content">
      <view class="form-container">
        <view class="form-header">
          <text class="form-title">设置登录密码</text>
          <text class="form-subtitle">为您的账号设置一个安全密码</text>
        </view>

        <up-form class="password-form">
          <view class="form-item">
            <up-form-item label="设置密码">
              <up-input
                v-model="form.password"
                type="password"
                placeholder="请输入8-20位密码"
                :clearable="true"
                @input="validatePassword"
              ></up-input>
            </up-form-item>
          </view>

          <view class="form-item">
            <up-form-item label="确认密码">
              <up-input
                v-model="form.confirmPassword"
                type="password"
                placeholder="请再次输入密码"
                :clearable="true"
                @input="validateConfirmPassword"
              ></up-input>
            </up-form-item>
          </view>
        </up-form>

        <!-- 密码强度提示 -->
        <view class="password-tips">
          <text class="tips-title">密码要求：</text>
          <view class="tips-list">
            <text class="tip-item" :class="{ valid: passwordValidation.length }"
              >• 8-20位字符</text
            >
            <text
              class="tip-item"
              :class="{ valid: passwordValidation.hasLetter }"
              >• 包含字母</text
            >
            <text
              class="tip-item"
              :class="{ valid: passwordValidation.hasNumber }"
              >• 包含数字</text
            >
          </view>
        </view>

        <up-button
          class="submit-button"
          type="primary"
          :loading="loading"
          text="完成注册"
          @click="submitRegister"
          shape="circle"
          :disabled="!canSubmit"
        ></up-button>
      </view>
    </view>
  </view>
</template>

<script setup lang="ts">
import { computed, onMounted, reactive, ref, watch } from "vue"
import * as api from "@/api/api"
import { useStore } from "vuex"

interface PasswordForm {
  password: string
  confirmPassword: string
}

const store = useStore()
const loading = ref(false)
const mobile = ref("")
const code = ref("")
const statusBarHeight = ref(0)

const form = reactive<PasswordForm>({
  password: "",
  confirmPassword: ""
})

// 密码验证状态
const passwordValidation = reactive({
  length: false,
  hasLetter: false,
  hasNumber: false
})

// 计算是否可以提交
const canSubmit = computed(() => {
  const result =
    passwordValidation.length &&
    passwordValidation.hasLetter &&
    passwordValidation.hasNumber &&
    form.password === form.confirmPassword &&
    form.password.length >= 8 &&
    form.confirmPassword.length > 0
  console.log("canSubmit检查:", {
    passwordValidation,
    password: form.password,
    confirmPassword: form.confirmPassword,
    passwordMatch: form.password === form.confirmPassword,
    result
  })
  return result
})

// 验证密码
const validatePassword = (value: string) => {
  console.log("验证密码:", value)
  passwordValidation.length = value.length >= 8 && value.length <= 20
  passwordValidation.hasLetter = /[a-zA-Z]/.test(value)
  passwordValidation.hasNumber = /\d/.test(value)
  console.log("密码验证结果:", passwordValidation)
}

// 验证确认密码
const validateConfirmPassword = (value: string) => {
  console.log("验证确认密码:", value)
  if (value && value !== form.password) {
    showNotify("两次输入的密码不一致", "warning")
  }
}

// 监听密码变化
watch(
  () => form.password,
  (newValue) => {
    validatePassword(newValue)
  }
)

// 监听确认密码变化
watch(
  () => form.confirmPassword,
  (newValue) => {
    validateConfirmPassword(newValue)
  }
)

// 提交注册
const submitRegister = async () => {
  if (loading.value) return
  if (!canSubmit.value) {
    showNotify("请检查密码设置要求", "warning")
    return
  }

  if (form.password !== form.confirmPassword) {
    showNotify("两次输入的密码不一致", "warning")
    return
  }

  loading.value = true

  try {
    // 调用注册接口
    const registerRes = await api.POST_REGISTER_USER({
      username: mobile.value,
      mobile: mobile.value,
      password: form.password,
      code: code.value
    })

    if (registerRes.code === 200) {
      showNotify("注册成功", "success")

      // 注册成功后自动登录
      const loginRes = await store.dispatch("login", {
        username: mobile.value,
        password: form.password
      })

      if (loginRes.success) {
        // 注册并登录成功
        uni.showModal({
          title: "注册成功",
          content:
            "恭喜您，账号注册成功！根据平台要求，下单前需要完成实名认证，是否立即前往？",
          cancelText: "稍后认证",
          confirmText: "立即前往",
          success: (res) => {
            if (res.confirm) {
              uni.redirectTo({
                url: "/pages/views/certification/index"
              })
            } else {
              uni.switchTab({
                url: "/pages/views/home/<USER>"
              })
            }
          }
        })
      } else {
        // 注册成功但自动登录失败，提示用户手动登录
        showNotify("注册成功，但自动登录失败，请手动登录", "warning")
        setTimeout(() => {
          uni.redirectTo({
            url: "/pages/views/login/login"
          })
        }, 1500)
      }
    } else {
      showNotify(registerRes.message || "注册失败", "error")
    }
  } catch (error) {
    console.error("注册失败", error)
    showNotify("注册失败，请重试", "error")
  } finally {
    loading.value = false
  }
}

// 显示通知
const showNotify = (message: string, type = "default") => {
  uni.showToast({
    title: message,
    icon: type === "success" ? "success" : "none",
    duration: 2000
  })
}

// 返回登录页
const backToLogin = () => {
  uni.navigateBack()
}

// 组件加载时初始化
onMounted(() => {
  // 获取页面参数
  const pages = getCurrentPages()
  const currentPage = pages[pages.length - 1] as any
  const options = currentPage.options

  mobile.value = options.mobile || ""
  code.value = options.code || ""

  // 获取状态栏高度
  try {
    const sysInfo = uni.getSystemInfoSync()
    statusBarHeight.value = sysInfo.statusBarHeight || 0
  } catch (e) {
    console.error("获取系统信息失败", e)
  }
})
</script>

<style lang="scss" scoped>
@import "@/uni.scss";

.set-password-container {
  min-height: 100vh;
  background-color: #ffffff;
  display: flex;
  flex-direction: column;
}

.status-bar {
  height: var(--status-bar-height);
  width: 100%;
  background-color: #ffffff;
}

.header {
  height: 100rpx;
  display: flex;
  align-items: center;
  padding: 0 30rpx;
  position: relative;
}

.back-button {
  width: 60rpx;
  height: 60rpx;
  display: flex;
  align-items: center;
  justify-content: center;
}

.header-title {
  position: absolute;
  left: 50%;
  transform: translateX(-50%);
  font-size: 32rpx;
  font-weight: 500;
  color: #333333;
}

.main-content {
  flex: 1;
  padding: 40rpx;
}

.form-container {
  background-color: #ffffff;
  border-radius: $uni-border-radius-lg;
  padding: 40rpx;
  box-shadow: $uni-shadow-base;
}

.form-header {
  margin-bottom: 40rpx;
  text-align: center;
}

.form-title {
  font-size: 40rpx;
  font-weight: bold;
  color: #333333;
  margin-bottom: 16rpx;
  display: block;
}

.form-subtitle {
  font-size: 28rpx;
  color: #666666;
  display: block;
}

.password-form {
  margin-bottom: 30rpx;
}

.form-item {
  margin-bottom: 30rpx;
}

.password-tips {
  margin-bottom: 40rpx;
  padding: 20rpx;
  background-color: #f8f9fa;
  border-radius: $uni-border-radius-base;
}

.tips-title {
  font-size: 26rpx;
  color: #333333;
  font-weight: 500;
  margin-bottom: 12rpx;
  display: block;
}

.tips-list {
  display: flex;
  flex-direction: column;
  gap: 8rpx;
}

.tip-item {
  font-size: 24rpx;
  color: #999999;
  transition: color 0.3s ease;

  &.valid {
    color: $uni-primary;
  }
}

.submit-button {
  width: 100%;
  height: 80rpx;
  font-size: 32rpx;
  font-weight: 500;
  letter-spacing: 4rpx;
  box-shadow: 0 10rpx 20rpx rgba($uni-primary, 0.25);
  transition: all 0.3s ease;
  border-radius: $uni-border-radius-lg;
  background: linear-gradient(45deg, $uni-primary, $uni-neon);
}

.submit-button:active {
  transform: translateY(4rpx);
  box-shadow: 0 6rpx 10rpx rgba($uni-primary, 0.2);
}
</style>
