// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

FileInfo _$FileInfoFromJson(Map<String, dynamic> json) => FileInfo(
      id: json['id'] as String,
      fileName: json['fileName'] as String,
      originalName: json['originalName'] as String?,
      url: json['url'] as String,
      thumbnailUrl: json['thumbnailUrl'] as String?,
      fileSize: (json['fileSize'] as num).toInt(),
      fileType: json['fileType'] as String,
      mimeType: json['mimeType'] as String?,
      width: (json['width'] as num?)?.toInt(),
      height: (json['height'] as num?)?.toInt(),
      duration: (json['duration'] as num?)?.toInt(),
      createTime: (json['createTime'] as num).toInt(),
    );

Map<String, dynamic> _$FileInfoToJson(FileInfo instance) => <String, dynamic>{
      'id': instance.id,
      'fileName': instance.fileName,
      'originalName': instance.originalName,
      'url': instance.url,
      'thumbnailUrl': instance.thumbnailUrl,
      'fileSize': instance.fileSize,
      'fileType': instance.fileType,
      'mimeType': instance.mimeType,
      'width': instance.width,
      'height': instance.height,
      'duration': instance.duration,
      'createTime': instance.createTime,
    };

FileUploadRequest _$FileUploadRequestFromJson(Map<String, dynamic> json) =>
    FileUploadRequest(
      fileName: json['fileName'] as String,
      fileType: json['fileType'] as String,
      fileSize: (json['fileSize'] as num).toInt(),
      mimeType: json['mimeType'] as String?,
      category: json['category'] as String?,
    );

Map<String, dynamic> _$FileUploadRequestToJson(FileUploadRequest instance) =>
    <String, dynamic>{
      'fileName': instance.fileName,
      'fileType': instance.fileType,
      'fileSize': instance.fileSize,
      'mimeType': instance.mimeType,
      'category': instance.category,
    };

FileUploadResponse _$FileUploadResponseFromJson(Map<String, dynamic> json) =>
    FileUploadResponse(
      id: json['id'] as String,
      url: json['url'] as String,
      thumbnailUrl: json['thumbnailUrl'] as String?,
      uploadUrl: json['uploadUrl'] as String?,
      uploadToken: json['uploadToken'] as String?,
    );

Map<String, dynamic> _$FileUploadResponseToJson(FileUploadResponse instance) =>
    <String, dynamic>{
      'id': instance.id,
      'url': instance.url,
      'thumbnailUrl': instance.thumbnailUrl,
      'uploadUrl': instance.uploadUrl,
      'uploadToken': instance.uploadToken,
    };

ImageItem _$ImageItemFromJson(Map<String, dynamic> json) => ImageItem(
      fileId: json['fileId'] as String,
      url: json['url'] as String,
      thumbnailUrl: json['thumbnailUrl'] as String?,
    );

Map<String, dynamic> _$ImageItemToJson(ImageItem instance) => <String, dynamic>{
      'fileId': instance.fileId,
      'url': instance.url,
      'thumbnailUrl': instance.thumbnailUrl,
    };

CategoryItem _$CategoryItemFromJson(Map<String, dynamic> json) => CategoryItem(
      name: json['name'] as String,
      value: json['value'] as String,
      icon: json['icon'] as String?,
      description: json['description'] as String?,
    );

Map<String, dynamic> _$CategoryItemToJson(CategoryItem instance) =>
    <String, dynamic>{
      'name': instance.name,
      'value': instance.value,
      'icon': instance.icon,
      'description': instance.description,
    };

ConditionOption _$ConditionOptionFromJson(Map<String, dynamic> json) =>
    ConditionOption(
      value: json['value'] as String,
      label: json['label'] as String,
      desc: json['desc'] as String?,
      color: json['color'] as String?,
    );

Map<String, dynamic> _$ConditionOptionToJson(ConditionOption instance) =>
    <String, dynamic>{
      'value': instance.value,
      'label': instance.label,
      'desc': instance.desc,
      'color': instance.color,
    };
