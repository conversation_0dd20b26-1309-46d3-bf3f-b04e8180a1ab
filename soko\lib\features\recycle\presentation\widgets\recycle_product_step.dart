import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

/// 回收订单创建 - 商品信息步骤
class RecycleCreateProductStep extends ConsumerStatefulWidget {

  const RecycleCreateProductStep({
    super.key,
    this.productName,
    this.productDescription,
    required this.onProductInfoChanged,
  });
  final String? productName;
  final String? productDescription;
  final Function(String?, String?) onProductInfoChanged;

  @override
  ConsumerState<RecycleCreateProductStep> createState() =>
      _RecycleCreateProductStepState();
}

class _RecycleCreateProductStepState
    extends ConsumerState<RecycleCreateProductStep> {
  final TextEditingController _nameController = TextEditingController();
  final TextEditingController _descController = TextEditingController();

  @override
  void initState() {
    super.initState();
    _nameController.text = widget.productName ?? '';
    _descController.text = widget.productDescription ?? '';
  }

  @override
  void dispose() {
    _nameController.dispose();
    _descController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: EdgeInsets.all(16.w),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // 步骤标题
          Text(
            '填写商品信息',
            style: TextStyle(
              fontSize: 24.sp,
              fontWeight: FontWeight.w600,
              color: Colors.black87,
            ),
          ),
          SizedBox(height: 8.h),
          Text(
            '请详细描述您要回收的商品',
            style: TextStyle(
              fontSize: 16.sp,
              color: Colors.grey[600],
            ),
          ),
          SizedBox(height: 24.h),

          // 商品信息表单
          Expanded(
            child: _buildProductForm(),
          ),
        ],
      ),
    );
  }

  /// 构建商品信息表单
  Widget _buildProductForm() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // 产品名称
        Text(
          '商品名称 *',
          style: TextStyle(
            fontSize: 16.sp,
            fontWeight: FontWeight.w500,
            color: Colors.black87,
          ),
        ),
        SizedBox(height: 8.h),
        TextField(
          controller: _nameController,
          decoration: InputDecoration(
            hintText: '请输入商品名称，如：iPhone 14 Pro、MacBook Air等',
            border: OutlineInputBorder(
              borderRadius: BorderRadius.circular(8.r),
            ),
          ),
          onChanged: (value) {
            widget.onProductInfoChanged(value, _descController.text);
          },
        ),
        SizedBox(height: 24.h),

        // 产品描述
        Text(
          '商品描述',
          style: TextStyle(
            fontSize: 16.sp,
            fontWeight: FontWeight.w500,
            color: Colors.black87,
          ),
        ),
        SizedBox(height: 8.h),
        TextField(
          controller: _descController,
          maxLines: 4,
          decoration: InputDecoration(
            hintText: '请详细描述商品信息，如：\n• 品牌型号\n• 购买时间\n• 使用情况\n• 外观成色\n• 功能状态等',
            border: OutlineInputBorder(
              borderRadius: BorderRadius.circular(8.r),
            ),
          ),
          onChanged: (value) {
            widget.onProductInfoChanged(_nameController.text, value);
          },
        ),
        SizedBox(height: 16.h),

        // 提示信息
        Container(
          width: double.infinity,
          padding: EdgeInsets.all(12.w),
          decoration: BoxDecoration(
            color: Colors.blue.withValues(alpha: 0.1),
            borderRadius: BorderRadius.circular(8.r),
          ),
          child: Row(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Icon(
                Icons.info_outline,
                color: Colors.blue,
                size: 16.w,
              ),
              SizedBox(width: 8.w),
              Expanded(
                child: Text(
                  '详细的商品信息有助于我们提供更准确的估价',
                  style: TextStyle(
                    fontSize: 12.sp,
                    color: Colors.blue[700],
                  ),
                ),
              ),
            ],
          ),
        ),
      ],
    );
  }
}
