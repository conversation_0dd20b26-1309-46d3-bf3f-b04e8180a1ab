# 中古虾(SOKO) 前端项目概览

## 📋 项目基本信息

### 项目名称
**中古虾 (SOKO)** - 特摄模玩主题电商平台

### 项目简介
中古虾是一个专注于特摄模玩领域的综合性电商平台，采用现代化的前端技术栈构建。平台不仅提供传统的B2C电商功能，还创新性地集成了C2C回收业务，为特摄模玩爱好者提供一站式的购买、销售和回收服务。

### 项目定位
- **垂直电商平台**: 专注特摄模玩细分市场
- **双向商业模式**: B2C购买 + C2C回收的闭环生态
- **移动优先**: 基于UniApp的跨平台移动应用
- **用户体验导向**: 注重特摄文化和用户体验设计

## 🛠️ 技术栈概览

### 核心框架
- **Vue 3.4.29**: 渐进式JavaScript框架，使用Composition API
- **UniApp**: 跨平台开发框架，支持H5、小程序、App等多端
- **TypeScript 4.9.4**: 静态类型检查，提升代码质量和开发效率

### 构建工具
- **Vite 5.2.8**: 现代化构建工具，快速的开发服务器和构建
- **Vue TSC**: Vue TypeScript编译器
- **ESLint**: 代码质量检查工具

### UI框架与样式
- **uView Plus**: UniApp生态的UI组件库
- **Tailwind CSS 3.3.3**: 原子化CSS框架
- **SCSS**: CSS预处理器
- **Tailwind CSS Animate**: 动画扩展插件

### 状态管理与工具
- **Vuex 4.1.0**: 状态管理模式
- **Vue Router**: 路由管理
- **Vue i18n 9.11.0**: 国际化支持

### 开发工具
- **Sass 1.59.3**: CSS预处理器
- **PostCSS**: CSS后处理工具
- **Autoprefixer**: CSS自动前缀

### 第三方库
- **Day.js**: 轻量级日期处理库
- **MD5**: 数据加密
- **JSEncrypt**: RSA加密
- **Moment.js**: 日期时间处理
- **Big.js**: 高精度数值计算
- **Clipboard**: 剪贴板操作

## 🏗️ 项目架构

### 目录结构
```
src/
├── api/                    # API接口层
├── common/                 # 公共资源
├── components/             # 公共组件
├── composables/            # 组合式函数
├── constants/              # 常量定义
├── enum/                   # 枚举类型
├── pages/                  # 页面文件
│   └── views/             # 具体页面视图
├── static/                # 静态资源
├── store/                 # 状态管理
├── styles/                # 样式文件
├── tools/                 # 工具函数
├── types/                 # TypeScript类型定义
├── uni_modules/           # UniApp模块
├── util/                  # 工具类
└── App.vue               # 应用入口
```

### 技术特色
1. **组件化架构**: 基于Vue 3 Composition API的组件化开发
2. **类型安全**: 全面使用TypeScript确保类型安全
3. **原子化CSS**: Tailwind CSS提供高效的样式开发
4. **跨平台兼容**: UniApp确保多端一致性体验
5. **现代化构建**: Vite提供快速的开发和构建体验

## 🎯 业务模式

### 核心业务
1. **电商购物**: 特摄模玩商品的浏览、购买、支付
2. **回收服务**: 用户闲置模玩的回收、估价、交易
3. **用户管理**: 会员体系、个人中心、地址管理
4. **订单系统**: 购买订单、回收订单的全生命周期管理

### 商业价值
- **垂直市场深耕**: 专注特摄模玩细分领域，建立专业壁垒
- **双向价值创造**: 购买满足需求，回收释放价值
- **用户粘性增强**: 完整的生态闭环提升用户留存
- **数据价值挖掘**: 用户行为数据支撑精准营销

## 👥 目标用户

### 主要用户群体
1. **特摄爱好者**: 奥特曼、假面骑士、超级战队等特摄作品粉丝
2. **模玩收藏家**: 专业的模型玩具收藏爱好者
3. **二手交易者**: 有闲置模玩处理需求的用户
4. **礼品购买者**: 为特摄爱好者购买礼品的用户

### 用户特征
- **年龄分布**: 主要集中在18-35岁
- **消费能力**: 中等偏上消费水平
- **技术接受度**: 对移动应用使用熟练
- **社群属性**: 具有强烈的圈层文化认同

## 🎨 设计理念

### 视觉风格
- **年轻活力**: 明亮的色彩搭配体现年轻用户群体特征
- **科技感**: 现代化的UI设计语言
- **品牌一致性**: 统一的视觉识别系统

### 交互设计
- **移动优先**: 针对移动端优化的交互体验
- **直观易用**: 简洁明了的操作流程
- **反馈及时**: 丰富的交互反馈和状态提示
- **无障碍访问**: 考虑不同用户群体的使用需求

## 📱 平台支持

### 目标平台
- **H5网页**: 微信浏览器、手机浏览器
- **微信小程序**: 微信生态内的轻应用
- **支付宝小程序**: 支付宝生态扩展
- **App应用**: iOS和Android原生应用

### 兼容性要求
- **浏览器**: 现代浏览器ES2020+特性支持
- **设备**: 320px-2560px宽度范围全覆盖
- **网络**: 3G网络可用，2G网络核心功能可用
- **系统**: iOS 12+, Android 8+

## 🚀 项目优势

### 技术优势
1. **现代化技术栈**: 采用最新的前端技术和最佳实践
2. **跨平台能力**: 一套代码多端运行，降低开发成本
3. **性能优化**: 完善的性能优化策略和监控体系
4. **可维护性**: 良好的代码结构和文档体系

### 业务优势
1. **垂直领域专业性**: 深度理解特摄模玩用户需求
2. **创新商业模式**: 购买+回收的双向价值创造
3. **用户体验优先**: 注重用户体验的产品设计
4. **生态闭环**: 完整的商业生态链条

## 📈 发展规划

### 短期目标
- 完善核心功能，提升用户体验
- 扩大用户规模，建立品牌知名度
- 优化运营效率，提升转化率

### 长期愿景
- 成为特摄模玩领域的领导品牌
- 构建完整的特摄文化生态
- 拓展更多相关品类和服务

---

*本文档最后更新时间: 2025年6月*
