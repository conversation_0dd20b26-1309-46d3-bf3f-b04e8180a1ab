<template>
  <view class="flex flex-col min-h-screen bg-slate-50">
    <!-- 使用自定义NavBar组件 -->
    <NavBar
      title="公告中心"
      bgColor="#FFFFFF"
      color="#333333"
      :showBack="true"
      :fixed="true"
    >
    </NavBar>

    <!-- 主内容区域 -->
    <view class="flex-1 p-4">
      <view class="max-w-2xl mx-auto w-full">
        <!-- 活动列表 -->
        <view class="space-y-4 pb-safe" v-if="announcementList.length">
          <view
            v-for="item in announcementList"
            :key="item.id"
            class="bg-white rounded-xl overflow-hidden shadow-sm active:bg-slate-50 transition-colors"
          >
            <!-- 标题区域 -->
            <view class="p-4 pb-2">
              <text class="text-base font-semibold text-text-primary">
                {{ item.title }}
              </text>
            </view>

            <!-- 标签 -->
            <view>
              <view class="px-4 pb-2 flex items-center gap-1">
                <view
                  class="py-1 px-2.5 text-xs w-fit rounded-full font-medium flex items-center gap-1 border"
                  :class="{
                    'bg-accent/10 text-accent border-blue-200':
                      item.typeName === '促销',
                    'bg-secondary/10 text-secondary border-green-200':
                      item.typeName === '系统',
                    'bg-heroic/10 text-heroic border-orange-200':
                      item.typeName === '活动',
                    'bg-purple-200 text-purple-600 border-purple-200':
                      item.typeName === '更新',
                    'bg-gray/10 text-gray-600 border-gray-200': ![
                      '促销',
                      '系统',
                      '活动',
                      '更新'
                    ].includes(item.typeName)
                  }"
                >
                  <van-icon
                    size="14"
                    :name="
                      {
                        促销: 'after-sale',
                        系统: 'info-o',
                        活动: 'notes-o',
                        更新: 'gift-o'
                      }[item.typeName] || 'info-o'
                    "
                    :color="
                      {
                        促销: '#3b82f6',
                        系统: '#10b981',
                        活动: '#f59e0b',
                        更新: '#8b5cf6'
                      }[item.typeName] || '#666666'
                    "
                  />
                  {{ item.typeName }}
                </view>

                <van-icon name="clock-o" size="14" color="#666666" />
                <text class="text-xs text-text-secondary">
                  {{
                    item.startTime
                      ? moment(item.startTime).format("YYYY-MM-DD")
                      : "-"
                  }}
                  ~
                  {{
                    item.endTime
                      ? moment(item.endTime).format("YYYY-MM-DD")
                      : "-"
                  }}
                </text>
              </view>
            </view>

            <!-- 图片区域 -->
            <view class="p-4 pb-2">
              <view
                class="relative h-40 bg-slate-50 rounded-lg overflow-hidden mx-4"
              >
                <CacheImgs
                  :src="item.url"
                  :alt="item.title"
                  class="w-full h-full object-cover"
                />
              </view>
            </view>

            <!-- 操作区域 -->
            <view class="p-4 pt-2" @click="openDetail(item.id)">
              <view class="flex justify-between items-center">
                <text class="text-xs text-text-secondary">点击查看详情</text>
                <van-icon name="arrow" color="#666666" />
              </view>
            </view>
          </view>
        </view>
        <!-- 无订单状态 -->
        <view class="flex flex-col items-center justify-center py-10" v-else>
          <text class="text-xl font-semibold mb-2 text-text-primary"
            >暂无公告</text
          >
        </view>
      </view>
    </view>
    <up-popup
      v-model:show="showPopup"
      mode="bottom"
      round="16"
      safe-area-inset-bottom
    >
      <!-- 标题区域 -->
      <view class="p-4 pb-2">
        <text class="text-base font-semibold text-text-primary">
          {{ announcementDetail.title }}
        </text>
      </view>

      <!-- 标签 -->
      <view>
        <view class="px-4 pb-2 flex items-center gap-1">
          <view
            class="py-1 px-2.5 text-xs w-fit rounded-full font-medium flex items-center gap-1 border"
            :class="{
              'bg-accent/10 text-accent border-blue-200':
                announcementDetail.typeName === '促销',
              'bg-secondary/10 text-secondary border-green-200':
                announcementDetail.typeName === '系统',
              'bg-heroic/10 text-heroic border-orange-200':
                announcementDetail.typeName === '活动',
              'bg-purple-200 text-purple-600 border-purple-200':
                announcementDetail.typeName === '更新',
              'bg-gray/10 text-gray-600 border-gray-200': ![
                '促销',
                '系统',
                '活动',
                '更新'
              ].includes(announcementDetail.typeName)
            }"
          >
            <van-icon
              size="14"
              :name="
                {
                  促销: 'after-sale',
                  系统: 'info-o',
                  活动: 'notes-o',
                  更新: 'gift-o'
                }[announcementDetail.typeName] || 'info-o'
              "
              :color="
                {
                  促销: '#3b82f6',
                  系统: '#10b981',
                  活动: '#f59e0b',
                  更新: '#8b5cf6'
                }[announcementDetail.typeName] || '#666666'
              "
            />
            {{ announcementDetail.typeName }}
          </view>

          <van-icon name="clock-o" color="#666666" size="14" />
          <text class="text-xs text-text-secondary">
            {{
              announcementDetail.startTime
                ? moment(announcementDetail.startTime).format("YYYY-MM-DD")
                : "-"
            }}
            ~
            {{
              announcementDetail.endTime
                ? moment(announcementDetail.endTime).format("YYYY-MM-DD")
                : "-"
            }}
          </text>
        </view>
      </view>
      <scroll-view scroll-y class="px-4 mt-2 max-h-[600rpx] min-h-[400rpx]">
        <p v-html="announcementDetail.content" class="content"></p>
      </scroll-view>
    </up-popup>
  </view>
</template>

<script lang="ts" setup>
import { ref } from "vue"
import CacheImgs from "@/components/CacheImgs.vue"
import NavBar from "@/components/NavBar.vue"
import { onLoad, onShow } from "@dcloudio/uni-app"
import { GET_ANNOUNCEMENT_DETAIL, GET_ANNOUNCEMENT_LIST } from "@/api/api"
import moment from "moment"
import { ANNOUNCEMENT_TYPE } from "@/enum"
import { toHtml } from "@/util/common/quilToHtml"
const announcementList = ref<any[]>([])
const announcementDetail = ref<any>({})
const detailLoading = ref(false)
const showPopup = ref(false)

onLoad((options) => {
  if (options.id) {
    openDetail(options.id)
  }
  getAnnouncementList()
})

const getAnnouncementList = async () => {
  const res = await GET_ANNOUNCEMENT_LIST()
  if (res.code === 200) {
    const resData = res.data
    announcementList.value = resData.map((item: any) => ({
      ...item,
      typeName:
        ANNOUNCEMENT_TYPE.find((type) => type.value === item.type)?.label || "-"
    }))
  } else {
    uni.showToast({ title: res.message, icon: "none" })
  }
}

function openDetail(id: string) {
  showPopup.value = true
  getAnnouncementDetail(id)
}

const getAnnouncementDetail = async (id: string) => {
  detailLoading.value = true
  const res = await GET_ANNOUNCEMENT_DETAIL(id)
  if (res.code === 200) {
    const resData = res.data
    announcementDetail.value = {
      ...resData,
      typeName:
        ANNOUNCEMENT_TYPE.find((type) => type.value === resData.type)?.label ||
        "-",
      content: toHtml(resData.content)
    }
    detailLoading.value = false
  } else {
    detailLoading.value = false
    uni.showToast({ title: res.message, icon: "none" })
  }
}
</script>
<style lang="scss" scoped></style>
