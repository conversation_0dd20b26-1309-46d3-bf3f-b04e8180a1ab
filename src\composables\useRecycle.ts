import { ref } from 'vue'
import type { RecyclingOrderVO, CreateOrderRequest } from '@/types/recycle'
import { recyclingApi } from '@/api/recycling'

export function useRecycle() {
  const loading = ref(false)
  const currentOrder = ref<RecyclingOrderVO | null>(null)

  // 获取订单详情
  const fetchOrderDetail = async (orderId: string): Promise<RecyclingOrderVO | null> => {
    loading.value = true
    try {
      const res = await recyclingApi.getOrderDetail(orderId)
      currentOrder.value = res.data
      return res.data
    } catch (error: any) {
      uni.showToast({
        title: error.message || '获取订单详情失败',
        icon: 'none'
      })
      return null
    } finally {
      loading.value = false
    }
  }

  // 创建回收订单
  const createOrder = async (orderData: CreateOrderRequest): Promise<string | null> => {
    loading.value = true
    uni.showLoading({ title: '提交中...' })

    try {
      const res = await recyclingApi.createOrder(orderData)
      
      uni.hideLoading()
      uni.showToast({
        title: '提交成功',
        icon: 'success'
      })

      return res.data
    } catch (error: any) {
      uni.hideLoading()
      uni.showToast({
        title: error.message || '提交失败',
        icon: 'none'
      })
      return null
    } finally {
      loading.value = false
    }
  }

  // 取消订单
  const cancelOrder = async (orderId: string): Promise<boolean> => {
    loading.value = true
    
    try {
      await recyclingApi.cancelOrder(orderId)
      
      // 更新当前订单状态
      if (currentOrder.value && currentOrder.value.id === orderId) {
        currentOrder.value.orderStatus = 'CANCELLED'
        currentOrder.value.orderStatusDesc = '已取消'
        currentOrder.value.updateTime = Date.now()
      }

      uni.showToast({
        title: '订单已取消',
        icon: 'success'
      })
      
      return true
    } catch (error: any) {
      uni.showToast({
        title: error.message || '取消订单失败',
        icon: 'none'
      })
      return false
    } finally {
      loading.value = false
    }
  }

  // 确认寄送
  const confirmShipment = async (orderId: string, shippingInfo: string): Promise<boolean> => {
    loading.value = true
    
    try {
      await recyclingApi.confirmShipment({ orderId, shippingInfo })
      
      // 更新当前订单状态
      if (currentOrder.value && currentOrder.value.id === orderId) {
        currentOrder.value.orderStatus = 'SHIPPING_CONFIRMED'
        currentOrder.value.orderStatusDesc = '确认寄送'
        currentOrder.value.shippingInfo = shippingInfo
        currentOrder.value.updateTime = Date.now()
      }

      uni.showToast({
        title: '确认寄送成功',
        icon: 'success'
      })
      
      return true
    } catch (error: any) {
      uni.showToast({
        title: error.message || '确认寄送失败',
        icon: 'none'
      })
      return false
    } finally {
      loading.value = false
    }
  }

  // 申请退回
  const requestReturn = async (orderId: string): Promise<boolean> => {
    loading.value = true
    
    try {
      await recyclingApi.completeReturn(orderId)
      
      // 更新当前订单状态
      if (currentOrder.value && currentOrder.value.id === orderId) {
        currentOrder.value.orderStatus = 'RETURN_REQUESTED'
        currentOrder.value.orderStatusDesc = '申请退回'
        currentOrder.value.updateTime = Date.now()
      }

      uni.showToast({
        title: '申请退回成功',
        icon: 'success'
      })
      
      return true
    } catch (error: any) {
      uni.showToast({
        title: error.message || '申请退回失败',
        icon: 'none'
      })
      return false
    } finally {
      loading.value = false
    }
  }

  return {
    loading,
    currentOrder,
    fetchOrderDetail,
    createOrder,
    cancelOrder,
    confirmShipment,
    requestReturn
  }
}
