import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

import 'package:soko/shared/presentation/widgets/custom_card.dart';

/// 支付订单信息组件
class PaymentOrderInfo extends StatelessWidget {

  const PaymentOrderInfo({
    super.key,
    required this.orderId,
    required this.amount,
    required this.subject,
  });
  final String orderId;
  final double amount;
  final String subject;

  @override
  Widget build(BuildContext context) {
    return CustomCard(
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // 标题
          Row(
            children: [
              Icon(
                Icons.receipt_long,
                color: Colors.green,
                size: 20.w,
              ),
              SizedBox(width: 8.w),
              Text(
                '订单信息',
                style: TextStyle(
                  fontSize: 16.sp,
                  fontWeight: FontWeight.w600,
                ),
              ),
            ],
          ),
          SizedBox(height: 16.h),

          // 订单详情
          _buildInfoRow('订单编号', orderId),
          SizedBox(height: 12.h),
          _buildInfoRow('商品名称', subject),
          SizedBox(height: 12.h),
          _buildInfoRow('订单金额', '¥${amount.toStringAsFixed(2)}', isAmount: true),

          // 分割线
          Padding(
            padding: EdgeInsets.symmetric(vertical: 16.h),
            child: Divider(
              color: Colors.grey[300],
              height: 1,
            ),
          ),

          // 支付金额（突出显示）
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Text(
                '应付金额',
                style: TextStyle(
                  fontSize: 18.sp,
                  fontWeight: FontWeight.w600,
                  color: Colors.black87,
                ),
              ),
              Text(
                '¥${amount.toStringAsFixed(2)}',
                style: TextStyle(
                  fontSize: 24.sp,
                  fontWeight: FontWeight.w600,
                  color: Colors.red,
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  /// 构建信息行
  Widget _buildInfoRow(String label, String value, {bool isAmount = false}) {
    return Row(
      mainAxisAlignment: MainAxisAlignment.spaceBetween,
      children: [
        Text(
          label,
          style: TextStyle(
            fontSize: 14.sp,
            color: Colors.grey[600],
          ),
        ),
        Expanded(
          child: Text(
            value,
            textAlign: TextAlign.right,
            style: TextStyle(
              fontSize: 14.sp,
              fontWeight: isAmount ? FontWeight.w500 : FontWeight.normal,
              color: isAmount ? Colors.red : Colors.black87,
            ),
            maxLines: 2,
            overflow: TextOverflow.ellipsis,
          ),
        ),
      ],
    );
  }
}
