import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:go_router/go_router.dart';

import 'package:soko/core/theme/app_colors.dart';
import 'package:soko/core/theme/app_text_styles.dart';
import 'package:soko/shared/presentation/widgets/custom_app_bar.dart';
import 'package:soko/shared/presentation/widgets/loading_widget.dart';
import 'package:soko/features/cart/presentation/providers/cart_provider.dart';
import 'package:soko/features/cart/presentation/widgets/coupon_selector.dart';
import 'package:soko/features/cart/presentation/widgets/cart_price_details.dart';
import 'package:soko/features/cart/domain/entities/coupon.dart';
import 'package:soko/features/cart/domain/services/price_calculation_service.dart';

/// 结算页面
class CheckoutPage extends ConsumerStatefulWidget {
  const CheckoutPage({super.key});

  @override
  ConsumerState<CheckoutPage> createState() => _CheckoutPageState();
}

class _CheckoutPageState extends ConsumerState<CheckoutPage> {
  String? _selectedAddressId;
  String _selectedPaymentMethod = 'wechat';
  bool _isSubmitting = false;

  // 模拟优惠券数据
  final List<Coupon> _mockCoupons = [
    Coupon(
      id: 'coupon_1',
      name: '新用户专享',
      description: '首次购买专享优惠',
      type: CouponType.amount,
      value: 20,
      minAmount: 100,
      validFrom: DateTime.now().subtract(const Duration(days: 1)).millisecondsSinceEpoch,
      validTo: DateTime.now().add(const Duration(days: 30)).millisecondsSinceEpoch,
      status: CouponStatus.available,
      usedCount: 0,
    ),
    Coupon(
      id: 'coupon_2',
      name: '满减优惠券',
      description: '满200减50',
      type: CouponType.amount,
      value: 50,
      minAmount: 200,
      validFrom: DateTime.now().subtract(const Duration(days: 1)).millisecondsSinceEpoch,
      validTo: DateTime.now().add(const Duration(days: 15)).millisecondsSinceEpoch,
      status: CouponStatus.available,
      usedCount: 0,
    ),
  ];

  @override
  Widget build(BuildContext context) {
    final cartState = ref.watch(cartProvider);
    final selectedItems = cartState.items.where((item) => item.selected && item.available).toList();

    if (selectedItems.isEmpty) {
      return const Scaffold(
        appBar: CustomAppBar(title: '结算'),
        body: Center(
          child: Text('没有选中的商品'),
        ),
      );
    }

    return Scaffold(
      backgroundColor: AppColors.background,
      appBar: const CustomAppBar(title: '确认订单'),
      body: Column(
        children: [
          Expanded(
            child: SingleChildScrollView(
              child: Column(
                children: [
                  // 收货地址
                  _buildAddressSection(),
                  SizedBox(height: 8.h),
                  // 商品列表
                  _buildProductList(selectedItems),
                  SizedBox(height: 8.h),
                  // 优惠券选择
                  _buildCouponSection(cartState),
                  SizedBox(height: 8.h),
                  // 支付方式
                  _buildPaymentSection(),
                  SizedBox(height: 8.h),
                  // 价格详情
                  const CartPriceDetails(),
                ],
              ),
            ),
          ),
          // 底部提交栏
          _buildSubmitBar(cartState),
        ],
      ),
    );
  }

  /// 构建收货地址部分
  Widget _buildAddressSection() {
    return Container(
      margin: EdgeInsets.all(16.w),
      padding: EdgeInsets.all(16.w),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(8.r),
        boxShadow: const [
          BoxShadow(
            color: AppColors.shadow,
            blurRadius: 4,
            offset: Offset(0, 2),
          ),
        ],
      ),
      child: Row(
        children: [
          Icon(
            Icons.location_on_outlined,
            color: AppColors.primary,
            size: 24.sp,
          ),
          SizedBox(width: 12.w),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  '收货地址',
                  style: AppTextStyles.bodyLarge.copyWith(
                    fontWeight: FontWeight.w600,
                  ),
                ),
                SizedBox(height: 4.h),
                Text(
                  '请选择收货地址',
                  style: AppTextStyles.bodyMedium.copyWith(
                    color: AppColors.textSecondary,
                  ),
                ),
              ],
            ),
          ),
          Icon(
            Icons.chevron_right,
            color: AppColors.textTertiary,
            size: 20.sp,
          ),
        ],
      ),
    );
  }

  /// 构建商品列表
  Widget _buildProductList(List selectedItems) {
    return Container(
      margin: EdgeInsets.symmetric(horizontal: 16.w),
      padding: EdgeInsets.all(16.w),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(8.r),
        boxShadow: const [
          BoxShadow(
            color: AppColors.shadow,
            blurRadius: 4,
            offset: Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(
                Icons.shopping_bag_outlined,
                color: AppColors.primary,
                size: 20.sp,
              ),
              SizedBox(width: 8.w),
              Text(
                '商品清单',
                style: AppTextStyles.bodyLarge.copyWith(
                  fontWeight: FontWeight.w600,
                ),
              ),
              const Spacer(),
              Text(
                '共${selectedItems.length}件',
                style: AppTextStyles.bodySmall.copyWith(
                  color: AppColors.textSecondary,
                ),
              ),
            ],
          ),
          SizedBox(height: 12.h),
          ...selectedItems.map(_buildProductItem),
        ],
      ),
    );
  }

  /// 构建商品项
  Widget _buildProductItem(item) {
    return Container(
      padding: EdgeInsets.symmetric(vertical: 8.h),
      child: Row(
        children: [
          // 商品图片
          Container(
            width: 60.w,
            height: 60.w,
            decoration: BoxDecoration(
              borderRadius: BorderRadius.circular(6.r),
              color: AppColors.background,
            ),
            child: item.productImage != null
                ? ClipRRect(
                    borderRadius: BorderRadius.circular(6.r),
                    child: Image.network(
                      item.productImage!,
                      fit: BoxFit.cover,
                      errorBuilder: (context, error, stackTrace) => const Icon(
                        Icons.image_not_supported,
                        color: AppColors.textTertiary,
                      ),
                    ),
                  )
                : const Icon(
                    Icons.image_not_supported,
                    color: AppColors.textTertiary,
                  ),
          ),
          SizedBox(width: 12.w),
          // 商品信息
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  item.productName,
                  style: AppTextStyles.bodyMedium,
                  maxLines: 2,
                  overflow: TextOverflow.ellipsis,
                ),
                if (item.skuName != null) ...[
                  SizedBox(height: 2.h),
                  Text(
                    item.skuName!,
                    style: AppTextStyles.bodySmall.copyWith(
                      color: AppColors.textSecondary,
                    ),
                  ),
                ],
                SizedBox(height: 4.h),
                Row(
                  children: [
                    Text(
                      '¥${item.price.toStringAsFixed(2)}',
                      style: AppTextStyles.bodyMedium.copyWith(
                        color: AppColors.primary,
                        fontWeight: FontWeight.w600,
                      ),
                    ),
                    const Spacer(),
                    Text(
                      'x${item.quantity}',
                      style: AppTextStyles.bodyMedium.copyWith(
                        color: AppColors.textSecondary,
                      ),
                    ),
                  ],
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  /// 构建优惠券部分
  Widget _buildCouponSection(cartState) {
    final availableCoupons = ref.read(cartProvider.notifier).getAvailableCoupons(_mockCoupons);
    final totalAmount = cartState.items
        .where((item) => item.selected && item.available)
        .fold<double>(0, (sum, item) => sum + item.totalPrice);

    return Container(
      margin: EdgeInsets.symmetric(horizontal: 16.w),
      child: CouponSelector(
        availableCoupons: availableCoupons,
        selectedCoupon: cartState.selectedCoupon,
        totalAmount: totalAmount,
        onCouponSelected: (coupon) {
          ref.read(cartProvider.notifier).selectCoupon(coupon);
        },
      ),
    );
  }

  /// 构建支付方式部分
  Widget _buildPaymentSection() {
    return Container(
      margin: EdgeInsets.symmetric(horizontal: 16.w),
      padding: EdgeInsets.all(16.w),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(8.r),
        boxShadow: const [
          BoxShadow(
            color: AppColors.shadow,
            blurRadius: 4,
            offset: Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(
                Icons.payment_outlined,
                color: AppColors.primary,
                size: 20.sp,
              ),
              SizedBox(width: 8.w),
              Text(
                '支付方式',
                style: AppTextStyles.bodyLarge.copyWith(
                  fontWeight: FontWeight.w600,
                ),
              ),
            ],
          ),
          SizedBox(height: 12.h),
          _buildPaymentOption('wechat', '微信支付', Icons.wechat),
          _buildPaymentOption('alipay', '支付宝', Icons.account_balance_wallet),
        ],
      ),
    );
  }

  /// 构建支付选项
  Widget _buildPaymentOption(String value, String title, IconData icon) {
    final isSelected = _selectedPaymentMethod == value;
    
    return GestureDetector(
      onTap: () {
        setState(() {
          _selectedPaymentMethod = value;
        });
      },
      child: Container(
        padding: EdgeInsets.symmetric(vertical: 8.h),
        child: Row(
          children: [
            Icon(
              icon,
              color: isSelected ? AppColors.primary : AppColors.textSecondary,
              size: 24.sp,
            ),
            SizedBox(width: 12.w),
            Expanded(
              child: Text(
                title,
                style: AppTextStyles.bodyMedium.copyWith(
                  color: isSelected ? AppColors.primary : AppColors.textPrimary,
                  fontWeight: isSelected ? FontWeight.w600 : FontWeight.normal,
                ),
              ),
            ),
            Icon(
              isSelected ? Icons.radio_button_checked : Icons.radio_button_unchecked,
              color: isSelected ? AppColors.primary : AppColors.textTertiary,
              size: 20.sp,
            ),
          ],
        ),
      ),
    );
  }

  /// 构建提交栏
  Widget _buildSubmitBar(cartState) {
    final calculation = cartState.priceCalculation ?? 
        PriceCalculationService.calculateCartPrice(
          items: cartState.items,
          coupon: cartState.selectedCoupon,
          memberDiscount: cartState.memberDiscount,
        );

    return Container(
      padding: EdgeInsets.all(16.w),
      decoration: const BoxDecoration(
        color: Colors.white,
        boxShadow: [
          BoxShadow(
            color: AppColors.shadow,
            blurRadius: 8,
            offset: Offset(0, -2),
          ),
        ],
      ),
      child: SafeArea(
        child: Row(
          children: [
            // 价格信息
            Expanded(
              child: Column(
                mainAxisSize: MainAxisSize.min,
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    '实付金额',
                    style: AppTextStyles.bodyMedium,
                  ),
                  Text(
                    '¥${calculation.finalAmount.toStringAsFixed(2)}',
                    style: AppTextStyles.headlineSmall.copyWith(
                      color: AppColors.primary,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                ],
              ),
            ),
            SizedBox(width: 16.w),
            // 提交订单按钮
            ElevatedButton(
              onPressed: _isSubmitting ? null : _submitOrder,
              style: ElevatedButton.styleFrom(
                backgroundColor: AppColors.primary,
                padding: EdgeInsets.symmetric(horizontal: 32.w, vertical: 12.h),
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(8.r),
                ),
              ),
              child: _isSubmitting
                  ? SizedBox(
                      width: 20.w,
                      height: 20.w,
                      child: const CircularProgressIndicator(
                        strokeWidth: 2,
                        valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
                      ),
                    )
                  : Text(
                      '提交订单',
                      style: AppTextStyles.bodyMedium.copyWith(
                        color: Colors.white,
                        fontWeight: FontWeight.w600,
                      ),
                    ),
            ),
          ],
        ),
      ),
    );
  }

  /// 提交订单
  Future<void> _submitOrder() async {
    if (_selectedAddressId == null) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(content: Text('请选择收货地址')),
      );
      return;
    }

    setState(() {
      _isSubmitting = true;
    });

    try {
      // 模拟提交订单
      await Future.delayed(const Duration(seconds: 2));
      
      // 提交成功，导航到订单详情页
      if (mounted) {
        context.pushReplacement('/order/success');
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('提交失败：$e')),
        );
      }
    } finally {
      if (mounted) {
        setState(() {
          _isSubmitting = false;
        });
      }
    }
  }
}
