<template>
  <view class="product-page bg-gray-100 min-h-screen flex flex-col">
    <!-- 顶部导航栏 -->
    <NavBar
      :title="isAllProducts ? '全部商品' : titleName"
      bgColor="#FFFFFF"
      :showBack="true"
      :fixed="true"
      :showRight="true"
      @leftClick="goBack"
    >
      <template #right>
        <van-icon name="search" size="20" color="#333" @click="goToSearch" />
      </template>
    </NavBar>

    <!-- 过滤器和排序 -->
    <view
      class="bg-white p-2 flex items-center justify-between border-b sticky z-10 shadow-sm"
      :style="{ top: `calc(${statusBarHeight} + 46px)` }"
    >
      <!-- 排序选项 -->
      <view class="flex-1 flex justify-around items-center">
        <!-- 综合下拉菜单 -->
        <view class="relative sort-dropdown">
          <view
            class="py-2 px-4 text-sm flex items-center justify-center sort-dropdown-trigger"
            :class="
              sortOption === 'featured'
                ? 'text-primary font-medium'
                : 'text-text-primary'
            "
            @tap="toggleSortDropdown"
          >
            <text>{{ sortOptions[sortOption] || "综合" }}</text>
            <i
              class="fas fa-chevron-down ml-1 text-xs"
              :class="isSortDropdownOpen ? 'transform rotate-180' : ''"
            ></i>
          </view>

          <!-- 遮罩层 -->
          <view
            v-if="isSortDropdownOpen"
            class="fixed left-0 right-0 top-0 bottom-0 z-10"
            @tap.stop="closeSortDropdown"
          ></view>

          <!-- 下拉菜单 -->
          <view
            v-if="isSortDropdownOpen"
            class="absolute top-full left-0 mt-1 bg-white rounded-lg shadow-lg z-20 w-32 border border-gray-200 sort-dropdown-menu"
          >
            <view
              v-for="(label, value) in sortOptions"
              :key="value"
              class="px-3 py-2 text-xs border-b border-gray-100 last:border-0"
              :class="
                sortOption === value
                  ? 'text-primary bg-gray-50 font-medium'
                  : 'text-text-primary'
              "
              @tap="changeSortOption(value)"
            >
              <view class="flex items-center">
                <i
                  v-if="sortOption === value"
                  class="fas fa-check mr-2 text-primary text-2xs"
                ></i>
                <text v-else class="w-4 mr-2"></text>
                <text>{{ label }}</text>
              </view>
            </view>
          </view>
        </view>

        <view
          class="py-2 px-4 text-sm flex items-center justify-center"
          :class="
            sortOption === 'sales'
              ? 'text-primary font-medium'
              : 'text-text-primary'
          "
          @tap="changeSortOption('sales')"
        >
          <text>销量</text>
        </view>
        <view
          class="py-2 px-4 text-sm flex items-center justify-center"
          :class="
            sortOption === 'newable'
              ? 'text-primary font-medium'
              : 'text-text-primary'
          "
          @tap="changeSortOption('newable')"
        >
          <text>新品</text>
        </view>

        <!-- 筛选按钮 -->
        <view
          class="py-2 px-4 text-sm flex items-center justify-center"
          @tap="toggleFilterPanel"
        >
          <i class="fas fa-filter text-text-primary mr-1"></i>
          <text>筛选</text>
        </view>
      </view>
    </view>

    <!-- 主内容区域 -->
    <van-pull-refresh v-model="refreshLoading" @refresh="onRefresh">
      <view class="flex-1 p-4">
        <!-- 加载状态 -->
        <template v-if="isLoading">
          <view class="grid grid-cols-2 gap-4">
            <view
              v-for="item in 8"
              :key="item"
              class="bg-white rounded-xl overflow-hidden shadow-sm"
            >
              <view class="h-40 w-full bg-slate-200"></view>
              <view class="p-3 space-y-2">
                <view class="h-4 w-3/4 bg-slate-200 rounded"></view>
                <view class="h-4 w-1/2 bg-slate-200 rounded"></view>
                <view class="h-6 w-1/3 bg-slate-200 rounded"></view>
              </view>
            </view>
          </view>
        </template>

        <!-- 无结果状态 -->
        <template v-else-if="filteredProducts.length === 0">
          <view class="flex flex-col items-center justify-center py-10">
            <view
              class="w-20 h-20 bg-slate-100 rounded-full flex items-center justify-center mb-4"
            >
              <i class="fas fa-search text-text-disabled text-3xl"></i>
            </view>
            <text class="text-lg font-medium mb-2">未找到相关商品</text>
            <text class="text-text-secondary text-center mb-6"
              >尝试使用其他筛选条件</text
            >
          </view>
        </template>

        <!-- 商品列表 -->
        <template v-else>
          <view class="grid grid-cols-2 gap-4">
            <view
              v-for="product in displayProducts"
              :key="product.id"
              class="bg-white rounded-xl overflow-hidden shadow-md border-2 border-gray-300 relative"
              @tap="goToProductDetail(product.id)"
            >
              <view class="relative h-40 bg-white">
                <CacheImgs
                  :src="product.image"
                  mode="aspectFit"
                  class="w-full h-full"
                ></CacheImgs>
                <!-- 现货/预定标签 -->
                <text
                  class="absolute top-2 left-2 px-2 py-0.5 rounded-full text-xs text-white font-medium"
                  :class="
                    product.stockType === 'PRESALE'
                      ? 'bg-villain bg-opacity-75'
                      : 'bg-secondary bg-opacity-75'
                  "
                >
                  {{ product.stockType === "PRESALE" ? "预定" : "现货" }}
                </text>
                <!-- 右上角标签：新品优先，其次折扣 -->
                <text
                  v-if="product.newable"
                  class="absolute top-2 right-2 bg-primary bg-opacity-75 text-white px-2 py-0.5 rounded-full text-xs"
                  >新品</text
                >
                <text
                  v-else-if="product.discount"
                  class="absolute top-2 right-2 bg-heroic bg-opacity-75 text-white px-2 py-0.5 rounded-full text-xs"
                  >最高-{{ product.discount }}%</text
                >
              </view>
              <view class="h-px bg-gray-200 w-full"></view>
              <view class="p-3 flex flex-col h-[76px]">
                <view class="flex-1">
                  <text class="font-medium text-sm line-clamp-2">{{
                    product.name
                  }}</text>
                </view>
                <view class="flex items-center mt-auto">
                  <view class="flex items-center gap-1">
                    <text class="text-primary font-bold text-base"
                      >¥{{ product.price }}</text
                    >
                  </view>
                </view>
              </view>
            </view>
          </view>

          <!-- 加载状态提示 -->
          <view v-if="isLoadingMore" class="py-4 flex justify-center">
            <view class="flex items-center space-x-2">
              <view
                class="w-5 h-5 border-2 border-primary border-t-transparent rounded-full animate-spin"
              ></view>
              <text class="text-text-secondary">加载中...</text>
            </view>
          </view>

          <!-- 无更多数据提示 -->
          <view v-if="noMoreData" class="py-4 flex justify-center">
            <text class="text-text-secondary text-sm">已经到底啦～</text>
          </view>

          <!-- 回到顶部按钮和调试信息 -->
          <view
            v-if="displayProducts.length > 8"
            class="fixed bottom-6 right-6 z-10"
          >
            <view
              class="h-10 w-10 rounded-full bg-white shadow-md flex items-center justify-center"
              @tap="scrollToTop"
            >
              <i class="fas fa-chevron-up text-text-primary"></i>
            </view>
          </view>
        </template>
      </view>
    </van-pull-refresh>

    <!-- 筛选面板 -->
    <up-popup
      ref="filterPopup"
      :show="showFilterPopup"
      mode="bottom"
      :round="10"
      @close="showFilterPopup = false"
    >
      <view class="h-[80vh] p-4">
        <view class="pb-4 border-b">
          <text class="text-lg font-medium">筛选条件</text>
          <text class="text-text-secondary text-sm block mt-1"
            >设置筛选条件以找到您想要的商品</text
          >
        </view>

        <scroll-view scroll-y class="h-[calc(80vh-10rem)] py-4">
          <view class="space-y-6">
            <!-- 分类筛选 -->
            <view class="space-y-2">
              <text class="text-sm font-medium">分类</text>
              <view class="grid grid-cols-2 gap-2">
                <view
                  v-for="(category, index) in categories"
                  :key="index"
                  class="flex items-center space-x-2"
                >
                  <checkbox
                    :checked="selectedCategories.includes(category)"
                    :value="category"
                    @tap="handleCategoryChange(category)"
                    style="transform: scale(0.8)"
                  ></checkbox>
                  <text class="text-sm">{{ category }}</text>
                </view>
              </view>
            </view>

            <!-- 价格范围 -->
            <view class="space-y-4">
              <view class="flex justify-between">
                <text class="text-sm font-medium">价格范围</text>
                <text class="text-sm"
                  >¥{{ priceRange[0] }} - ¥{{ priceRange[1] }}</text
                >
              </view>
              <slider
                :min="0"
                :max="10000"
                :step="100"
                :value="priceRange[0]"
                @change="onMinPriceChange"
                show-value
                class="mx-4 my-2"
              ></slider>
              <slider
                :min="0"
                :max="99999"
                :step="100"
                :value="priceRange[1]"
                @change="onMaxPriceChange"
                show-value
                class="mx-4 my-2"
              ></slider>
            </view>
          </view>
        </scroll-view>

        <view class="flex justify-between pt-4 border-t">
          <view
            class="px-6 py-2 rounded-lg border border-gray-300 text-center"
            @tap="resetFilters"
          >
            重置
          </view>
          <view
            class="px-6 py-2 rounded-lg bg-primary text-white text-center"
            @tap="applyFiltersAndClosePanel"
          >
            应用筛选
          </view>
        </view>
      </view>
    </up-popup>
  </view>
</template>

<script setup>
import CacheImgs from "@/components/CacheImgs.vue"
import { getCategoryById, listProduct } from "@/api/api"
import { onLoad, onPageScroll, onReachBottom } from "@dcloudio/uni-app"
import { computed, onMounted, onUnmounted, ref, onBeforeMount } from "vue"
import NavBar from "@/components/NavBar.vue"

// 添加状态栏高度变量
const statusBarHeight = ref("0px")

// 分类数据
const categories = ref([])
const categoryId = ref(null)
const titleName = ref("")
const parentCode = ref("")
const isAllProducts = ref(true)
const refreshLoading = ref(false)
// 获取状态栏高度
onBeforeMount(() => {
  const systemInfo = uni.getSystemInfoSync()
  statusBarHeight.value = `${systemInfo.statusBarHeight}px`
  console.log("商品列表页面获取状态栏高度:", statusBarHeight.value)
})

// 根据parentCode判断使用的查询参数
const queryKey = ref("categoryId")
const queryValue = ref(null)

const queryParams = ref({
  name: "",
  categoryId: null,
  typeId: null,
  acgId: null,
  brandId: null,
  // 价格范围
  minPrice: null,
  maxPrice: null,
  // 新品
  newable: null,
  // 排序字段
  sortField: "",
  // 排序方向
  sortOrder: "",
  // 页码
  page: 1,
  // 每页数量，改小一些确保有多页数据
  pageSize: 10
})

// 接收URL参数
onLoad((options) => {
  // 检查是否有parentCode参数
  if (options.parentCode) {
    parentCode.value = options.parentCode

    // 根据parentCode确定查询参数类型
    switch (options.parentCode) {
      case "BRAND":
        queryParams.value.brandId = options.brandId
        queryKey.value = "brandId"
        break
      case "TYPE":
        queryParams.value.typeId = options.typeId
        queryKey.value = "typeId"
        break
      case "ACG":
        queryParams.value.acgId = options.acgId
        queryKey.value = "acgId"
        break
      case "CLASSIFICATION":
        queryParams.value.categoryId = options.categoryId
        queryKey.value = "categoryId"
        break
      default:
        queryKey.value = "categoryId"
    }

    // 设置对应的查询值
    if (options[queryKey.value]) {
      queryValue.value = options[queryKey.value]
      isAllProducts.value = false
    }
  }

  // 检查是否有搜索关键词参数
  if (options.name) {
    const name = decodeURIComponent(options.name)
    // 设置查询参数
    queryParams.value.name = name
    // 同步到搜索关键词变量
    searchKeyword.value = name
    // 设置页面标题为搜索结果
    titleName.value = `${name}`
    isAllProducts.value = false
  } else if (options.categoryId && options.categoryName) {
    categoryId.value = options.categoryId
    titleName.value = decodeURIComponent(options.categoryName)
    isAllProducts.value = false

    // 如果有特定类别，保存到已选分类中
    selectedCategories.value = [titleName.value]
  }
})

// 获取分类数据
const fetchCategories = async () => {
  if (!categoryId.value) {
    // 如果没有categoryId，直接加载商品数据
    fetchProducts()
    return
  }

  try {
    const result = await getCategoryById(categoryId.value)

    // 将分类数据处理为简单的字符串数组
    const allCategories = []

    // 处理根据ID查询和列表查询的不同结果结构
    if (categoryId.value && result.data) {
      // 单个分类查询结果
      allCategories.push(result.data.name)
    } else if (result.data && Array.isArray(result.data)) {
      // 分类列表查询结果
      result.data.forEach((category) => {
        allCategories.push(category.name)
      })
    }

    categories.value = allCategories

    // 更新产品数据，根据分类筛选
    if (!isAllProducts.value) {
      // 如果是特定类别，设置当前选择的分类
      if (categories.value.includes(titleName.value)) {
        selectedCategories.value = [titleName.value]
      }
    }
  } catch (error) {
    console.error("获取分类数据失败", error)
    uni.showToast({
      title: "获取分类数据失败",
      icon: "none",
      duration: 2000
    })
  } finally {
    // 无论如何都要加载商品数据
    fetchProducts()
  }
}

// 状态变量
const priceRange = ref([0, 10000])
const sortOption = ref("featured")
const showNewOnly = ref(false)
const showDiscountOnly = ref(false)
const selectedCategories = ref([])
const isLoading = ref(true)
const isLoadingMore = ref(false)
const noMoreData = ref(false)
const totalPages = ref(1)
const currentPage = ref(1)
const filteredProducts = ref([])
const visibleProducts = ref(8) // 初始显示8个产品
const isSortDropdownOpen = ref(false)
const filterPopup = ref(null)
const searchKeyword = ref("")
const showFilterPopup = ref(false)
// 记录上一次查询返回的数据量
const lastQueryCount = ref(0)
// 记录总记录数
const totalCount = ref(0)

// 定义一个新的排序常量
const NEW_PRODUCT_OPTION = "new_product"

// 排序选项
const sortOptions = {
  featured: "综合",
  "price-asc": "价格：低到高",
  "price-desc": "价格：高到低",
  newest: "最新上架"
}

// 计算属性：当前显示的产品
const displayProducts = computed(() => {
  return filteredProducts.value
})

// 页面加载
onMounted(() => {
  // 只有当categoryId有值时才获取分类数据，否则直接获取产品
  if (categoryId.value) {
    fetchCategories()
  } else {
    fetchProducts()
  }
  startScrollChecker()
})

// 页面卸载
onUnmounted(() => {
  // 清理资源
  stopScrollChecker()
})

// 节流函数
function throttle(fn, delay) {
  let lastCall = 0
  return function (...args) {
    const now = new Date().getTime()
    if (now - lastCall < delay) {
      return
    }
    lastCall = now
    return fn.apply(this, args)
  }
}

// 使用页面生命周期函数 onPageScroll 处理滚动
const scrollHandler = throttle((e) => {
  // 如果正在加载或没有更多数据，不处理
  if (isLoadingMore.value || noMoreData.value) return
}, 300)

// 使用正确的页面生命周期函数 - 仅记录滚动
onPageScroll((e) => {
  scrollHandler(e)
})

// 单独函数用于执行触底检测
const checkReachBottom = () => {
  // 避免重复加载
  if (isLoadingMore.value) {
    return
  }

  // 如果已经验证过没有更多数据，不再加载
  if (noMoreData.value && hasManuallyVerified.value) {
    return
  }

  // 如果商品列表为空，不需要加载更多
  if (filteredProducts.value.length === 0) {
    noMoreData.value = true
    hasManuallyVerified.value = true
    return
  }

  // 根据总记录数判断是否还有更多数据
  const currentLoadedCount = filteredProducts.value.length
  if (currentLoadedCount >= totalCount.value && totalCount.value > 0) {
    noMoreData.value = true
    hasManuallyVerified.value = true
    return
  }

  // 触发加载下一页
  loadNextPage()
}

// 触底钩子函数 - 让系统来决定何时触底
onReachBottom(() => {
  checkReachBottom()
})

// 滚动检测器 - 使用定时器，每秒检查是否应该加载更多
let scrollTimer = null
// 是否已手动验证过没有更多数据
const hasManuallyVerified = ref(false)

// 启动滚动检测器
const startScrollChecker = () => {
  // 先清除可能存在的定时器
  if (scrollTimer) {
    clearInterval(scrollTimer)
  }

  // 创建新的滚动检测定时器
  scrollTimer = setInterval(() => {
    // 如果正在加载，不再触发
    if (isLoadingMore.value) return

    // 如果已经验证过没有更多数据，不再触发加载
    if (noMoreData.value && hasManuallyVerified.value) return

    // 根据总记录数判断是否还有更多数据
    const currentLoadedCount = filteredProducts.value.length
    if (currentLoadedCount >= totalCount.value && totalCount.value > 0) return

    // 获取页面高度信息
    uni
      .createSelectorQuery()
      .selectViewport()
      .scrollOffset((res) => {
        if (!res) return

        // 获取系统信息
        uni.getSystemInfo({
          success: (sysInfo) => {
            const windowHeight = sysInfo.windowHeight
            const scrollTop = res.scrollTop
            const scrollHeight = res.scrollHeight

            // 计算距离底部的位置
            const distanceToBottom = scrollHeight - scrollTop - windowHeight

            // 如果距离底部很近（300px内），触发加载
            if (distanceToBottom < 300 && distanceToBottom >= 0) {
              checkReachBottom()
            }
          }
        })
      })
      .exec()
  }, 1000) // 每秒检查一次
}

// 停止滚动检测器
const stopScrollChecker = () => {
  if (scrollTimer) {
    clearInterval(scrollTimer)
    scrollTimer = null
  }
}

// 加载下一页数据
const loadNextPage = () => {
  // 检查是否可以加载下一页
  if (isLoadingMore.value) {
    return
  }

  // 如果已经验证过没有更多数据，不再加载
  if (noMoreData.value && hasManuallyVerified.value) {
    return
  }

  // 如果商品列表为空，不再加载
  if (filteredProducts.value.length === 0) {
    return
  }

  // 根据总记录数判断是否还有更多数据
  const currentLoadedCount = filteredProducts.value.length
  if (currentLoadedCount >= totalCount.value && totalCount.value > 0) {
    noMoreData.value = true
    hasManuallyVerified.value = true
    return
  }

  // 确保只触发一次加载
  isLoadingMore.value = true

  // 延迟0.5秒后加载
  setTimeout(() => {
    currentPage.value += 1
    fetchProducts(true)
  }, 500)
}

// 点击遮罩层关闭下拉菜单
const closeSortDropdown = () => {
  isSortDropdownOpen.value = false
}

// 应用过滤器
const applyFilters = () => {
  currentPage.value = 1
  noMoreData.value = false
  fetchProducts()
}

// 重置过滤器
const resetFilters = () => {
  priceRange.value = [0, 10000]
  showNewOnly.value = false
  showDiscountOnly.value = false

  // 如果是从特定类别进入，保留该类别筛选
  if (!isAllProducts.value && titleName.value) {
    selectedCategories.value = [titleName.value]
  } else {
    selectedCategories.value = []
  }

  sortOption.value = "featured"
  currentPage.value = 1
  noMoreData.value = false

  applyFilters()

  // 如果过滤面板是打开的，关闭它
  showFilterPopup.value = false
}

// 处理分类选择
const handleCategoryChange = (category) => {
  const index = selectedCategories.value.indexOf(category)
  if (index === -1) {
    selectedCategories.value.push(category)
  } else {
    selectedCategories.value.splice(index, 1)
  }
}

// 切换排序下拉菜单
const toggleSortDropdown = () => {
  isSortDropdownOpen.value = !isSortDropdownOpen.value
}

// 改变排序选项
const changeSortOption = (option) => {
  // 如果点击的是当前已选中的选项，仅关闭下拉菜单，不触发查询
  if (sortOption.value === option) {
    isSortDropdownOpen.value = false
    return
  }

  sortOption.value = option
  isSortDropdownOpen.value = false
  applyFilters()
}

// 切换筛选面板
const toggleFilterPanel = () => {
  showFilterPopup.value = true
}

// 应用筛选并关闭面板
const applyFiltersAndClosePanel = () => {
  applyFilters()
  showFilterPopup.value = false
}

// 价格范围滑块变化处理
const onMinPriceChange = (e) => {
  priceRange.value[0] = e.detail.value
}

const onMaxPriceChange = (e) => {
  priceRange.value[1] = e.detail.value
}

// 滚动到顶部
const scrollToTop = () => {
  uni.pageScrollTo({
    scrollTop: 0,
    duration: 300
  })
}

// 返回上一页
const goBack = () => {
  // 获取当前页面栈
  const pages = getCurrentPages()
  // 如果页面栈长度大于1，说明有上一页，直接返回
  if (pages.length > 1) {
    uni.navigateBack()
  } else {
    // 否则跳转到首页
    uni.switchTab({
      url: "/pages/views/shop/shop"
    })
  }
}

// 跳转到搜索页面
const goToSearch = () => {
  uni.navigateTo({
    url: "/pages/views/search/search"
  })
}

// 跳转到产品详情页面
const goToProductDetail = (productId) => {
  uni.navigateTo({
    url: `/pages/views/product/detail?id=${productId}`
  })
}

// 从后端获取商品数据
const fetchProducts = async (isLoadMore = false) => {
  if (!isLoadMore) {
    isLoading.value = true
    currentPage.value = 1
    noMoreData.value = false
    hasManuallyVerified.value = false
    filteredProducts.value = [] // 清空现有数据
  } else {
    isLoadingMore.value = true
  }

  // 构建查询参数
  const params = { ...queryParams.value }
  params.page = currentPage.value

  // 应用分类筛选
  if (selectedCategories.value.length > 0 && categories.value.length > 0) {
    // 如果是特定分类，使用categoryId
    if (!isAllProducts.value && categoryId.value) {
      params[queryKey.value] = categoryId.value
    }
  }

  // 应用价格范围
  if (priceRange.value[0] > 0) {
    params.minPrice = priceRange.value[0]
  }
  if (priceRange.value[1] < 10000) {
    params.maxPrice = priceRange.value[1]
  }

  // 应用折扣筛选
  if (showDiscountOnly.value) {
    params.discount = true
  }

  // 应用排序
  switch (sortOption.value) {
    case "price-asc":
      params.sortField = "price"
      params.sortOrder = "asc"
      break
    case "price-desc":
      params.sortField = "price"
      params.sortOrder = "desc"
      break
    case "newest":
      params.sortField = "createTime" // 最新上架按创建时间排序
      params.sortOrder = "desc"
      break
    case "newable":
      params.sortField = "createTime" // 新品也按创建时间排序，但会额外添加iNew=true筛选
      params.sortOrder = "desc"
      params.newable = true
      break
    case "sales":
      params.sortField = "sales"
      params.sortOrder = "desc"
      break
    case "featured":
    default:
      params.sortField = null
      params.sortOrder = null
      break
  }

  try {
    const result = await listProduct(params)

    if (result.code === 200) {
      // 处理返回的商品数据
      const products = result.data.records || []

      // 更新总页数和总记录数
      totalPages.value = result.data.pages || 1
      totalCount.value = result.data.total || 0

      // 记录本次查询返回的数据量
      lastQueryCount.value = products.length

      // 格式化商品数据
      const formattedProducts = products.map((item) => ({
        id: item.id,
        name: item.name,
        category: item.categoryName || "",
        price: item.minPrice,
        image: item.files[0]?.thumbnailUrl || item.files[0]?.url || "",
        newable: item.newable || false,
        discount: item.skus?.maxDiscount || null,
        // 添加库存类型字段，默认为现货
        stockType: item.salesType || "STOCK"
      }))

      // 应用前端筛选条件
      let filtered = [...formattedProducts]

      // 如果是加载更多，追加数据
      if (isLoadMore) {
        filteredProducts.value = [...filteredProducts.value, ...filtered]
      } else {
        filteredProducts.value = filtered
      }

      // 检查是否有数据
      const hasNoData = filtered.length === 0

      // 根据总记录数和当前已加载记录数判断是否有更多数据
      const currentLoadedCount = filteredProducts.value.length
      const hasMoreData = currentLoadedCount < totalCount.value

      // 处理没有数据的情况
      if (hasNoData) {
        // 如果是首次加载就没有数据，标记为没有更多数据
        noMoreData.value = true
        hasManuallyVerified.value = true
      } else {
        // 基于总记录数判断是否还有更多
        if (!hasMoreData) {
          noMoreData.value = true
          hasManuallyVerified.value = true
        } else {
          noMoreData.value = false
        }
      }
    } else {
      uni.showToast({
        title: result.message || "获取商品列表失败",
        icon: "none",
        duration: 2000
      })

      if (!isLoadMore) {
        filteredProducts.value = []
        // 没有数据时也标记为没有更多数据
        noMoreData.value = true
        hasManuallyVerified.value = true
      }
    }
  } catch (error) {
    // 只在网络请求失败等实际错误时显示提示
    uni.showToast({
      title: "获取商品列表失败",
      icon: "none",
      duration: 2000
    })

    if (!isLoadMore) {
      filteredProducts.value = []
      // 加载失败也标记为没有更多数据，避免继续加载
      noMoreData.value = true
      hasManuallyVerified.value = true
    }
  } finally {
    isLoading.value = false
    isLoadingMore.value = false
  }
}

const onRefresh = () => {
  fetchCategories()
  refreshLoading.value = false
}
</script>

<style lang="scss" scoped>
.product-page {
  /* 避免页面底部被导航栏遮挡 */
  padding-bottom: env(safe-area-inset-bottom);
}

/* 移除滚动条 */
::-webkit-scrollbar {
  display: none;
  width: 0 !important;
  height: 0 !important;
  -webkit-appearance: none;
  background: transparent;
}

/* 搜索框样式 */
.search-input-container {
  border: 2px solid transparent;
  box-sizing: border-box;
  background-clip: padding-box;

  &.is-focused {
    background-color: white;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
    position: relative;

    &:before {
      content: "";
      position: absolute;
      top: -2px;
      right: -2px;
      bottom: -2px;
      left: -2px;
      z-index: -1;
      border-radius: 9999px;
      background: linear-gradient(to right, #ef4444, #ec4899);
    }
  }
}

/* 点击其他区域关闭排序下拉菜单 */
@media screen {
  .close-dropdown-overlay {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    z-index: 10;
  }
}
</style>
