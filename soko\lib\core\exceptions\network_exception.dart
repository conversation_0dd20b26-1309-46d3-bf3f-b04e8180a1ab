/// 网络异常类
class NetworkException implements Exception {

  const NetworkException({
    required this.message,
    required this.code,
    this.statusCode,
    this.data,
  });
  final String message;
  final String code;
  final int? statusCode;
  final dynamic data;

  @override
  String toString() {
    return 'NetworkException(message: $message, code: $code, statusCode: $statusCode)';
  }

  /// 是否为网络连接错误
  bool get isConnectionError {
    return code == 'CONNECTION_ERROR' || 
           code == 'CONNECTION_TIMEOUT' ||
           code == 'RECEIVE_TIMEOUT' ||
           code == 'SEND_TIMEOUT';
  }

  /// 是否为认证错误
  bool get isAuthError {
    return statusCode == 401 || statusCode == 403;
  }

  /// 是否为服务器错误
  bool get isServerError {
    return statusCode != null && statusCode! >= 500;
  }

  /// 是否为客户端错误
  bool get isClientError {
    return statusCode != null && statusCode! >= 400 && statusCode! < 500;
  }

  /// 获取用户友好的错误消息
  String get userFriendlyMessage {
    if (isConnectionError) {
      return '网络连接失败，请检查网络设置后重试';
    }
    
    if (isAuthError) {
      return '登录已过期，请重新登录';
    }
    
    if (isServerError) {
      return '服务器繁忙，请稍后重试';
    }
    
    return message;
  }
}
