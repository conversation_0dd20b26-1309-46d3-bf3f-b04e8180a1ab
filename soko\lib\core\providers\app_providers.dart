import 'package:flutter_riverpod/flutter_riverpod.dart';

import 'package:soko/core/network/dio_client.dart';
import 'package:soko/core/services/storage_service.dart';

/// 网络客户端提供者
final dioClientProvider = Provider<DioClient>((ref) {
  return DioClient.instance;
});

/// 存储服务提供者
final storageServiceProvider = Provider<StorageService>((ref) {
  return StorageService;
});

/// 应用配置提供者
final appConfigProvider = Provider<Map<String, dynamic>>((ref) {
  // 这里可以从本地存储或远程配置中获取应用配置
  return {
    'theme': 'light',
    'language': 'zh_CN',
    'enableNotifications': true,
    'enableAnalytics': true,
  };
});

/// 用户认证状态提供者
final authStateProvider = StateNotifierProvider<AuthStateNotifier, AuthState>((ref) {
  return AuthStateNotifier(ref);
});

/// 认证状态
enum AuthStatus { unknown, authenticated, unauthenticated }

class AuthState {

  const AuthState({
    required this.status,
    this.user,
    this.token,
  });
  final AuthStatus status;
  final Map<String, dynamic>? user;
  final String? token;

  AuthState copyWith({
    AuthStatus? status,
    Map<String, dynamic>? user,
    String? token,
  }) {
    return AuthState(
      status: status ?? this.status,
      user: user ?? this.user,
      token: token ?? this.token,
    );
  }

  bool get isAuthenticated => status == AuthStatus.authenticated;
  bool get isUnauthenticated => status == AuthStatus.unauthenticated;
  bool get isUnknown => status == AuthStatus.unknown;
}

/// 认证状态管理器
class AuthStateNotifier extends StateNotifier<AuthState> {

  AuthStateNotifier(this.ref) : super(const AuthState(status: AuthStatus.unknown)) {
    _checkAuthStatus();
  }
  final Ref ref;

  /// 检查认证状态
  Future<void> _checkAuthStatus() async {
    final token = StorageService.getToken();
    final userInfo = StorageService.getUserInfo();

    if (token != null && token.isNotEmpty) {
      state = AuthState(
        status: AuthStatus.authenticated,
        token: token,
        user: userInfo,
      );
    } else {
      state = const AuthState(status: AuthStatus.unauthenticated);
    }
  }

  /// 登录
  Future<void> login(String token, Map<String, dynamic> user) async {
    await StorageService.saveToken(token);
    await StorageService.saveUserInfo(user);

    state = AuthState(
      status: AuthStatus.authenticated,
      token: token,
      user: user,
    );
  }

  /// 登出
  Future<void> logout() async {
    await StorageService.logout();

    state = const AuthState(status: AuthStatus.unauthenticated);
  }

  /// 更新用户信息
  Future<void> updateUser(Map<String, dynamic> user) async {
    await StorageService.saveUserInfo(user);

    state = state.copyWith(user: user);
  }
}

/// 应用主题提供者
final themeProvider = StateNotifierProvider<ThemeNotifier, String>((ref) {
  return ThemeNotifier();
});

class ThemeNotifier extends StateNotifier<String> {
  ThemeNotifier() : super('light') {
    _loadTheme();
  }

  Future<void> _loadTheme() async {
    final theme = StorageService.getString('theme', defaultValue: 'light');
    state = theme!;
  }

  Future<void> setTheme(String theme) async {
    await StorageService.setString('theme', theme);
    state = theme;
  }
}

/// 语言提供者
final languageProvider = StateNotifierProvider<LanguageNotifier, String>((ref) {
  return LanguageNotifier();
});

class LanguageNotifier extends StateNotifier<String> {
  LanguageNotifier() : super('zh_CN') {
    _loadLanguage();
  }

  Future<void> _loadLanguage() async {
    final language = StorageService.getString('language', defaultValue: 'zh_CN');
    state = language!;
  }

  Future<void> setLanguage(String language) async {
    await StorageService.setString('language', language);
    state = language;
  }
}
