// 回收模块常量配置

// 订单状态枚举
export enum RecycleOrderStatus {
  DRAFT = 'DRAFT',
  PENDING_APPROVAL = 'PENDING_APPROVAL',
  PRICE_QUOTED = 'PRICE_QUOTED',
  SHIPPING_CONFIRMED = 'SHIPPING_CONFIRMED',
  CANCELLED = 'CANCELLED',
  RECEIVED = 'RECEIVED',
  PAYMENT_CONFIRMED = 'PAYMENT_CONFIRMED',
  RETURN_REQUESTED = 'RETURN_REQUESTED',
  COMPLETED = 'COMPLETED',
  RETURNED = 'RETURNED'
}

// 商品状况枚举
export enum ProductCondition {
  EXCELLENT = 'excellent',
  GOOD = 'good',
  FAIR = 'fair',
  POOR = 'poor'
}

// 商品类别枚举
export enum ProductCategory {
  MOBILE = 'mobile',
  COMPUTER = 'computer',
  APPLIANCE = 'appliance',
  OTHER = 'other'
}

// 商品类别选项
export const PRODUCT_CATEGORIES = [
  { name: '手机数码', value: ProductCategory.MOBILE },
  { name: '电脑办公', value: ProductCategory.COMPUTER },
  { name: '家用电器', value: ProductCategory.APPLIANCE },
  { name: '其他', value: ProductCategory.OTHER }
]

// 商品状况选项
export const CONDITION_OPTIONS = [
  {
    value: ProductCondition.EXCELLENT,
    label: '成色极佳',
    desc: '几乎全新，无明显使用痕迹，功能完好'
  },
  {
    value: ProductCondition.GOOD,
    label: '成色良好',
    desc: '轻微使用痕迹，功能完好，外观良好'
  },
  {
    value: ProductCondition.FAIR,
    label: '成色一般',
    desc: '明显使用痕迹，功能正常，外观有瑕疵'
  },
  {
    value: ProductCondition.POOR,
    label: '成色较差',
    desc: '重度使用痕迹，部分功能异常或外观损坏'
  }
]

// 快递公司选项
export const COURIER_COMPANIES = [
  { name: '顺丰速运', value: 'SF', code: 'SF' },
  { name: '中通快递', value: 'ZTO', code: 'ZTO' },
  { name: '圆通速递', value: 'YTO', code: 'YTO' },
  { name: '申通快递', value: 'STO', code: 'STO' },
  { name: '韵达速递', value: 'YD', code: 'YD' },
  { name: '百世快递', value: 'BEST', code: 'BEST' },
  { name: '京东快递', value: 'JD', code: 'JD' },
  { name: '邮政EMS', value: 'EMS', code: 'EMS' }
]

// 订单状态映射
export const ORDER_STATUS_MAP = {
  [RecycleOrderStatus.DRAFT]: '草稿',
  [RecycleOrderStatus.PENDING_APPROVAL]: '待审核',
  [RecycleOrderStatus.PRICE_QUOTED]: '已报价',
  [RecycleOrderStatus.SHIPPING_CONFIRMED]: '确认寄送',
  [RecycleOrderStatus.CANCELLED]: '已取消',
  [RecycleOrderStatus.RECEIVED]: '已收货',
  [RecycleOrderStatus.PAYMENT_CONFIRMED]: '已付款',
  [RecycleOrderStatus.RETURN_REQUESTED]: '申请退回',
  [RecycleOrderStatus.COMPLETED]: '已完成',
  [RecycleOrderStatus.RETURNED]: '已退回'
}

// 订单状态样式映射
export const ORDER_STATUS_STYLE_MAP = {
  [RecycleOrderStatus.PENDING_APPROVAL]: 'bg-yellow-100 text-yellow-800',
  [RecycleOrderStatus.PRICE_QUOTED]: 'bg-blue-100 text-blue-800',
  [RecycleOrderStatus.SHIPPING_CONFIRMED]: 'bg-purple-100 text-purple-800',
  [RecycleOrderStatus.RECEIVED]: 'bg-indigo-100 text-indigo-800',
  [RecycleOrderStatus.COMPLETED]: 'bg-green-100 text-green-800',
  [RecycleOrderStatus.CANCELLED]: 'bg-gray-100 text-gray-800',
  [RecycleOrderStatus.RETURNED]: 'bg-red-100 text-red-800'
}

// 回收流程步骤
export const RECYCLE_PROCESS_STEPS = [
  {
    title: '提交申请',
    desc: '填写商品信息并上传图片',
    icon: '📝'
  },
  {
    title: '专业评估',
    desc: '24小时内完成评估报价',
    icon: '🔍'
  },
  {
    title: '确认寄送',
    desc: '满意报价后寄送商品',
    icon: '📦'
  },
  {
    title: '完成交易',
    desc: '收货确认后立即打款',
    icon: '💰'
  }
]

// 图片上传配置
export const IMAGE_UPLOAD_CONFIG = {
  maxCount: 6,
  maxSize: 5 * 1024 * 1024, // 5MB
  allowedTypes: ['jpg', 'jpeg', 'png', 'webp'],
  compressQuality: 0.8
}

// 表单验证规则
export const FORM_VALIDATION_RULES = {
  productName: {
    required: true,
    minLength: 2,
    maxLength: 50,
    message: '商品名称长度应在2-50个字符之间'
  },
  productDesc: {
    required: true,
    minLength: 10,
    maxLength: 500,
    message: '商品描述长度应在10-500个字符之间'
  },
  contactPerson: {
    required: true,
    minLength: 2,
    maxLength: 20,
    message: '联系人姓名长度应在2-20个字符之间'
  },
  contactPhone: {
    required: true,
    pattern: /^1[3-9]\d{9}$/,
    message: '请输入正确的手机号码'
  },
  expectedPrice: {
    required: true,
    min: 1,
    max: 999999,
    message: '期望价格应在1-999999之间'
  },
  trackingNumber: {
    required: true,
    minLength: 8,
    maxLength: 20,
    message: '快递单号长度应在8-20个字符之间'
  }
}

// 默认收件地址
export const DEFAULT_RECEIVER_ADDRESS = {
  name: '中古虾回收中心',
  phone: '************',
  address: '上海市浦东新区张江高科技园区科苑路123号中古虾大厦A座10楼',
  zipCode: '201203'
}

// 寄送注意事项
export const SHIPPING_NOTES = [
  '请使用原包装盒或气泡膜等防护材料包装商品',
  '寄送前请备份重要数据并恢复出厂设置',
  '建议选择保价服务，确保商品安全',
  '请保留快递单据，便于查询物流信息',
  '如有疑问，请联系客服：************'
]

// API接口路径
export const API_PATHS = {
  CREATE_ORDER: '/api/recycle/orders',
  GET_ORDERS: '/api/recycle/orders',
  GET_ORDER_DETAIL: '/api/recycle/orders',
  CANCEL_ORDER: '/api/recycle/orders/{id}/cancel',
  CONFIRM_SHIPMENT: '/api/recycle/orders/confirm-shipment',
  REQUEST_RETURN: '/api/recycle/orders/{id}/return',
  UPLOAD_IMAGE: '/api/file/upload',
  GET_CATEGORIES: '/api/recycle/categories',
  GET_CONFIG: '/api/recycle/config'
}

// 错误消息
export const ERROR_MESSAGES = {
  NETWORK_ERROR: '网络连接失败，请检查网络设置',
  SERVER_ERROR: '服务器错误，请稍后重试',
  VALIDATION_ERROR: '表单验证失败，请检查输入信息',
  UPLOAD_ERROR: '图片上传失败，请重试',
  ORDER_NOT_FOUND: '订单不存在或已被删除',
  PERMISSION_DENIED: '权限不足，无法执行此操作'
}

// 成功消息
export const SUCCESS_MESSAGES = {
  ORDER_CREATED: '回收订单创建成功',
  ORDER_CANCELLED: '订单取消成功',
  SHIPMENT_CONFIRMED: '寄送信息确认成功',
  RETURN_REQUESTED: '退回申请提交成功',
  IMAGE_UPLOADED: '图片上传成功'
}
