<template>
  <view v-if="show" class="fixed inset-0 z-50 flex items-center justify-center px-4">
    <!-- 遮罩 -->
    <view class="absolute inset-0 bg-black opacity-30"></view>
    <!-- 弹窗内容 -->
    <view class="relative z-10 bg-white rounded-2xl shadow-lg w-full max-w-md animate-popup overflow-hidden text-sm">
      <!-- 顶部标题 -->
      <view class="flex items-center justify-between px-6 pt-6 pb-2">
        <text class="text-lg font-bold">确认订单</text>
        <van-icon
          name="close"
          size="24px"
          class="p-2 text-2xl text-gray-400 hover:text-gray-600 active:scale-95 transition-transform"
          style="border:0; outline:0; background:none; appearance:none;"
          @tap="onClose"
          aria-label="关闭"
        />
      </view>
      <!-- 会员卡片 -->
      <view class="flex items-center bg-gray-50 rounded-xl mx-6 mt-2 px-4 py-3 mb-2">
        <view class="w-12 h-12 bg-black rounded-full flex items-center justify-center mr-4">
          <van-icon name="diamond" color="#fff" size="28" />
        </view>
        <view class="flex-1">
          <text class="block text-sm font-semibold text-gray-900 mb-1">{{ plan?.name || '会员' }}</text>
          <text class="block text-xs text-gray-500 mb-1">{{ plan?.description || '尊享专属权益' }}</text>
          <text class="block text-xs text-gray-400">服务期限 <span class="text-gray-900 font-medium">{{ duration }}年</span></text>
        </view>
      </view>
      <!-- 服务期限选择 -->
      <view class="mx-6 mb-2 flex items-center">
        <text class="text-sm text-gray-900 mr-2">选择服务年限</text>
        <view class="flex items-center gap-0 bg-white rounded-lg border border-gray-200 px-0.5 py-0.5" style="min-width: 70px;">
          <button
            class="w-5 h-5 flex items-center justify-center rounded transition-colors text-xs text-gray-700 hover:bg-gray-100 active:bg-gray-200 border-0 outline-none"
            :disabled="duration <= 1"
            @tap="changeDuration(-1)"
            style="transition: all 0.2s;"
          >-</button>
          <text class="mx-1 min-w-4 text-center text-lg font-medium select-none">{{ duration }}年</text>
          <button
            class="w-5 h-5 flex items-center justify-center rounded transition-colors text-xs text-gray-700 hover:bg-gray-100 active:bg-gray-200 border-0 outline-none"
            :disabled="duration >= 5"
            @tap="changeDuration(1)"
            style="transition: all 0.2s;"
          >+</button>
        </view>
      </view>
      <!-- 优惠券选择 -->
      <view class="mx-6 mb-2">
        <view class="flex items-center justify-between mb-1">
          <text class="font-medium text-gray-900">优惠券</text>
        </view>
        <view class="relative">
          <view
            class="flex items-center border border-gray-200 rounded-xl px-4 py-3 cursor-pointer min-h-12 bg-white"
            @tap="showCouponList = !showCouponList"
            style="transition: border-color 0.2s;"
          >
            <van-icon name="coupon-o" size="20" class="mr-2 text-gray-400" />
            <template v-if="selectedCoupon">
              <text class="flex-1 text-sm text-gray-900">{{ selectedCoupon.name }}</text>
              <text class="text-xs text-red-500 ml-2">-¥{{ selectedCoupon.amount }}</text>
            </template>
            <template v-else>
              <text class="flex-1 text-sm text-gray-400">选择优惠券</text>
            </template>
            <van-icon :name="showCouponList ? 'arrow-up' : 'arrow-down'" size="18" class="ml-2 text-gray-400" />
          </view>
          <view v-if="showCouponList && coupons.length" class="absolute left-0 right-0 bg-white border border-gray-200 rounded-lg mt-1 z-10 max-h-40 overflow-y-auto shadow-lg"
            style="border-width: 2px; border-color: #e5e7eb;">
            <view v-for="coupon in coupons" :key="coupon.id" class="flex items-center px-3 py-2 cursor-pointer hover:bg-gray-50"
              :class="selectedCouponId === coupon.id ? 'bg-primary/10' : ''"
              @tap="selectCoupon(coupon.id); showCouponList = false">
              <text class="flex-1 text-sm text-gray-900">{{ coupon.name }}</text>
              <text class="text-xs text-red-500 ml-2">-¥{{ coupon.amount }}</text>
              <van-icon v-if="selectedCouponId === coupon.id" name="success" color="#6366f1" size="18" class="ml-2" />
            </view>
          </view>
          <view v-if="showCouponList && !coupons.length" class="absolute left-0 right-0 bg-white border border-gray-200 rounded-lg mt-1 z-10 px-4 py-6 text-center text-gray-400 text-sm shadow-lg"
            style="border-width: 2px; border-color: #e5e7eb;">
            暂无可用优惠券
          </view>
        </view>
      </view>
      <!-- 支付方式 -->
      <view class="mx-6 mt-2 mb-2">
        <text class="font-medium text-gray-900 block mb-2">支付方式</text>
        <view class="flex flex-col gap-2">
          <up-radio-group v-model="payMethod">
            <view v-for="item in payMethods" :key="item.value"
              class="flex items-center w-full border border-gray-200 rounded-lg px-3 py-2 cursor-pointer"
              :class="payMethod === item.value ? 'border-primary bg-primary/5' : ''">
              <span class="mr-2">
                <van-icon name="alipay" color="#1677FF" size="22" />
              </span>
              <up-radio :name="item.value" shape="circle" activeColor="#6366f1" class="mr-2" />
              <text class="text-sm text-gray-900">{{ item.label }}</text>
            </view>
          </up-radio-group>
        </view>
      </view>
      <!-- 金额明细 -->
      <view class="mx-6 mt-4 mb-2 bg-gray-50 rounded-xl px-4 py-3">
        <view class="flex justify-between items-center mb-1">
          <text class="text-gray-500 text-sm">商品金额</text>
          <text class="text-gray-900 text-sm">¥{{ plan?.price || 0 }}</text>
        </view>
        <view class="flex justify-between items-center mb-1">
          <text class="text-gray-500 text-sm">优惠金额</text>
          <text class="text-red-500 text-sm">-¥{{ selectedCoupon?.amount || 0 }}</text>
        </view>
        <view class="flex justify-between items-center mt-2">
          <text class="text-gray-900 font-semibold">实付金额</text>
          <text class="text-gray-900 font-bold text-base">¥{{ realAmount }}</text>
        </view>
      </view>
      <!-- 支付按钮 -->
      <view class="mx-6 mt-4 mb-2">
        <button
          class="w-full bg-black text-white rounded-3xl py-2 font-semibold shadow-lg active:opacity-80 active:translate-y-0.5 transition-transform min-h-8 flex items-center justify-center text-sm"
          :disabled="loading"
          @tap="onPay"
        >
          <span v-if="!loading" class="font-normal">立即支付 ¥{{ realAmount }}</span>
          <span v-else>支付中...</span>
        </button>
      </view>
      <!-- 协议提示 -->
      <view class="mx-6 mb-4 text-center text-xs text-gray-400">
        点击支付即表示同意<text class="text-primary mx-1" @tap="goServiceAgreement">《服务协议》</text>和<text class="text-primary mx-1" @tap="goPrivacyPolicy">《隐私政策》</text>
      </view>
    </view>
  </view>
</template>

<script setup lang="ts">
import { ref, computed, toRefs, watch, onUnmounted } from 'vue'
const props = defineProps<{
  show: boolean
  plan: { name: string; description: string; price: number } | null
  coupons: Array<{ id: string; name: string; amount: number }>
  loading?: boolean
  modelValue?: boolean
  selectedCouponId?: string | null
}>()
const emit = defineEmits(['update:show', 'close', 'pay', 'select-coupon', 'update:duration'])
const { show, plan, coupons, loading, selectedCouponId } = toRefs(props)

const showCouponList = ref(false)
const payMethod = ref('ALIPAY')
const payMethods = [
  { value: 'ALIPAY', label: '支付宝' },
] as const

const duration = ref(1)

const selectedCoupon = computed(() => coupons.value.find(c => c.id === selectedCouponId.value) || null)
const realAmount = computed(() => {
  const price = plan.value?.price || 0
  const discount = selectedCoupon.value?.amount || 0
  return price * duration.value - discount > 0 ? price * duration.value - discount : 0
})

function onClose() {
  emit('update:show', false)
  emit('close')
}
function onPay() {
  emit('pay', selectedCouponId?.value || null, payMethod.value, duration.value)
}
function selectCoupon(id: string) {
  emit('select-coupon', id)
}
function changeDuration(delta: number) {
  const next = duration.value + delta
  if (next >= 1 && next <= 5) {
    duration.value = next
    emit('update:duration', next)
  }
}

function goServiceAgreement() {
  uni.navigateTo({ url: '/pages/agreement/service' })
}
function goPrivacyPolicy() {
  uni.navigateTo({ url: '/pages/agreement/privacy' })
}

watch(show, (val) => {
  if (val) {
    document.body.style.overflow = 'hidden'
  } else {
    document.body.style.overflow = ''
  }
})

onUnmounted(() => {
  document.body.style.overflow = ''
})
</script>

<style scoped>
.fixed {
  position: fixed;
}
.inset-0 {
  top: 0; right: 0; bottom: 0; left: 0;
}
.z-50 {
  z-index: 50;
}
.animate-popup {
  animation: popup-in 0.25s cubic-bezier(.4,2,.6,1) both;
}
@keyframes popup-in {
  0% { transform: scale(0.96); opacity: 0; }
  100% { transform: scale(1); opacity: 1; }
}
</style> 