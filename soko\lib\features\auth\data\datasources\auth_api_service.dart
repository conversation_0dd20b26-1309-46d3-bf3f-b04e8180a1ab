import 'package:dio/dio.dart';

import 'package:soko/core/api/base_api_service.dart';
import 'package:soko/core/network/api_response.dart';
import 'package:soko/features/auth/domain/entities/user.dart';

/// 认证API服务
class AuthApiService extends BaseApiService {
  /// 登录请求模型
  static const String _loginPath = '/auth/login';
  static const String _registerPath = '/auth/register';
  static const String _logoutPath = '/auth/logout';
  static const String _refreshTokenPath = '/auth/refresh';
  static const String _sendSmsPath = '/auth/sms/send';
  static const String _verifySmsPath = '/auth/sms/verify';
  static const String _resetPasswordPath = '/auth/password/reset';
  static const String _changePasswordPath = '/auth/password/change';
  static const String _userInfoPath = '/auth/user/info';
  static const String _updateUserInfoPath = '/auth/user/update';

  /// 手机号密码登录
  Future<ApiResponse<LoginResponse>> loginWithPassword({
    required String phone,
    required String password,
  }) async {
    return post<LoginResponse>(
      _loginPath,
      data: {
        'phone': phone,
        'password': password,
        'loginType': 'password',
      },
      fromJson: (data) => LoginResponse.fromJson(data as Map<String, dynamic>),
    );
  }

  /// 手机号验证码登录
  Future<ApiResponse<LoginResponse>> loginWithSms({
    required String phone,
    required String smsCode,
  }) async {
    return post<LoginResponse>(
      _loginPath,
      data: {
        'phone': phone,
        'smsCode': smsCode,
        'loginType': 'sms',
      },
      fromJson: (data) => LoginResponse.fromJson(data as Map<String, dynamic>),
    );
  }

  /// 注册
  Future<ApiResponse<LoginResponse>> register({
    required String phone,
    required String password,
    required String smsCode,
    String? nickname,
    String? inviteCode,
  }) async {
    return post<LoginResponse>(
      _registerPath,
      data: {
        'phone': phone,
        'password': password,
        'smsCode': smsCode,
        if (nickname != null) 'nickname': nickname,
        if (inviteCode != null) 'inviteCode': inviteCode,
      },
      fromJson: (data) => LoginResponse.fromJson(data as Map<String, dynamic>),
    );
  }

  /// 登出
  Future<ApiResponse<void>> logout() async {
    return post<void>(_logoutPath);
  }

  /// 刷新Token
  Future<ApiResponse<TokenResponse>> refreshToken({
    required String refreshToken,
  }) async {
    return post<TokenResponse>(
      _refreshTokenPath,
      data: {
        'refreshToken': refreshToken,
      },
      fromJson: (data) => TokenResponse.fromJson(data as Map<String, dynamic>),
    );
  }

  /// 发送短信验证码
  Future<ApiResponse<void>> sendSmsCode({
    required String phone,
    required String type, // 'login', 'register', 'reset_password'
  }) async {
    return post<void>(
      _sendSmsPath,
      data: {
        'phone': phone,
        'type': type,
      },
    );
  }

  /// 验证短信验证码
  Future<ApiResponse<void>> verifySmsCode({
    required String phone,
    required String smsCode,
    required String type,
  }) async {
    return post<void>(
      _verifySmsPath,
      data: {
        'phone': phone,
        'smsCode': smsCode,
        'type': type,
      },
    );
  }

  /// 重置密码
  Future<ApiResponse<void>> resetPassword({
    required String phone,
    required String newPassword,
    required String smsCode,
  }) async {
    return post<void>(
      _resetPasswordPath,
      data: {
        'phone': phone,
        'newPassword': newPassword,
        'smsCode': smsCode,
      },
    );
  }

  /// 修改密码
  Future<ApiResponse<void>> changePassword({
    required String oldPassword,
    required String newPassword,
  }) async {
    return post<void>(
      _changePasswordPath,
      data: {
        'oldPassword': oldPassword,
        'newPassword': newPassword,
      },
    );
  }

  /// 获取用户信息
  Future<ApiResponse<User>> getUserInfo() async {
    return get<User>(
      _userInfoPath,
      fromJson: (data) => User.fromJson(data as Map<String, dynamic>),
    );
  }

  /// 更新用户信息
  Future<ApiResponse<User>> updateUserInfo({
    String? nickname,
    String? avatar,
    String? gender,
    String? birthday,
    String? realName,
    String? idCard,
  }) async {
    return put<User>(
      _updateUserInfoPath,
      data: {
        if (nickname != null) 'nickname': nickname,
        if (avatar != null) 'avatar': avatar,
        if (gender != null) 'gender': gender,
        if (birthday != null) 'birthday': birthday,
        if (realName != null) 'realName': realName,
        if (idCard != null) 'idCard': idCard,
      },
      fromJson: (data) => User.fromJson(data as Map<String, dynamic>),
    );
  }
}

/// 登录响应模型
class LoginResponse {

  const LoginResponse({
    required this.accessToken,
    required this.refreshToken,
    required this.expiresIn,
    required this.user,
  });

  factory LoginResponse.fromJson(Map<String, dynamic> json) {
    return LoginResponse(
      accessToken: json['accessToken'] as String,
      refreshToken: json['refreshToken'] as String,
      expiresIn: json['expiresIn'] as int,
      user: User.fromJson(json['user'] as Map<String, dynamic>),
    );
  }
  final String accessToken;
  final String refreshToken;
  final int expiresIn;
  final User user;

  Map<String, dynamic> toJson() {
    return {
      'accessToken': accessToken,
      'refreshToken': refreshToken,
      'expiresIn': expiresIn,
      'user': user.toJson(),
    };
  }
}

/// Token响应模型
class TokenResponse {

  const TokenResponse({
    required this.accessToken,
    required this.refreshToken,
    required this.expiresIn,
  });

  factory TokenResponse.fromJson(Map<String, dynamic> json) {
    return TokenResponse(
      accessToken: json['accessToken'] as String,
      refreshToken: json['refreshToken'] as String,
      expiresIn: json['expiresIn'] as int,
    );
  }
  final String accessToken;
  final String refreshToken;
  final int expiresIn;

  Map<String, dynamic> toJson() {
    return {
      'accessToken': accessToken,
      'refreshToken': refreshToken,
      'expiresIn': expiresIn,
    };
  }
}
