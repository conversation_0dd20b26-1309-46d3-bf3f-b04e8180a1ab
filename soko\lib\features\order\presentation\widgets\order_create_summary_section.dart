import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

import 'package:soko/shared/presentation/widgets/custom_card.dart';

/// 订单创建摘要组件
class OrderCreateSummarySection extends StatelessWidget {

  const OrderCreateSummarySection({
    super.key,
    required this.itemsAmount,
    required this.deliveryFee,
    required this.discountAmount,
    required this.totalAmount,
  });
  final double itemsAmount;
  final double deliveryFee;
  final double discountAmount;
  final double totalAmount;

  @override
  Widget build(BuildContext context) {
    return CustomCard(
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // 标题
          Row(
            children: [
              Icon(
                Icons.receipt_long,
                color: Colors.teal,
                size: 20.w,
              ),
              SizedBox(width: 8.w),
              Text(
                '费用明细',
                style: TextStyle(
                  fontSize: 16.sp,
                  fontWeight: FontWeight.w600,
                ),
              ),
            ],
          ),
          SizedBox(height: 16.h),

          // 费用明细
          _buildAmountRow('商品总额', itemsAmount),
          
          if (deliveryFee > 0) ...[
            SizedBox(height: 8.h),
            _buildAmountRow('配送费', deliveryFee),
          ],
          
          if (discountAmount > 0) ...[
            SizedBox(height: 8.h),
            _buildAmountRow('优惠金额', -discountAmount, isDiscount: true),
          ],

          // 分割线
          Padding(
            padding: EdgeInsets.symmetric(vertical: 12.h),
            child: Divider(
              color: Colors.grey[300],
              height: 1,
            ),
          ),

          // 总计
          _buildAmountRow('实付金额', totalAmount, isTotal: true),

          // 优惠券入口（如果有的话）
          if (discountAmount == 0) ...[
            SizedBox(height: 12.h),
            _buildCouponEntry(),
          ],
        ],
      ),
    );
  }

  /// 构建金额行
  Widget _buildAmountRow(
    String label,
    double amount, {
    bool isDiscount = false,
    bool isTotal = false,
  }) {
    return Row(
      mainAxisAlignment: MainAxisAlignment.spaceBetween,
      children: [
        Text(
          label,
          style: TextStyle(
            fontSize: isTotal ? 16.sp : 14.sp,
            fontWeight: isTotal ? FontWeight.w600 : FontWeight.normal,
            color: isTotal ? Colors.black87 : Colors.grey[600],
          ),
        ),
        Text(
          isDiscount 
              ? '-¥${amount.abs().toStringAsFixed(2)}'
              : '¥${amount.toStringAsFixed(2)}',
          style: TextStyle(
            fontSize: isTotal ? 18.sp : 14.sp,
            fontWeight: isTotal ? FontWeight.w600 : FontWeight.normal,
            color: isDiscount 
                ? Colors.green
                : isTotal 
                    ? Colors.red 
                    : Colors.black87,
          ),
        ),
      ],
    );
  }

  /// 构建优惠券入口
  Widget _buildCouponEntry() {
    return InkWell(
      onTap: _showCouponSelector,
      borderRadius: BorderRadius.circular(8.r),
      child: Container(
        width: double.infinity,
        padding: EdgeInsets.all(12.w),
        decoration: BoxDecoration(
          color: Colors.orange.withValues(alpha: 0.05),
          borderRadius: BorderRadius.circular(8.r),
          border: Border.all(color: Colors.orange.withValues(alpha: 0.2)),
        ),
        child: Row(
          children: [
            Icon(
              Icons.local_offer,
              color: Colors.orange,
              size: 16.w,
            ),
            SizedBox(width: 8.w),
            Text(
              '使用优惠券',
              style: TextStyle(
                fontSize: 14.sp,
                color: Colors.orange[700],
              ),
            ),
            const Spacer(),
            Text(
              '选择优惠券',
              style: TextStyle(
                fontSize: 12.sp,
                color: Colors.orange,
              ),
            ),
            Icon(
              Icons.chevron_right,
              color: Colors.orange,
              size: 16.w,
            ),
          ],
        ),
      ),
    );
  }

  /// 显示优惠券选择器
  void _showCouponSelector() {
    // TODO(coupon): 实现优惠券选择功能
    // 这里应该跳转到优惠券选择页面或显示优惠券选择弹窗
  }
}
