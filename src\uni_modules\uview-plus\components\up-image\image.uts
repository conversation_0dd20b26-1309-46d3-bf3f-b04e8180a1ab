/*
 * <AUTHOR> jry
 * @Description  :
 * @version      : 4.0
 * @Date         : 2024-04-22 16:44:21
 * @LastAuthor   : jry
 * @lastTime     : 2024-08-28 11:01:51
 * @FilePath     : /uview-plus/libs/config/props/image.js
 */
export default {
    // image组件
    image: {
        src: '',
        mode: 'aspectFill',
        width: '300',
        height: '225',
        shape: 'square',
        radius: '0',
        lazyLoad: false,
        showMenuByLongpress: true,
        loadingIcon: 'photo',
        errorIcon: 'error-circle',
        showLoading: true,
        showError: true,
        fade: true,
        webp: false,
        duration: 500,
        bgColor: '#f3f4f6'
    }
} as UTSJSONObject
