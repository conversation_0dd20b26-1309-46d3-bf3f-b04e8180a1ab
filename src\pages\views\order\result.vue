<template>
  <view class="result-page">
    <!-- 状态栏 -->
    <NavBar title="支付结果" :showBack="true"  />
    
    <!-- 主要内容区域 -->
    <view class="content">
      <!-- 状态图标和文字 -->
      <view class="status-section">
        <view class="icon-wrapper" :class="status === 'success' ? 'success' : 'failed'">
          <van-icon :name="status === 'success' ? 'checked' : 'close'" size="60" />
        </view>
        
        <view class="status-text" :class="status === 'success' ? 'success' : 'failed'">
          {{ status === 'success' ? '支付成功' : '支付失败' }}
        </view>
        
        <view class="status-desc">
          {{ status === 'success' ? '您的订单已确认，我们将尽快为您发货' : '抱歉，您的订单支付失败。' }}
        </view>
      </view>

      <!-- 订单详情 -->
      <view v-if="status === 'success' && orderDetail" class="order-detail">
        <view class="detail-item">
          <text class="label">支付金额</text>
          <text class="amount">¥{{ orderDetail.payAmount || orderDetail.totalAmount || '0.00' }}</text>
        </view>
        <view class="detail-item">
          <text class="label">{{ orderDetail.paymentMethod || '支付宝' }}</text>
          <text class="time">{{ formatDate(orderDetail.payTime) }}</text>
        </view>
      </view>

      <!-- 操作按钮 -->
      <view class="button-group">
        <button
          v-if="status === 'success'"
          class="btn btn-outline"
          @tap="goHome"
        >
          <van-icon name="wap-home-o" size="16" />
          <text>继续购物</text>
        </button>
        
        <button
          v-if="status === 'failed'"
          class="btn btn-primary"
          @tap="retryPayment"
        >
          重新支付
        </button>
        
        <button
          class="btn btn-primary"
          @tap="viewOrder"
        >
          <van-icon name="orders-o" size="16" />
          <text>查看订单</text>
        </button>
      </view>
    </view>
  </view>
</template>

<script setup lang="ts">
import { ref } from 'vue';
import { onLoad } from '@dcloudio/uni-app';
import NavBar from '@/components/NavBar.vue';

interface OrderDetail {
  id?: string;
  orderId?: string;
  orderNo?: string;
  totalAmount?: number | string;
  payAmount?: number | string;
  paymentMethod?: string;
  payType?: string;
  payTime?: string | number;
}

const status = ref<'success' | 'failed'>('success');
const orderId = ref<string | null>(null);
const statusBarHeight = ref(0);
const orderDetail = ref<OrderDetail | null>(null);

onLoad((options: any) => {
  if (options) {
    status.value = options.status === 'success' ? 'success' : 'failed';
    orderId.value = options.orderId || null;
    
    // 初始化订单详情对象
    if (!orderDetail.value) {
      orderDetail.value = {};
    }
    
    // 尝试解析传递的订单详情
    if (options.orderDetail) {
      try {
        orderDetail.value = JSON.parse(options.orderDetail);
      } catch (e) {
        console.error('解析订单详情失败:', e);
      }
    }
    
    // 处理从确认订单页面传递的参数
    if (options.payAmount) {
      orderDetail.value.payAmount = options.payAmount;
    }
    
    if (options.paymentMethod) {
      orderDetail.value.paymentMethod = decodeURIComponent(options.paymentMethod);
    }
    
    if (options.payTime) {
      orderDetail.value.payTime = options.payTime;
    }
    
    // 设置订单ID
    if (orderId.value) {
      orderDetail.value.orderId = orderId.value;
    }
  }
  
  // 如果没有订单详情，创建一个默认的
  if (!orderDetail.value && status.value === 'success') {
    orderDetail.value = {
      payAmount: '299.00',
      paymentMethod: '微信支付',
      payTime: Date.now()
    };
  }
  
  // #ifdef APP-PLUS || MP-WEIXIN
  statusBarHeight.value = uni.getSystemInfoSync().statusBarHeight || 20;
  // #endif
});

// 格式化日期
function formatDate(timestamp: string | number | undefined): string {
  if (!timestamp) return '';
  
  const date = new Date(Number(timestamp));
  const year = date.getFullYear();
  const month = String(date.getMonth() + 1).padStart(2, '0');
  const day = String(date.getDate()).padStart(2, '0');
  const hours = String(date.getHours()).padStart(2, '0');
  const minutes = String(date.getMinutes()).padStart(2, '0');
  const seconds = String(date.getSeconds()).padStart(2, '0');
  
  return `${year}-${month}-${day} ${hours}:${minutes}:${seconds}`;
}

function viewOrder() {
  if (orderId.value) {
    uni.redirectTo({ url: `/pages/views/order/detail?id=${orderId.value}` });
  } else {
    uni.switchTab({ url: '/pages/views/order/list' });
  }
}

function retryPayment() {
  if (orderId.value) {
    uni.redirectTo({ url: `/pages/views/order/detail?id=${orderId.value}` });
  } else {
    uni.navigateBack({ delta: 1 });
  }
}

function goHome() {
  uni.switchTab({ url: '/pages/views/home/<USER>' });
}
</script>

<style scoped lang="scss">
.result-page {
  min-height: 100vh;
  background-color: #f5f5f5;
  display: flex;
  flex-direction: column;
}

.status-bar {
  background-color: #f5f5f5;
  width: 100%;
}

.header {
  background-color: #ffffff;
  padding: 32rpx;
  text-align: center;
  border-bottom: 1px solid #f0f0f0;
  
  .title {
    font-size: 36rpx;
    font-weight: bold;
    color: #333333;
  }
}

.content {
  flex: 1;
  padding: 40rpx 32rpx;
  display: flex;
  flex-direction: column;
  align-items: center;
}

.status-section {
  text-align: center;
  margin-bottom: 60rpx;
  
  .icon-wrapper {
    width: 120rpx;
    height: 120rpx;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto 40rpx;
    
    &.success {
      background-color: rgba(7, 193, 96, 0.1);
      
      :deep(.van-icon) {
        color: #07c160 !important;
      }
    }
    
    &.failed {
      background-color: rgba(255, 69, 58, 0.1);
      
      :deep(.van-icon) {
        color: #ff453a !important;
      }
    }
  }
  
  .status-text {
    font-size: 48rpx;
    font-weight: bold;
    margin-bottom: 20rpx;
    
    &.success {
      color: #07c160;
    }
    
    &.failed {
      color: #ff453a;
    }
  }
  
  .status-desc {
    font-size: 28rpx;
    color: #666666;
    line-height: 1.5;
  }
}

.order-detail {
  width: 100%;
  background-color: #ffffff;
  border-radius: 16rpx;
  padding: 32rpx;
  margin-bottom: 60rpx;
  
  .detail-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 24rpx 0;
    
    &:not(:last-child) {
      border-bottom: 1px solid #f0f0f0;
    }
    
    .label {
      font-size: 28rpx;
      color: #666666;
    }
    
    .amount {
      font-size: 48rpx;
      font-weight: bold;
      color: #333333;
    }
    
    .time {
      font-size: 28rpx;
      color: #666666;
    }
  }
}

.button-group {
  width: 100%;
  display: flex;
  flex-direction: column;
  gap: 24rpx;
  
  .btn {
    width: 100%;
    height: 88rpx;
    border-radius: 44rpx;
    font-size: 32rpx;
    font-weight: 600;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 16rpx;
    border: none;
    transition: all 0.3s ease;
    
    &.btn-primary {
      background-color: #007aff;
      color: #ffffff;
      
      &:active {
        background-color: #0056cc;
        transform: translateY(2rpx);
      }
    }
    
    &.btn-outline {
      background-color: #ffffff;
      color: #666666;
      border: 2rpx solid #e0e0e0;
      
      &:active {
        background-color: #f5f5f5;
        transform: translateY(2rpx);
      }
    }
    
    :deep(.van-icon) {
      margin-right: 8rpx;
    }
  }
}


</style>