import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:freezed_annotation/freezed_annotation.dart';

import 'package:soko/core/state/base_state.dart';
import 'package:soko/features/recycle/domain/entities/recycle_order.dart';
import 'package:soko/features/recycle/domain/repositories/recycle_repository.dart';
import 'package:soko/features/recycle/data/repositories/recycle_repository_impl.dart';

part 'recycle_order_list_provider.freezed.dart';

/// 回收订单列表数据模型
@freezed
class RecycleOrderListData with _$RecycleOrderListData {
  const factory RecycleOrderListData({
    @Default([]) List<RecycleOrder> orders,
    @Default(1) int currentPage,
    @Default(20) int pageSize,
    @Default(true) bool hasMore,
    @Default(false) bool isLoadingMore,
    String? statusFilter,
    String? categoryFilter,
    String? searchKeyword,
    DateTime? startDate,
    DateTime? endDate,
  }) = _RecycleOrderListData;
}

/// 回收订单列表状态管理
class RecycleOrderListNotifier extends StateNotifier<BaseState<RecycleOrderListData>> {
  RecycleOrderListNotifier(this._recycleRepository) : super(const BaseState.idle());

  final RecycleRepository _recycleRepository;

  /// 加载订单列表
  Future<void> loadOrders({bool refresh = false}) async {
    if (refresh || state is BaseState<RecycleOrderListData>.idle) {
      state = const BaseState.loading();
    }

    try {
      final currentData = state.maybeWhen(
        success: (data) => data,
        orElse: () => const RecycleOrderListData(),
      );

      final page = refresh ? 1 : currentData.currentPage;
      
      final result = await _recycleRepository.getRecycleOrders(
        page: page,
        pageSize: currentData.pageSize,
        status: currentData.statusFilter,
        category: currentData.categoryFilter,
        keyword: currentData.searchKeyword,
        startDate: currentData.startDate,
        endDate: currentData.endDate,
      );

      final newOrders = refresh 
          ? result.data 
          : [...currentData.orders, ...result.data];

      state = BaseState.success(
        currentData.copyWith(
          orders: newOrders,
          currentPage: page,
          hasMore: result.hasMore,
          isLoadingMore: false,
        ),
      );
    } catch (error) {
      state = BaseState.error(error.toString());
    }
  }

  /// 刷新订单列表
  Future<void> refreshOrders() async {
    await loadOrders(refresh: true);
  }

  /// 加载更多订单
  Future<void> loadMoreOrders() async {
    final currentData = state.maybeWhen(
      success: (data) => data,
      orElse: () => null,
    );

    if (currentData == null || !currentData.hasMore || currentData.isLoadingMore) {
      return;
    }

    // 设置加载更多状态
    state = BaseState.success(currentData.copyWith(isLoadingMore: true));

    try {
      final nextPage = currentData.currentPage + 1;
      
      final result = await _recycleRepository.getRecycleOrders(
        page: nextPage,
        pageSize: currentData.pageSize,
        status: currentData.statusFilter,
        category: currentData.categoryFilter,
        keyword: currentData.searchKeyword,
        startDate: currentData.startDate,
        endDate: currentData.endDate,
      );

      final newOrders = [...currentData.orders, ...result.data];

      state = BaseState.success(
        currentData.copyWith(
          orders: newOrders,
          currentPage: nextPage,
          hasMore: result.hasMore,
          isLoadingMore: false,
        ),
      );
    } catch (error) {
      // 加载更多失败时，恢复之前的状态
      state = BaseState.success(currentData.copyWith(isLoadingMore: false));
    }
  }

  /// 按状态筛选
  void filterByStatus(String? status) {
    final currentData = state.maybeWhen(
      success: (data) => data,
      orElse: () => const RecycleOrderListData(),
    );

    state = BaseState.success(
      currentData.copyWith(
        statusFilter: status,
        currentPage: 1,
        orders: [],
        hasMore: true,
      ),
    );

    loadOrders(refresh: true);
  }

  /// 应用筛选条件
  void applyFilters(Map<String, dynamic> filters) {
    final currentData = state.maybeWhen(
      success: (data) => data,
      orElse: () => const RecycleOrderListData(),
    );

    state = BaseState.success(
      currentData.copyWith(
        categoryFilter: filters['category'],
        searchKeyword: filters['keyword'],
        startDate: filters['startDate'],
        endDate: filters['endDate'],
        currentPage: 1,
        orders: [],
        hasMore: true,
      ),
    );

    loadOrders(refresh: true);
  }

  /// 搜索订单
  void searchOrders(String keyword) {
    final currentData = state.maybeWhen(
      success: (data) => data,
      orElse: () => const RecycleOrderListData(),
    );

    state = BaseState.success(
      currentData.copyWith(
        searchKeyword: keyword.isEmpty ? null : keyword,
        currentPage: 1,
        orders: [],
        hasMore: true,
      ),
    );

    loadOrders(refresh: true);
  }

  /// 取消订单
  Future<void> cancelOrder(String orderId) async {
    try {
      await _recycleRepository.cancelRecycleOrder(orderId);
      
      // 更新本地状态
      final currentData = state.maybeWhen(
        success: (data) => data,
        orElse: () => null,
      );

      if (currentData != null) {
        final updatedOrders = currentData.orders.map((order) {
          if (order.id == orderId) {
            return order.copyWith(
              orderStatus: 'CANCELLED',
              orderStatusDesc: '已取消',
            );
          }
          return order;
        }).toList();

        state = BaseState.success(
          currentData.copyWith(orders: updatedOrders),
        );
      }
    } catch (error) {
      // 这里可以显示错误提示
      rethrow;
    }
  }

  /// 确认寄送
  Future<void> confirmShipment(String orderId, Map<String, dynamic> shippingInfo) async {
    try {
      await _recycleRepository.confirmShipment(orderId, shippingInfo);
      
      // 更新本地状态
      final currentData = state.maybeWhen(
        success: (data) => data,
        orElse: () => null,
      );

      if (currentData != null) {
        final updatedOrders = currentData.orders.map((order) {
          if (order.id == orderId) {
            return order.copyWith(
              orderStatus: 'SHIPPING_CONFIRMED',
              orderStatusDesc: '已寄送',
            );
          }
          return order;
        }).toList();

        state = BaseState.success(
          currentData.copyWith(orders: updatedOrders),
        );
      }
    } catch (error) {
      rethrow;
    }
  }
}

/// 回收订单列表Provider
final recycleOrderListProvider = StateNotifierProvider<RecycleOrderListNotifier, BaseState<RecycleOrderListData>>((ref) {
  final recycleRepository = ref.watch(recycleRepositoryProvider);
  return RecycleOrderListNotifier(recycleRepository);
});

/// 回收仓库Provider（需要在repository文件中定义）
final recycleRepositoryProvider = Provider<RecycleRepository>((ref) {
  // TODO: 实现RecycleRepository的具体实现
  throw UnimplementedError('RecycleRepository implementation needed');
});
