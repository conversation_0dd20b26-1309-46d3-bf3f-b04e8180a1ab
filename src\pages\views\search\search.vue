<template>
  <view class="search-container">
    <!-- 搜索头部 -->
    <view
      class="search-header"
      :style="{ paddingTop: `calc(${statusBarHeight} + 16rpx)` }"
    >
      <view class="back-icon" @tap="goBack">
        <van-icon name="arrow-left" color="#333333" size="20" />
      </view>
      <view
        class="search-input-wrap"
        :class="{ 'search-input-focused': isFocused }"
      >
        <van-icon name="search" color="#666666" size="20" />
        <input
          type="text"
          class="search-input"
          v-model="searchKeyword"
          placeholder="搜索你想要的特摄模玩"
          confirm-type="search"
          @confirm="handleSearch"
          @focus="handleFocus"
          @blur="handleBlur"
          focus
          @input="onSearchInput"
        />
        <van-icon
          v-if="searchKeyword"
          name="close"
          color="#999999"
          size="20"
          @click="clearSearch"
        />
      </view>
      <text class="search-btn" @tap="handleSearch">搜索</text>
    </view>

    <!-- 搜索内容区域 -->
    <scroll-view
      scroll-y
      class="search-content"
      :style="{ height: contentHeight + 'px' }"
    >
      <!-- 搜索历史 - 当有搜索历史且未输入搜索词时显示 -->
      <view
        class="search-section"
        v-if="searchHistory.length > 0 && !searchKeyword"
      >
        <view class="section-header">
          <text class="section-title">搜索历史</text>
          <van-icon
            name="delete-o"
            color="#999999"
            size="20"
            @click="clearHistory"
          />
        </view>
        <view class="history-tags">
          <view
            class="tag-item"
            v-for="(item, index) in searchHistory"
            :key="index"
            @tap="useHistoryItem(item)"
          >
            {{ item }}
          </view>
        </view>
      </view>

      <!-- 热门搜索 - 当未输入搜索词时显示 -->
      <view class="search-section" v-if="!searchKeyword">
        <view class="section-header">
          <text class="section-title">热门搜索</text>
        </view>
        <view class="hot-tags" v-if="hotSearchList.length > 0">
          <view
            class="tag-item"
            v-for="(item, index) in hotSearchList"
            :key="index"
            @tap="useHistoryItem(item)"
          >
            {{ item }}
          </view>
        </view>
        <view v-else class="empty-state">
          <text class="empty-text">暂无热门搜索数据</text>
        </view>
      </view>

      <!-- 搜索联想 - 当输入搜索词但未执行搜索时显示 -->
      <view class="search-suggestion" v-if="searchKeyword">
        <view
          class="suggestion-item"
          v-for="(item, index) in hotSearchList"
          :key="index"
          @tap="useHistoryItem(item)"
        >
          <van-icon name="search" color="#999999" size="18" />
          <text class="suggestion-text">{{ item }}</text>
        </view>
      </view>
    </scroll-view>
  </view>
</template>

<script setup>
import { ref, computed, onMounted, onBeforeMount } from "vue"
import { Icon as VanIcon } from "vant"
import { GET_PRODUCT_HOTKEYWORD } from "@/api/api.js"
// 搜索关键词
const searchKeyword = ref("")
// 搜索历史
const searchHistory = ref([])
// 输入框聚焦状态
const isFocused = ref(true)
// 内容区域高度
const contentHeight = ref(0)
// 状态栏高度
const statusBarHeight = ref("0px")

// 热门搜索列表
const hotSearchList = ref([])

// 获取状态栏高度
onBeforeMount(() => {
  const systemInfo = uni.getSystemInfoSync()
  statusBarHeight.value = `${systemInfo.statusBarHeight}px`
  console.log("搜索页面获取状态栏高度:", statusBarHeight.value)

  uni.$on("windowResize", calculateContentHeight)
  return () => {
    uni.$off("windowResize", calculateContentHeight)
  }
})

// 计算内容区域高度
const calculateContentHeight = () => {
  const { windowHeight } = uni.getSystemInfoSync()
  const query = uni.createSelectorQuery()
  query.select(".search-header").boundingClientRect()
  query.exec((res) => {
    if (res && res[0]) {
      const headerHeight = res[0].height
      contentHeight.value = windowHeight - headerHeight
    } else {
      // 默认头部高度约为50px加上状态栏高度
      const systemInfo = uni.getSystemInfoSync()
      contentHeight.value = windowHeight - (50 + systemInfo.statusBarHeight)
    }
  })
}

// 处理输入框聚焦
const handleFocus = () => {
  isFocused.value = true
}

// 处理输入框失焦
const handleBlur = () => {
  isFocused.value = false
}

// 返回上一页
const goBack = () => {
  uni.navigateBack()
}

// 清空搜索栏
const clearSearch = () => {
  searchKeyword.value = ""
}

// 清除历史记录
const clearHistory = () => {
  uni.showModal({
    title: "提示",
    content: "确定要清空搜索历史吗？",
    success: function (res) {
      if (res.confirm) {
        searchHistory.value = []
        uni.setStorageSync("searchHistory", JSON.stringify([]))
        uni.showToast({
          title: "已清空搜索历史",
          icon: "none"
        })
      }
    }
  })
}

// 使用历史记录项
const useHistoryItem = (keyword) => {
  searchKeyword.value = keyword
  // 直接调用handleSearch函数进行跳转
  handleSearch()
}

// 搜索输入处理
const onSearchInput = () => {
  // 清空isSearchResult标志已不再需要
}

// 处理搜索动作
const handleSearch = () => {
  if (!searchKeyword.value.trim()) {
    uni.showToast({
      title: "请输入搜索关键词",
      icon: "none"
    })
    return
  }

  // 保存到搜索历史
  if (!searchHistory.value.includes(searchKeyword.value)) {
    searchHistory.value.unshift(searchKeyword.value)
    // 只保留10条搜索历史
    if (searchHistory.value.length > 10) {
      searchHistory.value = searchHistory.value.slice(0, 10)
    }
    // 保存到本地存储
    uni.setStorageSync("searchHistory", JSON.stringify(searchHistory.value))
  }

  // 跳转到商品列表页并传递搜索关键词
  uni.navigateTo({
    url: `/pages/views/product/products?name=${encodeURIComponent(
      searchKeyword.value
    )}`
  })
}

// 页面加载时从本地获取搜索历史
onMounted(() => {
  getHotSearchList()
  const history = uni.getStorageSync("searchHistory")
  if (history) {
    try {
      searchHistory.value = JSON.parse(history)
    } catch (e) {
      searchHistory.value = []
    }
  }
  // 计算内容区域高度
  calculateContentHeight()
})

const getHotSearchList = async () => {
  const res = await GET_PRODUCT_HOTKEYWORD()
  if (res.code === 200) {
    hotSearchList.value = res.data
  } else {
    uni.showToast({
      title: res.message,
      icon: "none"
    })
  }
}
</script>

<style lang="scss" scoped>
.search-container {
  background-color: #ffffff;
  height: 100vh;
  display: flex;
  flex-direction: column;
  overflow: hidden;
}

.search-header {
  padding-left: 24rpx;
  padding-right: 24rpx;
  padding-bottom: 16rpx;
  display: flex;
  align-items: center;
  border-bottom: 1px solid #f2f2f2;
  background-color: #ffffff;
  position: sticky;
  top: 0;
  z-index: 10;
}

.back-icon {
  padding: 8rpx 16rpx 8rpx 0;
  display: flex;
  align-items: center;
}

.search-input-wrap {
  flex: 1;
  height: 72rpx;
  background-color: #ffffff;
  border-radius: 100rpx;
  display: flex;
  align-items: center;
  padding: 0 24rpx;
  box-sizing: border-box;
  border: 1px solid #e8e8e8;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.03);
  transition: all 0.1s ease-out;
  position: relative;
  overflow: visible;
}

.search-input-focused {
  border: 1px solid transparent;
  background-origin: border-box;
  position: relative;
  background-clip: padding-box;
  box-shadow: 0 2rpx 16rpx rgba(239, 68, 68, 0.2);

  &::before {
    content: "";
    position: absolute;
    top: -1px;
    left: -1px;
    right: -0.8px;
    bottom: -1px;
    border-radius: 100rpx;
    background: linear-gradient(to right, #ef4444, #ec4899);
    z-index: -1;
  }
}

.search-input {
  flex: 1;
  height: 72rpx;
  font-size: 14px;
  padding: 0 16rpx;
  color: #333333;
}

.search-btn {
  padding: 0 0 0 20rpx;
  font-size: 16px;
  font-weight: 500;
  background-image: linear-gradient(to right, #ef4444, #ec4899);
  -webkit-background-clip: text;
  background-clip: text;
  color: transparent;
  transition: opacity 0.1s ease-out;
}

.search-content {
  flex: 1;
  background-color: #ffffff;
  padding: 24rpx;
  box-sizing: border-box;
  overflow-y: auto;
  -webkit-overflow-scrolling: touch;
}

.search-section {
  margin-bottom: 32rpx;
}

.section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16rpx;
}

.section-title {
  font-size: 16px;
  font-weight: 600;
  color: #333333;
}

.history-tags,
.hot-tags {
  display: flex;
  flex-wrap: wrap;
}

.tag-item {
  padding: 8rpx 24rpx;
  background-color: #f6f6f6;
  color: #333333;
  font-size: 14px;
  border-radius: 100rpx;
  margin-right: 16rpx;
  margin-bottom: 16rpx;
}

.tag-item:active {
  background-color: #ef4444;
  color: #ffffff;
}

.search-suggestion {
  margin-top: 24rpx;
}

.suggestion-item {
  display: flex;
  align-items: center;
  padding: 24rpx 0;
  border-bottom: 1px solid #f2f2f2;
}

.suggestion-text {
  margin-left: 16rpx;
  font-size: 14px;
  color: #333333;
}
.empty-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 40rpx 0;

  .empty-text {
    font-size: 14px;
    color: #999;
  }
}
</style>
