<template>
  <view class="min-h-screen bg-gray-50">
    <!-- 自定义导航栏 -->
    <StatusBarPlaceholder />
    <NavBar title="创建回收订单" />

    <form @submit.prevent="submitOrder" class="pb-20">
      <!-- 商品信息 -->
      <view class="bg-white mt-2 px-4 py-6">
        <text class="text-base font-semibold text-gray-800 mb-4 block">商品信息</text>

        <!-- 商品名称 -->
        <view class="mb-6">
          <text class="text-sm font-medium text-gray-700 mb-2 block">
            商品名称 <text class="text-red-500">*</text>
          </text>
          <input
            v-model="formData.productName"
            placeholder="请输入商品品牌和型号"
            class="w-full px-3 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-primary-500 text-base"
          />
        </view>

        <!-- 商品型号 -->
        <view class="mb-6">
          <text class="text-sm font-medium text-gray-700 mb-2 block">商品型号</text>
          <input
            v-model="formData.productModel"
            placeholder="如：iPhone 14 Pro Max"
            class="w-full px-3 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-primary-500 text-base"
          />
        </view>

        <!-- 商品类别 -->
        <view class="mb-6">
          <text class="text-sm font-medium text-gray-700 mb-2 block">
            商品类别 <text class="text-red-500">*</text>
          </text>
          <picker
            :value="categoryIndex"
            :range="categoryList"
            range-key="name"
            @change="onCategoryChange"
          >
            <view class="w-full px-3 py-3 border border-gray-300 rounded-lg bg-white flex justify-between items-center">
              <text class="text-base" :class="formData.productCategory ? 'text-gray-800' : 'text-gray-400'">
                {{ formData.productCategory || '请选择商品类别' }}
              </text>
              <text class="text-gray-400">></text>
            </view>
          </picker>
        </view>

        <!-- 商品描述 -->
        <view class="mb-6">
          <text class="text-sm font-medium text-gray-700 mb-2 block">
            商品描述 <text class="text-red-500">*</text>
          </text>
          <textarea
            v-model="formData.productDesc"
            placeholder="请详细描述商品的功能、外观等信息"
            class="w-full px-3 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-primary-500 text-base resize-none"
            :maxlength="500"
            auto-height
          />
          <text class="text-xs text-gray-500 mt-1 block text-right">
            {{ formData.productDesc.length }}/500
          </text>
        </view>

        <!-- 商品状况 -->
        <view class="mb-6">
          <text class="text-sm font-medium text-gray-700 mb-3 block">
            商品状况 <text class="text-red-500">*</text>
          </text>
          <radio-group @change="onConditionChange" class="space-y-3">
            <label
              class="flex items-start space-x-3 p-3 border border-gray-200 rounded-lg cursor-pointer hover:bg-gray-50"
              :class="{ 'border-primary-500 bg-primary-50': formData.condition === condition.value }"
              v-for="condition in conditionOptions"
              :key="condition.value"
            >
              <radio
                :value="condition.value"
                :checked="formData.condition === condition.value"
                class="mt-1"
              />
              <view class="flex-1">
                <text class="text-base font-medium text-gray-800 block">{{ condition.label }}</text>
                <text class="text-sm text-gray-500">{{ condition.desc }}</text>
              </view>
            </label>
          </radio-group>
        </view>

        <!-- 期望价格 -->
        <view class="mb-6">
          <text class="text-sm font-medium text-gray-700 mb-2 block">
            期望价格 <text class="text-red-500">*</text>
          </text>
          <view class="relative">
            <text class="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-500 text-base">¥</text>
            <input
              v-model="formData.expectedPrice"
              type="digit"
              placeholder="0.00"
              class="w-full pl-8 pr-3 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-primary-500 text-base"
            />
          </view>
          <text class="text-xs text-gray-500 mt-1 block">仅供参考，最终价格以商家评估为准</text>
        </view>
      </view>
      
      <!-- 商品图片 -->
      <view class="bg-white mt-2 px-4 py-6">
        <text class="text-base font-semibold text-gray-800 mb-2 block">商品图片</text>
        <text class="text-sm text-gray-500 mb-4 block">最多6张，建议包含正面、背面、细节图</text>
        
        <view class="grid grid-cols-3 gap-3">
          <view 
            class="aspect-square bg-gray-100 rounded-lg relative overflow-hidden" 
            v-for="(image, index) in imageList" 
            :key="index"
          >
            <image :src="image.url" class="w-full h-full object-cover" @click="previewImage(index)"/>
            <view 
              class="absolute top-1 right-1 w-6 h-6 bg-red-500 rounded-full flex items-center justify-center text-white text-xs"
              @click="deleteImage(index)"
            >
              ×
            </view>
          </view>
          
          <view 
            class="aspect-square bg-gray-100 rounded-lg flex flex-col items-center justify-center border-2 border-dashed border-gray-300"
            v-if="imageList.length < 6"
            @click="chooseImage"
          >
            <text class="text-2xl text-gray-400 mb-1">+</text>
            <text class="text-xs text-gray-400">添加图片</text>
          </view>
        </view>
        
        <view class="mt-4 space-y-1">
          <text class="text-xs text-gray-500 block">• 图片清晰，光线充足</text>
          <text class="text-xs text-gray-500 block">• 包含商品正面、背面、接口等关键部位</text>
          <text class="text-xs text-gray-500 block">• 如有包装盒、配件请一并拍摄</text>
        </view>
      </view>
      
      <!-- 联系信息 -->
      <view class="bg-white mt-2 px-4 py-6">
        <text class="text-base font-semibold text-gray-800 mb-4 block">联系信息</text>
        
        <view class="mb-6">
          <text class="text-sm font-medium text-gray-700 mb-2 block">
            联系人 <text class="text-red-500">*</text>
          </text>
          <input 
            v-model="formData.contactPerson" 
            placeholder="请输入联系人姓名"
            class="w-full px-3 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-primary-500 text-base"
          />
        </view>
        
        <view class="mb-6">
          <text class="text-sm font-medium text-gray-700 mb-2 block">
            联系电话 <text class="text-red-500">*</text>
          </text>
          <input 
            v-model="formData.contactPhone" 
            type="number"
            placeholder="请输入手机号码"
            class="w-full px-3 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-primary-500 text-base"
          />
        </view>
      </view>
      
      <!-- 提交按钮 -->
      <view class="fixed bottom-0 left-0 right-0 bg-white border-t border-gray-200 p-4">
        <button 
          class="w-full py-3 rounded-lg text-base font-medium transition-colors"
          :class="canSubmit ? 'bg-primary-500 text-white active:bg-primary-600' : 'bg-gray-300 text-gray-500 cursor-not-allowed'"
          :disabled="!canSubmit || isSubmitting"
          @click="submitOrder"
        >
          {{ isSubmitting ? '提交中...' : '提交回收申请' }}
        </button>
        <text class="text-xs text-gray-500 text-center mt-2 block">
          提交后，我们将在24小时内完成评估并给出报价
        </text>
      </view>
    </form>
  </view>
</template>

<script setup lang="ts">
import { ref, computed, reactive } from 'vue'
import StatusBarPlaceholder from '@/components/StatusBarPlaceholder.vue'
import NavBar from '@/components/NavBar.vue'

// 类型定义
interface FormData {
  productName: string
  productModel: string
  productCategory: string
  productDesc: string
  condition: string
  expectedPrice: string
  contactPerson: string
  contactPhone: string
}

interface ImageItem {
  fileId: string
  url: string
  thumbnailUrl: string
}

interface CategoryItem {
  name: string
  value: string
}

interface ConditionOption {
  value: string
  label: string
  desc: string
}

// 响应式数据
const formData = reactive<FormData>({
  productName: '',
  productModel: '',
  productCategory: '',
  productDesc: '',
  condition: '',
  expectedPrice: '',
  contactPerson: '',
  contactPhone: ''
})

const imageList = ref<ImageItem[]>([])
const categoryIndex = ref(0)
const isSubmitting = ref(false)

// 静态数据
const categoryList: CategoryItem[] = [
  { name: '手机数码', value: 'mobile' },
  { name: '电脑办公', value: 'computer' },
  { name: '家用电器', value: 'appliance' },
  { name: '其他', value: 'other' }
]

const conditionOptions: ConditionOption[] = [
  { value: 'excellent', label: '成色极佳', desc: '几乎全新，无明显使用痕迹' },
  { value: 'good', label: '成色良好', desc: '轻微使用痕迹，功能完好' },
  { value: 'fair', label: '成色一般', desc: '明显使用痕迹，功能正常' },
  { value: 'poor', label: '成色较差', desc: '重度使用痕迹，部分功能异常' }
]

// 计算属性
const canSubmit = computed(() => {
  return formData.productName &&
         formData.productCategory &&
         formData.productDesc &&
         formData.condition &&
         formData.expectedPrice &&
         formData.contactPerson &&
         formData.contactPhone &&
         imageList.value.length > 0 &&
         !isSubmitting.value
})

// 方法
const onCategoryChange = (e: any) => {
  categoryIndex.value = e.detail.value
  formData.productCategory = categoryList[e.detail.value].name
}

const onConditionChange = (e: any) => {
  formData.condition = e.detail.value
}

// 选择图片
const chooseImage = async () => {
  try {
    const res = await uni.chooseImage({
      count: 6 - imageList.value.length,
      sizeType: ['compressed'],
      sourceType: ['album', 'camera']
    })

    // 批量上传图片
    for (const filePath of res.tempFilePaths) {
      await uploadImage(filePath)
    }
  } catch (error) {
    uni.showToast({
      title: '选择图片失败',
      icon: 'none'
    })
  }
}

// 上传图片
const uploadImage = async (filePath: string): Promise<void> => {
  uni.showLoading({ title: '上传中...' })

  try {
    // 压缩图片
    const compressedPath = await compressImage(filePath)

    // 调用API上传图片
    // const result = await recycleApi.uploadImage(compressedPath)

    // 模拟上传成功，添加到列表中
    imageList.value.push({
      fileId: Date.now().toString(),
      url: compressedPath,
      thumbnailUrl: compressedPath
    })

    uni.hideLoading()
  } catch (error) {
    uni.hideLoading()
    uni.showToast({
      title: '上传失败',
      icon: 'none'
    })
  }
}

// 压缩图片
const compressImage = async (filePath: string): Promise<string> => {
  return new Promise((resolve, reject) => {
    uni.compressImage({
      src: filePath,
      quality: 80,
      success: (res) => {
        resolve(res.tempFilePath)
      },
      fail: (error) => {
        // 如果压缩失败，使用原图片
        resolve(filePath)
      }
    })
  })
}

// 删除图片
const deleteImage = (index: number) => {
  imageList.value.splice(index, 1)
}

// 预览图片
const previewImage = (index: number) => {
  const urls = imageList.value.map(img => img.url)
  uni.previewImage({
    current: index,
    urls: urls
  })
}

// 提交订单
const submitOrder = async () => {
  if (!canSubmit.value) return

  isSubmitting.value = true

  try {
    // TODO: 调用API提交订单
    const requestData = {
      ...formData,
      imageFiles: imageList.value.map(img => img.fileId)
    }

    // 模拟API调用
    await new Promise(resolve => setTimeout(resolve, 2000))

    uni.showToast({
      title: '提交成功',
      icon: 'success'
    })

    // 跳转到订单列表
    setTimeout(() => {
      uni.redirectTo({
        url: '/pages/views/recycle/order-list'
      })
    }, 1500)

  } catch (error: any) {
    uni.showToast({
      title: error.message || '提交失败',
      icon: 'none'
    })
  } finally {
    isSubmitting.value = false
  }
}
</script>

<style lang="scss" scoped>
// 自定义样式
</style>
