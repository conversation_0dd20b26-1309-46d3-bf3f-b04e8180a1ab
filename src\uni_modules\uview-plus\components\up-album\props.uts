import { defineMixin } from '../../libs/vue'
import defProps from './album.uts'
let crtProp = defProps['album'] as UTSJSONObject
import defPropsImage from '../up-image/image.uts'
let crtPropImage = defPropsImage['image'] as UTSJSONObject
export const propsAlbum = defineMixin({
    props: {
        // 图片地址，Array<String>|Array<Object>形式
        urls: {
            type: Array<any>,
            default: crtProp['urls']
        },
        // 指定从数组的对象元素中读取哪个属性作为图片地址
        keyName: {
            type: String,
            default: crtProp['keyName']
        },
        // 单图时，图片长边的长度
        singleSize: {
            type: [String, Number],
            default: crtProp['singleSize']
        },
        // 多图时，图片边长
        multipleSize: {
            type: [String, Number],
            default: crtProp['multipleSize']
        },
        // 多图时，图片水平和垂直之间的间隔
        space: {
            type: [String, Number],
            default: crtProp['space']
        },
        // 单图时，图片缩放裁剪的模式
        singleMode: {
            type: String,
            default: crtProp['singleMode']
        },
        // 多图时，图片缩放裁剪的模式
        multipleMode: {
            type: String,
            default: crtProp['multipleMode']
        },
        // 最多展示的图片数量，超出时最后一个位置将会显示剩余图片数量
        maxCount: {
            type: [String, Number],
            default: crtProp['maxCount']
        },
        // 是否可以预览图片
        previewFullImage: {
            type: Boolean,
            default: crtProp['previewFullImage']
        },
        // 每行展示图片数量，如设置，singleSize和multipleSize将会无效
        rowCount: {
            type: [String, Number],
            default: crtProp['rowCount']
        },
        // 超出maxCount时是否显示查看更多的提示
        showMore: {
            type: Boolean,
            default: crtProp['showMore']
        },
        // 图片形状，circle-圆形，square-方形
        shape: {
            type: String,
            default: crtPropImage['shape']
        },
        // 圆角，单位任意
        radius: {
            type: [String, Number],
            default: crtPropImage['radius']
        },
        // 自适应换行
        autoWrap: {
            type: Boolean,
            default: crtProp['autoWrap']
        },
        // 单位
        unit: {
            type: String,
            default: crtProp['unit']
        }
    }
})
