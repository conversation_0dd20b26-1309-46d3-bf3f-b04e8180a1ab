<template>
  <view class="certification-container">
    <NavBar title="实名认证" bgColor="#FFFFFF" :showBack="true" />
    
    <view class="content">
      <view class="status-card">
        <van-icon :name="statusIcon" :color="statusColor" :size="50"></van-icon>
        <text class="status-text" :style="{ color: statusColor }">{{
          statusText
        }}</text>
        <text v-if="isVerified && certifiedInfo.realName" class="info-text"
          >姓名: {{ displayRealName }}</text
        >
        <text v-if="isVerified && certifiedInfo.idCard" class="info-text"
          >身份证号: {{ displayIdCard }}</text
        >
      </view>

      <view class="form-card" v-if="!isVerified">
        <up-form :model="formData">
          <up-form-item label="姓名" prop="realName" label-width="80">
            <up-input v-model="formData.realName" placeholder="请输入真实姓名" border="none"></up-input>
          </up-form-item>
          <up-form-item label="身份证号" prop="idCard" label-width="80">
            <up-input v-model="formData.idCard" placeholder="请输入身份证号码" border="none"></up-input>
          </up-form-item>
        </up-form>
      </view>

      <view class="action-area" v-if="!isVerified">
        <up-button
          type="primary"
          @click="handleInitiateCertification"
          :loading="loading"
          :disabled="!formData.realName || !formData.idCard"
          >{{ buttonText }}</up-button
        >
      </view>

      <view class="tips">
        <text class="tip-title">温馨提示:</text>
        <text class="tip-content"
          >1.
          根据相关法律法规要求，进行实名认证后才能进行下单购买等操作。</text
        >
        <text class="tip-content"
          >2. 您的信息我们将严格保密，仅用于身份验证。</text
        >
      </view>
    </view>
  </view>
</template>

<script setup>
import { ref, computed, onMounted } from "vue"
import { useStore } from "vuex"
import { initCertification, getCertification } from "@/api/api"
import NavBar from "@/components/NavBar.vue"

const store = useStore()
const loading = ref(false)

// 表单数据
const formData = ref({
  realName: '',
  idCard: ''
})

// 从 store 获取实名状态
const certificationStatus = computed(
  () => store.state.$userInfo?.verified
)
const certifiedInfo = computed(() => store.state.$userInfo)

// 根据实名状态计算UI显示
const isVerified = computed(() => certificationStatus.value === true)
// 不再使用 isPending，因为现在状态只有认证/未认证两种
const isPending = computed(() => false) // 移除处理中状态

// 认证状态对应的UI文本和图标
const statusText = computed(() => {
  if (isVerified.value) {
    return "已实名认证"
  } else {
    return "未实名认证"
  }
})

const statusIcon = computed(() => {
  if (isVerified.value) {
    return "success"
  } else {
    return "question"
  }
})

const statusColor = computed(() => {
  if (isVerified.value) {
    return "#19be6b" // success
  } else {
    return "#909399" // info
  }
})

const buttonText = computed(() => {
  return "开始实名认证"
})

// 认证后显示的姓名和身份证信息（根据实际数据结构可能需要调整）
const displayRealName = computed(() => {
  if (isVerified.value) {
    return certifiedInfo.value?.realName || '未获取到姓名'
  }
  return formData.value.realName
})

const displayIdCard = computed(() => {
  if (isVerified.value) {
    return certifiedInfo.value?.idCard || '未获取到身份证号'
  }
  return formData.value.idCard
})

// 验证身份证号
const validateIdCard = (idCard) => {
  // 身份证号码为15位或者18位，15位时全为数字，18位前17位为数字，最后一位是校验位，可能为数字或字符X
  const reg = /(^\d{15}$)|(^\d{18}$)|(^\d{17}(\d|X|x)$)/
  return reg.test(idCard)
}

// 发起认证
const handleInitiateCertification = async () => {
  // 表单验证
  if (!formData.value.realName || !formData.value.idCard) {
    uni.showToast({
      title: "请填写完整的认证信息",
      icon: "none"
    })
    return
  }
  
  if (!validateIdCard(formData.value.idCard)) {
    uni.showToast({
      title: "请输入正确的身份证号码",
      icon: "none"
    })
    return
  }

  loading.value = true
  try {
    const result = await initCertification({
      realName: formData.value.realName,
      idCard: formData.value.idCard
    })
    
    if (result.code === 200) {
      uni.showToast({
        title: "认证已提交成功",
        icon: "success"
      })
      // 重新获取用户信息，更新认证状态
      refreshStatus()
    } else {
      uni.showToast({
        title: result.message || "认证失败，请重试",
        icon: "none"
      })
    }
  } catch (error) {
    console.error("认证提交失败", error)
    uni.showToast({
      title: "操作失败，请重试",
      icon: "none"
    })
  } finally {
    loading.value = false
  }
}

// 刷新认证状态
const refreshStatus = async () => {
  loading.value = true
  try {
    // 重新获取用户信息，包含认证状态
    const userInfoResult = await uni.$u.http.get('/user/info')
    if (userInfoResult.code === 200) {
      // 更新 Vuex store 中的用户信息
      store.commit("UPDATE_USER_INFO", userInfoResult.data)
    }
  } catch (error) {
    console.error("刷新用户信息失败", error)
  } finally {
    loading.value = false
  }
}

onMounted(() => {
  // 页面加载时获取最新的用户信息和认证状态
  refreshStatus()
})
</script>

<style lang="scss" scoped>
.certification-container {
  display: flex;
  flex-direction: column;
  height: 100vh;
  background-color: #f5f5f5;
}

.content {
  padding: 32rpx;
}

.status-card {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  background-color: #ffffff;
  border-radius: 16rpx;
  padding: 60rpx;
  margin-bottom: 40rpx;
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.05);
}

.status-text {
  font-size: 36rpx;
  font-weight: bold;
  margin-top: 20rpx;
}

.info-text {
  font-size: 28rpx;
  color: #606266;
  margin-top: 12rpx;
}

.form-card {
  background-color: #ffffff;
  border-radius: 16rpx;
  padding: 0 30rpx;
  margin-bottom: 40rpx;
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.05);
}

.action-area {
  margin-bottom: 40rpx;
}

.tips {
  background-color: #ffffff;
  border-radius: 16rpx;
  padding: 30rpx;
  font-size: 26rpx;
  color: #606266;
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.05);
}

.tip-title {
  display: block;
  font-weight: bold;
  margin-bottom: 16rpx;
  color: #303133;
}

.tip-content {
  display: block;
  line-height: 1.6;
  margin-bottom: 10rpx;
}
</style> 