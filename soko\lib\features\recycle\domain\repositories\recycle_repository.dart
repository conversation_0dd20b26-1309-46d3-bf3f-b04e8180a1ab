import 'package:soko/features/recycle/domain/entities/recycle_order.dart'
    hide CreateRecycleOrderRequest;
import 'package:soko/features/recycle/domain/entities/recycle_models.dart';
import 'package:soko/core/models/paginated_data.dart';

/// 回收业务仓库接口
abstract class RecycleRepository {
  /// 获取回收订单列表
  Future<PaginatedData<RecycleOrder>> getRecycleOrders({
    int page = 1,
    int pageSize = 20,
    String? status,
    String? category,
    String? keyword,
    DateTime? startDate,
    DateTime? endDate,
  });

  /// 获取回收订单详情
  Future<RecycleOrder> getRecycleOrderDetail(String orderId);

  /// 创建回收订单
  Future<RecycleOrder> createRecycleOrder(CreateRecycleOrderRequest request);

  /// 取消回收订单
  Future<void> cancelRecycleOrder(String orderId);

  /// 确认寄送
  Future<void> confirmShipment(
      String orderId, Map<String, dynamic> shippingInfo);

  /// 获取回收分类
  Future<List<CategoryItem>> getRecycleCategories();

  /// 获取回收统计信息
  Future<Map<String, dynamic>> getRecycleStats();

  /// 获取回收流程说明
  Future<List<Map<String, dynamic>>> getRecycleProcess();

  /// 获取最近订单
  Future<List<RecycleOrder>> getRecentOrders({int limit = 5});

  /// 获取物流信息
  Future<LogisticsInfo> getLogisticsInfo(String orderId);

  /// 更新物流状态
  Future<void> updateLogisticsStatus(String orderId);

  /// 报告物流异常
  Future<void> reportLogisticsException(String orderId, String reason);

  /// 价格评估
  Future<PriceEvaluationResult> evaluatePrice(PriceEvaluationRequest request);

  /// 获取评估历史
  Future<List<PriceEvaluationResult>> getEvaluationHistory();

  /// 快速价格预估
  Future<double> getQuickEstimate({
    required String brandId,
    required String modelId,
    required String conditionId,
  });

  /// 获取价格趋势
  Future<List<PriceTrendData>> getPriceTrend(String productId);
}
