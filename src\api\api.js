import publicKeyManager from "@/util/common/publicKey"
import JSEncrypt from "jsencrypt"
import { h } from "@vue/runtime-core"

// 封装一个获取 http 实例的函数，增加健壮性检查
const getHttpInstance = () => {
  if (!uni.$u || !uni.$u.http) {
    console.error(
      "uView Plus HTTP 实例尚未初始化！请检查 main.ts 中的初始化顺序。"
    )
    // 可以选择抛出错误或者返回 null/undefined 来中断后续操作
    throw new Error("uView Plus HTTP instance is not initialized.")
    // return null;
  }
  const http = uni.$u.http
  return http
}

// 登录相关API
// 获取公钥 - 优化版本，使用缓存
export const getPublicKeyOptimized = async () => {
  const http = getHttpInstance()
  try {
    const publicKey = await publicKeyManager.getPublicKey()
    return {
      data: {
        success: true,
        publicKey
      }
    }
  } catch (error) {
    console.error("获取公钥失败", error)
    return {
      data: {
        success: false,
        message: error.message || "获取公钥失败"
      }
    }
  }
}

// 获取公钥
export const getPublicKey = async () => {
  const http = getHttpInstance()
  return http.get("/public/pubkey")
}

export const POST_REGISTER_USER = (params) => {
  const http = getHttpInstance()
  return http.post("/user", params)
}

// 检查用户是否存在
export const checkUser = (params) => {
  const http = getHttpInstance()
  return http.get(`/user/check/${params.username}`)
}

// 获取用户信息
export const getUserInfo = () => {
  const http = getHttpInstance()
  return http.get(`/user/info`)
}

// 发送验证码
export const sendVerificationCode = (params) => {
  const http = getHttpInstance()
  return http.post("/third/sms", params)
}

// 通用登录
export const login = (params) =>
  getHttpInstance().post("/login", params, {
    header: {
      "Content-Type": "application/x-www-form-urlencoded"
    }
  })

// 验证码登录
export const loginWithCode = (params) => {
  const http = getHttpInstance()
  return http.post("/sms/login", {
    mobile: params.username,
    code: params.code
  })
}

// 密码登录
export const loginWithPassword = async (params) => {
  // 使用缓存的公钥管理器
  const { username, password } = params

  try {
    // 获取公钥用于加密
    const publicKey = await publicKeyManager.getPublicKey()

    // 使用JSEncrypt加密密码
    const encrypt = new JSEncrypt()
    encrypt.setPublicKey(publicKey)
    const encryptedPassword = encrypt.encrypt(password)

    if (!encryptedPassword) {
      console.error("密码加密失败")
      throw new Error("密码加密失败")
    }

    // 调用登录接口
    return login({
      username,
      password: encryptedPassword
    })
  } catch (error) {
    console.error("登录失败", error)
    return {
      data: {
        success: false,
        message: error.message || "登录失败"
      }
    }
  }
}

// 后端登出接口
export const logout = () => {
  const http = getHttpInstance()
  return http.post("/logout")
}

// 上传文件
export const uploadFile = (file, onProgress) => {
  return new Promise((resolve, reject) => {
    // 获取baseURL
    const baseURL = uni.$u.http.config.baseURL || ""

    const uploadTask = uni.uploadFile({
      url: `${baseURL}/file/upload`, // 上传地址
      filePath: file, // 要上传文件资源的路径
      name: "file", // 文件对应的 key，后端通过此key获取文件
      header: {
        // 如果需要token验证，添加token
        Authorization: uni.getStorageSync("token")
          ? `Bearer ${uni.getStorageSync("token")}`
          : ""
      },
      success: (res) => {
        if (res.statusCode === 200) {
          // 尝试解析响应数据
          try {
            const result = JSON.parse(res.data)
            resolve(result)
          } catch (e) {
            resolve({
              success: false,
              message: "服务器响应解析失败"
            })
          }
        } else {
          resolve({
            success: false,
            message: `上传失败，状态码: ${res.statusCode}`
          })
        }
      },
      fail: (err) => {
        reject({
          success: false,
          message: err.errMsg || "上传失败"
        })
      }
    })

    // 监听上传进度
    if (typeof onProgress === "function") {
      uploadTask.onProgressUpdate((res) => {
        onProgress(res.progress)
      })
    }

    return uploadTask
  })
}

// 校验验证码
export const checkVerificationCode = (params) => {
  const http = getHttpInstance()
  return http.post("/third/sms/verify", params)
}

// 更新用户
export const updateUser = (data) => {
  const http = getHttpInstance()
  return http.put(`/user/${data.id}`, data)
}

// 更新用户密码（旧密码验证）
export const updateUserPassword = (data) => {
  const http = getHttpInstance()
  return http.put(`/user/password/${data.id}`, data)
}

// 地址相关api
// 获取登录用户的地址列表
export const getAddressList = () => {
  const http = getHttpInstance()
  return http.get(`/address/list`)
}

// 新增地址
export const saveAddress = (data) => {
  const http = getHttpInstance()
  return http.post("/address", data)
}

// 更新地址
export const updateAddress = (data) => {
  const http = getHttpInstance()
  return http.put(`/address/${data.id}`, data)
}

// 删除地址
export const deleteAddress = (data) => {
  const http = getHttpInstance()
  return http.delete(`/address/${data.id}`, data)
}

// 获取地址详情
export const getAddressById = (id) => {
  const http = getHttpInstance()
  return http.get(`/address/${id}`)
}

// 根据类型获取分类
export const listCategoryByType = (data) => {
  const http = getHttpInstance()
  return http.get(`/category/tree?type=${data.type}&status=${data.status}`)
}

// 根据id获取分类
export const getCategoryById = (id) => {
  const http = getHttpInstance()
  return http.get(`/category/${id}`)
}

// 获取所有ACG类别
export const listAcg = () => {
  const http = getHttpInstance()
  return http.get("/category/tree?code=ACG")
}

export const getBanners = () => {
  const http = getHttpInstance()
  return http.get("/banner/list")
}

export const getNewProductList = (limit) => {
  const http = getHttpInstance()
  return http.get("/product/list/new", { params: limit })
}

export const getCouponsPage = (limit) => {
  const http = getHttpInstance()
  return http.get("/coupons/page/user", { params: limit })
}

export const listProductByAcg = (acg) => {
  const http = getHttpInstance()
  return http.post("/product/list/acg", acg)
}

// 获取商品列表
export const listProduct = (params) => {
  const http = getHttpInstance()
  return http.post("/product/search", params)
}

// 获取商品详情
export const getProductDetail = (data) => {
  const http = getHttpInstance()
  return http.get(`/product/${data.id}`, {
    params: data.status ? { status: data.status } : undefined
  })
}

// 获取公告列表
export const GET_ANNOUNCEMENT_LIST = () => {
  const http = getHttpInstance()
  return http.get(`/announcement/list`)
}

// 获取公告详情
export const GET_ANNOUNCEMENT_DETAIL = (id) => {
  const http = getHttpInstance()
  return http.get(`/announcement/${id}`)
}

// 购物车相关API
// 添加商品到购物车
export const addToCart = (data) => {
  const http = getHttpInstance()
  return http.post("/cart", data)
}

// 获取购物车列表
export const getCartList = () => {
  const http = getHttpInstance()
  return http.get("/cart/list")
}

// 分页获取购物车列表
export const getCartPage = (params) => {
  const http = getHttpInstance()
  return http.get("/cart/page", { params })
}

// 更新购物车商品数量
export const updateCartQuantity = (data) => {
  const http = getHttpInstance()
  return http.put("/cart", data)
}

// 删除购物车商品
export const deleteCartItem = (id) => {
  const http = getHttpInstance()
  return http.delete(`/cart/${id}`)
}

// 批量删除购物车商品
export const batchDeleteCartItems = (data) => {
  const http = getHttpInstance()
  return http.delete("/cart/batch", { data })
}

// 清空购物车
export const clearCart = () => {
  const http = getHttpInstance()
  return http.delete("/cart/clear")
}

// 创建订单
export const createOrder = (data) => {
  const http = getHttpInstance()
  return http.post("/app/order", data)
}

// 获取系统参数
export const getSystemParam = () => {
  const http = getHttpInstance()
  return http.get(`/system/list`)
}

// 获取订单详情
export const getOrderDetail = async (id) => {
  const http = getHttpInstance()
  try {
    const res = await http.get(`/app/order/${id}`)
    return [null, res.data]
  } catch (err) {
    return [err, null]
  }
}

// 获取订单分页列表
export const getOrderList = async (params) => {
  const http = getHttpInstance()
  return http.get("/app/order/page", { params })
}

// 确认收货
export const confirmReceiveOrder = async (orderId, version) => {
  const http = getHttpInstance()
  try {
    // 后端要求version作为@RequestBody传递
    const res = await http.post(`/app/order/${orderId}/receive`, {
      version: version
    })
    return [null, res]
  } catch (err) {
    return [err, null]
  }
}

// 取消订单
export const cancelOrder = async (orderId, version) => {
  const http = getHttpInstance()
  try {
    const response = await http.put(`app/order/${orderId}/cancel`, {
      version: version
    })
    return [null, response]
  } catch (error) {
    return [error, null]
  }
}

// 支付订单
export const payOrder = async (payData) => {
  const http = getHttpInstance()
  try {
    const res = await http.post("/app/order/pay", payData)
    return [null, res]
  } catch (err) {
    return [err, null]
  }
}

// 根据订单ID获取订单快照列表
export const getOrderSnapshots = async (orderId) => {
  const http = getHttpInstance()
  // 确保使用订单ID而不是订单号
  return http.get(`/order/snapshots/${orderId}`)
}

// 修改订单收货地址
export const updateOrderAddress = async (orderId, addressData) => {
  const http = getHttpInstance()
  return http.put(`/app/order/address/${orderId}`, addressData)
}

// 删除订单
export const deleteOrder = async (orderId, version) => {
  const http = getHttpInstance()
  return await http.delete(`/app/order/status/${orderId}`, {
    version: version
  })
}

export const GET_COUPONS_PRODUCT = async (data) => {
  const http = getHttpInstance()
  return http.get(`/coupons/product/${data.productId}`, {
    params: { orderAmount: data.orderAmount }
  })
}

// 提交退货申请
export const submitReturnRequest = async (returnData) => {
  const http = getHttpInstance()
  try {
    const res = await http.post("/app/after-sale/apply", returnData)
    return [null, res]
  } catch (err) {
    return [err, null]
  }
}

// 获取系统参数映射
export const getSystemMap = (type) => {
  const http = getHttpInstance()
  return http.get(`/system/map/${type}`)
}

// 查询我的售后列表
export const getMyAfterSaleList = async (params) => {
  const http = getHttpInstance()
  return http.get("/app/after-sale/my-after-sales", { params })
}

// 获取售后详情
export const getAfterSaleDetail = async (afterSaleId) => {
  const http = getHttpInstance()
  try {
    const res = await http.get(`/app/after-sale/${afterSaleId}`)
    return [null, res.data]
  } catch (err) {
    return [err, null]
  }
}

// 取消售后申请
export const cancelAfterSale = async (afterSaleId) => {
  const http = getHttpInstance()
  try {
    const res = await http.post(`/app/after-sale/${afterSaleId}/cancel`)
    return [null, res]
  } catch (err) {
    return [err, null]
  }
}

// 提交退货物流信息
export const submitReturnLogistics = (data) => {
  const http = getHttpInstance()
  return http.post("/app/after-sale/return-info", data)
}

// 查询单个SKU库存（实时）
export const getSkuStock = (skuId) => {
  const http = getHttpInstance()
  return http.get(`/product/stock/${skuId}`)
}

// 批量查询SKU库存
export const getBatchSkuStock = (skuIdList) => {
  const http = getHttpInstance()
  return http.post("/product/stock/batch", skuIdList)
}

// 获取当前会员信息
export const getMemberInfo = () => {
  const http = getHttpInstance()
  return http.get("/member/info")
}

// 根据会员类型获取权益
export const getPrivilegesByType = (memberType) => {
  const http = getHttpInstance()
  return http.get(`/member/privilege/by-type/${memberType}`)
}

export const DELETE_USER_DEACTIVATE = (params) => {
  const http = getHttpInstance()
  return http.delete("/user/deactivate", { ...params })
}

export const GET_PRODUCT_HOTKEYWORD = () => {
  const http = getHttpInstance()
  return http.get("/product/hot-keyword")
}

export const GET_NOTICES_LIST = (params) => {
  const http = getHttpInstance()
  return http.get("/message/userMessage", { params })
}

export const POST_READ_MESSAGE = (params) => {
  console.log(params)
  const http = getHttpInstance()
  return http.post(`/message/readMessage`, params)
}

// 新增：按类型和状态查询用户优惠券集合
export const getUserCoupons = (params) => {
  const http = getHttpInstance()
  return http.get("/coupon-user/list", { params })
}

// 创建会员订单
export const createMemberOrder = (data) => {
  const http = getHttpInstance()
  return http.post(`/member/order`, data)
}

export const SPLASHSCREEN = () => {
  const http = getHttpInstance()
  return http.get("/announcement/splash-screen")
}

/**
 * 发起实名认证
 * @param {object} params - 包含回调URL等参数
 * @returns
 */
export const initCertification = (params) => {
  const http = getHttpInstance()
  return http.post("/user/certification/init", params)
}

/**
 * 获取用户实名认证信息
 * @returns
 */
export const getCertification = () => {
  const http = getHttpInstance()
  return http.get("/user/certification")
}

/**
 * 查询实名认证结果
 * @returns
 */
export const getCertificationResult = () => {
  const http = getHttpInstance()
  return http.get("/user/certification/query")
}
