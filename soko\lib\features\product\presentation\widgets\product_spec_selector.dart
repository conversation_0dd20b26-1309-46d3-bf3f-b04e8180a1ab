import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

import 'package:soko/core/theme/app_colors.dart';
import 'package:soko/core/theme/app_text_styles.dart';
import 'package:soko/features/product/domain/entities/product.dart';

/// 商品规格选择器
class ProductSpecSelector extends StatelessWidget {

  const ProductSpecSelector({
    super.key,
    required this.specs,
    this.selectedSpecId,
    this.onSpecSelected,
  });
  final List<ProductSku> specs;
  final String? selectedSpecId;
  final Function(String specId)? onSpecSelected;

  @override
  Widget build(BuildContext context) {
    if (specs.isEmpty) {
      return const SizedBox.shrink();
    }

    return Container(
      padding: EdgeInsets.all(16.w),
      decoration: BoxDecoration(
        color: Colors.white,
        border: Border(
          bottom: BorderSide(
            color: AppColors.grey200,
          ),
        ),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // 标题
          Text(
            '选择规格',
            style: AppTextStyles.bodyLarge.copyWith(
              fontWeight: FontWeight.w600,
            ),
          ),
          SizedBox(height: 12.h),
          // 规格选项
          Wrap(
            spacing: 8.w,
            runSpacing: 8.h,
            children: specs.map((spec) {
              final isSelected = spec.id == selectedSpecId;
              final isOutOfStock = spec.stock <= 0;

              return _buildSpecOption(
                spec: spec,
                isSelected: isSelected,
                isOutOfStock: isOutOfStock,
              );
            }).toList(),
          ),
        ],
      ),
    );
  }

  /// 构建规格选项
  Widget _buildSpecOption({
    required ProductSku spec,
    required bool isSelected,
    required bool isOutOfStock,
  }) {
    Color backgroundColor;
    Color borderColor;
    Color textColor;

    if (isOutOfStock) {
      backgroundColor = AppColors.grey100;
      borderColor = AppColors.grey300;
      textColor = AppColors.grey400;
    } else if (isSelected) {
      backgroundColor = AppColors.primary.withOpacity(0.1);
      borderColor = AppColors.primary;
      textColor = AppColors.primary;
    } else {
      backgroundColor = Colors.white;
      borderColor = AppColors.grey300;
      textColor = AppColors.grey700;
    }

    return GestureDetector(
      onTap: isOutOfStock ? null : () => onSpecSelected?.call(spec.id),
      child: Container(
        padding: EdgeInsets.symmetric(horizontal: 16.w, vertical: 8.h),
        decoration: BoxDecoration(
          color: backgroundColor,
          borderRadius: BorderRadius.circular(8.r),
          border: Border.all(color: borderColor),
        ),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            // 规格名称
            Text(
              spec.name,
              style: AppTextStyles.bodyMedium.copyWith(
                color: textColor,
                fontWeight: isSelected ? FontWeight.w600 : FontWeight.normal,
              ),
            ),
            // 价格信息
            if (spec.price > 0) ...[
              SizedBox(height: 2.h),
              Text(
                '¥${spec.price.toStringAsFixed(2)}',
                style: AppTextStyles.bodySmall.copyWith(
                  color: textColor,
                ),
              ),
            ],
            // 库存信息
            if (isOutOfStock) ...[
              SizedBox(height: 2.h),
              Text(
                '缺货',
                style: AppTextStyles.caption.copyWith(
                  color: AppColors.error,
                ),
              ),
            ] else if (spec.stock <= 10) ...[
              SizedBox(height: 2.h),
              Text(
                '仅剩${spec.stock}件',
                style: AppTextStyles.caption.copyWith(
                  color: AppColors.warning,
                ),
              ),
            ],
          ],
        ),
      ),
    );
  }
}

/// 数量选择器
class QuantitySelector extends StatelessWidget {

  const QuantitySelector({
    super.key,
    required this.quantity,
    this.maxQuantity = 999,
    this.minQuantity = 1,
    this.onQuantityChanged,
  });
  final int quantity;
  final int maxQuantity;
  final int minQuantity;
  final Function(int quantity)? onQuantityChanged;

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: EdgeInsets.all(16.w),
      decoration: BoxDecoration(
        color: Colors.white,
        border: Border(
          bottom: BorderSide(
            color: AppColors.grey200,
          ),
        ),
      ),
      child: Row(
        children: [
          Text(
            '数量',
            style: AppTextStyles.bodyLarge.copyWith(
              fontWeight: FontWeight.w600,
            ),
          ),
          const Spacer(),
          // 数量控制器
          Container(
            decoration: BoxDecoration(
              border: Border.all(color: AppColors.grey300),
              borderRadius: BorderRadius.circular(8.r),
            ),
            child: Row(
              mainAxisSize: MainAxisSize.min,
              children: [
                // 减少按钮
                _buildQuantityButton(
                  icon: Icons.remove,
                  onTap: quantity > minQuantity
                      ? () => onQuantityChanged?.call(quantity - 1)
                      : null,
                ),
                // 数量显示
                Container(
                  width: 60.w,
                  height: 36.h,
                  alignment: Alignment.center,
                  decoration: BoxDecoration(
                    border: Border.symmetric(
                      vertical: BorderSide(color: AppColors.grey300),
                    ),
                  ),
                  child: Text(
                    quantity.toString(),
                    style: AppTextStyles.bodyMedium.copyWith(
                      fontWeight: FontWeight.w600,
                    ),
                  ),
                ),
                // 增加按钮
                _buildQuantityButton(
                  icon: Icons.add,
                  onTap: quantity < maxQuantity
                      ? () => onQuantityChanged?.call(quantity + 1)
                      : null,
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  /// 构建数量按钮
  Widget _buildQuantityButton({
    required IconData icon,
    VoidCallback? onTap,
  }) {
    return GestureDetector(
      onTap: onTap,
      child: Container(
        width: 36.w,
        height: 36.h,
        alignment: Alignment.center,
        child: Icon(
          icon,
          size: 18.sp,
          color: onTap != null ? AppColors.grey700 : AppColors.grey400,
        ),
      ),
    );
  }
}

/// 商品规格弹窗
class ProductSpecBottomSheet extends StatefulWidget {

  const ProductSpecBottomSheet({
    super.key,
    required this.product,
    this.selectedSpecId,
    this.quantity = 1,
    this.onConfirm,
  });
  final Product product;
  final String? selectedSpecId;
  final int quantity;
  final Function(String specId, int quantity)? onConfirm;

  @override
  State<ProductSpecBottomSheet> createState() => _ProductSpecBottomSheetState();
}

class _ProductSpecBottomSheetState extends State<ProductSpecBottomSheet> {
  late String? _selectedSpecId;
  late int _quantity;

  @override
  void initState() {
    super.initState();
    _selectedSpecId = widget.selectedSpecId ??
        (widget.product.skus?.isNotEmpty ?? false
            ? widget.product.skus!.first.id
            : null);
    _quantity = widget.quantity;
  }

  @override
  Widget build(BuildContext context) {
    final selectedSpec = _selectedSpecId != null && widget.product.skus != null
        ? widget.product.skus!.firstWhere(
            (spec) => spec.id == _selectedSpecId,
            orElse: () => widget.product.skus!.first,
          )
        : null;

    return Container(
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.vertical(
          top: Radius.circular(16.r),
        ),
      ),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          // 顶部拖拽条
          Container(
            width: 40.w,
            height: 4.h,
            margin: EdgeInsets.symmetric(vertical: 8.h),
            decoration: BoxDecoration(
              color: AppColors.border,
              borderRadius: BorderRadius.circular(2.r),
            ),
          ),
          // 商品信息
          Container(
            padding: EdgeInsets.all(16.w),
            child: Row(
              children: [
                // 商品图片
                Container(
                  width: 80.w,
                  height: 80.w,
                  decoration: BoxDecoration(
                    borderRadius: BorderRadius.circular(8.r),
                    color: AppColors.background,
                  ),
                  // TODO: 添加商品图片
                ),
                SizedBox(width: 12.w),
                // 商品信息
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        widget.product.name,
                        style: AppTextStyles.bodyMedium.copyWith(
                          fontWeight: FontWeight.w600,
                        ),
                        maxLines: 2,
                        overflow: TextOverflow.ellipsis,
                      ),
                      SizedBox(height: 4.h),
                      if (selectedSpec != null)
                        Text(
                          '¥${selectedSpec.price.toStringAsFixed(2)}',
                          style: AppTextStyles.bodyLarge.copyWith(
                            color: AppColors.primary,
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                    ],
                  ),
                ),
              ],
            ),
          ),
          // 规格选择
          if (widget.product.skus != null)
            ProductSpecSelector(
              specs: widget.product.skus!,
              selectedSpecId: _selectedSpecId,
              onSpecSelected: (specId) {
                setState(() {
                  _selectedSpecId = specId;
                  // 重置数量为1
                  _quantity = 1;
                });
              },
            ),
          // 数量选择
          QuantitySelector(
            quantity: _quantity,
            maxQuantity: selectedSpec?.stock ?? 999,
            onQuantityChanged: (quantity) {
              setState(() {
                _quantity = quantity;
              });
            },
          ),
          // 底部按钮
          Container(
            padding: EdgeInsets.all(16.w),
            child: SizedBox(
              width: double.infinity,
              child: ElevatedButton(
                onPressed: _selectedSpecId != null &&
                        selectedSpec != null &&
                        selectedSpec.stock > 0
                    ? () {
                        widget.onConfirm?.call(_selectedSpecId!, _quantity);
                        Navigator.of(context).pop();
                      }
                    : null,
                child: Text(
                  selectedSpec?.stock == 0 ? '缺货' : '确定',
                ),
              ),
            ),
          ),
          // 底部安全区域
          SizedBox(height: MediaQuery.of(context).padding.bottom),
        ],
      ),
    );
  }
}
