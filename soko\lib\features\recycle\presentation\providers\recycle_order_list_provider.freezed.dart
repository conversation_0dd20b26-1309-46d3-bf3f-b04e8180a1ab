// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'recycle_order_list_provider.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#adding-getters-and-methods-to-our-models');

/// @nodoc
mixin _$RecycleOrderListData {
  List<RecycleOrder> get orders => throw _privateConstructorUsedError;
  int get currentPage => throw _privateConstructorUsedError;
  int get pageSize => throw _privateConstructorUsedError;
  bool get hasMore => throw _privateConstructorUsedError;
  bool get isLoadingMore => throw _privateConstructorUsedError;
  String? get statusFilter => throw _privateConstructorUsedError;
  String? get categoryFilter => throw _privateConstructorUsedError;
  String? get searchKeyword => throw _privateConstructorUsedError;
  DateTime? get startDate => throw _privateConstructorUsedError;
  DateTime? get endDate => throw _privateConstructorUsedError;

  /// Create a copy of RecycleOrderListData
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $RecycleOrderListDataCopyWith<RecycleOrderListData> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $RecycleOrderListDataCopyWith<$Res> {
  factory $RecycleOrderListDataCopyWith(RecycleOrderListData value,
          $Res Function(RecycleOrderListData) then) =
      _$RecycleOrderListDataCopyWithImpl<$Res, RecycleOrderListData>;
  @useResult
  $Res call(
      {List<RecycleOrder> orders,
      int currentPage,
      int pageSize,
      bool hasMore,
      bool isLoadingMore,
      String? statusFilter,
      String? categoryFilter,
      String? searchKeyword,
      DateTime? startDate,
      DateTime? endDate});
}

/// @nodoc
class _$RecycleOrderListDataCopyWithImpl<$Res,
        $Val extends RecycleOrderListData>
    implements $RecycleOrderListDataCopyWith<$Res> {
  _$RecycleOrderListDataCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of RecycleOrderListData
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? orders = null,
    Object? currentPage = null,
    Object? pageSize = null,
    Object? hasMore = null,
    Object? isLoadingMore = null,
    Object? statusFilter = freezed,
    Object? categoryFilter = freezed,
    Object? searchKeyword = freezed,
    Object? startDate = freezed,
    Object? endDate = freezed,
  }) {
    return _then(_value.copyWith(
      orders: null == orders
          ? _value.orders
          : orders // ignore: cast_nullable_to_non_nullable
              as List<RecycleOrder>,
      currentPage: null == currentPage
          ? _value.currentPage
          : currentPage // ignore: cast_nullable_to_non_nullable
              as int,
      pageSize: null == pageSize
          ? _value.pageSize
          : pageSize // ignore: cast_nullable_to_non_nullable
              as int,
      hasMore: null == hasMore
          ? _value.hasMore
          : hasMore // ignore: cast_nullable_to_non_nullable
              as bool,
      isLoadingMore: null == isLoadingMore
          ? _value.isLoadingMore
          : isLoadingMore // ignore: cast_nullable_to_non_nullable
              as bool,
      statusFilter: freezed == statusFilter
          ? _value.statusFilter
          : statusFilter // ignore: cast_nullable_to_non_nullable
              as String?,
      categoryFilter: freezed == categoryFilter
          ? _value.categoryFilter
          : categoryFilter // ignore: cast_nullable_to_non_nullable
              as String?,
      searchKeyword: freezed == searchKeyword
          ? _value.searchKeyword
          : searchKeyword // ignore: cast_nullable_to_non_nullable
              as String?,
      startDate: freezed == startDate
          ? _value.startDate
          : startDate // ignore: cast_nullable_to_non_nullable
              as DateTime?,
      endDate: freezed == endDate
          ? _value.endDate
          : endDate // ignore: cast_nullable_to_non_nullable
              as DateTime?,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$RecycleOrderListDataImplCopyWith<$Res>
    implements $RecycleOrderListDataCopyWith<$Res> {
  factory _$$RecycleOrderListDataImplCopyWith(_$RecycleOrderListDataImpl value,
          $Res Function(_$RecycleOrderListDataImpl) then) =
      __$$RecycleOrderListDataImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {List<RecycleOrder> orders,
      int currentPage,
      int pageSize,
      bool hasMore,
      bool isLoadingMore,
      String? statusFilter,
      String? categoryFilter,
      String? searchKeyword,
      DateTime? startDate,
      DateTime? endDate});
}

/// @nodoc
class __$$RecycleOrderListDataImplCopyWithImpl<$Res>
    extends _$RecycleOrderListDataCopyWithImpl<$Res, _$RecycleOrderListDataImpl>
    implements _$$RecycleOrderListDataImplCopyWith<$Res> {
  __$$RecycleOrderListDataImplCopyWithImpl(_$RecycleOrderListDataImpl _value,
      $Res Function(_$RecycleOrderListDataImpl) _then)
      : super(_value, _then);

  /// Create a copy of RecycleOrderListData
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? orders = null,
    Object? currentPage = null,
    Object? pageSize = null,
    Object? hasMore = null,
    Object? isLoadingMore = null,
    Object? statusFilter = freezed,
    Object? categoryFilter = freezed,
    Object? searchKeyword = freezed,
    Object? startDate = freezed,
    Object? endDate = freezed,
  }) {
    return _then(_$RecycleOrderListDataImpl(
      orders: null == orders
          ? _value._orders
          : orders // ignore: cast_nullable_to_non_nullable
              as List<RecycleOrder>,
      currentPage: null == currentPage
          ? _value.currentPage
          : currentPage // ignore: cast_nullable_to_non_nullable
              as int,
      pageSize: null == pageSize
          ? _value.pageSize
          : pageSize // ignore: cast_nullable_to_non_nullable
              as int,
      hasMore: null == hasMore
          ? _value.hasMore
          : hasMore // ignore: cast_nullable_to_non_nullable
              as bool,
      isLoadingMore: null == isLoadingMore
          ? _value.isLoadingMore
          : isLoadingMore // ignore: cast_nullable_to_non_nullable
              as bool,
      statusFilter: freezed == statusFilter
          ? _value.statusFilter
          : statusFilter // ignore: cast_nullable_to_non_nullable
              as String?,
      categoryFilter: freezed == categoryFilter
          ? _value.categoryFilter
          : categoryFilter // ignore: cast_nullable_to_non_nullable
              as String?,
      searchKeyword: freezed == searchKeyword
          ? _value.searchKeyword
          : searchKeyword // ignore: cast_nullable_to_non_nullable
              as String?,
      startDate: freezed == startDate
          ? _value.startDate
          : startDate // ignore: cast_nullable_to_non_nullable
              as DateTime?,
      endDate: freezed == endDate
          ? _value.endDate
          : endDate // ignore: cast_nullable_to_non_nullable
              as DateTime?,
    ));
  }
}

/// @nodoc

class _$RecycleOrderListDataImpl implements _RecycleOrderListData {
  const _$RecycleOrderListDataImpl(
      {final List<RecycleOrder> orders = const [],
      this.currentPage = 1,
      this.pageSize = 20,
      this.hasMore = true,
      this.isLoadingMore = false,
      this.statusFilter,
      this.categoryFilter,
      this.searchKeyword,
      this.startDate,
      this.endDate})
      : _orders = orders;

  final List<RecycleOrder> _orders;
  @override
  @JsonKey()
  List<RecycleOrder> get orders {
    if (_orders is EqualUnmodifiableListView) return _orders;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(_orders);
  }

  @override
  @JsonKey()
  final int currentPage;
  @override
  @JsonKey()
  final int pageSize;
  @override
  @JsonKey()
  final bool hasMore;
  @override
  @JsonKey()
  final bool isLoadingMore;
  @override
  final String? statusFilter;
  @override
  final String? categoryFilter;
  @override
  final String? searchKeyword;
  @override
  final DateTime? startDate;
  @override
  final DateTime? endDate;

  @override
  String toString() {
    return 'RecycleOrderListData(orders: $orders, currentPage: $currentPage, pageSize: $pageSize, hasMore: $hasMore, isLoadingMore: $isLoadingMore, statusFilter: $statusFilter, categoryFilter: $categoryFilter, searchKeyword: $searchKeyword, startDate: $startDate, endDate: $endDate)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$RecycleOrderListDataImpl &&
            const DeepCollectionEquality().equals(other._orders, _orders) &&
            (identical(other.currentPage, currentPage) ||
                other.currentPage == currentPage) &&
            (identical(other.pageSize, pageSize) ||
                other.pageSize == pageSize) &&
            (identical(other.hasMore, hasMore) || other.hasMore == hasMore) &&
            (identical(other.isLoadingMore, isLoadingMore) ||
                other.isLoadingMore == isLoadingMore) &&
            (identical(other.statusFilter, statusFilter) ||
                other.statusFilter == statusFilter) &&
            (identical(other.categoryFilter, categoryFilter) ||
                other.categoryFilter == categoryFilter) &&
            (identical(other.searchKeyword, searchKeyword) ||
                other.searchKeyword == searchKeyword) &&
            (identical(other.startDate, startDate) ||
                other.startDate == startDate) &&
            (identical(other.endDate, endDate) || other.endDate == endDate));
  }

  @override
  int get hashCode => Object.hash(
      runtimeType,
      const DeepCollectionEquality().hash(_orders),
      currentPage,
      pageSize,
      hasMore,
      isLoadingMore,
      statusFilter,
      categoryFilter,
      searchKeyword,
      startDate,
      endDate);

  /// Create a copy of RecycleOrderListData
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$RecycleOrderListDataImplCopyWith<_$RecycleOrderListDataImpl>
      get copyWith =>
          __$$RecycleOrderListDataImplCopyWithImpl<_$RecycleOrderListDataImpl>(
              this, _$identity);
}

abstract class _RecycleOrderListData implements RecycleOrderListData {
  const factory _RecycleOrderListData(
      {final List<RecycleOrder> orders,
      final int currentPage,
      final int pageSize,
      final bool hasMore,
      final bool isLoadingMore,
      final String? statusFilter,
      final String? categoryFilter,
      final String? searchKeyword,
      final DateTime? startDate,
      final DateTime? endDate}) = _$RecycleOrderListDataImpl;

  @override
  List<RecycleOrder> get orders;
  @override
  int get currentPage;
  @override
  int get pageSize;
  @override
  bool get hasMore;
  @override
  bool get isLoadingMore;
  @override
  String? get statusFilter;
  @override
  String? get categoryFilter;
  @override
  String? get searchKeyword;
  @override
  DateTime? get startDate;
  @override
  DateTime? get endDate;

  /// Create a copy of RecycleOrderListData
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$RecycleOrderListDataImplCopyWith<_$RecycleOrderListDataImpl>
      get copyWith => throw _privateConstructorUsedError;
}
