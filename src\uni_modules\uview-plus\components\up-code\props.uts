/*
 * <AUTHOR> jry
 * @Description  :
 * @version      : 4.0
 * @Date         : 2024-08-28 23:05:58
 * @LastAuthor   : jry
 * @lastTime     : 2024-08-28 23:05:58
 * @FilePath     : /uview-plus/libs/config/props/code.js
 */
import { defineMixin } from '../../libs/vue'
import defProps from './code'
let crtProp = defProps['code'] as UTSJSONObject

export const propsCode = defineMixin({
    props: {
        // 倒计时总秒数
        seconds: {
            type: [Number],
            default: crtProp['seconds']
        },
        // 尚未开始时提示
        startText: {
            type: String,
            default: crtProp['startText']
        },
        // 正在倒计时中的提示
        changeText: {
            type: String,
            default: crtProp['changeText']
        },
        // 倒计时结束时的提示
        endText: {
            type: String,
            default: crtProp['endText']
        },
        // 是否在H5刷新或各端返回再进入时继续倒计时
        keepRunning: {
            type: Boolean,
            default: crtProp['keepRunning']
        },
        // 为了区分多个页面，或者一个页面多个倒计时组件本地存储的继续倒计时变了
        uniqueKey: {
            type: String,
            default: crtProp['uniqueKey']
        }
    }
})
