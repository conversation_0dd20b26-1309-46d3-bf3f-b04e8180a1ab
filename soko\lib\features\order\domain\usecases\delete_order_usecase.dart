import 'package:dartz/dartz.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';

import 'package:soko/core/error/failures.dart';
import 'package:soko/core/usecases/usecase.dart';
import 'package:soko/features/order/data/repositories/order_repository_impl.dart';
import 'package:soko/features/order/domain/repositories/order_repository.dart';

/// 删除订单用例
class DeleteOrderUseCase implements UseCase<bool, String> {

  DeleteOrderUseCase(this.repository);
  final OrderRepository repository;

  @override
  Future<Either<Failure, bool>> call(String orderId) async {
    return repository.deleteOrder(orderId);
  }
}

/// DeleteOrderUseCase 提供者
final deleteOrderUseCaseProvider = Provider<DeleteOrderUseCase>((ref) {
  final repository = ref.read(orderRepositoryProvider);
  return DeleteOrderUseCase(repository);
});
