let version = '4'

// 开发环境才提示，生产环境不会提示
if (process.env.NODE_ENV === 'development') {
	console.log(`\n %c uview-plus V${version} %c https://ijry.github.io/uview-plus/ \n\n`, 'color: #ffffff; background: #3c9cff; padding:5px 0;', 'color: #3c9cff;background: #ffffff; padding:5px 0;');
}

export default {
    v: '4',
    version: '4',
    // 主题名称
    type: [
        'primary',
        'success',
        'info',
        'error',
        'warning'
    ],
    // 颜色部分，本来可以通过scss的:export导出供js使用，但是奈何nvue不支持
    color: {
        'up-primary': '#2979ff',
        'up-warning': '#ff9900',
        'up-success': '#19be6b',
        'up-error': '#fa3534',
        'up-info': '#909399',
        'up-main-color': '#303133',
        'up-content-color': '#606266',
        'up-tips-color': '#909399',
        'up-light-color': '#c0c4cc'
    },
	// 默认单位，可以通过配置为rpx，那么在用于传入组件大小参数为数值时，就默认为rpx
	unit: 'px'
} as UTSJSONObject
