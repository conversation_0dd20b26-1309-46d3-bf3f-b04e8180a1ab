我需要将现有的 UniApp 项目完全重构为 Flutter 项目。请按照以下要求执行：

**项目结构要求：**
- 将重构后的 Flutter 代码放置在 `soko/` 目录下
- 使用标准的 Flutter 项目结构和最佳实践

**业务逻辑保持：**
- 完全保持原有的业务逻辑和功能不变
- 所有实体类的字段名称、类型和结构必须与原项目完全一致
- API 接口调用逻辑和数据处理流程保持不变
- 用户交互流程和页面跳转逻辑保持一致

**UI/UX 改进：**
- 可以重新设计 UI 界面，使其更符合 Flutter Material Design 或 Cupertino 设计规范
- 确保整个应用的 UI 风格统一一致
- 优化用户体验，但不改变核心功能流程

**代码质量要求：**
- 遵循 Flutter 开发最佳实践
- 使用合适的状态管理方案（如 Provider、Riverpod 或 Bloc）
- 实现良好的代码分层架构（如 Clean Architecture）
- 添加适当的错误处理和异常管理
- 确保代码可维护性和可扩展性

**执行步骤：**
1. 首先分析现有 UniApp 项目的结构和功能
2. 制定详细的重构计划和任务列表
3. 按模块逐步实现 Flutter 版本
4. 确保每个功能模块都经过测试验证

请开始分析现有项目并制定重构计划。打开的md文件是目前的任务进度

[ ] NAME:Current Task List DESCRIPTION:Root task for conversation __NEW_AGENT__
-[/] NAME:UniApp 到 Flutter 重构项目 - 第三阶段开始 DESCRIPTION:从第三阶段订单管理模块开始，继续完成 UniApp 到 Flutter 的重构工作，保持业务逻辑不变，优化 UI/UX 体验
--[x] NAME:第三阶段：订单管理模块开发 DESCRIPTION:实现订单创建、订单列表、订单详情、订单状态管理、支付集成等功能
---[x] NAME:订单数据模型开发 DESCRIPTION:创建订单实体类、订单状态枚举、订单项实体等数据模型，保持与 UniApp 版本一致
---[x] NAME:订单 API 服务封装 DESCRIPTION:封装订单相关的 API 接口，包括创建订单、查询订单列表、获取订单详情、更新订单状态等
---[x] NAME:订单状态管理 DESCRIPTION:实现订单状态管理逻辑，包括状态流转、状态验证、状态显示等功能
---[x] NAME:订单列表页面 DESCRIPTION:实现订单列表页面，包括分页加载、状态筛选、下拉刷新、上拉加载等功能
---[x] NAME:订单详情页面 DESCRIPTION:实现订单详情页面，包括订单信息展示、商品信息、物流信息、操作按钮等
---[x] NAME:订单创建流程 DESCRIPTION:实现从购物车到订单创建的完整流程，包括地址选择、支付方式、优惠券使用等
---[x] NAME:订单操作功能 DESCRIPTION:实现订单相关操作，包括取消订单、申请退款、确认收货、评价等功能
---[x] NAME:支付集成开发 DESCRIPTION:集成支付功能，包括支付宝、微信支付等支付方式，实现支付流程和状态管理
--[x] NAME:第四阶段：回收业务模块开发 DESCRIPTION:实现回收首页、创建回收订单、订单管理、物流跟踪等核心差异化业务功能
---[x] NAME:回收数据模型开发 DESCRIPTION:创建回收订单实体、回收状态枚举、回收商品信息等数据模型，保持与 UniApp 版本一致
---[ ] NAME:回收 API 服务封装 DESCRIPTION:封装回收相关的 API 接口，包括创建回收订单、查询订单列表、上传图片、物流信息等
---[ ] NAME:回收首页开发 DESCRIPTION:实现回收业务首页，包括业务介绍、流程说明、快速创建入口、最新订单等
---[ ] NAME:创建回收订单页面 DESCRIPTION:实现创建回收订单页面，包括商品信息填写、图片上传、成色选择、联系信息等
---[ ] NAME:回收订单列表页面 DESCRIPTION:实现回收订单列表页面，包括状态筛选、分页加载、订单卡片展示等
---[ ] NAME:回收订单详情页面 DESCRIPTION:实现回收订单详情页面，包括订单信息、商品图片、状态进度、操作按钮等
---[ ] NAME:图片上传组件 DESCRIPTION:实现图片上传组件，包括多图上传、图片预览、图片压缩、上传进度等
--[ ] NAME:第五阶段：个人中心模块开发 DESCRIPTION:实现用户信息、会员体系、地址管理、设置等用户管理功能
---[ ] NAME:用户信息数据模型 DESCRIPTION:创建用户信息实体、会员等级枚举、地址信息实体等数据模型
---[ ] NAME:用户 API 服务封装 DESCRIPTION:封装用户相关的 API 接口，包括获取用户信息、更新信息、地址管理、会员信息等
---[ ] NAME:个人中心首页 DESCRIPTION:实现个人中心首页，包括用户头像、基本信息、会员状态、功能入口等
---[ ] NAME:用户信息编辑页面 DESCRIPTION:实现用户信息编辑页面，包括头像上传、昵称修改、性别选择、生日设置等
---[ ] NAME:地址管理功能 DESCRIPTION:实现地址管理功能，包括地址列表、新增地址、编辑地址、删除地址、设置默认地址等
---[ ] NAME:会员体系功能 DESCRIPTION:实现会员体系功能，包括会员等级展示、权益介绍、成长值查看、会员升级等
---[ ] NAME:设置页面功能 DESCRIPTION:实现设置页面功能，包括通知设置、隐私设置、关于我们、意见反馈、退出登录等
---[ ] NAME:我的收藏功能 DESCRIPTION:实现我的收藏功能，包括收藏商品列表、取消收藏、批量管理等
--[ ] NAME:第六阶段：支付与优惠券模块开发 DESCRIPTION:实现支付流程、优惠券管理、会员权益等提升转化率的功能
--[ ] NAME:第七阶段：消息与通知模块开发 DESCRIPTION:实现消息中心、推送通知、公告等用户运营功能
--[ ] NAME:第八阶段：测试与质量保证 DESCRIPTION:编写单元测试、集成测试，进行性能优化，确保功能正确性和稳定性