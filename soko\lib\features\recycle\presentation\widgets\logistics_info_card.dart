import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

import 'package:soko/features/recycle/domain/entities/recycle_models.dart';
import 'package:soko/shared/presentation/widgets/status_badge.dart';

/// 物流信息卡片组件
class LogisticsInfoCard extends StatelessWidget {
  const LogisticsInfoCard({
    super.key,
    required this.logistics,
    this.onCopyTrackingNumber,
    this.onOpenOfficialApp,
  });

  final LogisticsInfo logistics;
  final VoidCallback? onCopyTrackingNumber;
  final VoidCallback? onOpenOfficialApp;

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: EdgeInsets.all(16.w),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(12.r),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.05),
            blurRadius: 8,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // 标题和状态
          Row(
            children: [
              Icon(
                Icons.local_shipping,
                size: 20.w,
                color: Theme.of(context).primaryColor,
              ),
              SizedBox(width: 8.w),
              Text(
                '物流信息',
                style: TextStyle(
                  fontSize: 16.sp,
                  fontWeight: FontWeight.w600,
                ),
              ),
              const Spacer(),
              StatusBadge(
                status: logistics.statusDesc,
                type: _getStatusBadgeType(),
              ),
            ],
          ),
          SizedBox(height: 16.h),
          
          // 快递公司
          _buildInfoRow(
            context,
            '快递公司',
            logistics.courierCompany,
            icon: Icons.business,
          ),
          SizedBox(height: 12.h),
          
          // 运单号
          _buildInfoRow(
            context,
            '运单号',
            logistics.trackingNumber,
            icon: Icons.confirmation_number,
            copyable: true,
            onCopy: onCopyTrackingNumber,
          ),
          SizedBox(height: 12.h),
          
          // 预计送达时间
          if (logistics.estimatedDeliveryTime != null) ...[
            _buildInfoRow(
              context,
              '预计送达',
              _formatDateTime(logistics.estimatedDeliveryTime!),
              icon: Icons.schedule,
            ),
            SizedBox(height: 12.h),
          ],
          
          // 实际送达时间
          if (logistics.actualDeliveryTime != null) ...[
            _buildInfoRow(
              context,
              '实际送达',
              _formatDateTime(logistics.actualDeliveryTime!),
              icon: Icons.check_circle,
            ),
            SizedBox(height: 12.h),
          ],
          
          // 操作按钮
          if (onOpenOfficialApp != null) ...[
            SizedBox(height: 8.h),
            SizedBox(
              width: double.infinity,
              child: OutlinedButton.icon(
                onPressed: onOpenOfficialApp,
                icon: Icon(
                  Icons.open_in_new,
                  size: 16.w,
                ),
                label: Text('打开${logistics.courierCompany}应用'),
                style: OutlinedButton.styleFrom(
                  foregroundColor: Theme.of(context).primaryColor,
                  side: BorderSide(color: Theme.of(context).primaryColor),
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(8.r),
                  ),
                ),
              ),
            ),
          ],
        ],
      ),
    );
  }

  /// 构建信息行
  Widget _buildInfoRow(
    BuildContext context,
    String label,
    String value, {
    IconData? icon,
    bool copyable = false,
    VoidCallback? onCopy,
  }) {
    return Row(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        if (icon != null) ...[
          Icon(
            icon,
            size: 16.w,
            color: Colors.grey[600],
          ),
          SizedBox(width: 8.w),
        ],
        SizedBox(
          width: 80.w,
          child: Text(
            label,
            style: TextStyle(
              fontSize: 14.sp,
              color: Colors.grey[600],
            ),
          ),
        ),
        Expanded(
          child: Row(
            children: [
              Expanded(
                child: Text(
                  value,
                  style: TextStyle(
                    fontSize: 14.sp,
                    color: Colors.grey[800],
                    fontWeight: copyable ? FontWeight.w500 : FontWeight.normal,
                  ),
                ),
              ),
              if (copyable && onCopy != null) ...[
                SizedBox(width: 8.w),
                GestureDetector(
                  onTap: onCopy,
                  child: Container(
                    padding: EdgeInsets.all(4.w),
                    decoration: BoxDecoration(
                      color: Theme.of(context).primaryColor.withValues(alpha: 0.1),
                      borderRadius: BorderRadius.circular(4.r),
                    ),
                    child: Icon(
                      Icons.copy,
                      size: 14.w,
                      color: Theme.of(context).primaryColor,
                    ),
                  ),
                ),
              ],
            ],
          ),
        ),
      ],
    );
  }

  /// 获取状态徽章类型
  StatusBadgeType _getStatusBadgeType() {
    if (logistics.isCompleted) {
      return StatusBadgeType.success;
    } else if (logistics.hasException) {
      return StatusBadgeType.error;
    } else if (logistics.isInTransit) {
      return StatusBadgeType.primary;
    } else {
      return StatusBadgeType.warning;
    }
  }

  /// 格式化日期时间
  String _formatDateTime(int timestamp) {
    final dateTime = DateTime.fromMillisecondsSinceEpoch(timestamp);
    return '${dateTime.month.toString().padLeft(2, '0')}-${dateTime.day.toString().padLeft(2, '0')} '
           '${dateTime.hour.toString().padLeft(2, '0')}:${dateTime.minute.toString().padLeft(2, '0')}';
  }
}
