<template>
  <view
    class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium"
    :class="statusClass"
  >
    <view v-if="showIcon" class="w-2 h-2 rounded-full mr-1" :class="dotClass"></view>
    {{ statusText }}
  </view>
</template>

<script setup lang="ts">
import { computed } from 'vue'
import { ORDER_STATUS_MAP, ORDER_STATUS_STYLE_MAP } from '@/constants/recycle'

// Props
interface Props {
  status: string
  showIcon?: boolean
}

const props = withDefaults(defineProps<Props>(), {
  showIcon: false
})

// 计算属性
const statusText = computed(() => {
  return ORDER_STATUS_MAP[props.status as keyof typeof ORDER_STATUS_MAP] || props.status
})

const statusClass = computed(() => {
  return ORDER_STATUS_STYLE_MAP[props.status as keyof typeof ORDER_STATUS_STYLE_MAP] || 'bg-gray-100 text-gray-800'
})

const dotClass = computed(() => {
  const colorMap: Record<string, string> = {
    'PENDING_APPROVAL': 'bg-yellow-500',
    'PRICE_QUOTED': 'bg-blue-500',
    'SHIPPING_CONFIRMED': 'bg-purple-500',
    'RECEIVED': 'bg-indigo-500',
    'COMPLETED': 'bg-green-500',
    'CANCELLED': 'bg-gray-500',
    'RETURNED': 'bg-red-500'
  }
  return colorMap[props.status] || 'bg-gray-500'
})
</script>

<style lang="scss" scoped>
// 自定义样式
</style>
