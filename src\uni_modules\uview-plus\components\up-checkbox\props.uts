import { defineMixin } from '../../libs/vue'
import defProps from './checkbox'
let crtProp = defProps['checkbox'] as UTSJSONObject
export const propsCheckbox = defineMixin({
    props: {
        // checkbox的名称
        name: {
            type: [String, Number, Boolean],
            default: crtProp['name']
        },
        // 形状，square为方形，circle为圆型
        shape: {
            type: String,
            default: crtProp['shape']
        },
        // 整体的大小
        size: {
            type: [String, Number],
            default: crtProp['size']
        },
        // 是否默认选中
        checked: {
            type: Boolean,
            default: crtProp['checked']
        },
        // 是否禁用
        disabled: {
            type: [String, Boolean],
            default: crtProp['disabled']
        },
        // 是否禁止点击提示语选中单选框
        labelDisabled: {
            type: [String, Boolean],
            default: crtProp['labelDisabled']
        },
        // 选中状态下的颜色，如设置此值，将会覆盖parent的activeColor值
        activeColor: {
            type: String,
            default: crtProp['activeColor']
        },
        // 未选中的颜色
        inactiveColor: {
            type: String,
            default: crtProp['inactiveColor']
        },
        // 图标的大小，单位px
        iconSize: {
            type: [String, Number],
            default: crtProp['iconSize']
        },
        // 图标颜色
        iconColor: {
            type: String,
            default: crtProp['iconColor']
        },
        // label提示文字，因为nvue下，直接slot进来的文字，由于特殊的结构，无法修改样式
        label: {
            type: [String, Number],
            default: crtProp['label']
        },
        // label的字体大小，px单位
        labelSize: {
            type: [String, Number],
            default: crtProp['labelSize']
        },
        // label的颜色
        labelColor: {
            type: String,
            default: crtProp['labelColor']
        },
		// 是否独立使用
        usedAlone: {
            type: Boolean,
            default: false
        }
    }
})
