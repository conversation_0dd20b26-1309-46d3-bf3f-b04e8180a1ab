/*
 * <AUTHOR> jry
 * @Description  :
 * @version      : 4.0
 * @Date         : 2024-12-22 19:21:21
 * @LastAuthor   : jry
 * @lastTime     : 2024-12-22 19:21:21
 * @FilePath     : /uview-plus/libs/config/props/badge
 */
export type actionSheetItem = {
  name?: string
  subname?: string
  loading?: boolean
  disabled?: boolean
  color?: string
  fontSize?: string
  openType?: string
}
export default {
    // action-sheet组件
    actionSheet: {
        show: false,
        title: '',
        description: '',
        actions: [] as Array<actionSheetItem>,
        index: '',
        cancelText: '',
        closeOnClickAction: true,
        safeAreaInsetBottom: true,
        openType: '',
        closeOnClickOverlay: true,
        round: 0,
        wrapMaxHeight: '600px'
    }
} as UTSJSONObject
