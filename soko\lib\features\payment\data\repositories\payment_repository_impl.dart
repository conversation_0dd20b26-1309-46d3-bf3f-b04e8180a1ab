import 'package:dartz/dartz.dart' hide Order;
import 'package:flutter_riverpod/flutter_riverpod.dart';

import 'package:soko/core/error/failures.dart';
import 'package:soko/core/network/network_info.dart';
import 'package:soko/features/payment/domain/entities/payment_request.dart';
import 'package:soko/features/payment/domain/repositories/payment_repository.dart';
import 'package:soko/features/payment/data/datasources/payment_api_service.dart';

/// 支付仓库实现
class PaymentRepositoryImpl implements PaymentRepository {

  PaymentRepositoryImpl({
    required this.apiService,
    required this.networkInfo,
  });
  final PaymentApiService apiService;
  final NetworkInfo networkInfo;

  @override
  Future<Either<Failure, PaymentResponse>> createPayment(PaymentRequest request) async {
    if (await networkInfo.isConnected) {
      try {
        final paymentData = request.toJson();
        final response = await apiService.createPayment(paymentData);
        return Right(response);
      } catch (e) {
        return Left(ServerFailure(e.toString()));
      }
    } else {
      return const Left(NetworkFailure('网络连接失败'));
    }
  }

  @override
  Future<Either<Failure, PaymentResponse>> queryPaymentStatus(String paymentOrderNo) async {
    if (await networkInfo.isConnected) {
      try {
        final response = await apiService.queryPaymentStatus(paymentOrderNo);
        return Right(response);
      } catch (e) {
        return Left(ServerFailure(e.toString()));
      }
    } else {
      return const Left(NetworkFailure('网络连接失败'));
    }
  }

  @override
  Future<Either<Failure, bool>> cancelPayment(String paymentOrderNo) async {
    if (await networkInfo.isConnected) {
      try {
        await apiService.cancelPayment(paymentOrderNo);
        return const Right(true);
      } catch (e) {
        return Left(ServerFailure(e.toString()));
      }
    } else {
      return const Left(NetworkFailure('网络连接失败'));
    }
  }

  @override
  Future<Either<Failure, bool>> verifyPaymentCallback(Map<String, dynamic> callbackData) async {
    if (await networkInfo.isConnected) {
      try {
        final result = await apiService.verifyPaymentCallback(callbackData);
        return Right(result['success'] ?? false);
      } catch (e) {
        return Left(ServerFailure(e.toString()));
      }
    } else {
      return const Left(NetworkFailure('网络连接失败'));
    }
  }
}

/// 支付仓库提供者
final paymentRepositoryProvider = Provider<PaymentRepository>((ref) {
  final apiService = ref.read(paymentApiServiceProvider);
  final networkInfo = ref.read(networkInfoProvider);
  return PaymentRepositoryImpl(
    apiService: apiService,
    networkInfo: networkInfo,
  );
});
