import 'package:dio/dio.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:retrofit/retrofit.dart';

import 'package:soko/core/models/paginated_data.dart';
import 'package:soko/core/network/dio_client.dart';
import 'package:soko/features/order/domain/entities/order.dart';

part 'order_api_service.g.dart';

/// 订单API服务
@RestApi()
abstract class OrderApiService {
  factory OrderApiService(Dio dio, {String baseUrl}) = _OrderApiService;

  /// 获取订单列表
  @GET('/orders')
  Future<PaginatedData<Order>> getOrders({
    @Query('page') int? page,
    @Query('pageSize') int? pageSize,
    @Query('status') String? status,
    @Query('startDate') String? startDate,
    @Query('endDate') String? endDate,
  });

  /// 获取订单详情
  @GET('/orders/{orderId}')
  Future<Order> getOrderById(@Path('orderId') String orderId);

  /// 创建订单
  @POST('/orders')
  Future<Order> createOrder(@Body() Map<String, dynamic> orderData);

  /// 更新订单状态
  @PUT('/orders/{orderId}/status')
  Future<Order> updateOrderStatus(
    @Path('orderId') String orderId,
    @Body() Map<String, dynamic> statusData,
  );

  /// 取消订单
  @POST('/orders/{orderId}/cancel')
  Future<void> cancelOrder(@Path('orderId') String orderId);

  /// 确认收货
  @POST('/orders/{orderId}/confirm')
  Future<void> confirmReceived(@Path('orderId') String orderId);

  /// 申请退款
  @POST('/orders/{orderId}/refund')
  Future<void> requestRefund(
    @Path('orderId') String orderId,
    @Body() Map<String, dynamic> refundData,
  );

  /// 删除订单
  @DELETE('/orders/{orderId}')
  Future<void> deleteOrder(@Path('orderId') String orderId);
}

/// 订单API服务提供者
final orderApiServiceProvider = Provider<OrderApiService>((ref) {
  final dio = ref.read(dioClientProvider);
  return OrderApiService(dio);
});
