<template>
  <view class="min-h-screen bg-gray-50">
    <!-- 自定义导航栏 -->
    <StatusBarPlaceholder />
    <NavBar title="订单详情" />

    <view v-if="loading" class="text-center py-8">
      <text class="text-gray-500">加载中...</text>
    </view>

    <view v-else-if="!orderDetail" class="text-center py-16">
      <text class="text-gray-500">订单不存在</text>
    </view>

    <view v-else class="pb-20">
      <!-- 订单状态 -->
      <view class="bg-white px-4 py-6 mb-2">
        <view class="flex items-center justify-between mb-4">
          <text class="text-lg font-semibold text-gray-800">订单状态</text>
          <view
            class="px-3 py-1 rounded-full text-sm font-medium"
            :class="getStatusClass(orderDetail.orderStatus)"
          >
            {{ orderDetail.orderStatusDesc }}
          </view>
        </view>
        
        <!-- 订单时间线 -->
        <view class="space-y-3">
          <view
            class="flex items-start space-x-3"
            v-for="(timeline, index) in orderTimeline"
            :key="index"
          >
            <view
              class="w-3 h-3 rounded-full mt-1 flex-shrink-0"
              :class="timeline.completed ? 'bg-primary-500' : 'bg-gray-300'"
            ></view>
            <view class="flex-1">
              <text class="text-sm font-medium" :class="timeline.completed ? 'text-gray-800' : 'text-gray-500'">
                {{ timeline.title }}
              </text>
              <text v-if="timeline.time" class="text-xs text-gray-500 block">
                {{ formatTime(timeline.time) }}
              </text>
            </view>
          </view>
        </view>
      </view>

      <!-- 商品信息 -->
      <view class="bg-white px-4 py-6 mb-2">
        <text class="text-lg font-semibold text-gray-800 mb-4 block">商品信息</text>
        
        <view class="flex items-start space-x-4 mb-4">
          <image
            :src="orderDetail.mainImage || '/static/default-product.png'"
            class="w-20 h-20 rounded-lg object-cover"
            mode="aspectFill"
          />
          <view class="flex-1">
            <text class="text-base font-medium text-gray-800 block">
              {{ orderDetail.brandName }} {{ orderDetail.model }}
            </text>
            <text class="text-sm text-gray-600 mt-1 block">
              {{ orderDetail.categoryName }}
            </text>
            <text class="text-sm text-gray-600 mt-1 block">
              状况：{{ getConditionLabel(orderDetail.conditionDescription) }}
            </text>
          </view>
        </view>

        <view v-if="orderDetail.productDesc" class="mb-4">
          <text class="text-sm font-medium text-gray-700 mb-2 block">商品描述</text>
          <text class="text-sm text-gray-600">{{ orderDetail.productDesc }}</text>
        </view>

        <!-- 商品图片 -->
        <view v-if="orderDetail.files && orderDetail.files.length > 0" class="mb-4">
          <text class="text-sm font-medium text-gray-700 mb-2 block">商品图片</text>
          <view class="grid grid-cols-4 gap-2">
            <image
              v-for="file in orderDetail.files"
              :key="file.id"
              :src="file.thumbnailUrl || file.url"
              class="aspect-square rounded-lg object-cover"
              mode="aspectFill"
              @click="previewImages(file.url)"
            />
          </view>
        </view>
      </view>

      <!-- 价格信息 -->
      <view class="bg-white px-4 py-6 mb-2">
        <text class="text-lg font-semibold text-gray-800 mb-4 block">价格信息</text>
        
        <view class="space-y-3">
          <view class="flex justify-between items-center">
            <text class="text-sm text-gray-600">期望价格</text>
            <text class="text-sm font-medium text-gray-800">¥{{ orderDetail.estimatedPrice }}</text>
          </view>
          
          <view v-if="orderDetail.reviewedPrice" class="flex justify-between items-center">
            <text class="text-sm text-gray-600">评估价格</text>
            <text class="text-sm font-medium text-primary-600">¥{{ orderDetail.reviewedPrice }}</text>
          </view>
          
          <view v-if="orderDetail.finalPrice" class="flex justify-between items-center pt-2 border-t border-gray-200">
            <text class="text-base font-medium text-gray-800">最终价格</text>
            <text class="text-lg font-semibold text-green-600">¥{{ orderDetail.finalPrice }}</text>
          </view>
        </view>
      </view>

      <!-- 联系信息 -->
      <view class="bg-white px-4 py-6 mb-2">
        <text class="text-lg font-semibold text-gray-800 mb-4 block">联系信息</text>
        
        <view class="space-y-3">
          <view class="flex justify-between items-center">
            <text class="text-sm text-gray-600">联系人</text>
            <text class="text-sm font-medium text-gray-800">{{ orderDetail.contactPerson || '未填写' }}</text>
          </view>
          
          <view class="flex justify-between items-center">
            <text class="text-sm text-gray-600">联系电话</text>
            <text class="text-sm font-medium text-gray-800">{{ orderDetail.userPhone || '未填写' }}</text>
          </view>
        </view>
      </view>

      <!-- 快递信息 -->
      <view v-if="orderDetail.shippingInfo" class="bg-white px-4 py-6 mb-2">
        <text class="text-lg font-semibold text-gray-800 mb-4 block">快递信息</text>
        
        <view class="space-y-3">
          <view class="flex justify-between items-center">
            <text class="text-sm text-gray-600">快递公司</text>
            <text class="text-sm font-medium text-gray-800">{{ shippingData.courierCompany }}</text>
          </view>
          
          <view class="flex justify-between items-center">
            <text class="text-sm text-gray-600">快递单号</text>
            <text class="text-sm font-medium text-primary-600">{{ shippingData.trackingNumber }}</text>
          </view>
          
          <view class="flex justify-between items-center">
            <text class="text-sm text-gray-600">寄件人</text>
            <text class="text-sm font-medium text-gray-800">{{ shippingData.senderName }}</text>
          </view>
        </view>
      </view>

      <!-- 订单信息 -->
      <view class="bg-white px-4 py-6 mb-2">
        <text class="text-lg font-semibold text-gray-800 mb-4 block">订单信息</text>
        
        <view class="space-y-3">
          <view class="flex justify-between items-center">
            <text class="text-sm text-gray-600">订单编号</text>
            <text class="text-sm font-medium text-gray-800">{{ orderDetail.id }}</text>
          </view>
          
          <view class="flex justify-between items-center">
            <text class="text-sm text-gray-600">创建时间</text>
            <text class="text-sm font-medium text-gray-800">{{ formatTime(orderDetail.createTime) }}</text>
          </view>
          
          <view class="flex justify-between items-center">
            <text class="text-sm text-gray-600">更新时间</text>
            <text class="text-sm font-medium text-gray-800">{{ formatTime(orderDetail.updateTime) }}</text>
          </view>
        </view>
      </view>

      <!-- 操作按钮 -->
      <view v-if="availableActions.length > 0" class="fixed bottom-0 left-0 right-0 bg-white border-t border-gray-200 p-4">
        <view v-if="availableActions.length === 1" class="w-full">
          <button
            class="w-full py-3 rounded-lg text-base font-medium transition-colors"
            :class="getActionClass(availableActions[0].type)"
            @click="handleAction(availableActions[0].key)"
          >
            {{ availableActions[0].label }}
          </button>
        </view>
        
        <view v-else class="flex space-x-3">
          <button
            v-for="action in availableActions"
            :key="action.key"
            class="flex-1 py-3 rounded-lg text-base font-medium transition-colors"
            :class="getActionClass(action.type)"
            @click="handleAction(action.key)"
          >
            {{ action.label }}
          </button>
        </view>
      </view>
    </view>
  </view>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from 'vue'
import StatusBarPlaceholder from '@/components/StatusBarPlaceholder.vue'
import NavBar from '@/components/NavBar.vue'
import { recycleApi, recycleUtils } from '@/api/recycle'
import type { RecycleOrder } from '@/api/recycle'

// 类型定义
interface OrderDetail {
  id: string
  userId: string
  userPhone?: string
  brandName: string
  model: string
  categoryName: string
  productDesc?: string
  conditionDescription: string
  estimatedPrice: number
  finalPrice?: number
  orderStatus: string
  orderStatusDesc: string
  reviewedPrice?: number
  shippingInfo?: string
  contactPerson?: string
  createTime: number
  updateTime: number
  mainImage?: string
  files?: Array<{
    id: string
    url: string
    thumbnailUrl: string
  }>
}

interface OrderAction {
  key: string
  label: string
  type: 'primary' | 'danger' | 'warning' | 'default'
}

interface TimelineItem {
  title: string
  time?: number
  completed: boolean
}

// 响应式数据
const orderDetail = ref<OrderDetail | null>(null)
const loading = ref(false)

// 计算属性
const shippingData = computed(() => {
  if (!orderDetail.value?.shippingInfo) return {}
  try {
    return JSON.parse(orderDetail.value.shippingInfo)
  } catch {
    return {}
  }
})

const orderTimeline = computed((): TimelineItem[] => {
  if (!orderDetail.value) return []
  
  const status = orderDetail.value.orderStatus
  const createTime = orderDetail.value.createTime
  const updateTime = orderDetail.value.updateTime
  
  return [
    { title: '提交申请', time: createTime, completed: true },
    { title: '等待审核', completed: ['PENDING_APPROVAL', 'PRICE_QUOTED', 'SHIPPING_CONFIRMED', 'RECEIVED', 'COMPLETED'].includes(status) },
    { title: '评估报价', time: status !== 'PENDING_APPROVAL' ? updateTime : undefined, completed: ['PRICE_QUOTED', 'SHIPPING_CONFIRMED', 'RECEIVED', 'COMPLETED'].includes(status) },
    { title: '确认寄送', time: status === 'SHIPPING_CONFIRMED' ? updateTime : undefined, completed: ['SHIPPING_CONFIRMED', 'RECEIVED', 'COMPLETED'].includes(status) },
    { title: '商家收货', time: status === 'RECEIVED' ? updateTime : undefined, completed: ['RECEIVED', 'COMPLETED'].includes(status) },
    { title: '交易完成', time: status === 'COMPLETED' ? updateTime : undefined, completed: status === 'COMPLETED' }
  ]
})

const availableActions = computed((): OrderAction[] => {
  if (!orderDetail.value) return []
  
  const status = orderDetail.value.orderStatus
  const actionsMap: Record<string, OrderAction[]> = {
    'PENDING_APPROVAL': [
      { key: 'cancel', label: '取消订单', type: 'danger' }
    ],
    'PRICE_QUOTED': [
      { key: 'cancel', label: '取消订单', type: 'danger' },
      { key: 'confirm_shipment', label: '确认寄送', type: 'primary' }
    ],
    'RECEIVED': [
      { key: 'request_return', label: '申请退回', type: 'warning' }
    ]
  }
  return actionsMap[status] || []
})

// 方法
const fetchOrderDetail = async () => {
  const pages = getCurrentPages()
  const currentPage = pages[pages.length - 1]
  const orderId = currentPage.options?.id
  
  if (!orderId) {
    uni.showToast({
      title: '订单ID不存在',
      icon: 'none'
    })
    return
  }

  loading.value = true

  try {
    // TODO: 调用API获取订单详情
    // const res = await getRecycleOrderDetail(orderId)
    // orderDetail.value = res.data
    
    // 模拟数据
    orderDetail.value = {
      id: orderId,
      userId: 'user123',
      userPhone: '13800138000',
      brandName: 'iPhone',
      model: '14 Pro Max',
      categoryName: '手机数码',
      productDesc: '256GB 深空黑色，9成新，功能完好，无维修记录',
      conditionDescription: 'good',
      estimatedPrice: 6000,
      reviewedPrice: 5800,
      finalPrice: 5800,
      orderStatus: 'PRICE_QUOTED',
      orderStatusDesc: '已审核报价',
      contactPerson: '张三',
      createTime: Date.now() - 86400000,
      updateTime: Date.now() - 3600000,
      mainImage: '/static/iphone.jpg',
      files: [
        { id: '1', url: '/static/iphone1.jpg', thumbnailUrl: '/static/iphone1_thumb.jpg' },
        { id: '2', url: '/static/iphone2.jpg', thumbnailUrl: '/static/iphone2_thumb.jpg' }
      ]
    }
  } catch (error) {
    uni.showToast({
      title: '加载失败',
      icon: 'none'
    })
  } finally {
    loading.value = false
  }
}

const getStatusClass = (status: string): string => {
  const statusMap: Record<string, string> = {
    'PENDING_APPROVAL': 'bg-yellow-100 text-yellow-800',
    'PRICE_QUOTED': 'bg-blue-100 text-blue-800',
    'SHIPPING_CONFIRMED': 'bg-purple-100 text-purple-800',
    'RECEIVED': 'bg-indigo-100 text-indigo-800',
    'COMPLETED': 'bg-green-100 text-green-800',
    'CANCELLED': 'bg-gray-100 text-gray-800',
    'RETURNED': 'bg-red-100 text-red-800'
  }
  return statusMap[status] || 'bg-gray-100 text-gray-800'
}

const getConditionLabel = (condition: string): string => {
  const conditionMap: Record<string, string> = {
    'excellent': '成色极佳',
    'good': '成色良好',
    'fair': '成色一般',
    'poor': '成色较差'
  }
  return conditionMap[condition] || condition
}

const getActionClass = (type: string): string => {
  const classMap: Record<string, string> = {
    'primary': 'bg-primary-500 text-white active:bg-primary-600',
    'danger': 'bg-red-500 text-white active:bg-red-600',
    'warning': 'bg-yellow-500 text-white active:bg-yellow-600',
    'default': 'bg-gray-200 text-gray-800 active:bg-gray-300'
  }
  return classMap[type] || classMap.default
}

const previewImages = (currentUrl: string) => {
  if (!orderDetail.value?.files) return
  
  const urls = orderDetail.value.files.map(file => file.url)
  const current = urls.indexOf(currentUrl)
  
  uni.previewImage({
    current: current >= 0 ? current : 0,
    urls: urls
  })
}

const handleAction = async (action: string) => {
  if (!orderDetail.value) return
  
  switch (action) {
    case 'cancel':
      await cancelOrder()
      break
    case 'confirm_shipment':
      goToShippingInfo()
      break
    case 'request_return':
      await requestReturn()
      break
  }
}

const cancelOrder = async () => {
  try {
    const res = await uni.showModal({
      title: '确认取消',
      content: '确定要取消这个回收订单吗？'
    })

    if (res.confirm) {
      // TODO: 调用取消订单API
      uni.showToast({
        title: '订单已取消',
        icon: 'success'
      })
      
      // 更新订单状态
      if (orderDetail.value) {
        orderDetail.value.orderStatus = 'CANCELLED'
        orderDetail.value.orderStatusDesc = '已取消'
        orderDetail.value.updateTime = Date.now()
      }
    }
  } catch (error) {
    uni.showToast({
      title: '取消失败',
      icon: 'none'
    })
  }
}

const goToShippingInfo = () => {
  uni.navigateTo({
    url: `/pages/views/recycle/shipping-info?orderId=${orderDetail.value?.id}`
  })
}

const requestReturn = async () => {
  try {
    const res = await uni.showModal({
      title: '申请退回',
      content: '确定要申请退回商品吗？'
    })

    if (res.confirm) {
      // TODO: 调用申请退回API
      uni.showToast({
        title: '申请成功',
        icon: 'success'
      })
      
      // 更新订单状态
      if (orderDetail.value) {
        orderDetail.value.orderStatus = 'RETURN_REQUESTED'
        orderDetail.value.orderStatusDesc = '申请退回'
        orderDetail.value.updateTime = Date.now()
      }
    }
  } catch (error) {
    uni.showToast({
      title: '申请失败',
      icon: 'none'
    })
  }
}

const formatTime = (timestamp: number): string => {
  const date = new Date(timestamp)
  return date.toLocaleString('zh-CN', {
    year: 'numeric',
    month: '2-digit',
    day: '2-digit',
    hour: '2-digit',
    minute: '2-digit'
  })
}

// 生命周期
onMounted(() => {
  fetchOrderDetail()
})
</script>

<style lang="scss" scoped>
// 自定义样式
</style>
