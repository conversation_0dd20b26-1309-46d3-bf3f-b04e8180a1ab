import 'package:json_annotation/json_annotation.dart';

part 'coupon.g.dart';

/// 优惠券类型枚举
enum CouponType {
  @JsonValue('DISCOUNT')
  discount, // 折扣券
  
  @JsonValue('AMOUNT')
  amount, // 满减券
  
  @JsonValue('SHIPPING')
  shipping, // 包邮券
}

/// 优惠券状态枚举
enum CouponStatus {
  @JsonValue('AVAILABLE')
  available, // 可用
  
  @JsonValue('USED')
  used, // 已使用
  
  @JsonValue('EXPIRED')
  expired, // 已过期
  
  @JsonValue('DISABLED')
  disabled, // 已禁用
}

/// 优惠券实体类
@JsonSerializable()
class Coupon { // 排除商品

  const Coupon({
    required this.id,
    required this.name,
    this.description,
    required this.type,
    required this.value,
    this.minAmount,
    this.maxDiscount,
    required this.validFrom,
    required this.validTo,
    required this.status,
    this.usageLimit,
    required this.usedCount,
    this.categoryIds,
    this.productIds,
    this.excludeProductIds,
  });

  factory Coupon.fromJson(Map<String, dynamic> json) => _$CouponFromJson(json);
  @JsonKey(name: 'id')
  final String id;

  @JsonKey(name: 'name')
  final String name;

  @JsonKey(name: 'description')
  final String? description;

  @JsonKey(name: 'type')
  final CouponType type;

  @JsonKey(name: 'value')
  final double value; // 折扣值或减免金额

  @JsonKey(name: 'minAmount')
  final double? minAmount; // 最低消费金额

  @JsonKey(name: 'maxDiscount')
  final double? maxDiscount; // 最大优惠金额（折扣券用）

  @JsonKey(name: 'validFrom')
  final int validFrom;

  @JsonKey(name: 'validTo')
  final int validTo;

  @JsonKey(name: 'status')
  final CouponStatus status;

  @JsonKey(name: 'usageLimit')
  final int? usageLimit; // 使用次数限制

  @JsonKey(name: 'usedCount')
  final int usedCount; // 已使用次数

  @JsonKey(name: 'categoryIds')
  final List<String>? categoryIds; // 适用分类

  @JsonKey(name: 'productIds')
  final List<String>? productIds; // 适用商品

  @JsonKey(name: 'excludeProductIds')
  final List<String>? excludeProductIds;

  Map<String, dynamic> toJson() => _$CouponToJson(this);

  /// 是否可用
  bool get isAvailable {
    if (status != CouponStatus.available) return false;
    
    final now = DateTime.now().millisecondsSinceEpoch;
    if (now < validFrom || now > validTo) return false;
    
    if (usageLimit != null && usedCount >= usageLimit!) return false;
    
    return true;
  }

  /// 是否已过期
  bool get isExpired {
    final now = DateTime.now().millisecondsSinceEpoch;
    return now > validTo;
  }

  /// 获取优惠券显示文本
  String get displayText {
    switch (type) {
      case CouponType.discount:
        final discount = (value * 10).toInt();
        return '$discount折';
      case CouponType.amount:
        return '减¥${value.toStringAsFixed(0)}';
      case CouponType.shipping:
        return '包邮';
    }
  }

  /// 获取使用条件文本
  String get conditionText {
    if (minAmount != null && minAmount! > 0) {
      return '满¥${minAmount!.toStringAsFixed(0)}可用';
    }
    return '无门槛';
  }

  /// 计算优惠金额
  double calculateDiscount(double amount) {
    if (!isAvailable) return 0;
    if (minAmount != null && amount < minAmount!) return 0;

    switch (type) {
      case CouponType.discount:
        final discount = amount * (1 - value);
        if (maxDiscount != null) {
          return discount > maxDiscount! ? maxDiscount! : discount;
        }
        return discount;
      case CouponType.amount:
        return amount >= value ? value : 0.0;
      case CouponType.shipping:
        return 0; // 包邮券不直接减免金额
    }
  }

  /// 是否适用于指定商品
  bool isApplicableToProduct(String productId, String? categoryId) {
    // 检查排除商品
    if (excludeProductIds?.contains(productId) == true) {
      return false;
    }

    // 检查指定商品
    if (productIds != null && productIds!.isNotEmpty) {
      return productIds!.contains(productId);
    }

    // 检查指定分类
    if (categoryIds != null && categoryIds!.isNotEmpty && categoryId != null) {
      return categoryIds!.contains(categoryId);
    }

    // 无限制则适用于所有商品
    return productIds == null && categoryIds == null;
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is Coupon && other.id == id;
  }

  @override
  int get hashCode => id.hashCode;

  @override
  String toString() {
    return 'Coupon(id: $id, name: $name, type: $type, value: $value)';
  }
}

/// 会员等级枚举
enum MemberLevel {
  @JsonValue('BRONZE')
  bronze, // 青铜会员
  
  @JsonValue('SILVER')
  silver, // 白银会员
  
  @JsonValue('GOLD')
  gold, // 黄金会员
  
  @JsonValue('PLATINUM')
  platinum, // 铂金会员
  
  @JsonValue('DIAMOND')
  diamond, // 钻石会员
}

/// 会员折扣信息
@JsonSerializable()
class MemberDiscount { // 积分倍率

  const MemberDiscount({
    required this.level,
    required this.discountRate,
    this.freeShippingThreshold,
    required this.pointsMultiplier,
  });

  factory MemberDiscount.fromJson(Map<String, dynamic> json) => 
      _$MemberDiscountFromJson(json);
  @JsonKey(name: 'level')
  final MemberLevel level;

  @JsonKey(name: 'discountRate')
  final double discountRate; // 折扣率 (0.9 表示9折)

  @JsonKey(name: 'freeShippingThreshold')
  final double? freeShippingThreshold; // 包邮门槛

  @JsonKey(name: 'pointsMultiplier')
  final double pointsMultiplier;

  Map<String, dynamic> toJson() => _$MemberDiscountToJson(this);

  /// 获取会员等级名称
  String get levelName {
    switch (level) {
      case MemberLevel.bronze:
        return '青铜会员';
      case MemberLevel.silver:
        return '白银会员';
      case MemberLevel.gold:
        return '黄金会员';
      case MemberLevel.platinum:
        return '铂金会员';
      case MemberLevel.diamond:
        return '钻石会员';
    }
  }

  /// 获取折扣显示文本
  String get discountText {
    final discount = (discountRate * 10).toInt();
    return '$discount折';
  }

  /// 计算会员折扣金额
  double calculateDiscount(double amount) {
    return amount * (1 - discountRate);
  }

  /// 是否享受包邮
  bool isFreeShipping(double amount) {
    if (freeShippingThreshold == null) return false;
    return amount >= freeShippingThreshold!;
  }
}

/// 价格计算结果
class PriceCalculation { // 会员信息

  const PriceCalculation({
    required this.originalAmount,
    required this.productAmount,
    required this.memberDiscount,
    required this.couponDiscount,
    required this.shippingFee,
    required this.finalAmount,
    this.appliedCoupon,
    this.memberInfo,
  });
  final double originalAmount; // 原价总额
  final double productAmount; // 商品金额
  final double memberDiscount; // 会员折扣
  final double couponDiscount; // 优惠券折扣
  final double shippingFee; // 运费
  final double finalAmount; // 最终金额
  final Coupon? appliedCoupon; // 应用的优惠券
  final MemberDiscount? memberInfo;

  /// 总优惠金额
  double get totalDiscount => memberDiscount + couponDiscount;

  /// 是否有优惠
  bool get hasDiscount => totalDiscount > 0;

  /// 是否包邮
  bool get isFreeShipping => shippingFee == 0;

  @override
  String toString() {
    return 'PriceCalculation(originalAmount: $originalAmount, finalAmount: $finalAmount, totalDiscount: $totalDiscount)';
  }
}
