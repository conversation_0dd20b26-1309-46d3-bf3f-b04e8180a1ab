<template>
  <view class="image-uploader">
    <text v-if="title" class="text-base font-semibold text-gray-800 mb-2 block">{{ title }}</text>
    <text v-if="subtitle" class="text-sm text-gray-500 mb-4 block">{{ subtitle }}</text>
    
    <view class="grid grid-cols-3 gap-3">
      <!-- 已上传的图片 -->
      <view 
        class="aspect-square bg-gray-100 rounded-lg relative overflow-hidden" 
        v-for="(image, index) in imageList" 
        :key="index"
      >
        <image 
          :src="image.url" 
          class="w-full h-full object-cover" 
          @click="previewImage(index)"
        />
        <view 
          class="absolute top-1 right-1 w-6 h-6 bg-red-500 rounded-full flex items-center justify-center text-white text-xs active:bg-red-600"
          @click="deleteImage(index)"
        >
          ×
        </view>
        
        <!-- 上传进度 -->
        <view 
          v-if="image.uploading" 
          class="absolute inset-0 bg-black bg-opacity-50 flex items-center justify-center"
        >
          <view class="text-white text-xs">{{ image.progress || 0 }}%</view>
        </view>
      </view>
      
      <!-- 添加图片按钮 -->
      <view 
        class="aspect-square bg-gray-100 rounded-lg flex flex-col items-center justify-center border-2 border-dashed border-gray-300 active:bg-gray-200"
        v-if="imageList.length < maxCount"
        @click="chooseImage"
      >
        <text class="text-2xl text-gray-400 mb-1">+</text>
        <text class="text-xs text-gray-400">添加图片</text>
      </view>
    </view>
    
    <!-- 提示信息 -->
    <view v-if="tips.length > 0" class="mt-4 space-y-1">
      <text 
        v-for="(tip, index) in tips" 
        :key="index"
        class="text-xs text-gray-500 block"
      >
        • {{ tip }}
      </text>
    </view>
  </view>
</template>

<script setup lang="ts">
import { ref, watch } from 'vue'
import { IMAGE_UPLOAD_CONFIG } from '@/constants/recycle'

// 类型定义
interface ImageItem {
  fileId: string
  url: string
  thumbnailUrl: string
  uploading?: boolean
  progress?: number
}

// Props
interface Props {
  modelValue: ImageItem[]
  maxCount?: number
  title?: string
  subtitle?: string
  tips?: string[]
}

const props = withDefaults(defineProps<Props>(), {
  maxCount: IMAGE_UPLOAD_CONFIG.maxCount,
  tips: () => [
    '图片清晰，光线充足',
    '包含商品正面、背面、接口等关键部位',
    '如有包装盒、配件请一并拍摄'
  ]
})

// Emits
const emit = defineEmits<{
  'update:modelValue': [value: ImageItem[]]
  'upload-success': [image: ImageItem]
  'upload-error': [error: any]
}>()

// 响应式数据
const imageList = ref<ImageItem[]>(props.modelValue)

// 方法
const chooseImage = async () => {
  try {
    const res = await uni.chooseImage({
      count: props.maxCount - imageList.value.length,
      sizeType: ['compressed'],
      sourceType: ['album', 'camera']
    })

    // 批量上传图片
    for (const filePath of res.tempFilePaths) {
      await uploadImage(filePath)
    }
  } catch (error) {
    uni.showToast({
      title: '选择图片失败',
      icon: 'none'
    })
  }
}

const uploadImage = async (filePath: string): Promise<void> => {
  // 验证图片大小
  try {
    const fileInfo = await uni.getFileInfo({ filePath })
    if (fileInfo.size > IMAGE_UPLOAD_CONFIG.maxSize) {
      uni.showToast({
        title: '图片大小不能超过5MB',
        icon: 'none'
      })
      return
    }
  } catch (error) {
    console.warn('获取文件信息失败:', error)
  }

  // 压缩图片
  const compressedPath = await compressImage(filePath)
  
  // 创建临时图片项
  const tempImage: ImageItem = {
    fileId: Date.now().toString(),
    url: compressedPath,
    thumbnailUrl: compressedPath,
    uploading: true,
    progress: 0
  }
  
  imageList.value.push(tempImage)
  updateModelValue()

  try {
    // 模拟上传进度
    const progressInterval = setInterval(() => {
      if (tempImage.progress! < 90) {
        tempImage.progress! += Math.random() * 20
      }
    }, 200)

    // TODO: 调用实际的上传API
    // const result = await recycleApi.uploadImage(compressedPath)
    
    // 模拟上传成功
    await new Promise(resolve => setTimeout(resolve, 2000))
    
    clearInterval(progressInterval)
    
    // 更新图片信息
    tempImage.uploading = false
    tempImage.progress = 100
    // tempImage.fileId = result.data.id
    // tempImage.url = result.data.url
    // tempImage.thumbnailUrl = result.data.thumbnailUrl
    
    emit('upload-success', tempImage)
    updateModelValue()
    
  } catch (error) {
    // 上传失败，移除临时图片
    const index = imageList.value.indexOf(tempImage)
    if (index > -1) {
      imageList.value.splice(index, 1)
      updateModelValue()
    }
    
    emit('upload-error', error)
    uni.showToast({
      title: '上传失败',
      icon: 'none'
    })
  }
}

const compressImage = async (filePath: string): Promise<string> => {
  return new Promise((resolve) => {
    uni.compressImage({
      src: filePath,
      quality: IMAGE_UPLOAD_CONFIG.compressQuality * 100,
      success: (res) => {
        resolve(res.tempFilePath)
      },
      fail: () => {
        // 如果压缩失败，使用原图片
        resolve(filePath)
      }
    })
  })
}

const deleteImage = (index: number) => {
  imageList.value.splice(index, 1)
  updateModelValue()
}

const previewImage = (index: number) => {
  const urls = imageList.value.map(img => img.url)
  uni.previewImage({
    current: index,
    urls: urls
  })
}

const updateModelValue = () => {
  emit('update:modelValue', [...imageList.value])
}

// 监听props变化
watch(() => props.modelValue, (newValue) => {
  imageList.value = newValue
}, { deep: true })
</script>

<style lang="scss" scoped>
.image-uploader {
  // 自定义样式
}
</style>
