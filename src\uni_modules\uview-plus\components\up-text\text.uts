/*
 * <AUTHOR> jry,jry
 * @Description  :
 * @version      : 3.0
 * @Date         : 2024-04-22 16:44:21
 * @LastAuthor   : jry
 * @lastTime     : 2024-08-20 14:20:58
 * @FilePath     : /uview-plus/libs/config/props/text.js
 */
export default {
    // text 组件
    text: {
        type: '',
        show: true,
        text: '',
        prefixIcon: '',
        suffixIcon: '',
        mode: '',
        href: '',
        format: '',
        call: false,
        openType: '',
        bold: false,
        block: false,
        lines: '',
        color: '#303133',
        size: '15px',
        iconStyle: {
            fontSize: '15px'
        },
        decoration: 'none',
        margin: '0',
        lineHeight: '',
        align: 'left',
        wordWrap: 'normal',
        lang: 'en',
        sessionFrom: '',
        sendMessageTitle: '',
        sendMessagePath: '',
        sendMessageImg: '',
        showMessageCard: false,
        appParameter: '',
    }
} as UTSJSONObject
