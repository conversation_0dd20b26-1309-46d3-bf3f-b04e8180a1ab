<template>
  <view class="min-h-screen bg-gray-50">
    <!-- 自定义导航栏 -->
    <StatusBarPlaceholder />
    <NavBar title="快递信息" />

    <form @submit.prevent="submitShippingInfo" class="pb-20">
      <!-- 温馨提示 -->
      <view class="bg-blue-50 border border-blue-200 rounded-lg mx-4 mt-4 p-4">
        <view class="flex items-start space-x-2">
          <text class="text-blue-500 text-lg">ℹ️</text>
          <view class="flex-1">
            <text class="text-blue-800 text-sm font-medium block mb-1">寄送须知</text>
            <text class="text-blue-700 text-xs leading-relaxed">
              请确保商品包装完好，建议使用原包装盒。寄送前请备份重要数据并恢复出厂设置。
            </text>
          </view>
        </view>
      </view>

      <!-- 快递公司选择 -->
      <view class="bg-white mt-4 px-4 py-6">
        <text class="text-base font-semibold text-gray-800 mb-4 block">快递信息</text>

        <view class="mb-6">
          <text class="text-sm font-medium text-gray-700 mb-2 block">
            快递公司 <text class="text-red-500">*</text>
          </text>
          <picker
            :value="courierIndex"
            :range="courierList"
            range-key="name"
            @change="onCourierChange"
          >
            <view class="w-full px-3 py-3 border border-gray-300 rounded-lg bg-white flex justify-between items-center">
              <text class="text-base" :class="formData.courierCompany ? 'text-gray-800' : 'text-gray-400'">
                {{ formData.courierCompany || '请选择快递公司' }}
              </text>
              <text class="text-gray-400">></text>
            </view>
          </picker>
        </view>

        <view class="mb-6">
          <text class="text-sm font-medium text-gray-700 mb-2 block">
            快递单号 <text class="text-red-500">*</text>
          </text>
          <input
            v-model="formData.trackingNumber"
            placeholder="请输入快递单号"
            class="w-full px-3 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-primary-500 text-base"
          />
          <text class="text-xs text-gray-500 mt-1 block">请确保单号准确无误，便于跟踪物流</text>
        </view>
      </view>

      <!-- 寄件人信息 -->
      <view class="bg-white mt-2 px-4 py-6">
        <text class="text-base font-semibold text-gray-800 mb-4 block">寄件人信息</text>

        <view class="mb-6">
          <text class="text-sm font-medium text-gray-700 mb-2 block">
            寄件人姓名 <text class="text-red-500">*</text>
          </text>
          <input
            v-model="formData.senderName"
            placeholder="请输入寄件人姓名"
            class="w-full px-3 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-primary-500 text-base"
          />
        </view>

        <view class="mb-6">
          <text class="text-sm font-medium text-gray-700 mb-2 block">
            寄件人电话 <text class="text-red-500">*</text>
          </text>
          <input
            v-model="formData.senderPhone"
            type="number"
            placeholder="请输入寄件人电话"
            class="w-full px-3 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-primary-500 text-base"
          />
        </view>

        <view class="mb-6">
          <text class="text-sm font-medium text-gray-700 mb-2 block">
            寄件地址 <text class="text-red-500">*</text>
          </text>
          <textarea
            v-model="formData.senderAddress"
            placeholder="请输入详细的寄件地址"
            class="w-full px-3 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-primary-500 text-base resize-none"
            :maxlength="200"
            auto-height
          />
          <text class="text-xs text-gray-500 mt-1 block text-right">
            {{ formData.senderAddress.length }}/200
          </text>
        </view>
      </view>

      <!-- 收件地址 -->
      <view class="bg-white mt-2 px-4 py-6">
        <text class="text-base font-semibold text-gray-800 mb-4 block">收件地址</text>
        
        <view class="bg-gray-50 rounded-lg p-4">
          <text class="text-sm font-medium text-gray-800 block mb-2">中古虾回收中心</text>
          <text class="text-sm text-gray-600 block mb-1">收件人：客服中心</text>
          <text class="text-sm text-gray-600 block mb-1">电话：400-123-4567</text>
          <text class="text-sm text-gray-600 block">
            地址：上海市浦东新区张江高科技园区科苑路123号中古虾大厦A座10楼
          </text>
        </view>
        
        <view class="mt-3 p-3 bg-yellow-50 border border-yellow-200 rounded-lg">
          <text class="text-yellow-800 text-xs">
            💡 建议使用顺丰快递，时效更快，安全性更高
          </text>
        </view>
      </view>

      <!-- 注意事项 -->
      <view class="bg-white mt-2 px-4 py-6">
        <text class="text-base font-semibold text-gray-800 mb-4 block">寄送注意事项</text>
        
        <view class="space-y-3">
          <view class="flex items-start space-x-2">
            <text class="text-primary-500 text-sm mt-0.5">•</text>
            <text class="text-sm text-gray-600 flex-1">请使用原包装盒或气泡膜等防护材料包装商品</text>
          </view>
          
          <view class="flex items-start space-x-2">
            <text class="text-primary-500 text-sm mt-0.5">•</text>
            <text class="text-sm text-gray-600 flex-1">寄送前请备份重要数据并恢复出厂设置</text>
          </view>
          
          <view class="flex items-start space-x-2">
            <text class="text-primary-500 text-sm mt-0.5">•</text>
            <text class="text-sm text-gray-600 flex-1">建议选择保价服务，确保商品安全</text>
          </view>
          
          <view class="flex items-start space-x-2">
            <text class="text-primary-500 text-sm mt-0.5">•</text>
            <text class="text-sm text-gray-600 flex-1">请保留快递单据，便于查询物流信息</text>
          </view>
          
          <view class="flex items-start space-x-2">
            <text class="text-primary-500 text-sm mt-0.5">•</text>
            <text class="text-sm text-gray-600 flex-1">如有疑问，请联系客服：400-123-4567</text>
          </view>
        </view>
      </view>

      <!-- 提交按钮 -->
      <view class="fixed bottom-0 left-0 right-0 bg-white border-t border-gray-200 p-4">
        <button
          class="w-full py-3 rounded-lg text-base font-medium transition-colors"
          :class="canSubmit ? 'bg-primary-500 text-white active:bg-primary-600' : 'bg-gray-300 text-gray-500 cursor-not-allowed'"
          :disabled="!canSubmit || isSubmitting"
          @click="submitShippingInfo"
        >
          {{ isSubmitting ? '提交中...' : '确认寄送信息' }}
        </button>
        <text class="text-xs text-gray-500 text-center mt-2 block">
          确认后将通知商家准备收货，请确保信息准确
        </text>
      </view>
    </form>
  </view>
</template>

<script setup lang="ts">
import { ref, computed, reactive, onMounted } from 'vue'
import StatusBarPlaceholder from '@/components/StatusBarPlaceholder.vue'
import NavBar from '@/components/NavBar.vue'

// 类型定义
interface ShippingFormData {
  courierCompany: string
  trackingNumber: string
  senderName: string
  senderPhone: string
  senderAddress: string
}

interface CourierOption {
  name: string
  value: string
}

// 响应式数据
const formData = reactive<ShippingFormData>({
  courierCompany: '',
  trackingNumber: '',
  senderName: '',
  senderPhone: '',
  senderAddress: ''
})

const courierIndex = ref(0)
const isSubmitting = ref(false)
const orderId = ref('')

// 静态数据
const courierList: CourierOption[] = [
  { name: '顺丰速运', value: 'SF' },
  { name: '中通快递', value: 'ZTO' },
  { name: '圆通速递', value: 'YTO' },
  { name: '申通快递', value: 'STO' },
  { name: '韵达速递', value: 'YD' },
  { name: '百世快递', value: 'BEST' },
  { name: '京东快递', value: 'JD' },
  { name: '邮政EMS', value: 'EMS' }
]

// 计算属性
const canSubmit = computed(() => {
  return formData.courierCompany &&
         formData.trackingNumber &&
         formData.senderName &&
         formData.senderPhone &&
         formData.senderAddress &&
         !isSubmitting.value
})

// 方法
const onCourierChange = (e: any) => {
  courierIndex.value = e.detail.value
  formData.courierCompany = courierList[e.detail.value].name
}

const loadUserInfo = async () => {
  try {
    // TODO: 从用户信息或订单信息中预填充寄件人信息
    // const userInfo = await getUserInfo()
    // formData.senderName = userInfo.name
    // formData.senderPhone = userInfo.phone
    // formData.senderAddress = userInfo.address
  } catch (error) {
    console.error('加载用户信息失败:', error)
  }
}

const submitShippingInfo = async () => {
  if (!canSubmit.value) return

  isSubmitting.value = true

  try {
    const shippingInfo = {
      orderId: orderId.value,
      courierCompany: formData.courierCompany,
      trackingNumber: formData.trackingNumber,
      senderName: formData.senderName,
      senderPhone: formData.senderPhone,
      senderAddress: formData.senderAddress
    }

    // TODO: 调用API提交快递信息
    // await confirmShipment(shippingInfo)

    // 模拟API调用
    await new Promise(resolve => setTimeout(resolve, 2000))

    uni.showToast({
      title: '提交成功',
      icon: 'success'
    })

    // 返回订单详情页面
    setTimeout(() => {
      uni.navigateBack()
    }, 1500)

  } catch (error: any) {
    uni.showToast({
      title: error.message || '提交失败',
      icon: 'none'
    })
  } finally {
    isSubmitting.value = false
  }
}

// 生命周期
onMounted(() => {
  // 获取订单ID
  const pages = getCurrentPages()
  const currentPage = pages[pages.length - 1]
  orderId.value = currentPage.options?.orderId || ''

  if (!orderId.value) {
    uni.showToast({
      title: '订单ID不存在',
      icon: 'none'
    })
    return
  }

  // 加载用户信息
  loadUserInfo()
})
</script>

<style lang="scss" scoped>
// 自定义样式
</style>
