<template>
  <view class="min-h-screen bg-white">
    <!-- 顶部导航 -->
    <NavBar title="会员方案" :showBack="true" />
    <view class="px-6 py-8">
      <!-- 标题 -->
      <view class="text-center mb-12">
        <text class="text-2xl font-semibold text-gray-900 mb-3 block">选择适合您的会员方案</text>
        <text class="text-gray-500 block">升级高级会员，享受专业收藏体验</text>
      </view>

      <!-- 会员卡片 -->
      <view class="space-y-6 mb-12">
        <MemberCard
          v-for="(plan, idx) in plans"
          :key="plan.name"
          :title="plan.name"
          :subtitle="plan.description"
          :price="plan.price === 0 ? '免费' : `¥${plan.price}/${plan.period}`"
          :discount="plan.popular ? '全场9折起' : '无专属折扣'"
          :benefits="plan.features"
          :active="currentType === (plan.price === 0 ? 'FREE' : 'PRIMARY')"
          :color="plan.popular ? 'orange' : 'gray'"
          :buttonText="plan.price === 0 ? '当前方案' : '立即开通'"
          :disabled="currentType === (plan.price === 0 ? 'FREE' : 'PRIMARY')"
          @open="openMember(plan.price === 0 ? 'FREE' : 'PRIMARY')"
        >
          <template #badge v-if="plan.popular">
            <view class="absolute top-0 right-0 bg-black text-white text-xs px-4 py-2 rounded-bl-xl flex items-center">
              <van-icon name="star" class="mr-1" color="#fff" size="12" />
              推荐
            </view>
          </template>
        </MemberCard>
      </view>

      <!-- 会员特权详解 -->
      <view class="mb-12">
        <text class="text-lg font-semibold text-gray-900 mb-6 block text-center">高级会员特权详解</text>
        <view class="space-y-4">
          <view v-for="(item, idx) in privileges" :key="item.title" class="bg-gray-50 rounded-xl p-4 flex items-center gap-4 shadow-sm">
            <view class="w-12 h-12 bg-white rounded-full flex items-center justify-center">
              <van-icon :name="item.icon" size="24" color="#6366f1" />
            </view>
            <view>
              <text class="font-medium text-gray-900 mb-1 block">{{ item.title }}</text>
              <text class="text-sm text-gray-500 block">{{ item.desc }}</text>
            </view>
          </view>
        </view>
      </view>

      <!-- FAQ -->
      <view>
        <text class="text-lg font-semibold text-gray-900 mb-6 block text-center">常见问题</text>
        <view class="space-y-4">
          <view v-for="faq in faqs" :key="faq.question" class="bg-gray-50 rounded-xl p-4 shadow-sm">
            <text class="font-medium text-gray-900 mb-2 block">{{ faq.question }}</text>
            <text class="text-sm text-gray-500 block">{{ faq.answer }}</text>
          </view>
        </view>
      </view>
    </view>
    <MemberOpenPopup
      v-model:show="showOpenPopup"
      :plan="openPlan"
      :coupons="couponList"
      :loading="payLoading"
      :selectedCouponId="selectedCouponId"
      @pay="handlePay"
      @select-coupon="handleSelectCoupon"
      @close="handleClosePopup"
      @update:duration="val => duration.value = val"
    />
  </view>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue'
import MemberCard from '@/components/MemberCard.vue'
import { getMemberInfo, getPrivilegesByType, getUserCoupons, payOrder, createMemberOrder } from '@/api/api.js'
import NavBar from '@/components/NavBar.vue'
import MemberOpenPopup from '@/components/MemberOpenPopup.vue'

const planPrivileges = ref({ FREE: [], VIP: [] })

const plans = ref([
  {
    name: '普通会员',
    description: '基础服务',
    price: 0,
    period: '免费',
    popular: false,
    get features() { return planPrivileges.value.FREE.length ? planPrivileges.value.FREE : [] },
    limitations: ['无专属折扣', '无优先购买权', '无专属服务'],
  },
  {
    name: '高级会员',
    description: '专业收藏体验',
    price: 499,
    period: '年',
    popular: true,
    get features() { return planPrivileges.value.VIP.length ? planPrivileges.value.VIP : [
    ] },
    limitations: [],
  },
])

const privileges = ref([
  { icon: 'diamond', title: '专属折扣', desc: '全场商品享受9折起优惠价格' },
  { icon: 'fire', title: '优先购买', desc: '新品发布时享有优先预订' },
  { icon: 'shield-o', title: '品质保障', desc: '正品保证与完善的售后服务' },
  { icon: 'service-o', title: '专属服务', desc: 'VIP客服专线，优先处理问题' }
])

const faqs = ref([
  { question: '会员权益何时生效？', answer: '支付成功后会员权益立即生效，您可以马上享受所有高级会员特权。' },
  { question: '会员到期后会自动续费吗？', answer: '不会自动续费，您需要手动续费。到期前我们会提前通知您。' },
  { question: '可以申请退款吗？', answer: '由于虚拟商品特殊性，开通后不可申请退款。' },
])

const currentType = ref('FREE')
const showOpenPopup = ref(false)
const openPlan = ref<{ name: string; description: string; price: number } | null>(null)
const couponList = ref<Array<{ id: string; name: string; amount: number }>>([])
const selectedCouponId = ref<string | null>(null)
const payLoading = ref(false)
const duration = ref(1)

async function fetchMemberInfo() {
  const res = await getMemberInfo()
  if (res?.data?.memberType) {
    currentType.value = res.data.memberType
  }
}
onMounted(async () => {
  await fetchMemberInfo()
  // 获取普通会员权益
  const freeRes = await getPrivilegesByType('NORMAL')
  if (freeRes && Array.isArray(freeRes.data)) {
    planPrivileges.value.FREE = freeRes.data.map(item => item.description)
  }
  // 获取高级会员权益
  const vipRes = await getPrivilegesByType('VIP')
  if (vipRes && Array.isArray(vipRes.data)) {
    planPrivileges.value.VIP = vipRes.data.map(item => item.description)
  }
})

async function openMember(type: string) {
  if (type === currentType.value) {
    uni.showToast({ title: '您已在当前会员方案', icon: 'none' })
    return
  }
  if (type === 'VIP' || type === 'PRIMARY') {
    // 获取可用优惠券
    payLoading.value = true
    try {
      const res = await getUserCoupons({ type: 'MEMBER', status: 'CLAIMED' })
      couponList.value = Array.isArray(res?.data)
        ? res.data.map((c: any) => ({ id: c.couponInfo?.id || c.id, name: c.couponInfo?.name || c.name, amount: c.couponInfo?.value || c.amount }))
        : []
    } catch (e) {
      couponList.value = []
    } finally {
      payLoading.value = false
    }
    // 只支持开通高级会员
    const plan = plans.value.find(p => p.price > 0)
    openPlan.value = plan ? { name: plan.name, description: plan.description, price: plan.price } : null
    showOpenPopup.value = true
    selectedCouponId.value = null
  } else if (type === 'NORMAL' || type === 'FREE') {
    uni.showToast({ title: '您已是普通会员', icon: 'none' })
  }
}

async function handlePay(couponId: string | null, _payMethod?: string, payDuration?: number) {
  if (!openPlan.value) return
  payLoading.value = true
  try {
    // 1. 创建会员订单
    const periods = payDuration || duration.value;
    const pricePerPeriod = openPlan.value.price;
    const calculatedOriginalAmount = pricePerPeriod * periods;

    let calculatedDiscountAmount = 0;
    const finalCouponId = couponId || undefined;

    if (finalCouponId && couponList.value) {
      const selectedCoupon = couponList.value.find(c => c.id === finalCouponId);
      if (selectedCoupon) {
        calculatedDiscountAmount = selectedCoupon.amount;
      }
    }
    const calculatedPayAmount = calculatedOriginalAmount - calculatedDiscountAmount;

    const orderPayload = {
      memberType: 'VIP', // 使用 'VIP' 因为是开通高级会员，用户提供的 DTO 中 'NORMAL' 可能为示例
      duration: periods,
      originalAmount: calculatedOriginalAmount,
      payAmount: calculatedPayAmount,
      discountAmount: calculatedDiscountAmount,
      couponId: finalCouponId,
      payType: _payMethod,
    };

    const orderRes = await createMemberOrder(orderPayload)
    const orderId = orderRes?.data
    if (!orderId) {
      uni.showToast({ title: '下单失败', icon: 'none' })
      return
    }
    // 2. 获取支付宝支付串
    const [err, res] = await payOrder({ orderId: orderId, payType: 'ALIPAY', salesType: 'MEMBER' })
    if (res?.success && res?.data) {
      // 设置支付宝沙盒环境
      var EnvUtils = plus.android.importClass(
        'com.alipay.sdk.app.EnvUtils'
      ) as any // 添加类型断言
      EnvUtils.setEnv(EnvUtils.EnvEnum.SANDBOX)

      const orderInfo = res.data
      console.log('orderInfo', orderInfo)
      // 3. 调起支付宝支付
      uni.requestPayment({
        provider: 'alipay',
        orderInfo: orderInfo.trim(),
        success: () => {
          uni.showToast({ title: '支付成功', icon: 'success' })
          showOpenPopup.value = false
          // 延迟1.5秒后跳转到会员中心
          setTimeout(() => {
            uni.reLaunch({
              url: '/pages/views/member/memberCenter',
            })
          }, 1500)
        },
        fail: () => {
          uni.showToast({ title: '支付未完成', icon: 'none' })
        },
      })
    } else {
      uni.showToast({ title: res?.data?.message || '支付失败', icon: 'none' })
    }
  } catch (e: any) {
    uni.showToast({ title: e?.message || '支付异常', icon: 'none' })
  } finally {
    payLoading.value = false
  }
}

function handleSelectCoupon(id: string) {
  selectedCouponId.value = id
}

function handleClosePopup() {
  showOpenPopup.value = false
}
</script>

<style scoped>
.space-y-6 > :not([hidden]) ~ :not([hidden]) {
  --tw-space-y-reverse: 0;
  margin-top: calc(1.5rem * calc(1 - var(--tw-space-y-reverse)));
  margin-bottom: calc(1.5rem * var(--tw-space-y-reverse));
}
.mb-12 {
  margin-bottom: 3rem;
}
.px-6 {
  padding-left: 1.5rem;
  padding-right: 1.5rem;
}
.py-8 {
  padding-top: 2rem;
  padding-bottom: 2rem;
}
.rounded-xl {
  border-radius: 0.75rem;
}
.shadow-sm {
  box-shadow: 0 1px 2px 0 rgba(0,0,0,0.05);
}
.bg-gray-50 {
  background-color: #f9fafb;
}
.text-center {
  text-align: center;
}
.font-semibold {
  font-weight: 600;
}
.text-gray-900 {
  color: #111827;
}
.text-gray-500 {
  color: #6b7280;
}
.text-xs {
  font-size: 0.75rem;
}
.p-4 {
  padding: 1rem;
}
.flex {
  display: flex;
}
.items-center {
  align-items: center;
}
.gap-4 {
  gap: 1rem;
}
.bg-white {
  background-color: #fff;
}
.rounded-full {
  border-radius: 9999px;
}
.font-medium {
  font-weight: 500;
}
.mb-1 {
  margin-bottom: 0.25rem;
}
.block {
  display: block;
}
.mb-2 {
  margin-bottom: 0.5rem;
}
.mb-3 {
  margin-bottom: 0.75rem;
}
.mb-6 {
  margin-bottom: 1.5rem;
}
.w-12 {
  width: 3rem;
}
.h-12 {
  height: 3rem;
}
.w-8 {
  width: 2rem;
}
.h-5 {
  height: 1.25rem;
}
.w-5 {
  width: 1.25rem;
}
.absolute {
  position: absolute;
}
.top-0 {
  top: 0;
}
.right-0 {
  right: 0;
}
.bg-black {
  background-color: #000;
}
.text-white {
  color: #fff;
}
.px-4 {
  padding-left: 1rem;
  padding-right: 1rem;
}
.py-2 {
  padding-top: 0.5rem;
  padding-bottom: 0.5rem;
}
.rounded-bl-xl {
  border-bottom-left-radius: 0.75rem;
}
.mr-1 {
  margin-right: 0.25rem;
}
</style>