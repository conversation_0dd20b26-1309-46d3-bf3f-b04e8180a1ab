<template>
  <i
    :class="[type, 'fa-' + name, sizeClass]"
    :style="{ color: color }"
    @click="onClick"
  ></i>
</template>

<script setup>
import { computed } from 'vue'

const props = defineProps({
  // 图标类型：fas(solid)、far(regular)、fab(brands)等
  type: {
    type: String,
    default: 'fas',
  },
  // 图标名称
  name: {
    type: String,
    required: true,
  },
  // 图标大小，可以传入数字或字符串
  size: {
    type: [Number, String],
    default: 16,
  },
  // 图标颜色
  color: {
    type: String,
    default: '#333',
  },
})

const emit = defineEmits(['click'])

// 根据传入的size设置样式类
const sizeClass = computed(() => {
  const size =
    typeof props.size === 'number' ? props.size : parseInt(props.size)
  if (size <= 12) return 'fa-xs'
  if (size <= 16) return 'fa-sm'
  if (size <= 24) return ''
  if (size <= 32) return 'fa-lg'
  if (size <= 48) return 'fa-2x'
  if (size <= 64) return 'fa-3x'
  if (size <= 96) return 'fa-4x'
  if (size <= 128) return 'fa-5x'
  return 'fa-6x'
})

// 点击事件处理
const onClick = (e) => {
  emit('click', e)
}
</script>

<style scoped>
i {
  display: inline-flex;
  align-items: center;
  justify-content: center;
}
</style>
