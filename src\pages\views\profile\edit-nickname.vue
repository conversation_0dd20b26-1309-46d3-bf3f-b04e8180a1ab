<template>
  <view class="page min-h-screen bg-gray-50">
    <NavBar title="修改昵称" :showBack="true"></NavBar>

    <div class="edit-nickname-content p-4">
      <view class="mt-1">
        <view class="mb-4">
          <input
            class="w-full max-w-full h-12 bg-white rounded-xl shadow-md text-base px-4 py-3 box-border leading-normal"
            v-model="nickname"
            type="text"
            placeholder="请输入新昵称"
            maxlength="20"
          />
          <text class="mt-6 ml-1 text-xs text-text-secondary"
            >昵称长度3-20个字符，只能包含中文、英文、数字和下划线</text
          >
        </view>

        <view class="mt-6">
          <button
            class="w-full h-12 rounded-xl bg-gradient-to-r from-secondary to-energy text-white text-base font-medium shadow-md transition-all duration-300 hover:shadow-lg hover:opacity-95 active:transform active:scale-98 disabled:opacity-60 disabled:bg-gray-400 disabled:shadow-none flex items-center justify-center"
            @click="saveNickname"
          >
            保&nbsp;&nbsp;&nbsp;&nbsp;存
          </button>
        </view>
      </view>
    </div>
  </view>
</template>

<script setup lang="ts">
import { ref } from "vue"
import { useStore } from "vuex"
import NavBar from "@/components/NavBar.vue"

const store = useStore()
// 昵称值
const nickname = ref(store.state.$userInfo.nickname)

// 昵称验证函数
const validateNickname = (
  value: string
): { valid: boolean; message: string } => {
  // 去除首尾空格
  const trimmedValue = value.trim()

  // 长度验证
  if (!trimmedValue) {
    return { valid: false, message: "昵称不能为空" }
  }

  if (trimmedValue.length < 3) {
    return { valid: false, message: "昵称长度不能少于3个字符" }
  }

  if (trimmedValue.length > 20) {
    return { valid: false, message: "昵称长度不能超过20个字符" }
  }

  // 特殊字符验证 - 只允许中文、英文、数字、下划线
  const validPattern = /^[\u4e00-\u9fa5a-zA-Z0-9_]+$/
  if (!validPattern.test(trimmedValue)) {
    return { valid: false, message: "昵称只能包含中文、英文、数字和下划线" }
  }

  // 不能全是数字
  if (/^\d+$/.test(trimmedValue)) {
    return { valid: false, message: "昵称不能全是数字" }
  }

  // 不能全是下划线
  if (/^_+$/.test(trimmedValue)) {
    return { valid: false, message: "昵称不能全是下划线" }
  }

  // 敏感词过滤
  const sensitiveWords = [
    "admin",
    "administrator",
    "管理员",
    "root",
    "system",
    "系统",
    "test",
    "测试"
  ]
  const lowerValue = trimmedValue.toLowerCase()
  for (const word of sensitiveWords) {
    if (lowerValue.includes(word.toLowerCase())) {
      return { valid: false, message: "昵称包含敏感词，请重新输入" }
    }
  }

  return { valid: true, message: "" }
}

// 保存昵称
const saveNickname = async () => {
  // 昵称验证
  const validation = validateNickname(nickname.value)
  if (!validation.valid) {
    uni.showToast({
      title: validation.message,
      icon: "none"
    })
    return
  }

  // 调用更新用户资料的API
  const data = {
    id: store.state.$userInfo.id,
    nickname: nickname.value.trim(),
    version: store.state.$userInfo.version
  }
  const res = await store.dispatch("updateProfile", data)
  if (res.success) {
    uni.showToast({
      title: "昵称修改成功",
      icon: "success"
    })
  } else {
    uni.showToast({
      title: "昵称修改失败",
      icon: "none"
    })
  }

  // 返回上一页
  setTimeout(() => {
    uni.reLaunch({
      url: "/pages/views/profile/profile"
    })
  }, 500)
}
</script>

<style scoped lang="scss">
.edit-nickname-content {
  min-height: calc(100vh - var(--status-bar-height) - 44px);
  background-color: #f5f5f5;
}
</style>
