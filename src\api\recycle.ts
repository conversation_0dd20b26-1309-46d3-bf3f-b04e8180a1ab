// 回收模块API接口

// 类型定义
export interface RecycleOrder {
  id: string
  userId: string
  userPhone?: string
  brandName: string
  model: string
  categoryName: string
  productDesc?: string
  conditionDescription: string
  estimatedPrice: number
  finalPrice?: number
  orderStatus: string
  orderStatusDesc: string
  reviewedPrice?: number
  shippingInfo?: string
  contactPerson?: string
  createTime: number
  updateTime: number
  mainImage?: string
  files?: Array<{
    id: string
    url: string
    thumbnailUrl: string
  }>
}

export interface CreateOrderRequest {
  productName: string
  productDesc: string
  productModel?: string
  productCategory: string
  contactPerson: string
  contactPhone: string
  expectedPrice: number
  condition: string
  imageFiles: string[]
}

export interface OrderQueryParams {
  page: number
  size: number
  orderStatus?: string
  productName?: string
  startDate?: string
  endDate?: string
}

export interface ShippingInfoRequest {
  orderId: string
  courierCompany: string
  trackingNumber: string
  senderName: string
  senderPhone: string
  senderAddress: string
}

export interface ApiResponse<T = any> {
  success: boolean
  data: T
  message: string
  code: number
}

export interface PageResult<T> {
  records: T[]
  total: number
  size: number
  current: number
  pages: number
}

// 获取HTTP实例的辅助函数
const getHttpInstance = () => {
  if (!uni.$u || !uni.$u.http) {
    console.error('uView Plus HTTP 实例尚未初始化！')
    throw new Error('uView Plus HTTP instance is not initialized.')
  }
  return uni.$u.http
}

// 回收模块API
export const recycleApi = {
  // 创建回收订单
  createOrder: (data: CreateOrderRequest): Promise<ApiResponse<string>> => {
    const http = getHttpInstance()
    return http.post('/api/recycle/orders', data)
  },

  // 查询用户订单列表
  getUserOrders: (params: OrderQueryParams): Promise<ApiResponse<PageResult<RecycleOrder>>> => {
    const http = getHttpInstance()
    return http.get('/api/recycle/orders', { params })
  },

  // 获取订单详情
  getOrderDetail: (orderId: string): Promise<ApiResponse<RecycleOrder>> => {
    const http = getHttpInstance()
    return http.get(`/api/recycle/orders/${orderId}`)
  },

  // 取消订单
  cancelOrder: (orderId: string): Promise<ApiResponse<void>> => {
    const http = getHttpInstance()
    return http.post(`/api/recycle/orders/${orderId}/cancel`)
  },

  // 确认寄送
  confirmShipment: (data: ShippingInfoRequest): Promise<ApiResponse<void>> => {
    const http = getHttpInstance()
    return http.post('/api/recycle/orders/confirm-shipment', data)
  },

  // 申请退回
  requestReturn: (orderId: string): Promise<ApiResponse<void>> => {
    const http = getHttpInstance()
    return http.post(`/api/recycle/orders/${orderId}/return`)
  },

  // 上传图片文件
  uploadImage: (filePath: string): Promise<ApiResponse<{ id: string; url: string; thumbnailUrl: string }>> => {
    const http = getHttpInstance()
    return new Promise((resolve, reject) => {
      uni.uploadFile({
        url: `${http.config.baseURL}/api/file/upload`,
        filePath: filePath,
        name: 'file',
        header: {
          'Authorization': `Bearer ${uni.getStorageSync('token')}`
        },
        success: (uploadRes) => {
          try {
            const result = JSON.parse(uploadRes.data)
            if (result.success) {
              resolve(result)
            } else {
              reject(new Error(result.message))
            }
          } catch (error) {
            reject(error)
          }
        },
        fail: (error) => {
          reject(error)
        }
      })
    })
  },

  // 获取回收商品类别
  getCategories: (): Promise<ApiResponse<Array<{ name: string; value: string }>>> => {
    const http = getHttpInstance()
    return http.get('/api/recycle/categories')
  },

  // 获取回收配置
  getConfig: (): Promise<ApiResponse<any>> => {
    const http = getHttpInstance()
    return http.get('/api/recycle/config')
  }
}

// 工具函数
export const recycleUtils = {
  // 格式化订单状态
  formatOrderStatus: (status: string): string => {
    const statusMap: Record<string, string> = {
      'DRAFT': '草稿',
      'PENDING_APPROVAL': '待审核',
      'PRICE_QUOTED': '已报价',
      'SHIPPING_CONFIRMED': '确认寄送',
      'CANCELLED': '已取消',
      'RECEIVED': '已收货',
      'PAYMENT_CONFIRMED': '已付款',
      'RETURN_REQUESTED': '申请退回',
      'COMPLETED': '已完成',
      'RETURNED': '已退回'
    }
    return statusMap[status] || status
  },

  // 格式化商品状况
  formatCondition: (condition: string): string => {
    const conditionMap: Record<string, string> = {
      'excellent': '成色极佳',
      'good': '成色良好',
      'fair': '成色一般',
      'poor': '成色较差'
    }
    return conditionMap[condition] || condition
  },

  // 获取状态样式类
  getStatusClass: (status: string): string => {
    const statusMap: Record<string, string> = {
      'PENDING_APPROVAL': 'bg-yellow-100 text-yellow-800',
      'PRICE_QUOTED': 'bg-blue-100 text-blue-800',
      'SHIPPING_CONFIRMED': 'bg-purple-100 text-purple-800',
      'RECEIVED': 'bg-indigo-100 text-indigo-800',
      'COMPLETED': 'bg-green-100 text-green-800',
      'CANCELLED': 'bg-gray-100 text-gray-800',
      'RETURNED': 'bg-red-100 text-red-800'
    }
    return statusMap[status] || 'bg-gray-100 text-gray-800'
  },

  // 格式化时间
  formatTime: (timestamp: number): string => {
    const date = new Date(timestamp)
    const now = new Date()
    const diff = now.getTime() - date.getTime()

    if (diff < 60 * 1000) return '刚刚'
    if (diff < 60 * 60 * 1000) return `${Math.floor(diff / (60 * 1000))}分钟前`
    if (diff < 24 * 60 * 60 * 1000) return `${Math.floor(diff / (60 * 60 * 1000))}小时前`
    if (diff < 7 * 24 * 60 * 60 * 1000) return `${Math.floor(diff / (24 * 60 * 60 * 1000))}天前`

    return date.toLocaleDateString('zh-CN', {
      year: 'numeric',
      month: '2-digit',
      day: '2-digit'
    })
  },

  // 格式化价格
  formatPrice: (price: number): string => {
    return `¥${price.toFixed(2)}`
  },

  // 验证手机号
  validatePhone: (phone: string): boolean => {
    const phoneRegex = /^1[3-9]\d{9}$/
    return phoneRegex.test(phone)
  },

  // 验证快递单号
  validateTrackingNumber: (trackingNumber: string): boolean => {
    // 简单的快递单号验证，实际项目中可以根据不同快递公司制定不同规则
    return trackingNumber.length >= 8 && trackingNumber.length <= 20
  }
}
