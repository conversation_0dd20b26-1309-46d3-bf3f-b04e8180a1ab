```
.
├── common					#演示需要的一些文件
│   ├── api.js
│   ├── config.js
│   ├── demo.scss
│   ├── mixin.js
│   └── props.js
├── components					#演示项目封装的组件
│   └── page-nav
│       └── page-nav.vue
├── pages					#页面
│   ├── componentsA				#分包A
│   │   ├── ...
│   │   ├── ...
│   ├── componentsB				#分包B
│   │   ├── ...
│   │   ├── ...
│   ├── componentsC				#分包C
│   │   ├── ...
│   │   ├── ...
│   └── example					#演示项目首页
│       ├── components.config.js		#演示页面数据
│       └── components.nvue			#主演示页面
├── static					#演示项目需要的一些文件
│   ├── app-plus
│   │   └── mp-html
│   ├── common
│   │   └── js
│   └── uview
│       ├── common
│       └── example
├── store
│   └── index.js
├── uni_modules
│   └── uview-ui				#uView2.0主包
│       ├── LICENSE
│       ├── README.md
│       ├── changelog.md
│       ├── components				#所有的组件
│       ├── index.js
│       ├── index.scss
│       ├── libs
│       ├── package.json
│       └── theme.scss
├── unpackage
│   └── res
│       └── icons
├── util
│   └── request
│       ├── index.js
│       ├── requestInterceptors.js
│       └── responseInterceptors.js
├── App.vue
├── LICENSE
├── main.js
├── manifest.json
├── package-lock.json
├── pages.json					#页面配置
├── package.json
├── README.md
├── template.h5.html				#h5模板
├── tree.md
├── uni.scss
└── vue.config.js


created by beiqiao.
```
