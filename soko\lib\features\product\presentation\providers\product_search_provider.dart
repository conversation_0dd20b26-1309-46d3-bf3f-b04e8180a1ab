import 'package:flutter_riverpod/flutter_riverpod.dart';

import 'package:soko/features/product/data/datasources/product_api_service.dart';
import 'package:soko/features/product/domain/entities/product.dart';

/// 搜索建议状态
class SearchSuggestionState {

  const SearchSuggestionState({
    this.suggestions = const [],
    this.isLoading = false,
    this.error,
  });
  final List<String> suggestions;
  final bool isLoading;
  final String? error;

  SearchSuggestionState copyWith({
    List<String>? suggestions,
    bool? isLoading,
    String? error,
  }) {
    return SearchSuggestionState(
      suggestions: suggestions ?? this.suggestions,
      isLoading: isLoading ?? this.isLoading,
      error: error,
    );
  }
}

/// 搜索历史状态
class SearchHistoryState {

  const SearchHistoryState({
    this.history = const [],
  });
  final List<String> history;

  SearchHistoryState copyWith({
    List<String>? history,
  }) {
    return SearchHistoryState(
      history: history ?? this.history,
    );
  }
}

/// 搜索建议状态管理器
class SearchSuggestionNotifier extends StateNotifier<SearchSuggestionState> {

  SearchSuggestionNotifier() : super(const SearchSuggestionState());
  final ProductApiService _productApiService = ProductApiService();

  /// 获取搜索建议
  Future<void> getSuggestions(String keyword) async {
    if (keyword.trim().isEmpty) {
      state = state.copyWith(suggestions: []);
      return;
    }

    state = state.copyWith(isLoading: true);

    try {
      // TODO: 实现搜索建议API调用
      // 暂时使用模拟数据
      await Future.delayed(const Duration(milliseconds: 300));
      
      final suggestions = _generateMockSuggestions(keyword);
      
      state = state.copyWith(
        suggestions: suggestions,
        isLoading: false,
      );
    } catch (e) {
      state = state.copyWith(
        isLoading: false,
        error: e.toString(),
      );
    }
  }

  /// 清除建议
  void clearSuggestions() {
    state = state.copyWith(suggestions: []);
  }

  /// 生成模拟搜索建议
  List<String> _generateMockSuggestions(String keyword) {
    final allSuggestions = [
      '奥特曼',
      '奥特曼手办',
      '奥特曼模型',
      '假面骑士',
      '假面骑士腰带',
      '假面骑士变身器',
      '超级战队',
      '战队机器人',
      '高达',
      '高达模型',
      'RG高达',
      'MG高达',
      'PG高达',
      '手办',
      '景品',
      '一番赏',
    ];

    return allSuggestions
        .where((suggestion) => suggestion.contains(keyword))
        .take(8)
        .toList();
  }
}

/// 搜索历史状态管理器
class SearchHistoryNotifier extends StateNotifier<SearchHistoryState> {

  SearchHistoryNotifier() : super(const SearchHistoryState()) {
    _loadHistory();
  }
  static const int maxHistoryCount = 10;

  /// 加载搜索历史
  Future<void> _loadHistory() async {
    // TODO: 从本地存储加载搜索历史
    // 暂时使用空列表
    state = state.copyWith(history: []);
  }

  /// 添加搜索历史
  Future<void> addHistory(String keyword) async {
    if (keyword.trim().isEmpty) return;

    final newHistory = List<String>.from(state.history);
    
    // 移除已存在的相同关键词
    newHistory.remove(keyword);
    
    // 添加到开头
    newHistory.insert(0, keyword);
    
    // 限制历史记录数量
    if (newHistory.length > maxHistoryCount) {
      newHistory.removeRange(maxHistoryCount, newHistory.length);
    }

    state = state.copyWith(history: newHistory);
    
    // TODO: 保存到本地存储
    await _saveHistory();
  }

  /// 删除搜索历史
  Future<void> removeHistory(String keyword) async {
    final newHistory = List<String>.from(state.history);
    newHistory.remove(keyword);
    
    state = state.copyWith(history: newHistory);
    
    // TODO: 保存到本地存储
    await _saveHistory();
  }

  /// 清空搜索历史
  Future<void> clearHistory() async {
    state = state.copyWith(history: []);
    
    // TODO: 清空本地存储
    await _saveHistory();
  }

  /// 保存搜索历史到本地存储
  Future<void> _saveHistory() async {
    // TODO: 实现本地存储
  }
}

/// 热门搜索状态
class HotSearchState {

  const HotSearchState({
    this.hotKeywords = const [],
    this.isLoading = false,
    this.error,
  });
  final List<String> hotKeywords;
  final bool isLoading;
  final String? error;

  HotSearchState copyWith({
    List<String>? hotKeywords,
    bool? isLoading,
    String? error,
  }) {
    return HotSearchState(
      hotKeywords: hotKeywords ?? this.hotKeywords,
      isLoading: isLoading ?? this.isLoading,
      error: error,
    );
  }
}

/// 热门搜索状态管理器
class HotSearchNotifier extends StateNotifier<HotSearchState> {

  HotSearchNotifier() : super(const HotSearchState()) {
    loadHotKeywords();
  }
  final ProductApiService _productApiService = ProductApiService();

  /// 加载热门搜索关键词
  Future<void> loadHotKeywords() async {
    state = state.copyWith(isLoading: true);

    try {
      // TODO: 实现热门搜索API调用
      // 暂时使用模拟数据
      await Future.delayed(const Duration(milliseconds: 500));
      
      const hotKeywords = [
        '奥特曼',
        '假面骑士',
        '高达',
        '手办',
        '一番赏',
        '景品',
        '超级战队',
        '变身器',
      ];
      
      state = state.copyWith(
        hotKeywords: hotKeywords,
        isLoading: false,
      );
    } catch (e) {
      state = state.copyWith(
        isLoading: false,
        error: e.toString(),
      );
    }
  }

  /// 刷新热门搜索
  Future<void> refresh() async {
    await loadHotKeywords();
  }
}

/// 搜索建议状态提供者
final searchSuggestionProvider = StateNotifierProvider<SearchSuggestionNotifier, SearchSuggestionState>(
  (ref) => SearchSuggestionNotifier(),
);

/// 搜索历史状态提供者
final searchHistoryProvider = StateNotifierProvider<SearchHistoryNotifier, SearchHistoryState>(
  (ref) => SearchHistoryNotifier(),
);

/// 热门搜索状态提供者
final hotSearchProvider = StateNotifierProvider<HotSearchNotifier, HotSearchState>(
  (ref) => HotSearchNotifier(),
);
