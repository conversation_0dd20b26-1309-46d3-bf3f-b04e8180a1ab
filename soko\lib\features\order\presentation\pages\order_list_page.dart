import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';

import 'package:soko/core/enums/app_enums.dart';
import 'package:soko/core/state/base_state.dart';
import 'package:soko/shared/presentation/widgets/empty_widget.dart';
import 'package:soko/shared/presentation/widgets/error_widget.dart';
import 'package:soko/shared/presentation/widgets/loading_widget.dart';
import 'package:soko/features/order/domain/entities/order.dart';
import 'package:soko/features/order/presentation/providers/order_provider.dart';
import 'package:soko/features/order/presentation/providers/order_actions_provider.dart';
import 'package:soko/features/order/presentation/widgets/order_list_item.dart';

/// 订单列表页面
class OrderListPage extends ConsumerStatefulWidget {

  const OrderListPage({
    super.key,
    this.orderType,
  });
  final String? orderType;

  @override
  ConsumerState<OrderListPage> createState() => _OrderListPageState();
}

class _OrderListPageState extends ConsumerState<OrderListPage>
    with SingleTickerProviderStateMixin {
  late TabController _tabController;
  final ScrollController _scrollController = ScrollController();

  // 订单状态选项
  final List<OrderStatus?> _statusOptions = [
    null, // 全部
    OrderStatus.pending,
    OrderStatus.shipped,
    OrderStatus.completed,
  ];

  final List<String> _statusLabels = [
    '全部',
    '待付款',
    '待收货',
    '已完成',
  ];

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: _statusOptions.length, vsync: this);

    // 根据传入的订单类型设置初始选中的标签
    if (widget.orderType != null) {
      final index = _getInitialTabIndex();
      _tabController.index = index;
    }

    // 监听滚动事件，实现上拉加载更多
    _scrollController.addListener(_onScroll);

    // 初始加载数据
    WidgetsBinding.instance.addPostFrameCallback((_) {
      _loadOrders(refresh: true);
    });
  }

  @override
  void dispose() {
    _tabController.dispose();
    _scrollController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final orderState = ref.watch(orderProvider);

    return Scaffold(
      appBar: AppBar(
        title: const Text('我的订单'),
        centerTitle: true,
        bottom: TabBar(
          controller: _tabController,
          tabs: _statusLabels.map((label) => Tab(text: label)).toList(),
          onTap: _onTabChanged,
        ),
      ),
      body: RefreshIndicator(
        onRefresh: () => _loadOrders(refresh: true),
        child: _buildBody(orderState),
      ),
    );
  }

  /// 构建页面主体
  Widget _buildBody(PageState<Order> orderState) {
    if (orderState.isFirstPageLoading) {
      return const LoadingWidget();
    }

    if (orderState.isEmpty && orderState.error == null) {
      return const EmptyWidget(
        message: '暂无订单',
        icon: Icons.receipt_long,
      );
    }

    if (orderState.error != null && orderState.items.isEmpty) {
      return ErrorDisplayWidget(
        message: orderState.error!,
        onRetry: () => _loadOrders(refresh: true),
      );
    }

    return _buildOrderList(orderState);
  }

  /// 构建订单列表
  Widget _buildOrderList(PageState<Order> orderState) {
    final filteredOrders = _getFilteredOrders(orderState.items);

    return ListView.builder(
      controller: _scrollController,
      itemCount: filteredOrders.length + (orderState.isLoadingMore ? 1 : 0),
      itemBuilder: (context, index) {
        if (index == filteredOrders.length) {
          return const LoadingWidget();
        }

        final order = filteredOrders[index];
        return OrderListItem(
          order: order,
          onTap: () => _navigateToOrderDetail(order.id),
          onCancel: _cancelOrder,
          onPay: _payOrder,
          onConfirm: _confirmOrder,
          onRefund: _refundOrder,
          onDelete: _deleteOrder,
        );
      },
    );
  }

  /// 获取初始标签索引
  int _getInitialTabIndex() {
    switch (widget.orderType) {
      case 'pending':
        return 1;
      case 'shipping':
        return 2;
      case 'completed':
        return 3;
      default:
        return 0;
    }
  }

  /// 获取过滤后的订单列表
  List<Order> _getFilteredOrders(List<Order> orders) {
    final currentStatus = _statusOptions[_tabController.index];
    if (currentStatus == null) {
      return orders;
    }
    return orders.where((order) => order.statusEnum == currentStatus).toList();
  }

  /// 标签切换事件
  void _onTabChanged(int index) {
    _loadOrders(refresh: true);
  }

  /// 滚动事件监听
  void _onScroll() {
    if (_scrollController.position.pixels >=
        _scrollController.position.maxScrollExtent - 200) {
      _loadOrders();
    }
  }

  /// 加载订单数据
  Future<void> _loadOrders({bool refresh = false}) async {
    final currentStatus = _statusOptions[_tabController.index];
    final notifier = ref.read(orderProvider.notifier);

    if (refresh) {
      await notifier.refreshOrders(status: currentStatus);
    } else {
      await notifier.loadMoreOrders(status: currentStatus);
    }
  }

  /// 导航到订单详情
  void _navigateToOrderDetail(String orderId) {
    // TODO(navigation): 实现导航到订单详情页面
    // context.push('/order/detail/$orderId');
  }

  /// 取消订单
  Future<void> _cancelOrder(String orderId) async {
    final success =
        await ref.read(orderActionsProvider.notifier).cancelOrder(orderId);
    if (success && mounted) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(content: Text('订单已取消')),
      );
      // 刷新订单列表
      await _loadOrders(refresh: true);
    }
  }

  /// 支付订单
  void _payOrder(String orderId) {
    // TODO(payment): 实现支付订单功能
    // context.push('/payment/$orderId');
  }

  /// 确认收货
  Future<void> _confirmOrder(String orderId) async {
    final success =
        await ref.read(orderActionsProvider.notifier).confirmReceived(orderId);
    if (success && mounted) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(content: Text('确认收货成功')),
      );
      // 刷新订单列表
      await _loadOrders(refresh: true);
    }
  }

  /// 申请退款
  void _refundOrder(String orderId) {
    // TODO(refund): 实现申请退款功能
    // context.push('/refund/$orderId');
  }

  /// 删除订单
  Future<void> _deleteOrder(String orderId) async {
    final success =
        await ref.read(orderActionsProvider.notifier).deleteOrder(orderId);
    if (success && mounted) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(content: Text('订单已删除')),
      );
      // 刷新订单列表
      await _loadOrders(refresh: true);
    }
  }
}
