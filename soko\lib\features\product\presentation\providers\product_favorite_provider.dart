import 'package:flutter_riverpod/flutter_riverpod.dart';

import 'package:soko/core/state/base_notifier.dart';
import 'package:soko/features/product/data/datasources/product_api_service.dart';
import 'package:soko/features/product/domain/entities/product.dart';

/// 收藏状态
class FavoriteState {

  const FavoriteState({
    this.favoriteIds = const {},
    this.isLoading = false,
    this.error,
  });
  final Set<String> favoriteIds;
  final bool isLoading;
  final String? error;

  FavoriteState copyWith({
    Set<String>? favoriteIds,
    bool? isLoading,
    String? error,
  }) {
    return FavoriteState(
      favoriteIds: favoriteIds ?? this.favoriteIds,
      isLoading: isLoading ?? this.isLoading,
      error: error,
    );
  }

  /// 检查商品是否已收藏
  bool isFavorite(String productId) {
    return favoriteIds.contains(productId);
  }
}

/// 收藏列表状态
class FavoriteListState extends PageState<Product> {
  const FavoriteListState({
    super.items = const [],
    super.isLoading = false,
    super.error,
    super.hasMore = true,
    super.currentPage = 0,
  });

  @override
  FavoriteListState copyWith({
    List<Product>? items,
    bool? isLoading,
    String? error,
    bool? hasMore,
    int? currentPage,
  }) {
    return FavoriteListState(
      items: items ?? this.items,
      isLoading: isLoading ?? this.isLoading,
      error: error,
      hasMore: hasMore ?? this.hasMore,
      currentPage: currentPage ?? this.currentPage,
    );
  }
}

/// 收藏状态管理器
class FavoriteNotifier extends StateNotifier<FavoriteState> {

  FavoriteNotifier() : super(const FavoriteState()) {
    _loadFavoriteIds();
  }
  final ProductApiService _productApiService = ProductApiService();

  /// 加载收藏ID列表
  Future<void> _loadFavoriteIds() async {
    state = state.copyWith(isLoading: true);

    try {
      // TODO: 实现获取收藏列表API调用
      // 暂时使用模拟数据
      await Future.delayed(const Duration(milliseconds: 500));
      
      const favoriteIds = <String>{};
      
      state = state.copyWith(
        favoriteIds: favoriteIds,
        isLoading: false,
      );
    } catch (e) {
      state = state.copyWith(
        isLoading: false,
        error: e.toString(),
      );
    }
  }

  /// 切换收藏状态
  Future<void> toggleFavorite(String productId) async {
    final isFavorite = state.isFavorite(productId);
    
    // 乐观更新UI
    final newFavoriteIds = Set<String>.from(state.favoriteIds);
    if (isFavorite) {
      newFavoriteIds.remove(productId);
    } else {
      newFavoriteIds.add(productId);
    }
    
    state = state.copyWith(favoriteIds: newFavoriteIds);

    try {
      // TODO: 实现收藏/取消收藏API调用
      await Future.delayed(const Duration(milliseconds: 300));
      
      if (isFavorite) {
        // 取消收藏
        // await _productApiService.removeFavorite(productId);
      } else {
        // 添加收藏
        // await _productApiService.addFavorite(productId);
      }
    } catch (e) {
      // 回滚状态
      state = state.copyWith(favoriteIds: state.favoriteIds);
      
      // 显示错误信息
      state = state.copyWith(error: e.toString());
    }
  }

  /// 批量添加收藏
  Future<void> addFavorites(List<String> productIds) async {
    final newFavoriteIds = Set<String>.from(state.favoriteIds);
    newFavoriteIds.addAll(productIds);
    
    state = state.copyWith(favoriteIds: newFavoriteIds);

    try {
      // TODO: 实现批量添加收藏API调用
      await Future.delayed(const Duration(milliseconds: 500));
    } catch (e) {
      // 回滚状态
      final rollbackIds = Set<String>.from(state.favoriteIds);
      for (final id in productIds) {
        rollbackIds.remove(id);
      }
      state = state.copyWith(favoriteIds: rollbackIds, error: e.toString());
    }
  }

  /// 批量移除收藏
  Future<void> removeFavorites(List<String> productIds) async {
    final newFavoriteIds = Set<String>.from(state.favoriteIds);
    for (final id in productIds) {
      newFavoriteIds.remove(id);
    }
    
    state = state.copyWith(favoriteIds: newFavoriteIds);

    try {
      // TODO: 实现批量移除收藏API调用
      await Future.delayed(const Duration(milliseconds: 500));
    } catch (e) {
      // 回滚状态
      final rollbackIds = Set<String>.from(state.favoriteIds);
      rollbackIds.addAll(productIds);
      state = state.copyWith(favoriteIds: rollbackIds, error: e.toString());
    }
  }

  /// 刷新收藏状态
  Future<void> refresh() async {
    await _loadFavoriteIds();
  }

  /// 清除错误
  void clearError() {
    state = state.copyWith();
  }
}

/// 收藏列表状态管理器
class FavoriteListNotifier extends BasePageNotifier<Product> {
  final ProductApiService _productApiService = ProductApiService();

  @override
  Future<PageResponse<Product>> loadPage(int page) async {
    // TODO: 实现获取收藏列表API调用
    // 暂时使用模拟数据
    await Future.delayed(const Duration(milliseconds: 800));
    
    return const PageResponse<Product>(
      items: [],
      total: 0,
      page: 1,
      size: 20,
      hasMore: false,
    );
  }

  /// 从收藏列表中移除商品
  void removeFromList(String productId) {
    final newItems = state.items.where((item) => item.id != productId).toList();
    state = state.copyWith(items: newItems) as FavoriteListState;
  }
}

/// 收藏状态提供者
final favoriteProvider = StateNotifierProvider<FavoriteNotifier, FavoriteState>(
  (ref) => FavoriteNotifier(),
);

/// 收藏列表状态提供者
final favoriteListProvider = StateNotifierProvider<FavoriteListNotifier, PageState<Product>>(
  (ref) => FavoriteListNotifier(),
);

/// 检查商品是否收藏的提供者
final isProductFavoriteProvider = Provider.family<bool, String>((ref, productId) {
  final favoriteState = ref.watch(favoriteProvider);
  return favoriteState.isFavorite(productId);
});

/// 收藏数量提供者
final favoriteCountProvider = Provider<int>((ref) {
  final favoriteState = ref.watch(favoriteProvider);
  return favoriteState.favoriteIds.length;
});
