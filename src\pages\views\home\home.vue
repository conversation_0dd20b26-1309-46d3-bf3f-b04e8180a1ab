<template>
  <view
    class="flex flex-col items-center w-full min-h-screen bg-slate-50 px-0 pb-safe box-border"
  >
    <!-- 导航栏 -->
    <NavBar
      :showTitle="false"
      :goToSearchPage="goToSearchPage"
      bgColor="#FFFFFF"
      :showBack="false"
    >
      <!-- 自定义右侧内容 -->
      <template #right>
        <view class="flex items-center">
          <van-icon
            size="18"
            name="chat-o"
            @click="handleToNoticesCenter"
            :badge="warningBadge"
            class="mr-2"
          />
          <van-icon
            name="volume-o"
            size="18"
            @click="handleToAnnouncementCenter"
            class="mr-2"
          />
        </view>
      </template>
    </NavBar>

    <!-- banner部分 -->
    <view
      class="w-[calc(100%-32px)] mx-4 mt-4 rounded-xl overflow-hidden shadow-lg"
    >
      <up-swiper
        :list="bannerList"
        keyName="url"
        :height="200"
        :autoplay="true"
        :interval="3000"
        :circular="true"
        :radius="12"
        :indicator="true"
        indicatorMode="dot"
        indicatorInactiveColor="rgba(255, 255, 255, 0.5)"
        indicatorActiveColor="#ffffff"
        @change="swiperChange"
        @click="handleClickSwiper"
      ></up-swiper>
    </view>

    <!-- ACG橱窗部分 -->
    <view class="flex items-center mt-4 w-[calc(100%-32px)] mx-4">
      <view
        class="flex items-center font-bold text-base text-text-primary relative mb-3"
      >
        <view class="inline-block w-1 h-4 bg-primary mr-2 rounded"></view>
        <text>ACG 橱窗</text>
      </view>
    </view>
    <view
      class="w-[calc(100%-32px)] mx-4 rounded-xl overflow-hidden bg-white shadow-sm"
    >
      <template v-if="acgLoading">
        <view class="p-4 flex">
          <view
            v-for="n in 4"
            :key="n"
            class="w-20 h-8 bg-gray-200 rounded-full mr-3 animate-pulse"
          ></view>
        </view>
        <view class="px-4 pb-4">
          <view class="flex mb-4">
            <view
              class="w-20 h-4 bg-gray-200 rounded mr-2 animate-pulse"
            ></view>
            <view class="w-12 h-4 bg-gray-100 rounded animate-pulse"></view>
          </view>
          <scroll-view scroll-x class="flex whitespace-nowrap">
            <view v-for="n in 4" :key="n" class="inline-block w-[120px] mr-4">
              <view
                class="w-[120px] h-[120px] bg-gray-200 rounded mb-2 animate-pulse"
              ></view>
              <view class="h-4 bg-gray-100 rounded mb-1 animate-pulse"></view>
              <view class="h-4 bg-gray-100 rounded animate-pulse"></view>
            </view>
          </scroll-view>
        </view>
      </template>
      <template v-else>
        <view class="flex overflow-x-auto whitespace-nowrap p-4">
          <view
            v-for="ip in showAcg.list"
            :key="ip.id"
            class="px-4 py-2 mr-3 rounded-full text-sm"
            :class="{
              'bg-accent text-white font-medium shadow-sm': currentIp === ip.id,
              'bg-[#f1f5f9] text-text-secondary': currentIp !== ip.id
            }"
            @click="selectIp(ip.id)"
          >
            {{ ip.name }}
          </view>
        </view>

        <!-- IP内容区域 -->
        <view class="px-4 pb-4">
          <view class="flex justify-between items-center mb-4">
            <view>
              <text class="font-bold text-base text-heroic">万代</text>
              <text class="font-normal text-sm ml-1 text-text-secondary"
                >系列</text
              >
            </view>
            <view
              class="bg-primary/10 text-primary text-xs font-medium rounded-full px-3 py-1.5"
              >10%OFF起</view
            >
          </view>

          <!-- IP产品列表 -->
          <scroll-view scroll-x class="flex whitespace-nowrap">
            <view
              class="inline-block w-[120px] mr-4"
              v-for="(product, idx) in currentIpInfo"
              :key="idx"
              @click="navigateToDetail(product)"
            >
              <up-image
                :src="product.files[0].url"
                width="120"
                height="120"
                radius="8"
                class="bg-gray-100"
              ></up-image>
              <view
                class="text-xs mt-2 whitespace-normal overflow-hidden text-ellipsis h-[36px] text-text-secondary leading-snug"
              >
                {{ product.name }}
              </view>
              <view class="text-base font-medium text-primary mt-1"
                >¥{{ product.skus[0].price }}</view
              >
            </view>
          </scroll-view>
        </view>
      </template>
    </view>

    <!-- 新品速递部分 -->
    <view class="flex items-center mt-6 w-[calc(100%-32px)] mx-4">
      <view
        class="flex items-center justify-between font-bold text-base text-text-primary mb-3 w-full"
      >
        <view class="flex items-center">
          <view class="inline-block w-1 h-4 bg-primary mr-2 rounded"></view>
          <text>新品速递</text>
        </view>
        <view
          class="text-xs text-gray-500 font-medium flex items-center"
          @click="goToNewArrivalPage"
        >
          查看更多
          <van-icon name="arrow" size="10" class="ml-1 text-gray-500" />
        </view>
      </view>
    </view>
    <view
      class="w-[calc(100%-32px)] mx-4 rounded-xl overflow-hidden bg-white shadow-sm"
    >
      <template v-if="newArrivalLoading">
        <view class="p-4">
          <view
            class="w-full h-[320px] bg-gray-200 rounded-xl animate-pulse"
          ></view>
          <view class="flex justify-center mt-4 space-x-1.5">
            <view
              v-for="n in 4"
              :key="n"
              class="h-2 w-4 bg-gray-200 rounded-full animate-pulse"
            ></view>
          </view>
        </view>
      </template>
      <template v-else>
        <view class="p-4">
          <view
            class="w-full rounded-xl bg-white overflow-hidden relative shadow-sm"
          >
            <up-swiper
              :list="newArrivalState.newArrivals"
              height="320px"
              :autoplay="true"
              :interval="4000"
              circular
              keyName="image"
              @change="newArrivalSwiperChange"
            >
              <template v-slot:default="{ item }">
                <view
                  class="relative bg-white h-[318px] rounded-xl overflow-hidden flex flex-col"
                  @click="handleProductClick(item)"
                >
                  <view
                    class="w-full flex justify-center items-center relative h-[180px] mb-2 bg-slate-50 rounded-t-xl overflow-hidden"
                  >
                    <div class="w-full h-full flex items-center justify-center">
                      <CacheImgs
                        :src="item.image"
                        className="w-full h-full object-cover transition-transform duration-300 ease-in-out hover:scale-105"
                      />
                    </div>
                    <view
                      class="absolute top-3 left-3 py-1 px-3 bg-black/70 text-white text-xs rounded-full z-10 font-medium"
                      v-if="item.condition"
                    >
                      {{ item.condition }}
                    </view>
                  </view>

                  <view
                    class="flex-1 flex flex-col justify-between box-border px-4 pb-3"
                  >
                    <view class="mb-3">
                      <view
                        class="font-bold text-lg mb-1.5 leading-tight text-text-primary line-clamp-1"
                      >
                        {{ item.name }}
                      </view>
                      <view class="flex flex-wrap gap-2 mt-2">
                        <view
                          v-if="item.acg"
                          class="px-2 py-1 text-xs rounded-full bg-gray-100 text-text-secondary"
                        >
                          {{ item.acg }}
                        </view>
                        <view
                          v-if="item.brand"
                          class="px-2 py-1 text-xs rounded-full bg-gray-100 text-text-secondary"
                        >
                          {{ item.brand }}
                        </view>
                        <view
                          v-if="item.category"
                          class="px-2 py-1 text-xs rounded-full bg-gray-100 text-text-secondary"
                        >
                          {{ item.category }}
                        </view>
                      </view>
                    </view>

                    <view class="flex justify-between items-end">
                      <view class="flex-1">
                        <view
                          class="py-1 px-2.5 text-xs w-fit rounded-full font-medium"
                          :class="{
                            'bg-secondary/10 text-secondary':
                              item.deliveryStatus === '现货',
                            'bg-heroic/10 text-heroic':
                              item.deliveryStatus === '预售',
                            'bg-accent/10 text-accent':
                              item.deliveryStatus === '预计',
                            'bg-gray-100 text-text-secondary': ![
                              '现货',
                              '预售',
                              '预计'
                            ].includes(item.deliveryStatus)
                          }"
                        >
                          {{ item.deliveryStatus }}
                        </view>
                        <view
                          class="mt-1.5 text-sm text-text-secondary line-clamp-1"
                        >
                          {{ item.deliveryInfo }}
                        </view>
                      </view>

                      <view
                        class="price-bubble relative flex items-center rounded-full px-4 py-2.5 shadow-md bg-gradient-to-r from-orange-100 to-pink-100"
                      >
                        <text
                          class="font-semi bold text-lg font-[Albert_Sans] bg-gradient-to-r from-orange-500 to-red-500 bg-clip-text text-transparent"
                          >¥</text
                        >
                        <text
                          class="text-[24px] leading-none font-[Albert_Sans] bg-gradient-to-r from-orange-500 to-red-500 bg-clip-text text-transparent ml-0.5"
                        >
                          {{ item.minPrice || "-" }}
                        </text>
                      </view>
                    </view>
                  </view>
                </view>
              </template>
            </up-swiper>
          </view>

          <!-- 轮播指示器 -->
          <view class="flex justify-center mt-4 space-x-1.5">
            <view
              v-for="(_, dotIndex) in newArrivalState.newArrivals"
              :key="dotIndex"
              class="h-2 rounded-full transition-all duration-300"
              :class="[
                dotIndex === newArrivalState.currentNewArrival
                  ? 'bg-primary w-4'
                  : 'bg-gray-300 w-2'
              ]"
            ></view>
          </view>
        </view>
      </template>
    </view>

    <!-- 福袋部分 -->
    <view class="flex items-center mt-6 w-[calc(100%-32px)] mx-4">
      <view
        class="flex items-center font-bold text-base text-text-primary relative mb-3"
      >
        <view class="inline-block w-1 h-4 bg-primary mr-2 rounded"></view>
        <text>精品福袋</text>
      </view>
    </view>
    <scroll-view
      scroll-x
      class="w-[calc(100%-32px)] mx-4 pb-2 whitespace-nowrap"
    >
      <view
        v-for="bag in luckyBags"
        :key="bag.title"
        class="inline-block align-top mr-4 last:mr-0"
        style="width: 240px"
      >
        <view
          class="rounded-2xl shadow-lg bg-gradient-to-r from-orange-100 to-pink-100 p-0.5"
        >
          <view
            class="bg-white rounded-2xl overflow-hidden flex flex-col items-center relative"
          >
            <view class="relative w-full">
              <up-image
                :src="bag.image"
                width="100%"
                height="160"
                mode="aspectFill"
                radius="16"
                @click="() => navigateToLuckyBag(bag)"
                class="cursor-pointer"
              ></up-image>
              <view
                class="absolute top-3 left-3 bg-gradient-to-r from-pink-500 to-orange-400 text-white text-xs font-bold px-3 py-1 rounded-full shadow-md z-10"
              >
                {{ bag.tag }}
              </view>
            </view>
            <view
              class="mt-4 text-base font-bold text-text-primary text-center"
            >
              {{ bag.title }}
            </view>
            <view class="mt-2 text-xs text-text-secondary text-center">
              {{ bag.subtitle }}
            </view>
            <view class="mt-4 mb-6 w-full flex justify-center">
              <button
                class="bg-gradient-to-r from-orange-400 to-pink-500 text-white font-bold text-sm py-2 px-8 rounded-full shadow hover:scale-105 transition-transform duration-200"
                @click="() => navigateToLuckyBag(bag)"
              >
                立即抢购
              </button>
            </view>
          </view>
        </view>
      </view>
    </scroll-view>
    <van-overlay :show="isShowFirstScreen" @click="isShowFirstScreen = false">
      <div
        class="fixed top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 w-4/5 bg-white rounded-2xl shadow-xl overflow-hidden"
      >
        <div class="p-6 text-center">
          <h3 class="text-lg font-bold mb-4 text-gray-800">
            {{ firstScreenContent?.title }}
          </h3>
          <div
            class="text-sm text-gray-600 px-4 py-3 bg-gray-50 rounded-lg mb-4"
            v-html="toHtml(firstScreenContent?.content)"
          ></div>
          <button
            class="mt-2 bg-primary text-white py-3 px-8 rounded-full text-sm font-medium hover:bg-primary-dark transition-colors"
            @click.stop="isShowFirstScreen = false"
          >
            我知道了
          </button>
        </div>
      </div>
    </van-overlay>
  </view>
</template>

<script setup lang="ts">
import { reactive, ref, computed, onMounted } from "vue"
import { onShow, onLoad as uniOnLoad } from "@dcloudio/uni-app"
import {
  getBanners,
  getNewProductList,
  listAcg,
  listProductByAcg,
  SPLASHSCREEN
} from "@/api/api.js"
import CacheImgs from "@/components/CacheImgs.vue"
import NavBar from "@/components/NavBar.vue"
import { SALES_TYPE } from "@/enum"
import store from "@/store"
import { toHtml } from "@/util/common/quilToHtml"
const showAcg = reactive({
  list: [],
  data: {}
})

const handleToAnnouncementCenter = () => {
  uni.navigateTo({
    url: "/pages/views/announcement/announcement"
  })
}

const handleToNoticesCenter = () => {
  uni.navigateTo({
    url: "/pages/views/noticesCenter/noticesCenter"
  })
}

// 新品速递数据
const newArrivalState = reactive({
  currentNewArrival: 0,
  newArrivals: []
})

// 轮播图数据
const bannerList = ref([])

// 福袋数据
const luckyBags = reactive([
  {
    title: "假面骑士DX福袋",
    subtitle: "超值盲盒，限量发售",
    image: "/static/images/lucky_bag.png",
    route: "/pages/views/product/detail?id=2",
    tag: "限时特惠"
  },
  {
    title: "高达模型福袋",
    subtitle: "拼装乐趣，限量抢购",
    image: "/static/images/lucky_bag2.png",
    route: "/pages/views/product/detail?id=3",
    tag: "新品推荐"
  }
])

const currentIpInfo = ref([])
const currentIp = ref("")
const currentSwiper = ref(0)
const warningBadge = computed(() => {
  return store.state.$warningBadge > 0 ? store.state.$warningBadge : 0
}) as unknown as number
const acgLoading = ref(true)
const newArrivalLoading = ref(true)
const isShowFirstScreen = ref(false)
const firstScreenContent = ref({ title: "", content: "" })
// 页面加载
onShow((options) => {
  getBannerList()
  getAcgList()
  getnewProductInfo()
  store.commit("GET_STSTEMCONFIG")

  showFirstScreen()
})

const showFirstScreen = async () => {
  if (!store.state.$hasAlreadyShowModal) {
    const res = await SPLASHSCREEN()
    if (res.code === 200) {
      isShowFirstScreen.value = true
      firstScreenContent.value = res.data
      // 更新状态
      store.commit("SET_HAS_ALREADY_SHOW_MODAL", true)
    }
  }
}

const getBannerList = async () => {
  const res = await getBanners()
  if (res.code === 200) {
    bannerList.value = res.data.map((item) => {
      return { ...item, url: item.imageUrl }
    })
  }
}

const getnewProductInfo = async () => {
  newArrivalLoading.value = true
  try {
    const res = await getNewProductList({ limit: 10 })
    if (res.code === 200) {
      const sendDayList = store.state.$systemConfig.filter(
        (item) => item.type === "ship_time"
      )
      const resData = res.data
      newArrivalState.newArrivals = resData.map((item) => {
        const deliveryStatus = SALES_TYPE.find(
          (enumItem) => enumItem.value === item.salesType
        ).label
        const sendDay =
          sendDayList.find((dayItem) => dayItem.code === item.salesType)
            ?.value || 0
        const deliveryDate = new Date(
          Date.now() + Number(sendDay) * 24 * 60 * 60 * 1000
        ).toLocaleDateString()
        return {
          ...item,
          image: item.files[0].url,
          route: `/pages/views/product/detail?id=${item.id}`,
          deliveryStatus,
          deliveryInfo: `预计 ${deliveryDate} 内发货`
        }
      })
    } else {
      uni.showToast({
        title: res.message,
        icon: "none"
      })
    }
  } finally {
    newArrivalLoading.value = false
  }
}

// 跳转到搜索页面
const goToSearchPage = () => {
  uni.navigateTo({
    url: "/pages/views/search/search"
  })
}

// 轮播图切换事件
const swiperChange = (e: any) => {
  currentSwiper.value = e.current
}

// 新品轮播图切换事件
const newArrivalSwiperChange = (e: any) => {
  newArrivalState.currentNewArrival = e.current
}

// 处理产品点击事件
const handleProductClick = (item) => {
  // 跳转到对应路由
  uni.navigateTo({
    url: `/pages/views/product/detail?id=${item.id}`
  })
}

const selectIp = async (id: string) => {
  currentIp.value = id

  // 如果该IP的数据已加载，直接使用缓存数据
  if (showAcg.data[id]) {
    currentIpInfo.value = showAcg.data[id]
  } else {
    // 否则加载该IP的数据
    try {
      // 修改参数格式为数组
      const res = await listProductByAcg([id])
      if (res.code === 200) {
        showAcg.data[id] = res.data
        currentIpInfo.value = res.data
      }
    } catch (error) {
      console.error("获取IP产品数据失败", error)
      currentIpInfo.value = []
    }
  }
}

// 跳转到详情页
const navigateToDetail = (product: any) => {
  uni.navigateTo({
    url: `/pages/views/product/detail?id=${product.id}`
  })
}

// 跳转到福袋详情
const navigateToLuckyBag = (bag) => {
  uni.navigateTo({
    url: bag.route
  })
}

const handleClickSwiper = (swiperIndex: number) => {
  const { id, toUrl, param } = bannerList.value[swiperIndex]
  const paramObj = param && JSON.parse(param)
  // const swiperId = bannerList.value[swiperIndex].id;
  // uni.navigateTo({
  //   url: `/pages/views/product/detail?id=${swiperId}`,
  // });

  uni.navigateTo({
    url: `/pages/views${toUrl}?id=${paramObj?.id}`
  })
}

// 获取ACG数据
const getAcgList = async () => {
  acgLoading.value = true
  try {
    const res = await listAcg()
    if (res.code === 200) {
      showAcg.list = res.data[0].children
      if (showAcg.list.length > 0) {
        showAcg.data = {}
        currentIp.value = showAcg.list[0].id
        try {
          const productRes = await listProductByAcg([currentIp.value])
          if (productRes.code === 200) {
            showAcg.data[currentIp.value] = productRes.data
            currentIpInfo.value = productRes.data
          }
        } catch (error) {
          console.error("获取IP产品数据失败", error)
        }
      }
    }
  } catch (error) {
    console.error("获取ACG数据失败", error)
  } finally {
    acgLoading.value = false
  }
}

const goToNewArrivalPage = () => {
  uni.navigateTo({
    url: "/pages/views/product/products"
  })
}
</script>

<style lang="scss"></style>
