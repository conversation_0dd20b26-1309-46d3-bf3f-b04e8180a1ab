import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

/// 订单批量操作组件
class OrderBatchActions extends StatelessWidget {
  const OrderBatchActions({
    super.key,
    required this.selectedCount,
    this.onBatchCancel,
    this.onBatchExport,
    this.onBatchPrint,
    this.onBatchShip,
    this.onSelectAll,
    this.onClearSelection,
  });

  final int selectedCount;
  final VoidCallback? onBatchCancel;
  final VoidCallback? onBatchExport;
  final VoidCallback? onBatchPrint;
  final VoidCallback? onBatchShip;
  final VoidCallback? onSelectAll;
  final VoidCallback? onClearSelection;

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: EdgeInsets.symmetric(horizontal: 16.w, vertical: 12.h),
      decoration: BoxDecoration(
        color: Theme.of(context).primaryColor.withValues(alpha: 0.1),
        border: Border(
          bottom: BorderSide(
            color: Colors.grey[200]!,
            width: 1,
          ),
        ),
      ),
      child: Row(
        children: [
          // 选择信息
          Expanded(
            child: Text(
              '已选择 $selectedCount 个订单',
              style: TextStyle(
                fontSize: 14.sp,
                fontWeight: FontWeight.w500,
                color: Theme.of(context).primaryColor,
              ),
            ),
          ),
          
          // 操作按钮
          Row(
            children: [
              // 全选按钮
              if (onSelectAll != null)
                _buildActionButton(
                  '全选',
                  Icons.select_all,
                  onSelectAll!,
                  Colors.blue,
                ),
              
              // 清空选择
              if (onClearSelection != null) ...[
                SizedBox(width: 8.w),
                _buildActionButton(
                  '清空',
                  Icons.clear_all,
                  onClearSelection!,
                  Colors.grey,
                ),
              ],
              
              // 批量发货
              if (onBatchShip != null) ...[
                SizedBox(width: 8.w),
                _buildActionButton(
                  '发货',
                  Icons.local_shipping,
                  onBatchShip!,
                  Colors.green,
                ),
              ],
              
              // 批量导出
              if (onBatchExport != null) ...[
                SizedBox(width: 8.w),
                _buildActionButton(
                  '导出',
                  Icons.download,
                  onBatchExport!,
                  Colors.orange,
                ),
              ],
              
              // 批量打印
              if (onBatchPrint != null) ...[
                SizedBox(width: 8.w),
                _buildActionButton(
                  '打印',
                  Icons.print,
                  onBatchPrint!,
                  Colors.purple,
                ),
              ],
              
              // 批量取消
              if (onBatchCancel != null) ...[
                SizedBox(width: 8.w),
                _buildActionButton(
                  '取消',
                  Icons.cancel,
                  () => _showCancelConfirmDialog(context),
                  Colors.red,
                ),
              ],
            ],
          ),
        ],
      ),
    );
  }

  /// 构建操作按钮
  Widget _buildActionButton(
    String text,
    IconData icon,
    VoidCallback onPressed,
    Color color,
  ) {
    return GestureDetector(
      onTap: onPressed,
      child: Container(
        padding: EdgeInsets.symmetric(horizontal: 12.w, vertical: 6.h),
        decoration: BoxDecoration(
          color: color.withValues(alpha: 0.1),
          borderRadius: BorderRadius.circular(16.r),
          border: Border.all(color: color.withValues(alpha: 0.3)),
        ),
        child: Row(
          mainAxisSize: MainAxisSize.min,
          children: [
            Icon(
              icon,
              size: 14.w,
              color: color,
            ),
            SizedBox(width: 4.w),
            Text(
              text,
              style: TextStyle(
                fontSize: 12.sp,
                color: color,
                fontWeight: FontWeight.w500,
              ),
            ),
          ],
        ),
      ),
    );
  }

  /// 显示取消确认对话框
  void _showCancelConfirmDialog(BuildContext context) {
    showDialog<void>(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('批量取消订单'),
        content: Text('确定要取消选中的 $selectedCount 个订单吗？此操作不可撤销。'),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('取消'),
          ),
          TextButton(
            onPressed: () {
              Navigator.of(context).pop();
              onBatchCancel?.call();
            },
            style: TextButton.styleFrom(
              foregroundColor: Colors.red,
            ),
            child: const Text('确定取消'),
          ),
        ],
      ),
    );
  }
}
