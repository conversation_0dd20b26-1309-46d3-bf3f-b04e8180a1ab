import 'package:dartz/dartz.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';

import 'package:soko/core/error/failures.dart';
import 'package:soko/core/models/paginated_data.dart';
import 'package:soko/core/models/query_params.dart';
import 'package:soko/core/network/network_info.dart';
import 'package:soko/features/order/domain/entities/order.dart';
import 'package:soko/features/order/domain/entities/order_create_request.dart';
import 'package:soko/features/order/domain/repositories/order_repository.dart';
import 'package:soko/features/order/data/datasources/order_api_service.dart';

/// 订单仓库实现
class OrderRepositoryImpl implements OrderRepository {

  OrderRepositoryImpl({
    required this.apiService,
    required this.networkInfo,
  });
  final OrderApiService apiService;
  final NetworkInfo networkInfo;

  @override
  Future<Either<Failure, PaginatedData<Order>>> getOrders(
      QueryParams params,) async {
    if (await networkInfo.isConnected) {
      try {
        final response = await apiService.getOrders(
          page: params.page,
          pageSize: params.pageSize,
          status: params.filters?['status'],
          startDate: params.filters?['startDate'],
          endDate: params.filters?['endDate'],
        );
        return Right(response);
      } catch (e) {
        return Left(ServerFailure(e.toString()));
      }
    } else {
      return const Left(NetworkFailure('网络连接失败'));
    }
  }

  @override
  Future<Either<Failure, Order>> getOrderById(String orderId) async {
    if (await networkInfo.isConnected) {
      try {
        final order = await apiService.getOrderById(orderId);
        return Right(order);
      } catch (e) {
        return Left(ServerFailure(e.toString()));
      }
    } else {
      return const Left(NetworkFailure('网络连接失败'));
    }
  }

  @override
  Future<Either<Failure, Order>> createOrder(
      Map<String, dynamic> orderData,) async {
    if (await networkInfo.isConnected) {
      try {
        final order = await apiService.createOrder(orderData);
        return Right(order);
      } catch (e) {
        return Left(ServerFailure(e.toString()));
      }
    } else {
      return const Left(NetworkFailure('网络连接失败'));
    }
  }

  @override
  Future<Either<Failure, Order>> createOrderFromRequest(
      OrderCreateRequest request,) async {
    if (await networkInfo.isConnected) {
      try {
        final orderData = request.toJson();
        final order = await apiService.createOrder(orderData);
        return Right(order);
      } catch (e) {
        return Left(ServerFailure(e.toString()));
      }
    } else {
      return const Left(NetworkFailure('网络连接失败'));
    }
  }

  @override
  Future<Either<Failure, Order>> updateOrderStatus(
      String orderId, String status,) async {
    if (await networkInfo.isConnected) {
      try {
        final order =
            await apiService.updateOrderStatus(orderId, {'status': status});
        return Right(order);
      } catch (e) {
        return Left(ServerFailure(e.toString()));
      }
    } else {
      return const Left(NetworkFailure('网络连接失败'));
    }
  }

  @override
  Future<Either<Failure, bool>> cancelOrder(String orderId) async {
    if (await networkInfo.isConnected) {
      try {
        await apiService.cancelOrder(orderId);
        return const Right(true);
      } catch (e) {
        return Left(ServerFailure(e.toString()));
      }
    } else {
      return const Left(NetworkFailure('网络连接失败'));
    }
  }

  @override
  Future<Either<Failure, bool>> confirmReceived(String orderId) async {
    if (await networkInfo.isConnected) {
      try {
        await apiService.confirmReceived(orderId);
        return const Right(true);
      } catch (e) {
        return Left(ServerFailure(e.toString()));
      }
    } else {
      return const Left(NetworkFailure('网络连接失败'));
    }
  }

  @override
  Future<Either<Failure, bool>> requestRefund(
      String orderId, String reason,) async {
    if (await networkInfo.isConnected) {
      try {
        await apiService.requestRefund(orderId, {'reason': reason});
        return const Right(true);
      } catch (e) {
        return Left(ServerFailure(e.toString()));
      }
    } else {
      return const Left(NetworkFailure('网络连接失败'));
    }
  }

  @override
  Future<Either<Failure, bool>> deleteOrder(String orderId) async {
    if (await networkInfo.isConnected) {
      try {
        await apiService.deleteOrder(orderId);
        return const Right(true);
      } catch (e) {
        return Left(ServerFailure(e.toString()));
      }
    } else {
      return const Left(NetworkFailure('网络连接失败'));
    }
  }
}

/// 订单仓库提供者
final orderRepositoryProvider = Provider<OrderRepository>((ref) {
  final apiService = ref.read(orderApiServiceProvider);
  final networkInfo = ref.read(networkInfoProvider);
  return OrderRepositoryImpl(
    apiService: apiService,
    networkInfo: networkInfo,
  );
});
