<template>
  <view class="flex flex-col min-h-screen bg-slate-50 custom-nav-page">
    <!-- 自定义导航栏 -->
    <NavBar title="订单详情" bgColor="#FFFFFF" :showBack="true"> </NavBar>

    <!-- 主内容区域 -->
    <view class="flex-1 p-4 space-y-4 pb-8">
      <!-- 加载状态 -->
      <view v-if="isLoading" class="space-y-4">
        <view class="bg-white rounded-xl overflow-hidden shadow-sm p-4">
          <view class="flex justify-between items-start mb-3">
            <view class="h-5 w-32 bg-slate-200 rounded-md"></view>
            <view class="h-5 w-20 bg-slate-200 rounded-md"></view>
          </view>
          <view class="h-px w-full my-3 bg-slate-200"></view>
          <view class="space-y-3">
            <view class="flex gap-3">
              <view class="h-16 w-16 rounded-lg bg-slate-200"></view>
              <view class="flex-1 space-y-2">
                <view class="h-4 w-3/4 bg-slate-200 rounded-md"></view>
                <view class="h-4 w-1/2 bg-slate-200 rounded-md"></view>
                <view class="h-4 w-1/4 bg-slate-200 rounded-md"></view>
              </view>
            </view>
          </view>
        </view>

        <view
          class="bg-white rounded-xl overflow-hidden shadow-sm p-4 space-y-3"
        >
          <view class="h-5 w-24 bg-slate-200 rounded-md"></view>
          <view class="h-4 w-full bg-slate-200 rounded-md"></view>
          <view class="h-4 w-3/4 bg-slate-200 rounded-md"></view>
        </view>

        <view
          class="bg-white rounded-xl overflow-hidden shadow-sm p-4 space-y-3"
        >
          <view class="h-5 w-24 bg-slate-200 rounded-md"></view>
          <view class="space-y-2">
            <view class="h-4 w-full bg-slate-200 rounded-md"></view>
            <view class="h-4 w-full bg-slate-200 rounded-md"></view>
            <view class="h-4 w-3/4 bg-slate-200 rounded-md"></view>
          </view>
        </view>
      </view>

      <!-- 订单不存在 -->
      <view
        v-else-if="!order"
        class="flex flex-col items-center justify-center py-10"
      >
        <view
          class="w-24 h-24 bg-slate-100 rounded-full flex items-center justify-center mb-6"
        >
          <van-icon name="warning-o" size="36" color="#666666" />
        </view>
        <text class="text-2xl font-semibold mb-2 text-text-primary"
          >订单不存在</text
        >
        <text class="text-text-secondary text-center mb-8"
          >未找到该订单信息</text
        >
        <view
          @click="goToOrdersList"
          class="bg-gradient-to-r from-primary to-neon text-white rounded-full px-8 py-3 font-medium shadow-sm"
        >
          返回订单列表
        </view>
      </view>

      <!-- 订单详情 -->
      <view v-else class="space-y-5">
        <!-- 订单状态卡片 -->
        <view
          class="bg-white rounded-xl overflow-hidden shadow-sm order-card"
          style="animation-delay: 0s"
        >
          <!-- 订单状态头部 -->
          <view class="p-4 border-b border-slate-100">
            <view class="flex justify-between items-center">
              <view class="flex items-center flex-wrap">
                <text class="text-sm font-medium text-text-primary">{{
                  order.id
                }}</text>
                <view
                  @click="copyOrderId"
                  class="ml-2 p-1 hover:bg-slate-50 rounded-full active:bg-slate-100"
                >
                  <van-icon name="description-o" size="12" color="#666666" />
                </view>
                <!-- 预售/现货标识 -->
                <view class="ml-3 flex items-center">
                  <view
                    :class="[
                      'px-2 py-1 rounded-full text-xs font-medium flex items-center h-4',
                      getOrderTypeClass(order)
                    ]"
                  >
                    <van-icon
                      :name="getOrderTypeIcon(order)"
                      size="12"
                      :color="getOrderTypeIconColor(order)"
                      class="mr-1"
                    />
                    <text>{{ getOrderTypeText(order) }}</text>
                  </view>
                </view>
              </view>
              <view :class="getStatusBadgeClass(order.status)">
                <van-icon
                  :name="getStatusIconName(order.status)"
                  class="mr-2"
                  size="14"
                  :color="getStatusIconColor(order.status)"
                />
                <text class="text-xs font-medium">{{
                  getStatusText(order.status)
                }}</text>
              </view>
            </view>
            <text class="text-xs text-text-secondary mt-2 block"
              >下单时间: {{ order.date }}</text
            >

            <!-- 添加支付倒计时 -->
            <view
              v-if="order.status === 'pending' && countdownTime"
              class="mt-2 flex items-center bg-warning bg-opacity-10 py-2 px-3 rounded-lg"
            >
              <van-icon name="clock-o" class="mr-2" size="14" color="#FAAD14" />
              <text class="text-xs text-warning font-medium"
                >请在
                <text class="font-bold">{{ countdownTime }}</text>
                内完成支付</text
              >
            </view>

            <!-- 订单进度 -->
            <view class="mt-6 mb-2">
              <view class="flex justify-between relative">
                <view
                  v-for="(step, index) in orderSteps"
                  :key="index"
                  class="flex flex-col items-center z-10 w-16"
                  style="margin-top: 1rem"
                >
                  <view
                    :class="[
                      'h-7 w-7 rounded-full flex items-center justify-center transition-all duration-300',
                      isStepCompleted(order.status, step.status)
                        ? 'bg-primary text-white shadow-sm'
                        : 'bg-gray-200 text-text-disabled',
                    ]"
                  >
                    <van-icon
                      :name="step.icon"
                      :color="
                        isStepCompleted(order.status, step.status)
                          ? '#FFFFFF'
                          : '#A3A3A3'
                      "
                      size="14"
                    />
                  </view>
                  <text
                    class="text-xs mt-2 text-center"
                    :class="
                      isStepCompleted(order.status, step.status)
                        ? 'text-primary font-medium'
                        : 'text-text-disabled'
                    "
                  >
                    {{ step.label }}
                  </text>
                </view>

                <!-- 连接线 -->
                <view
                  class="absolute top-[calc(1rem+0.875rem)] left-0 right-0 h-0.5 bg-gray-200"
                ></view>
                <view
                  :class="[
                    'absolute top-[calc(1rem+0.875rem)] left-0 h-0.5 bg-gradient-to-r from-primary to-heroic transition-all duration-500',
                    getProgressWidth(order.status),
                  ]"
                ></view>
              </view>
            </view>
          </view>

          <!-- 商品列表 -->
          <view class="p-4">
            <text class="text-sm font-semibold text-text-primary block mb-3"
              >商品信息</text
            >

            <view class="space-y-3">
              <view
                v-if="!order.items || order.items.length === 0"
                class="bg-slate-50 rounded-lg p-4 text-center"
              >
                <van-icon
                  name="warning-o"
                  size="24"
                  color="#666666"
                  class="mb-2"
                />
                <text class="text-sm text-text-secondary block"
                  >暂无商品信息</text
                >
              </view>
              <view
                v-else
                v-for="item in order.items"
                :key="item.id"
                class="bg-slate-50 rounded-xl p-3"
              >
                <view class="flex gap-3">
                  <view
                    class="relative h-20 w-20 bg-white rounded-lg overflow-hidden flex-shrink-0 border border-slate-100"
                  >
                    <image
                      :src="item.image || '/static/placeholder.png'"
                      :alt="item.name"
                      class="w-full h-full object-contain"
                    />
                  </view>
                  <view class="flex-1 min-w-0">
                    <view class="flex justify-between items-start">
                      <text
                        class="text-sm font-medium text-text-primary truncate pr-2 flex-1"
                        >{{ item.name || "未知商品" }}</text
                      >
                      <view class="flex-shrink-0 flex flex-col items-end">
                        <text class="text-sm text-primary font-semibold"
                          >¥{{
                            item.price ? formatPrice(item.price) : "0.00"
                          }}</text
                        >
                        <text class="text-xs text-text-secondary mt-1"
                          >x{{ item.quantity }}</text
                        >
                      </view>
                    </view>
                    <text
                      v-if="item.options"
                      class="text-xs text-text-secondary mt-1 block"
                      >{{ item.options }}</text
                    >
                    <!-- 申请退货按钮 -->
                    <view v-if="order.status === 'completed'" class="mt-2 flex justify-end">
                      <view
                        v-if="item.orderStatus !== 'AFTER_SALE'"
                        @click="handleAfterSale(item)"
                        class="px-3 py-1 bg-primary bg-opacity-10 text-primary text-xs font-medium rounded-full flex items-center hover:bg-opacity-20 active:bg-opacity-30 transition-colors duration-150"
                      >
                        <van-icon name="replay" size="12" class="mr-1" />
                        申请退货
                      </view>
                      <view
                        v-else
                        class="px-3 py-1 bg-purple-500 bg-opacity-10 text-purple-500 text-xs font-medium rounded-full flex items-center"
                      >
                        <van-icon name="replay" size="12" class="mr-1" />
                        售后中
                      </view>
                    </view>
                  </view>
                </view>
              </view>
            </view>

            <!-- 商品总数 -->
            <view
              v-if="order.items && order.items.length > 0"
              class="text-xs text-text-secondary mt-3 text-right"
            >
              共 {{ order.items.length }} 件商品
            </view>
          </view>
        </view>

        <!-- 配送信息 -->
        <view
          v-if="order.address || order.trackingNumber"
          class="bg-white rounded-xl overflow-hidden shadow-sm order-card"
          style="animation-delay: 0.1s"
        >
          <view class="p-4">
            <text class="text-sm font-semibold mb-3 block text-text-primary"
              >配送信息</text
            >

            <view
              v-if="order.trackingNumber"
              class="flex justify-between items-center mb-2"
            >
              <text class="text-sm text-text-primary">物流单号</text>
              <view class="flex items-center">
                <text class="text-sm text-text-primary mr-2">{{
                  order.trackingNumber
                }}</text>
                <view
                  @click="copyTrackingNumber"
                  class="px-2 py-0.5 bg-slate-100 rounded text-sm text-text-secondary active:bg-slate-200 transition-colors cursor-pointer hover:text-primary flex items-center justify-center"
                >
                  <text class="text-sm">复制</text>
                </view>
              </view>
            </view>

            <view
              v-if="order.trackingCompany"
              class="flex justify-between items-center mb-2"
            >
              <text class="text-sm text-text-primary">物流公司</text>
              <text class="text-sm text-text-primary">{{
                order.trackingCompany
              }}</text>
            </view>

            <view
              v-if="order.estimatedDelivery"
              class="flex justify-between items-center mb-2"
            >
              <text class="text-sm text-text-primary">预计送达</text>
              <text class="text-sm text-text-primary">{{
                order.estimatedDelivery
              }}</text>
            </view>

            <view v-if="order.address" class="mt-4 bg-slate-50 p-3 rounded-xl">
              <view class="flex">
                <van-icon
                  name="location-o"
                  class="mr-2 flex-shrink-0 mt-0.5"
                  size="14"
                  color="#666666"
                />

                <view class="flex-1">
                  <view class="flex items-center flex-wrap">
                    <text class="text-sm font-medium text-text-primary mr-2">{{
                      order.recipientName || "收件人"
                    }}</text>
                    <text class="text-sm text-text-secondary">{{
                      order.recipientPhone || ""
                    }}</text>
                  </view>
                  <text class="text-sm text-text-secondary mt-2 block">{{
                    order.address
                  }}</text>
                </view>
                <!-- 添加修改地址按钮 - 仅在待付款或待发货状态显示 -->
                <view v-if="canEditAddress" class="flex-shrink-0 ml-2">
                  <view
                    @tap="onEditAddressTap"
                    class="px-2 py-1 bg-primary bg-opacity-10 text-primary text-xs font-medium rounded-full flex items-center hover:bg-opacity-20 active:bg-opacity-30 transition-colors duration-150"
                  >
                    <van-icon name="edit" size="12" class="mr-1" />
                    修改
                  </view>
                </view>
              </view>
            </view>
          </view>
        </view>

        <!-- 价格信息 -->
        <view
          class="bg-white rounded-xl overflow-hidden shadow-sm order-card"
          style="animation-delay: 0.2s"
        >
          <view class="p-4">
            <text class="text-sm font-semibold mb-3 block text-text-primary"
              >价格信息</text
            >

            <view class="space-y-2">
              <view class="flex justify-between items-center">
                <text class="text-sm text-text-primary">商品金额</text>
                <text class="text-sm text-text-primary"
                  >¥{{ formatPrice(getSubtotal(order.items)) }}</text
                >
              </view>
              <view class="flex justify-between items-center">
                <text class="text-sm text-text-primary">优惠金额</text>
                <text class="text-sm text-success"
                  >-¥{{ formatPrice(order.discountAmount || 0) }}</text
                >
              </view>
              <view class="flex justify-between items-center">
                <text class="text-sm text-text-primary">国内运费</text>
                <text class="text-sm text-text-primary">{{
                  Number(order.domesticFreight) > 0
                    ? `¥${formatPrice(order.domesticFreight)}`
                    : "免运费"
                }}</text>
              </view>
              <view class="flex justify-between items-center">
                <text class="text-sm text-text-primary">国际运费</text>
                <text class="text-sm text-text-primary">{{
                  Number(order.internationalFreight) > 0
                    ? `¥${formatPrice(order.internationalFreight)}`
                    : "免运费"
                }}</text>
              </view>

              <view
                v-if="order.paymentMethod"
                class="flex justify-between items-center"
              >
                <text class="text-sm text-text-primary">支付方式</text>
                <view class="flex items-center">
                  <van-icon
                    name="credit-pay"
                    class="mr-2"
                    size="12"
                    color="#666666"
                  />
                  <text class="text-sm text-text-primary">{{
                    order.paymentMethod
                  }}</text>
                </view>
              </view>

              <view class="h-px bg-slate-100 my-3"></view>

              <view class="flex justify-between items-center">
                <text class="text-sm font-semibold text-text-primary"
                  >实付金额</text
                >
                <text class="text-xl font-bold text-primary"
                  >¥{{ formatPrice(order.total) }}</text
                >
              </view>
            </view>
          </view>
        </view>

        <!-- 更多订单信息 -->
        <view
          class="bg-white rounded-xl overflow-hidden shadow-sm order-card"
          style="animation-delay: 0.25s"
        >
          <view
            class="p-4 flex justify-between items-center cursor-pointer"
            @click="toggleMoreOrderInfo"
          >
            <text class="text-sm font-semibold text-text-primary"
              >更多订单信息</text
            >
            <van-icon
              :name="showMoreOrderInfo ? 'arrow-up' : 'arrow-down'"
              size="14"
              color="#666666"
            />
          </view>
          <view v-if="showMoreOrderInfo" class="p-4 border-t border-slate-100">
            <view class="space-y-3">
              <view
                v-if="canViewTradeSnapshot(order.status)"
                class="flex justify-between items-start cursor-pointer"
                @click="goToTradeSnapshot(order?.id)"
              >
                <text class="text-sm text-text-primary whitespace-nowrap pt-0.5"
                  >交易快照</text
                >
                <view
                  class="flex items-center text-text-secondary hover:text-primary active:text-primary transition-colors ml-8"
                >
                  <text class="text-xs mr-1"
                    >发生交易争议时，可作为判断依据</text
                  >
                  <van-icon name="arrow" size="12" />
                </view>
              </view>

              <view class="flex justify-between items-start">
                <text class="text-sm text-text-primary whitespace-nowrap pt-0.5"
                  >订单号</text
                >
                <view class="flex items-start ml-8">
                  <text
                    class="text-sm text-text-primary break-all max-w-[180px] text-right"
                    style="line-height: 1.4"
                  >
                    {{ order.orderNo || order.id || "暂无" }}
                  </text>
                  <view class="flex items-center ml-2 flex-shrink-0">
                    <text class="text-sm text-gray-400 mx-1">|</text>
                    <view
                      @click="copyOrderNo"
                      class="px-2 py-0.5 bg-slate-100 rounded text-sm text-text-secondary active:bg-slate-200 transition-colors cursor-pointer hover:text-primary flex items-center justify-center"
                    >
                      <van-icon name="copy" size="14" class="mr-1" />
                      <text>复制</text>
                    </view>
                  </view>
                </view>
              </view>

              <view class="flex justify-between items-start">
                <text class="text-sm text-text-primary whitespace-nowrap pt-0.5"
                  >支付宝交易号</text
                >
                <view class="flex items-start ml-8">
                  <text
                    class="text-sm text-text-primary break-all max-w-[180px] text-right"
                    style="line-height: 1.4"
                  >
                    {{ order.alipayNo || "暂无" }}
                  </text>
                  <view
                    v-if="order.alipayNo"
                    class="flex items-center ml-2 flex-shrink-0"
                  >
                    <text class="text-sm text-gray-400 mx-1">|</text>
                    <view
                      @click="copyAlipayNo"
                      class="px-2 py-0.5 bg-slate-100 rounded text-sm text-text-secondary active:bg-slate-200 transition-colors cursor-pointer hover:text-primary flex items-center justify-center"
                    >
                      <van-icon name="copy" size="14" class="mr-1" />
                      <text>复制</text>
                    </view>
                  </view>
                </view>
              </view>

              <view class="flex justify-between items-start">
                <text class="text-sm text-text-primary whitespace-nowrap pt-0.5"
                  >创建时间</text
                >
                <text class="text-sm text-text-primary ml-8">{{
                  order.date || "暂无"
                }}</text>
              </view>
              <view class="flex justify-between items-start">
                <text class="text-sm text-text-primary whitespace-nowrap pt-0.5"
                  >付款时间</text
                >
                <text class="text-sm text-text-primary ml-8">{{
                  order.payTime || "暂无"
                }}</text>
              </view>
              <view
                v-if="
                  (order.status === 'shipping' ||
                    order.status === 'completed') &&
                  order.shipTime
                "
                class="flex justify-between items-start"
              >
                <text class="text-sm text-text-primary whitespace-nowrap pt-0.5"
                  >发货时间</text
                >
                <text class="text-sm text-text-primary ml-8">{{
                  order.shipTime || "暂无"
                }}</text>
              </view>
            </view>
          </view>
        </view>

        <!-- 底部操作区 -->
        <view
          class="mt-8 mb-8 order-actions-row order-card"
          style="animation-delay: 0.3s"
        >
          <view
            v-if="showCancelButton(order.status)"
            class="min-w-[90px] text-center bg-white border border-gray-300 rounded-full px-5 py-2.5 hover:bg-slate-50 active:bg-slate-100 transition-all duration-300 shadow-sm"
            @click="handleCancelOrder"
          >
            <text class="text-sm text-text-primary">取消订单</text>
          </view>
          <view
            v-if="showDeleteButton(order.status)"
            class="min-w-[90px] text-center bg-white border border-gray-300 rounded-full px-5 py-2.5 hover:bg-slate-50 active:bg-slate-100 transition-all duration-300 shadow-sm"
            @click="handleDeleteOrder"
          >
            <text class="text-sm text-text-primary">删除订单</text>
          </view>
          <view
            v-if="showLogisticsButton(order.status)"
            class="min-w-[90px] text-center bg-white border border-gray-300 rounded-full px-5 py-2.5 hover:bg-slate-50 active:bg-slate-100 transition-all duration-300 shadow-sm"
            @click="handleViewLogistics"
          >
            <text class="text-sm text-text-primary">查看物流</text>
          </view>
          <view
            class="min-w-[100px] text-center bg-gradient-to-r from-primary to-neon text-white rounded-full px-5 py-2.5 shadow-sm transition-all duration-300"
            @click="handleActionButton(order)"
          >
            <text class="text-sm font-medium">{{
              getActionButtonText(order.status)
            }}</text>
          </view>
        </view>
      </view>
    </view>

    <!-- 地址选择抽屉组件 -->
    <van-popup
      v-model:show="showAddressDrawer"
      position="bottom"
      round
      closeable
      close-icon-position="top-right"
      :style="{ height: '70%' }"
      @close="closeAddressDrawer"
    >
      <view class="p-4 pb-safe">
        <view class="text-lg font-semibold mb-6 text-center text-text-primary"
          >选择收货地址</view
        >

        <!-- 地址列表 -->
        <view
          v-if="addressList.length > 0"
          class="space-y-4 max-h-[calc(70vh-180px)] overflow-y-auto pb-4"
        >
          <view
            v-for="(item, index) in addressList"
            :key="index"
            class="bg-white rounded-xl p-4 border border-slate-100 shadow-sm flex justify-between items-center transition-all duration-200 active:bg-slate-50 relative"
            :class="{ 'border-primary': item._isDefault }"
            @tap="selectDeliveryAddress(item)"
          >
            <!-- 移除角标默认地址标记 -->

            <view class="flex-1">
              <view class="flex items-center mb-2">
                <text class="text-base font-medium text-text-primary">{{
                  item.name
                }}</text>
                <text class="ml-2 text-sm text-text-secondary">{{
                  item.phone
                }}</text>
                <!-- 只保留手机号右边的默认地址标签 -->
                <view v-if="item._isDefault" class="ml-2">
                  <text
                    class="text-xs text-primary px-1.5 py-0.5 bg-primary bg-opacity-10 rounded-full"
                    >默认</text
                  >
                </view>
              </view>
              <view class="text-sm text-text-secondary leading-relaxed">
                {{ item.province }}{{ item.city }}{{ item.district
                }}{{ item.detail || item.address }}
              </view>
              <!-- 移除额外的默认地址标记 -->
            </view>
            <view
              class="ml-2 w-8 h-8 flex items-center justify-center rounded-full bg-primary bg-opacity-10"
            >
              <van-icon name="arrow" size="14" color="var(--primary-color)" />
            </view>
          </view>
        </view>

        <!-- 空状态 -->
        <view
          v-else-if="!addressLoading"
          class="flex flex-col items-center justify-center py-12"
        >
          <van-icon name="location-o" size="48" color="#dddddd" />
          <text class="mt-4 text-text-secondary text-base">暂无收货地址</text>
          <view
            @tap="goToAddAddress"
            class="mt-6 bg-gradient-to-r from-primary to-neon text-white rounded-full px-8 py-2.5 shadow-sm"
          >
            添加新地址
          </view>
        </view>

        <!-- 加载状态 -->
        <view v-else class="flex flex-col items-center justify-center py-12">
          <van-loading type="spinner" color="var(--primary-color)" />
          <text class="mt-4 text-text-secondary">加载中...</text>
        </view>

        <!-- 底部按钮 -->
        <view
          v-if="addressList.length > 0"
          class="pt-4 pb-2 px-4 flex justify-center border-t border-slate-100 mt-4"
        >
          <view
            @tap="goToAddAddress"
            class="w-full py-3 bg-gradient-to-r from-primary to-neon text-white rounded-full flex items-center justify-center shadow-sm active:opacity-90"
          >
            <van-icon name="plus" class="mr-2" size="16" />
            <text class="font-medium">新增收货地址</text>
          </view>
        </view>
      </view>
    </van-popup>
  </view>
</template>

<script lang="ts" setup>
import { ref, onMounted, computed, onUnmounted, watch } from "vue";
import moment from "moment";
import NavBar from "@/components/NavBar.vue";
import "@/components/falcon/FaIcon.vue";
import {
  getOrderDetail,
  confirmReceiveOrder,
  updateOrderAddress,
  getAddressList,
  cancelOrder,
  deleteOrder,
} from "@/api/api";
import { onShow } from "@dcloudio/uni-app";
import { Icon as VanIcon } from "vant";
import { payOrder } from "@/api/api";
import { SALES_TYPE } from "@/enum/index";

// 订单状态类型
type OrderStatus =
  | "pending"
  | "paid"
  | "shipping"
  | "completed"
  | "cancelled"
  | "afterSale";

// 订单项类型
interface OrderItem {
  id: number;
  name?: string;
  productName?: string;
  price?: string | number;
  productPrice?: string | number;
  quantity: number;
  specification?: string;
  image?: string;
  fileUrl?: string;
  options?: string;
  productId?: string;
  skuId?: string;
  productSkuId?: string;
  totalPrice?: string | number;
  realPrice?: string | number;
  orderId?: string;
  orderNo?: string;
  isAfterSale?: boolean;
  orderStatus?: string;
  salesType?: 'STOCK' | 'PRESALE'; // 销售类型：STOCK现货，PRESALE预售
}

// 订单类型
interface Order {
  id: string;
  orderNo?: string;
  date: string;
  createdTime?: number;
  status: OrderStatus;
  orderStatus?: string;
  items: OrderItem[];
  total: string | number;
  totalAmount?: string | number;
  payAmount?: string | number;
  shipping: string | number;
  domesticFreight: string | number;
  internationalFreight: string | number;
  discountAmount: string | number;
  address?: string;
  trackingNumber?: string;
  trackingNo?: string;
  trackingCompany?: string;
  paymentMethod?: string;
  payType?: string;
  recipientName?: string;
  receiverName?: string;
  recipientPhone?: string;
  receiverPhone?: string;
  receiverProvince?: string;
  receiverCity?: string;
  receiverDistrict?: string;
  receiverAddress?: string;
  estimatedDelivery?: string;
  orderTime?: number;
  expireTime?: number;
  alipayNo?: string;
  payTime?: string;
  shipTime?: string;
  version?: number;
}

// 状态步骤
const orderSteps = [
  { status: 'pending', label: '待付款', icon: 'credit-pay' },
  { status: 'paid', label: '待发货', icon: 'clock-o' },
  { status: 'shipping', label: '待收货', icon: 'logistics' },
  { status: 'completed', label: '已完成', icon: 'passed' },
];

const orderId = ref("");
const isLoading = ref(true);
const order = ref<Order | null>(null);
const countdownTime = ref("");
const orderTimeParam = ref(0);
const countdownTimer = ref<ReturnType<typeof setInterval> | null>(null);
const showMoreOrderInfo = ref(false);

// 地址选择相关变量
const showAddressDrawer = ref(false);
const addressList = ref([]);
const addressLoading = ref(false);

// 是否可以编辑地址（待付款或已付款待发货状态）
const canEditAddress = computed(() => {
  if (!order.value) return false;
  const canEdit =
    order.value.status === "pending" || order.value.status === "paid";
  return canEdit;
});

// 切换更多订单信息显示
function toggleMoreOrderInfo() {
  showMoreOrderInfo.value = !showMoreOrderInfo.value;
}

// 跳转到交易快照
function goToTradeSnapshot(orderIdValue?: string) {
  if (orderIdValue) {
    try {
      uni.navigateTo({
        url: `/pages/views/order/snapshot-list?orderId=${orderIdValue}`,
        success: function () {},
        fail: function (err) {
          uni.showToast({
            title: "页面跳转失败，请稍后再试",
            icon: "none",
          });
        },
      });
    } catch (error) {
      uni.showToast({
        title: "页面跳转异常，请联系客服",
        icon: "none",
      });
    }
  } else {
    uni.showToast({
      title: "订单信息不完整，无法查看交易快照",
      icon: "none",
    });
  }
}

onMounted(() => {
  // 获取路由参数
  const query = uni.getStorageSync("currentOrderId") || {};
  let id = "";
  let orderTime = 0;

  try {
    // @ts-ignore
    const eventChannel = getOpenerEventChannel();
    eventChannel.on("acceptDataFromOpener", function (data: any) {
      id = data.id;
      orderTime = data.orderTime || 0;
    });
  } catch (error) {
    // console.log('No event channel available');
  }

  // 尝试从URL参数获取
  const pages = getCurrentPages();
  const currentPage = pages[pages.length - 1];
  // @ts-ignore
  if (currentPage && currentPage.$page && currentPage.$page.options) {
    // @ts-ignore
    id = currentPage.$page.options.id || "";
    // @ts-ignore
    orderTime = Number(currentPage.$page.options.orderTime) || 0;
  }

  orderId.value = id;
  orderTimeParam.value = orderTime;

  if (orderId.value) {
    isLoading.value = true;
    // 调用获取订单详情API
    getOrderDetail(orderId.value)
      .then((res: any) => {
        const data = res[1];

        if (!data) {
          uni.showToast({
            title: "获取订单详情失败: 数据为空",
            icon: "none",
          });
          order.value = null;
          isLoading.value = false;
          return;
        }

        if (data) {
          // 直接使用原始数据显示
          const rawOrder: Order = {
            id: data.id || "", // 存储数据库原始ID
            orderNo: data.orderNo,
            date: data.createdTime
              ? moment(data.createdTime).format("YYYY-MM-DD HH:mm:ss")
              : "",
            createdTime: data.createdTime,
            status: convertOrderStatus(data.orderStatus),
            orderStatus: data.orderStatus,
            items: [],
            total: "0",
            totalAmount: data.totalAmount,
            payAmount: data.payAmount,
            shipping: "0",
            domesticFreight: "0",
            internationalFreight: "0",
            discountAmount: "0",
            address: "",
            trackingNo: data.trackingNo,
            trackingNumber: data.trackingNo || "",
            trackingCompany: data.trackingCompany || "",
            payType: data.payType,
            recipientName: data.receiverName || "",
            receiverName: data.receiverName,
            recipientPhone: data.receiverPhone || "",
            receiverPhone: data.receiverPhone,
            receiverProvince: data.receiverProvince,
            receiverCity: data.receiverCity,
            receiverDistrict: data.receiverDistrict,
            receiverAddress: data.receiverAddress,
            orderTime: data.orderTime,
            expireTime: data.expireTime,
            alipayNo: data.alipayNo, // 使用 alipayNo
            payTime: data.payTime // 使用 payTime, 并假设后端直接返回格式化好的字符串或前端不需要 moment 格式化
              ? typeof data.payTime === "number" // 如果是时间戳，则格式化
                ? moment(data.payTime).format("YYYY-MM-DD HH:mm:ss")
                : data.payTime // 如果已经是字符串，直接使用
              : "",
            shipTime: data.shipTime // 发货时间
              ? typeof data.shipTime === "number"
                ? moment(data.shipTime).format("YYYY-MM-DD HH:mm:ss")
                : data.shipTime
              : "",
            version: data.version, // 尝试获取 version
          };

          // 处理金额
          if (data.payAmount) rawOrder.total = data.payAmount.toString();
          else if (data.totalAmount)
            rawOrder.total = data.totalAmount.toString();

          if (data.domesticFreight)
            rawOrder.domesticFreight = data.domesticFreight.toString();
          if (data.internationalFreight)
            rawOrder.internationalFreight =
              data.internationalFreight.toString();

          // 计算总运费
          rawOrder.shipping = (
            (Number(rawOrder.domesticFreight) || 0) +
            (Number(rawOrder.internationalFreight) || 0)
          ).toString();

          if (data.discountAmount)
            rawOrder.discountAmount = data.discountAmount.toString();

          // 地址信息
          if (data.receiverProvince || data.receiverCity) {
            rawOrder.address = [
              data.receiverProvince || "",
              data.receiverCity || "",
              data.receiverDistrict || "",
              data.receiverAddress || "",
            ]
              .filter(Boolean)
              .join(" ");
          }

          // 物流信息
          if (data.trackingNo) {
            rawOrder.trackingNumber = data.trackingNo;
            rawOrder.trackingCompany = data.trackingCompany || "";
          }
          rawOrder.paymentMethod = getPaymentMethod(data.payType);

          // 处理商品项
          if (Array.isArray(data.orderItems) && data.orderItems.length > 0) {
            rawOrder.items = data.orderItems.map((item: any) => ({
              id: item.id || 0,
              productId: item.productId,
              productSkuId: item.productSkuId,
              productName: item.productName,
              productPrice: item.productPrice,
              quantity: item.quantity || 1,
              totalPrice: item.totalPrice,
              realPrice: item.realPrice,
              specification: item.specification,
              fileUrl: item.fileUrl,
              orderId: item.orderId,
              orderNo: item.orderNo,

              name: item.productName || "未命名商品",
              price: (item.productPrice || "0").toString(),
              image: item.fileUrl || "/static/placeholder.png",
              options: item.specification
                ? formatSpecification(item.specification)
                : "",
              skuId: item.productSkuId || "",
              isAfterSale: item.isAfterSale,
              orderStatus: item.orderStatus,
            }));
          }

          // 再次确保售后状态正确设置
          if (
            data.orderStatus === "AFTER_SALE" &&
            rawOrder.status !== "afterSale"
          ) {
            rawOrder.status = "afterSale";
          }

          order.value = rawOrder;

          // 如果是待付款状态，启动倒计时
          if (order.value && order.value.status === "pending") {
            startCountdown();
          }
        }
      })
      .finally(() => {
        isLoading.value = false;
      });
  } else {
    // 如果没有订单ID，则不加载数据
    order.value = null;
    isLoading.value = false;
    uni.showToast({
      title: "订单ID不存在",
      icon: "none",
    });
  }
});

// 转换订单状态
function convertOrderStatus(status: string): OrderStatus {
  // 检查状态是否为空
  if (!status) {
    return "pending";
  }

  // 处理大小写不敏感的状态匹配
  const normalizedStatus = status.toUpperCase();

  const statusMap: { [key: string]: OrderStatus } = {
    UNPAID: "pending",
    PAID: "paid",
    SHIPPED: "shipping",
    RECEIVED: "completed",
    COMPLETED: "completed",
    CANCELLED: "cancelled",
    CLOSED: "cancelled",
    AFTER_SALE: "afterSale",
    REFUNDED: "afterSale",
    RETURNED: "afterSale",
  };

  // 判断是否包含"售后"或"退款"关键词
  if (
    typeof status === "string" &&
    (status.toLowerCase().includes("after") ||
      status.toLowerCase().includes("sale") ||
      status.includes("售后") ||
      status.includes("退款") ||
      status.includes("退货") ||
      status.toLowerCase().includes("refund") ||
      status.toLowerCase().includes("return"))
  ) {
    return "afterSale";
  }

  // 从映射表中查找状态，优先使用标准化后的状态
  const convertedStatus =
    statusMap[normalizedStatus] || statusMap[status] || "pending";

  // AFTER_SALE特殊处理
  if (normalizedStatus === "AFTER_SALE" && convertedStatus === "pending") {
    return "afterSale";
  }

  return convertedStatus;
}

// 格式化规格信息
function formatSpecification(specificationJson: any): string {
  try {
    const spec =
      typeof specificationJson === "string"
        ? JSON.parse(specificationJson)
        : specificationJson;
    if (!spec || typeof spec !== "object") return "";

    return Object.entries(spec)
      .map(([key, value]) => `${key}: ${value}`)
      .join(", ");
  } catch (error) {
    // console.error('解析规格信息失败:', error);
    return "";
  }
}

// 获取支付方式显示文本
function getPaymentMethod(payType: string): string {
  const payMap: { [key: string]: string } = {
    ALIPAY: "支付宝",
    WECHAT: "微信支付",
    BANK: "银行卡",
  };
  return payMap[payType] || "未知方式";
}

// 启动倒计时
function startCountdown() {
  if (!order.value) return;

  // 仅在待付款状态下启动倒计时
  if (order.value.status !== "pending") return;

  // 清除可能存在的定时器
  if (countdownTimer.value !== null) {
    clearInterval(countdownTimer.value);
  }

  // 计算剩余时间
  updateCountdown();

  // 设置定时器，每秒更新一次
  countdownTimer.value = setInterval(() => {
    updateCountdown();
  }, 1000);
}

// 更新倒计时显示
function updateCountdown() {
  if (!order.value) return;

  const now = Date.now();
  let remainingTime = 0;

  // 使用服务器返回的过期时间
  if (order.value.expireTime) {
    // 直接使用expireTime作为截止时间戳
    remainingTime = order.value.expireTime - now;
  } else if (order.value.orderTime) {
    // 如果没有expireTime但有orderTime，使用订单时间+15分钟作为备选
    const timeLimit = 15 * 60 * 1000; // 15分钟
    const expiryTime = order.value.orderTime + timeLimit;
    remainingTime = expiryTime - now;
  } else {
    // 没有任何时间信息，不显示倒计时
    countdownTime.value = "";
    return;
  }

  // 检查是否过期
  if (remainingTime <= 0) {
    countdownTime.value = "已超时";
    if (countdownTimer !== null) {
      clearInterval(countdownTimer.value);
      countdownTimer.value = null;
    }

    // 如果订单已过期，可能需要刷新状态
    if (order.value && order.value.status === "pending") {
      uni.showToast({
        title: "订单支付已超时",
        icon: "none",
      });

      // 可以选择刷新页面或重新获取订单详情
      setTimeout(() => {
        // 重新获取订单状态
        refreshOrderData();
      }, 2000);
    }

    return;
  }

  // 计算分钟和秒数
  const minutes = Math.floor(remainingTime / 60000);
  const seconds = Math.floor((remainingTime % 60000) / 1000);

  // 格式化显示
  countdownTime.value = `${minutes.toString().padStart(2, "0")}:${seconds
    .toString()
    .padStart(2, "0")}`;
}

// 计算商品小计
function getSubtotal(items: OrderItem[]): number {
  return items.reduce((sum, item) => {
    const price =
      typeof item.price === "string"
        ? parseFloat(item.price) || 0
        : typeof item.price === "number"
        ? item.price
        : 0;
    return sum + price * item.quantity;
  }, 0);
}

// 复制订单号
function copyOrderId() {
  if (order.value) {
    uni.setClipboardData({
      data: order.value.id,
      success: () => {
        uni.showToast({
          title: "订单号复制成功",
          icon: "success",
        });
      },
    });
  }
}

// 复制物流单号
function copyTrackingNumber() {
  if (order.value?.trackingNumber) {
    uni.setClipboardData({
      data: order.value.trackingNumber,
      success: () => {
        uni.showToast({
          title: "物流单号已复制",
          icon: "success",
          duration: 2000,
        });
      },
      fail: () => {
        uni.showToast({
          title: "复制失败，请重试",
          icon: "none",
          duration: 2000,
        });
      },
    });
  }
}

// 复制订单号 (orderNo)
function copyOrderNo() {
  if (order.value?.orderNo) {
    uni.setClipboardData({
      data: order.value.orderNo,
      success: () => {
        uni.showToast({
          title: "订单号复制成功",
          icon: "success",
        });
      },
    });
  } else if (order.value?.id) {
    uni.setClipboardData({
      data: order.value.id,
      success: () => {
        uni.showToast({
          title: "订单号复制成功",
          icon: "success",
        });
      },
    });
  }
}

// 复制支付宝交易号
function copyAlipayNo() {
  if (order.value?.alipayNo) {
    uni.setClipboardData({
      data: order.value.alipayNo,
      success: () => {
        uni.showToast({
          title: "支付宝交易号复制成功",
          icon: "success",
        });
      },
    });
  }
}

// 检查步骤是否完成
function isStepCompleted(
  currentStatus: OrderStatus,
  stepStatus: string
): boolean {
  const statusOrder = ['pending', 'paid', 'shipping', 'completed'];

  // 如果订单已取消，保留之前已经完成的步骤
  if (currentStatus === 'cancelled') {
    const pendingIndex = statusOrder.indexOf('pending');
    const stepIndex = statusOrder.indexOf(stepStatus);
    return stepIndex <= pendingIndex;
  }

  const currentIndex = statusOrder.indexOf(currentStatus);
  const stepIndex = statusOrder.indexOf(stepStatus);

  return stepIndex <= currentIndex;
}

// 获取进度条宽度
function getProgressWidth(status: OrderStatus): string {
  const widths = {
    pending: 'w-0',
    paid: 'w-1/3',       // 4个步骤
    shipping: 'w-2/3',   // 4个步骤
    completed: 'w-full', // 4个步骤
    cancelled: 'w-0',
  };
  return widths[status] || 'w-0';
}

// 状态样式
function getStatusBadgeClass(status: OrderStatus): string {
  switch (status) {
    case "pending":
      return "flex items-center border rounded-full px-2 py-1 h-6 border-warning text-warning";
    case "paid":
      return "flex items-center border rounded-full px-2 py-1 h-6 border-primary text-primary";
    case "shipping":
      return "flex items-center border rounded-full px-2 py-1 h-6 border-accent text-accent";
    case "completed":
      return "flex items-center border rounded-full px-2 py-1 h-6 border-success text-success";
    case "afterSale":
      return "flex items-center border rounded-full px-2 py-1 h-6 border-purple-500 text-purple-500";
    case "cancelled":
      return "flex items-center border rounded-full px-2 py-1 h-6 border-destructive text-destructive";
    default:
      return "flex items-center border rounded-full px-2 py-1 h-6 border-mecha text-mecha";
  }
}

// 状态图标名称
function getStatusIconName(status: OrderStatus): string {
  switch (status) {
    case "pending":
      return "clock-o";
    case "paid":
      return "gold-coin-o";
    case "shipping":
      return "logistics";
    case "completed":
      return "passed";
    case "afterSale":
      return "replay";
    case "cancelled":
      return "close";
    default:
      return "circle";
  }
}

// 状态图标颜色
function getStatusIconColor(status: OrderStatus): string {
  switch (status) {
    case "pending":
      return "#FAAD14"; // warning
    case "paid":
      return "#EF4444"; // primary
    case "shipping":
      return "#3B82F6"; // accent
    case "completed":
      return "#52C41A"; // success
    case "afterSale":
      return "#8B5CF6"; // purple
    case "cancelled":
      return "#FF4D4F"; // destructive
    default:
      return "#6B7280"; // mecha
  }
}

// 订单类型判断 - 根据salesType字段判断是否为预售订单
function isPresaleOrder(order: Order | null): boolean {
  if (!order || !order.items) return false;
  
  // 检查订单商品中是否有预售类型
  return order.items.some(item => {
    // 根据salesType字段判断：PRESALE为预售，STOCK为现货
    return item.salesType === 'PRESALE';
  });
}

// 获取订单类型文本
function getOrderTypeText(order: Order | null): string {
  const salesType = isPresaleOrder(order) ? 'PRESALE' : 'STOCK';
  const typeItem = SALES_TYPE.find(item => item.value === salesType);
  return typeItem ? typeItem.label : '现货';
}

// 获取订单类型样式类
function getOrderTypeClass(order: Order | null): string {
  if (isPresaleOrder(order)) {
    return 'bg-orange-100 text-orange-600 border border-orange-200';
  }
  return 'bg-green-100 text-green-600 border border-green-200';
}

// 获取订单类型图标
function getOrderTypeIcon(order: Order | null): string {
  return isPresaleOrder(order) ? 'clock-o' : 'goods-collect-o';
}

// 获取订单类型图标颜色
function getOrderTypeIconColor(order: Order | null): string {
  return isPresaleOrder(order) ? '#EA580C' : '#16A34A';
}

// 状态文本
function getStatusText(status: OrderStatus): string {
  switch (status) {
    case "pending":
      return "待付款";
    case "paid":
      return "已付款";
    case "shipping":
      return "待收货";
    case "completed":
      return "已完成";
    case "afterSale":
      return "售后中";
    case "cancelled":
      return "已取消";
    default:
      return "未知状态";
  }
}

// 操作按钮文本
function getActionButtonText(status: OrderStatus): string {
  switch (status) {
    case "pending":
      return "去付款";
    case "paid":
      return "提醒发货";
    case "shipping":
      return "确认收货";
    case "completed":
      return "再次购买";
    case "afterSale":
      return "查看进度";
    case "cancelled":
      return "联系客服";
    default:
      return "查看详情";
  }
}

// 是否显示取消按钮
function showCancelButton(status: OrderStatus): boolean {
  // 只有待付款状态才能取消订单
  return status === "pending";
}

// 是否显示删除按钮
function showDeleteButton(status: OrderStatus): boolean {
  return status === "completed" || status === "cancelled";
}

// 是否可以查看交易快照（已完成和已完成后的状态都可以）
function canViewTradeSnapshot(status: OrderStatus): boolean {
  return status === "completed" || status === "afterSale";
}

// 取消订单
function handleCancelOrder() {
  if (!order.value || !order.value.id) {
    uni.showToast({ title: "订单信息错误", icon: "none" });
    return;
  }

  uni.showModal({
    title: "取消订单",
    content: "确定要取消此订单吗？",
    success: async (res) => {
      if (res.confirm) {
        // 校验订单版本号是否存在且为数字
        if (typeof order.value?.version !== "number") {
          uni.showToast({
            title: "订单版本信息缺失，无法取消订单",
            icon: "none",
          });
          return;
        }

        uni.showLoading({ title: "正在取消..." });
        try {
          // 使用原始id和version进行取消订单
          const [err, result] = await cancelOrder(
            order.value.id,
            order.value.version
          );
          uni.hideLoading();
          if (err || (result && result.code !== 0 && result.code !== 200)) {
            // 假设成功的 code 是 0 或 200
            // console.error('取消订单失败:', err || result);
            uni.showToast({
              title: `操作失败: ${
                (err && err.message) || (result && result.msg) || "请稍后重试"
              }`,
              icon: "none",
            });
          } else {
            uni.showToast({ title: "订单已取消", icon: "success" });
            // 刷新订单详情
            refreshOrderData();
            // 或者直接更新本地状态，如果API不返回更新后的订单
            // if (order.value) {
            //   order.value.status = 'cancelled';
            // }
          }
        } catch (error) {
          uni.hideLoading();
          // console.error('取消订单异常:', error);
          uni.showToast({ title: "操作异常，请稍后重试", icon: "none" });
        }
      }
    },
  });
}

// 删除订单
function handleDeleteOrder() {
  if (!order.value || !order.value.id) {
    uni.showToast({ title: "订单信息错误", icon: "none" });
    return;
  }

  uni.showModal({
    title: "删除订单",
    content: "确定要删除此订单吗？删除后将无法恢复。",
    success: async (res) => {
      if (res.confirm) {
        // 校验订单版本号是否存在且为数字
        if (typeof order.value?.version !== "number") {
          uni.showToast({
            title: "订单版本信息缺失，无法删除订单",
            icon: "none",
          });
          return;
        }

        uni.showLoading({ title: "正在删除..." });
        try {
          // 使用原始id和version进行删除订单
          const [err, result] = await deleteOrder(
            order.value.id,
            order.value.version
          );
          uni.hideLoading();
          if (err || (result && result.code !== 0 && result.code !== 200)) {
            // 假设成功的 code 是 0 或 200
            // console.error('删除订单失败:', err || result);
            uni.showToast({
              title: `操作失败: ${
                (err && err.message) || (result && result.msg) || "请稍后重试"
              }`,
              icon: "none",
            });
          } else {
            uni.showToast({ title: "订单已删除", icon: "success" });
            // 刷新订单详情或跳转
            setTimeout(() => {
              uni.navigateTo({ url: "/pages/views/orders/list" });
            }, 1500);
          }
        } catch (error) {
          uni.hideLoading();
          // console.error('删除订单异常:', error);
          uni.showToast({ title: "操作异常，请稍后重试", icon: "none" });
        }
      }
    },
  });
}

// 按钮处理
async function handleActionButton(currentOrder: Order) {
  if (!currentOrder || !currentOrder.id) {
    uni.showToast({ title: "订单信息错误", icon: "none" });
    return;
  }

  switch (currentOrder.status) {
    case "pending":
      // 调用支付宝支付
      try {
        uni.showLoading({
          title: "正在跳转",
          mask: true,
        });
        const alipayOrder = await payOrder({
          orderId: currentOrder.id,
          payType: "ALIPAY",
        });
        uni.hideLoading();
        const result = alipayOrder[1]; // uni.request promisify后的结果在第二个元素

        if (result.code === 200 && result.data) {
          const platform = uni.getSystemInfoSync().platform;
          if (
            platform === "android" ||
            platform === "ios" ||
            platform === "app-plus"
          ) {
            try {
              // 设置支付宝沙盒环境
              var EnvUtils = plus.android.importClass(
                "com.alipay.sdk.app.EnvUtils"
              ) as any;
              EnvUtils.setEnv(EnvUtils.EnvEnum.SANDBOX);

              uni.requestPayment({
                provider: "alipay",
                orderInfo: result.data.trim(), //支付宝订单数据
                success: (e) => {
                  uni.showToast({
                    icon: "success",
                    title: "支付成功！",
                  });
                  refreshOrderData(); // 刷新订单详情
                },
                fail: (e) => {
                  console.error("支付失败或取消:", e);
                  uni.showToast({
                    title: "支付未完成",
                    icon: "none",
                    duration: 2000,
                  });
                },
              });
            } catch (paymentError) {
              console.error("支付过程异常:", paymentError);
              uni.showToast({
                title: "支付异常",
                icon: "none",
                duration: 2000,
              });
            }
          } else {
            uni.showToast({
              title: "当前环境不支持支付",
              icon: "none",
            });
          }
        } else {
          uni.showToast({
            title: result.msg || "创建支付订单失败",
            icon: "none",
          });
        }
      } catch (error) {
        uni.hideLoading();
        console.error("支付请求失败:", error);
        uni.showToast({
          title: "支付请求失败，请稍后重试",
          icon: "none",
        });
      }
      break;
    case "paid":
      // 提醒发货逻辑
      uni.showToast({ title: "已提醒卖家发货", icon: "success" });
      // console.log('提醒发货:', currentOrder.id);
      break;
    case "shipping":
      // 确认收货逻辑
      uni.showModal({
        title: "确认收货",
        content: "是否确认收到该订单的所有商品？",
        success: async (res) => {
          if (res.confirm) {
            // 校验订单版本号是否存在且为数字
            if (typeof currentOrder.version !== "number") {
              uni.showToast({
                title: "订单版本信息缺失，无法确认收货",
                icon: "none",
              });
              return;
            }

            uni.showLoading({ title: "正在提交..." });
            try {
              // 使用原始id进行确认收货
              const [err, result] = await confirmReceiveOrder(
                currentOrder.id,
                currentOrder.version
              );
              uni.hideLoading();
              if (err || (result && result.code !== 0 && result.code !== 200)) {
                // 假设成功的 code 是 0 或 200
                // console.error('确认收货失败:', err || result);
                uni.showToast({
                  title: `操作失败: ${
                    (err && err.message) ||
                    (result && result.msg) ||
                    "请稍后重试"
                  }`,
                  icon: "none",
                });
              } else {
                uni.showToast({ title: "确认收货成功!", icon: "success" });
                // 刷新订单详情
                refreshOrderData();
                // 或者直接更新本地状态，如果API不返回更新后的订单
                // if (order.value) {
                //   order.value.status = 'completed';
                // }
              }
            } catch (error) {
              uni.hideLoading();
              // console.error('确认收货异常:', error);
              uni.showToast({ title: "操作异常，请稍后重试", icon: "none" });
            }
          }
        },
      });
      break;
    case "completed":
      // 再次购买逻辑
      uni.showToast({
        title: "正在查找相似商品...",
        icon: "loading",
        duration: 2000,
      });
      // uni.navigateTo({ url: `/pages/product/list?similarTo=${currentOrder.items[0]?.productId}` });
      // console.log('再次购买:', currentOrder.id);
      break;
    case "afterSale":
      // 查看售后进度逻辑
      uni.showToast({
        title: "正在查询售后进度...",
        icon: "loading",
        duration: 2000,
      });
      // 这里可以跳转到售后详情页面
      // uni.navigateTo({ url: `/pages/views/order/afterSale-detail?orderId=${currentOrder.id}` });
      break;
    case "cancelled":
      // 联系客服逻辑
      uni.showToast({ title: "正在联系客服...", icon: "none" });
      // uni.makePhoneCall({ phoneNumber: '客服电话' });
      // console.log('联系客服:', currentOrder.id);
      break;
    default:
      uni.showToast({
        title: `点击了${getActionButtonText(currentOrder.status)}按钮`,
        icon: "none",
      });
      break;
  }
}

// 导航方法
function goBack() {
  uni.navigateBack();
}

function goToOrdersList() {
  uni.navigateTo({
    url: "/pages/views/orders/list",
  });
}

onUnmounted(() => {
  if (countdownTimer.value) {
    clearInterval(countdownTimer.value);
  }
});

// 修改formatPrice函数，添加null检查
function formatPrice(price: string | number | undefined): string {
  if (price === undefined || price === null) {
    return "0.00";
  }

  if (typeof price === "string") {
    price = parseFloat(price) || 0;
  }

  return price.toFixed(2).replace(/\B(?=(\d{3})+(?!\d))/g, ",");
}

// 刷新订单数据方法
function refreshOrderData() {
  if (orderId.value) {
    isLoading.value = true;
    getOrderDetail(orderId.value)
      .then((res: any) => {
        // console.log('API返回数据:', res);
        // API返回格式为[error, data]
        const data = res[1];

        if (!data) {
          // console.error('获取订单详情错误: 数据为空');
          uni.showToast({
            title: "获取订单详情失败: 数据为空",
            icon: "none",
          });
        } else if (data) {
          // 直接使用原始数据显示
          const rawOrder: Order = {
            id: data.id || "", // 存储数据库原始ID
            orderNo: data.orderNo,
            date: data.createdTime
              ? moment(data.createdTime).format("YYYY-MM-DD HH:mm:ss")
              : "",
            createdTime: data.createdTime,
            status: convertOrderStatus(data.orderStatus),
            orderStatus: data.orderStatus,
            items: [],
            total: "0",
            totalAmount: data.totalAmount,
            payAmount: data.payAmount,
            shipping: "0",
            domesticFreight: "0",
            internationalFreight: "0",
            discountAmount: "0",
            address: "",
            trackingNo: data.trackingNo,
            trackingNumber: data.trackingNo || "",
            trackingCompany: data.trackingCompany || "",
            payType: data.payType,
            recipientName: data.receiverName || "",
            receiverName: data.receiverName,
            recipientPhone: data.receiverPhone || "",
            receiverPhone: data.receiverPhone,
            receiverProvince: data.receiverProvince,
            receiverCity: data.receiverCity,
            receiverDistrict: data.receiverDistrict,
            receiverAddress: data.receiverAddress,
            orderTime: data.orderTime,
            expireTime: data.expireTime,
            alipayNo: data.alipayNo, // 使用 alipayNo
            payTime: data.payTime // 使用 payTime, 并假设后端直接返回格式化好的字符串或前端不需要 moment 格式化
              ? typeof data.payTime === "number" // 如果是时间戳，则格式化
                ? moment(data.payTime).format("YYYY-MM-DD HH:mm:ss")
                : data.payTime // 如果已经是字符串，直接使用
              : "",
            shipTime: data.shipTime // 发货时间
              ? typeof data.shipTime === "number"
                ? moment(data.shipTime).format("YYYY-MM-DD HH:mm:ss")
                : data.shipTime
              : "",
            version: data.version, // 尝试获取 version
          };

          // 处理金额
          if (data.payAmount) rawOrder.total = data.payAmount.toString();
          else if (data.totalAmount)
            rawOrder.total = data.totalAmount.toString();

          if (data.domesticFreight)
            rawOrder.domesticFreight = data.domesticFreight.toString();
          if (data.internationalFreight)
            rawOrder.internationalFreight =
              data.internationalFreight.toString();

          // 计算总运费
          rawOrder.shipping = (
            (Number(rawOrder.domesticFreight) || 0) +
            (Number(rawOrder.internationalFreight) || 0)
          ).toString();

          if (data.discountAmount)
            rawOrder.discountAmount = data.discountAmount.toString();

          // 地址信息
          if (data.receiverProvince || data.receiverCity) {
            rawOrder.address = [
              data.receiverProvince || "",
              data.receiverCity || "",
              data.receiverDistrict || "",
              data.receiverAddress || "",
            ]
              .filter(Boolean)
              .join(" ");
          }

          // 物流信息
          if (data.trackingNo) {
            rawOrder.trackingNumber = data.trackingNo;
            rawOrder.trackingCompany = data.trackingCompany || "";
          }
          rawOrder.paymentMethod = getPaymentMethod(data.payType);

          // 处理商品项
          if (Array.isArray(data.orderItems) && data.orderItems.length > 0) {
            rawOrder.items = data.orderItems.map((item: any) => ({
              id: item.id || 0,
              productId: item.productId,
              productSkuId: item.productSkuId,
              productName: item.productName,
              productPrice: item.productPrice,
              quantity: item.quantity || 1,
              totalPrice: item.totalPrice,
              realPrice: item.realPrice,
              specification: item.specification,
              fileUrl: item.fileUrl,
              orderId: item.orderId,
              orderNo: item.orderNo,

              name: item.productName || "未命名商品",
              price: (item.productPrice || "0").toString(),
              image: item.fileUrl || "/static/placeholder.png",
              options: item.specification
                ? formatSpecification(item.specification)
                : "",
              skuId: item.productSkuId || "",
              isAfterSale: item.isAfterSale,
              orderStatus: item.orderStatus,
            }));
          }

          // 确保售后状态正确设置
          if (
            data.orderStatus === "AFTER_SALE" &&
            rawOrder.status !== "afterSale"
          ) {
            rawOrder.status = "afterSale";
          }

          order.value = rawOrder;

          // 如果是待付款状态，启动倒计时
          if (order.value && order.value.status === "pending") {
            startCountdown();
          }
        }
      })
      .finally(() => {
        isLoading.value = false;
      });
  }
}

// 点击修改地址按钮
function onEditAddressTap() {
  openAddressDrawer();
}

// 修改地址 - 使用抽屉方式
function handleEditAddress() {
  if (!order.value || !order.value.id) {
    uni.showToast({ title: "订单信息错误", icon: "none" });
    return;
  }
  openAddressDrawer();
}

// 打开地址选择抽屉
function openAddressDrawer() {
  fetchAddressList();
  showAddressDrawer.value = true;
}

// 关闭地址选择抽屉
function closeAddressDrawer() {
  showAddressDrawer.value = false;
}

// 获取地址列表
async function fetchAddressList() {
  addressLoading.value = true;
  try {
    const res = await getAddressList();

    if (res && res.data) {
      const processedList = res.data.map((item) => {
        const isThisDefault =
          item.idefault === true ||
          item.idefault === 1 ||
          item.idefault === "1" ||
          item.idefault === "true" ||
          item.iDefault === true ||
          item.isDefault === true ||
          item.defaultStatus === true;

        item._isDefault = isThisDefault;
        return item;
      });

      addressList.value = processedList;
    } else {
      addressList.value = [];
    }
  } catch (error) {
    uni.showToast({
      title: "加载地址失败",
      icon: "none",
    });
    addressList.value = [];
  } finally {
    addressLoading.value = false;
  }
}

// 选择地址
async function selectDeliveryAddress(address) {
  if (!address) {
    uni.showToast({
      title: "地址信息不完整",
      icon: "none",
    });
    return;
  }

  const addressData = {
    receiverName: address.name || "",
    receiverPhone: address.phone || "",
    receiverProvince: address.province || "",
    receiverCity: address.city || "",
    receiverDistrict: address.district || "",
    receiverAddress: address.detail || address.address || "",
    version: order.value.version,
  };

  if (typeof addressData.version !== "number") {
    uni.showToast({
      title: "订单版本信息缺失，无法修改地址",
      icon: "none",
    });
    closeAddressDrawer();
    return;
  }

  uni.showLoading({ title: "更新地址中..." });

  try {
    const [err, result] = await updateOrderAddress(order.value.id, addressData);
    uni.hideLoading();

    if (err || (result && result.code !== 0 && result.code !== 200)) {
      uni.showToast({
        title: `修改失败: ${
          (err && err.message) || (result && result.msg) || "请稍后重试"
        }`,
        icon: "none",
      });
    } else {
      uni.showToast({
        title: "地址已更新",
        icon: "success",
      });
      closeAddressDrawer();
      refreshOrderData();
    }
  } catch (error) {
    uni.hideLoading();
    uni.showToast({
      title: "操作异常，请稍后重试",
      icon: "none",
    });
  }
}

// 跳转到添加地址页面
function goToAddAddress() {
  uni.setStorageSync("fromAddressDrawer", true);

  uni.navigateTo({
    url: "/pages/views/profile/address-edit",
    success: function () {},
    fail: function (err) {
      uni.showToast({
        title: "页面跳转失败",
        icon: "none",
      });
    },
  });
}

// 监听页面显示事件，用于处理从其他页面返回的情况
onShow(() => {
  if (showAddressDrawer.value) {
    fetchAddressList();
  }
});

// 是否显示物流按钮
function showLogisticsButton(status: OrderStatus): boolean {
  return status === "shipping" || status === "completed";
}

// 查看物流
function handleViewLogistics() {
  if (!order.value?.trackingNumber) {
    uni.showToast({
      title: "暂无物流信息",
      icon: "none",
    });
    return;
  }

  // 构建快递100查询链接
  const url = `https://www.kuaidi100.com/?nu=${order.value.trackingNumber}`;

  // 在APP中打开网页
  uni.navigateTo({
    url: `/pages/webview/index?url=${encodeURIComponent(url)}`,
    fail: (err) => {
      // 如果打开失败，尝试使用系统浏览器打开
      plus.runtime.openURL(url, (error) => {
        uni.showToast({
          title: "打开物流查询失败",
          icon: "none",
        });
      });
    },
  });
}

// 处理商品售后
function handleAfterSale(item: OrderItem) {
  if (!order.value || !order.value.id) {
    uni.showToast({ title: "订单信息错误", icon: "none" });
    return;
  }

  uni.navigateTo({
    url: `/pages/views/after-sale/afterSale-apply?orderId=${order.value.id}&itemId=${item.id}`,
    success: function () {
      uni.showToast({
        title: "您可以选择多个商品一起申请售后",
        icon: "none",
        duration: 2000,
      });
    },
    fail: function (err) {
      uni.showToast({
        title: "页面跳转失败，请稍后再试",
        icon: "none",
      });
    },
  });
}
</script>

<style>
@keyframes fade-in {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

@keyframes slide-up {
  from {
    transform: translateY(20px);
    opacity: 0;
  }
  to {
    transform: translateY(0);
    opacity: 1;
  }
}

.order-card {
  animation: slide-up 0.3s ease-out forwards;
  opacity: 0;
}

/* 为步骤切换添加缓和过渡 */
view,
text {
  transition: all 0.3s ease;
}

/* 在 style 部分追加如下样式，保证一行最多3个按钮，超出换行 */
.order-actions-row {
  display: flex;
  flex-wrap: wrap;
  justify-content: center;
  gap: 1rem;
}
.order-actions-row > view {
  flex: 1 1 30%;
  min-width: 90px;
  max-width: 33%;
  box-sizing: border-box;
}
@media (max-width: 500px) {
  .order-actions-row > view {
    min-width: 80px;
    max-width: 100%;
  }
}
</style>
