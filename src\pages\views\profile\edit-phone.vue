<template>
  <view class="page min-h-screen bg-gray-50">
    <NavBar title="修改手机号" :showBack="true"></NavBar>

    <div class="edit-content">
      <!-- 当前手机号展示 -->
      <view class="current-phone card-style">
        <text class="info-label text-text-secondary text-sm">当前手机号</text>
        <text class="phone-number text-2xl font-semibold text-text-primary">{{
          formattedPhone
        }}</text>
      </view>

      <!-- 步骤1：验证当前手机号 -->
      <view v-if="currentStep === 1" class="form-section card-style">
        <view class="step-title text-base font-medium text-text-primary"
          >验证当前手机号</view
        >

        <view class="form-item verification-code">
          <up-input
            class="code-input"
            v-model="currentVerificationCode"
            type="number"
            placeholder="请输入验证码"
            maxlength="6"
            border="bottom"
            borderColor="#EEEEEE"
            clearable
          />
          <up-button
            class="code-btn"
            :disabled="!canSendCurrentCode || currentCountDown > 0"
            @click="sendCurrentVerificationCode"
            :custom-style="codeButtonStyle"
          >
            {{
              currentCountDown > 0
                ? `${currentCountDown}秒后重新获取`
                : '获取验证码'
            }}
          </up-button>
        </view>

        <view class="form-item">
          <up-button
            class="submit-btn"
            @click="verifyCurrentPhone"
            :disabled="!isCurrentCodeValid"
            :custom-style="submitButtonStyle"
          >
            下一步
          </up-button>
        </view>
      </view>

      <!-- 步骤2：设置新手机号 -->
      <view v-if="currentStep === 2" class="form-section card-style">
        <view class="step-title text-base font-medium text-text-primary"
          >设置新手机号</view
        >

        <view class="form-item">
          <up-input
            class="phone-input"
            v-model="newPhone"
            type="number"
            placeholder="请输入新手机号"
            maxlength="11"
            border="bottom"
            borderColor="#EEEEEE"
            clearable
          />
        </view>

        <view class="form-item verification-code">
          <up-input
            class="code-input"
            v-model="newVerificationCode"
            type="number"
            placeholder="请输入验证码"
            maxlength="6"
            border="bottom"
            borderColor="#EEEEEE"
            clearable
          />
          <up-button
            class="code-btn"
            :disabled="!canSendNewCode || newCountDown > 0"
            @click="sendNewVerificationCode"
            :custom-style="codeButtonStyle"
          >
            {{
              newCountDown > 0 ? `${newCountDown}秒后重新获取` : '获取验证码'
            }}
          </up-button>
        </view>

        <view class="form-item">
          <up-button
            class="submit-btn"
            @click="saveNewPhone"
            :disabled="!isNewFormValid"
            :custom-style="submitButtonStyle"
          >
            保存
          </up-button>
        </view>
      </view>
    </div>
  </view>
</template>

<script setup lang="ts">
import { ref, computed, onUnmounted } from 'vue';
import { useStore } from 'vuex';
import NavBar from '@/components/NavBar.vue';
import {
  checkVerificationCode,
  updateUser,
  sendVerificationCode as sendSmsCode,
} from '@/api/api';

const store = useStore();

// 当前步骤：1=验证当前手机号，2=设置新手机号
const currentStep = ref(1);
const currentTimer = ref<ReturnType<typeof setInterval> | null>(null);
const newTimer = ref<ReturnType<typeof setInterval> | null>(null);
// 获取当前用户信息
const currentPhone = computed(() => {
  return store.state.$userInfo?.mobile || '';
});

// 格式化手机号显示，如果存在则加密中间4位
const formattedPhone = computed(() => {
  if (!currentPhone.value) return '未绑定手机号';

  // 如果是有效手机号，显示加密版本
  if (/^1[3-9]\d{9}$/.test(currentPhone.value)) {
    return currentPhone.value.replace(/(\d{3})\d{4}(\d{4})/, '$1****$2');
  }

  return currentPhone.value;
});

// ==================== 步骤1：验证当前手机号 ====================
const currentVerificationCode = ref('');
const currentCountDown = ref(0);

// 是否可以发送当前手机号验证码
const canSendCurrentCode = computed(() => {
  return !!currentPhone.value && /^1[3-9]\d{9}$/.test(currentPhone.value);
});

// 当前手机号验证码是否有效
const isCurrentCodeValid = computed(() => {
  return currentVerificationCode.value.length === 6;
});

// 发送当前手机号验证码
const sendCurrentVerificationCode = async () => {
  if (!canSendCurrentCode.value) {
    uni.showToast({
      title: '当前手机号无效',
      icon: 'none',
    });
    return;
  }

  try {
    uni.showLoading({ title: '发送中...' });

    // 调用发送验证码API
    const result = await sendSmsCode(currentPhone.value);

    uni.hideLoading();

    if (result.code === 200) {
      uni.showToast({
        title: '验证码已发送',
        icon: 'success',
      });

      // 开始倒计时
      currentCountDown.value = 60;
      currentTimer.value = setInterval(() => {
        currentCountDown.value--;
        if (currentCountDown.value <= 0) {
          clearInterval(currentTimer.value);
          currentTimer.value = null;
        }
      }, 1000);
    } else {
      uni.showToast({
        title: result?.message || '验证码发送失败',
        icon: 'none',
      });
    }
  } catch (error) {
    uni.hideLoading();
    uni.showToast({
      title: '验证码发送失败',
      icon: 'none',
    });
    console.error('发送验证码失败', error);
  }
};

// 验证当前手机号
const verifyCurrentPhone = async () => {
  if (!isCurrentCodeValid.value) {
    uni.showToast({
      title: '请输入完整的验证码',
      icon: 'none',
    });
    return;
  }

  try {
    uni.showLoading({ title: '验证中...' });

    // 校验验证码
    const params = {
      mobile: currentPhone.value,
      code: currentVerificationCode.value,
    };

    const res = await checkVerificationCode(params);

    uni.hideLoading();

    if (res.code === 200) {
      // 验证成功，进入第二步
      currentStep.value = 2;
    } else {
      uni.showToast({
        title: res?.message || '验证码错误',
        icon: 'none',
      });
    }
  } catch (error) {
    uni.hideLoading();
    uni.showToast({
      title: '验证失败',
      icon: 'none',
    });
    console.error('验证失败', error);
  }
};

// ==================== 步骤2：设置新手机号 ====================
const newPhone = ref('');
const newVerificationCode = ref('');
const newCountDown = ref(0);

// 校验新手机号格式
const isNewPhoneValid = computed(() => {
  const phoneRegex = /^1[3-9]\d{9}$/;
  return phoneRegex.test(newPhone.value);
});

// 是否可以发送新手机号验证码
const canSendNewCode = computed(() => {
  return isNewPhoneValid.value && newPhone.value !== currentPhone.value;
});

// 新手机号验证码是否有效
const isNewCodeValid = computed(() => {
  return newVerificationCode.value.length === 6;
});

// 表单是否有效
const isNewFormValid = computed(() => {
  return isNewPhoneValid.value && isNewCodeValid.value;
});

// 发送新手机号验证码
const sendNewVerificationCode = async () => {
  if (!canSendNewCode.value) {
    if (newPhone.value === currentPhone.value) {
      uni.showToast({
        title: '新手机号与当前手机号相同',
        icon: 'none',
      });
    } else {
      uni.showToast({
        title: '请输入有效的新手机号',
        icon: 'none',
      });
    }
    return;
  }

  try {
    uni.showLoading({ title: '发送中...' });

    // 调用发送验证码API
    const result = await sendSmsCode(newPhone.value);

    uni.hideLoading();

    if (result.code === 200) {
      uni.showToast({
        title: '验证码已发送',
        icon: 'success',
      });

      // 开始倒计时
      newCountDown.value = 60;
      newTimer.value = setInterval(() => {
        newCountDown.value--;
        if (newCountDown.value <= 0) {
          clearInterval(newTimer.value);
          newTimer.value = null;
        }
      }, 1000);
    } else {
      uni.showToast({
        title: result?.message || '验证码发送失败',
        icon: 'none',
      });
    }
  } catch (error) {
    uni.hideLoading();
    uni.showToast({
      title: '验证码发送失败',
      icon: 'none',
    });
    console.error('发送验证码失败', error);
  }
};

// 保存新手机号
const saveNewPhone = async () => {
  if (!isNewFormValid.value) {
    if (!isNewPhoneValid.value) {
      uni.showToast({
        title: '请输入有效的新手机号',
        icon: 'none',
      });
    } else {
      uni.showToast({
        title: '请输入完整的验证码',
        icon: 'none',
      });
    }
    return;
  }

  try {
    uni.showLoading({ title: '验证中...' });

    // 校验验证码
    const checkParams = {
      mobile: newPhone.value,
      code: newVerificationCode.value,
    };

    const checkRes = await checkVerificationCode(checkParams);

    if (checkRes.code !== 200) {
      uni.hideLoading();
      uni.showToast({
        title: checkRes?.message || '验证码错误',
        icon: 'none',
      });
      return;
    }

    // 验证通过，更新手机号
    const updateParams = {
      id: store.state.$userInfo.id,
      mobile: newPhone.value,
      version: store.state.$userInfo.version,
    };

    const res = await updateUser(updateParams);

    uni.hideLoading();

    if (res.code === 200) {
      uni.showToast({
        title: '手机号修改成功',
        icon: 'success',
      });

      // 更新用户数据
      store.commit('UPDATE_USER_INFO', { mobile: newPhone.value });

      // 返回上一页
      setTimeout(() => {
        uni.navigateBack();
      }, 1500);
    } else {
      uni.showToast({
        title: res?.message || '手机号修改失败',
        icon: 'none',
      });
    }
  } catch (error) {
    uni.hideLoading();
    uni.showToast({
      title: '提交失败',
      icon: 'none',
    });
    console.error('修改手机号失败', error);
  }
};

// 自定义样式
const codeButtonStyle = computed(() => {
  if (!canSendCurrentCode.value || currentCountDown.value > 0) {
    return {
      color: '#A3A3A3',
      backgroundColor: '#F5F5F5',
      border: 'none',
    };
  } else {
    return {
      background: 'linear-gradient(to right, #10B981, #34D399)',
      color: '#FFFFFF',
      border: 'none',
    };
  }
});

const submitButtonStyle = computed(() => {
  return {
    background: 'linear-gradient(to right, #10B981, #34D399)',
    color: '#FFFFFF',
    borderRadius: '0.75rem',
    height: '48px',
    width: '100%',
  };
});

// 清除计时器
onUnmounted(() => {
  if (currentTimer.value !== null) {
    clearInterval(currentTimer.value);
  }
  if (newTimer.value !== null) {
    clearInterval(newTimer.value);
  }
});
</script>

<style scoped lang="scss">
.edit-phone {
  min-height: 100vh;
  background-color: #f5f5f5;
  padding: 16px;
}

.edit-content {
  margin: 0 auto;
  max-width: 600px;
}

.card-style {
  background-color: #fff;
  border-radius: 12px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);
  padding: 20px;
  margin-bottom: 16px;
}

.info-label {
  display: block;
  margin-bottom: 4px;
}

.current-phone {
  display: flex;
  flex-direction: column;
  align-items: center;
  text-align: center;
  padding: 24px 16px;
}

.phone-number {
  margin-top: 8px;
}

.form-section {
  padding: 20px;
}

.step-title {
  margin-bottom: 20px;
  display: block;
}

.form-item {
  margin-bottom: 20px;
}

.verification-code {
  display: flex;
  align-items: flex-end;
  gap: 12px;
}

.code-input {
  flex: 1;
}

.code-btn {
  flex-shrink: 0;
  width: 120px;
  font-size: 14px;
}

.submit-btn {
  margin-top: 32px;
}
</style>
