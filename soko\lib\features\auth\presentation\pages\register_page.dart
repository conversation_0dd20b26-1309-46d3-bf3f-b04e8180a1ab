import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';

import 'package:soko/core/utils/validator_utils.dart';
import 'package:soko/shared/presentation/widgets/custom_button.dart';
import 'package:soko/shared/presentation/widgets/custom_text_field.dart';
import 'package:soko/features/auth/presentation/providers/auth_provider.dart';
import 'package:soko/features/auth/presentation/widgets/sms_code_button.dart';

/// 注册页面
class RegisterPage extends ConsumerStatefulWidget {
  const RegisterPage({super.key});

  @override
  ConsumerState<RegisterPage> createState() => _RegisterPageState();
}

class _RegisterPageState extends ConsumerState<RegisterPage> {
  final _formKey = GlobalKey<FormState>();
  final _phoneController = TextEditingController();
  final _passwordController = TextEditingController();
  final _confirmPasswordController = TextEditingController();
  final _smsCodeController = TextEditingController();
  final _nicknameController = TextEditingController();
  final _inviteCodeController = TextEditingController();

  bool _agreeToTerms = false;

  @override
  void dispose() {
    _phoneController.dispose();
    _passwordController.dispose();
    _confirmPasswordController.dispose();
    _smsCodeController.dispose();
    _nicknameController.dispose();
    _inviteCodeController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.white,
      appBar: AppBar(
        backgroundColor: Colors.transparent,
        elevation: 0,
        title: const Text(
          '注册',
          style: TextStyle(
            color: Colors.black,
            fontSize: 18,
            fontWeight: FontWeight.w600,
          ),
        ),
        leading: IconButton(
          onPressed: () => Navigator.of(context).pop(),
          icon: const Icon(
            Icons.arrow_back_ios,
            color: Colors.black,
            size: 20,
          ),
        ),
      ),
      body: SafeArea(
        child: SingleChildScrollView(
          padding: const EdgeInsets.all(24),
          child: Form(
            key: _formKey,
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                // 标题
                const Text(
                  '创建账户',
                  style: TextStyle(
                    fontSize: 28,
                    fontWeight: FontWeight.bold,
                  ),
                ),
                const SizedBox(height: 8),
                const Text(
                  '请填写以下信息完成注册',
                  style: TextStyle(
                    fontSize: 16,
                    color: Colors.grey,
                  ),
                ),
                const SizedBox(height: 32),

                // 手机号输入框
                PhoneTextField(
                  controller: _phoneController,
                  label: '手机号',
                  hintText: '请输入手机号',
                ),
                const SizedBox(height: 16),

                // 验证码输入框
                Row(
                  children: [
                    Expanded(
                      child: CodeTextField(
                        controller: _smsCodeController,
                        label: '验证码',
                        hintText: '请输入验证码',
                      ),
                    ),
                    const SizedBox(width: 12),
                    Padding(
                      padding: const EdgeInsets.only(top: 24),
                      child: SmsCodeButton(
                        phone: _phoneController.text,
                        type: 'register',
                      ),
                    ),
                  ],
                ),
                const SizedBox(height: 16),

                // 密码输入框
                PasswordTextField(
                  controller: _passwordController,
                  label: '密码',
                  hintText: '请输入密码（6-20位）',
                ),
                const SizedBox(height: 16),

                // 确认密码输入框
                PasswordTextField(
                  controller: _confirmPasswordController,
                  label: '确认密码',
                  hintText: '请再次输入密码',
                ),
                const SizedBox(height: 16),

                // 昵称输入框（可选）
                CustomTextField(
                  controller: _nicknameController,
                  label: '昵称（可选）',
                  hintText: '请输入昵称',
                ),
                const SizedBox(height: 16),

                // 邀请码输入框（可选）
                CustomTextField(
                  controller: _inviteCodeController,
                  label: '邀请码（可选）',
                  hintText: '请输入邀请码',
                ),
                const SizedBox(height: 24),

                // 同意条款
                Row(
                  children: [
                    Checkbox(
                      value: _agreeToTerms,
                      onChanged: (value) {
                        setState(() {
                          _agreeToTerms = value ?? false;
                        });
                      },
                    ),
                    Expanded(
                      child: GestureDetector(
                        onTap: () {
                          setState(() {
                            _agreeToTerms = !_agreeToTerms;
                          });
                        },
                        child: RichText(
                          text: const TextSpan(
                            style: TextStyle(
                              fontSize: 14,
                              color: Colors.grey,
                            ),
                            children: [
                              TextSpan(text: '我已阅读并同意'),
                              TextSpan(
                                text: '《用户协议》',
                                style: TextStyle(
                                  color: Colors.blue,
                                  decoration: TextDecoration.underline,
                                ),
                              ),
                              TextSpan(text: '和'),
                              TextSpan(
                                text: '《隐私政策》',
                                style: TextStyle(
                                  color: Colors.blue,
                                  decoration: TextDecoration.underline,
                                ),
                              ),
                            ],
                          ),
                        ),
                      ),
                    ),
                  ],
                ),
                const SizedBox(height: 32),

                // 注册按钮
                Consumer(
                  builder: (context, ref, child) {
                    final registerState = ref.watch(registerProvider);
                    
                    return PrimaryButton(
                      text: '注册',
                      width: double.infinity,
                      isLoading: registerState.isLoading,
                      isDisabled: !_agreeToTerms,
                      onPressed: _handleRegister,
                    );
                  },
                ),
                const SizedBox(height: 24),

                // 登录链接
                Row(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    const Text(
                      '已有账户？',
                      style: TextStyle(color: Colors.grey),
                    ),
                    TextButton(
                      onPressed: () => Navigator.of(context).pop(),
                      child: const Text(
                        '立即登录',
                        style: TextStyle(
                          fontWeight: FontWeight.w600,
                        ),
                      ),
                    ),
                  ],
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  /// 处理注册
  void _handleRegister() {
    if (!_validateForm()) return;

    ref.read(registerProvider.notifier).register(
      phone: _phoneController.text.trim(),
      password: _passwordController.text.trim(),
      smsCode: _smsCodeController.text.trim(),
      nickname: _nicknameController.text.trim().isEmpty 
          ? null 
          : _nicknameController.text.trim(),
      inviteCode: _inviteCodeController.text.trim().isEmpty 
          ? null 
          : _inviteCodeController.text.trim(),
    );

    // 监听注册结果
    ref.listen(registerProvider, (previous, next) {
      next.when(
        idle: () {},
        loading: () {},
        success: (data) {
          // 注册成功
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(content: Text('注册成功')),
          );
          Navigator.of(context).pop();
        },
        error: (message) {
          // 注册失败
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(content: Text(message)),
          );
        },
      );
    });
  }

  /// 验证表单
  bool _validateForm() {
    final phone = _phoneController.text.trim();
    final password = _passwordController.text.trim();
    final confirmPassword = _confirmPasswordController.text.trim();
    final smsCode = _smsCodeController.text.trim();

    if (phone.isEmpty) {
      _showError('请输入手机号');
      return false;
    }

    if (!ValidatorUtils.isValidPhone(phone)) {
      _showError('请输入正确的手机号');
      return false;
    }

    if (smsCode.isEmpty) {
      _showError('请输入验证码');
      return false;
    }

    if (password.isEmpty) {
      _showError('请输入密码');
      return false;
    }

    if (!ValidatorUtils.isValidPassword(password)) {
      _showError('密码长度应为6-20位');
      return false;
    }

    if (confirmPassword.isEmpty) {
      _showError('请确认密码');
      return false;
    }

    if (password != confirmPassword) {
      _showError('两次输入的密码不一致');
      return false;
    }

    if (!_agreeToTerms) {
      _showError('请同意用户协议和隐私政策');
      return false;
    }

    return true;
  }

  void _showError(String message) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(content: Text(message)),
    );
  }
}
