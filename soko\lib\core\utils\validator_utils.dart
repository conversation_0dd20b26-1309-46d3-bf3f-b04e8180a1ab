/// 验证工具类
class ValidatorUtils {
  // 私有构造函数
  ValidatorUtils._();

  // 正则表达式
  static final RegExp _emailRegex = RegExp(
    r'^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$',
  );
  
  static final RegExp _phoneRegex = RegExp(r'^1[3-9]\d{9}$');
  
  static final RegExp _idCardRegex = RegExp(
    r'^[1-9]\d{5}(18|19|20)\d{2}((0[1-9])|(1[0-2]))(([0-2][1-9])|10|20|30|31)\d{3}[0-9Xx]$',
  );
  
  static final RegExp _passwordRegex = RegExp(
    r'^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)[a-zA-Z\d@$!%*?&]{8,}$',
  );
  
  static final RegExp _urlRegex = RegExp(
    r'^https?:\/\/(www\.)?[-a-zA-Z0-9@:%._\+~#=]{1,256}\.[a-zA-Z0-9()]{1,6}\b([-a-zA-Z0-9()@:%_\+.~#?&//=]*)$',
  );

  /// 验证邮箱
  static bool isValidEmail(String email) {
    if (email.isEmpty) return false;
    return _emailRegex.hasMatch(email);
  }

  /// 验证手机号
  static bool isValidPhone(String phone) {
    if (phone.isEmpty) return false;
    return _phoneRegex.hasMatch(phone);
  }

  /// 验证身份证号
  static bool isValidIdCard(String idCard) {
    if (idCard.isEmpty) return false;
    if (!_idCardRegex.hasMatch(idCard)) return false;
    
    // 验证校验码
    return _validateIdCardChecksum(idCard);
  }

  /// 验证密码强度
  static bool isValidPassword(String password) {
    if (password.isEmpty) return false;
    return password.length >= 6; // 简化版本，实际项目可以更严格
  }

  /// 验证强密码（包含大小写字母和数字）
  static bool isStrongPassword(String password) {
    if (password.isEmpty) return false;
    return _passwordRegex.hasMatch(password);
  }

  /// 验证URL
  static bool isValidUrl(String url) {
    if (url.isEmpty) return false;
    return _urlRegex.hasMatch(url);
  }

  /// 验证数字
  static bool isNumeric(String str) {
    if (str.isEmpty) return false;
    return double.tryParse(str) != null;
  }

  /// 验证整数
  static bool isInteger(String str) {
    if (str.isEmpty) return false;
    return int.tryParse(str) != null;
  }

  /// 验证正数
  static bool isPositiveNumber(String str) {
    if (!isNumeric(str)) return false;
    final number = double.parse(str);
    return number > 0;
  }

  /// 验证非负数
  static bool isNonNegativeNumber(String str) {
    if (!isNumeric(str)) return false;
    final number = double.parse(str);
    return number >= 0;
  }

  /// 验证字符串长度
  static bool isValidLength(String str, {int? minLength, int? maxLength}) {
    if (minLength != null && str.length < minLength) return false;
    if (maxLength != null && str.length > maxLength) return false;
    return true;
  }

  /// 验证是否为空或空白字符
  static bool isEmpty(String? str) {
    return str == null || str.trim().isEmpty;
  }

  /// 验证是否不为空
  static bool isNotEmpty(String? str) {
    return !isEmpty(str);
  }

  /// 验证银行卡号
  static bool isValidBankCard(String cardNumber) {
    if (cardNumber.isEmpty) return false;
    
    // 移除空格和连字符
    final cleanNumber = cardNumber.replaceAll(RegExp(r'[\s-]'), '');
    
    // 检查长度（通常为13-19位）
    if (cleanNumber.length < 13 || cleanNumber.length > 19) return false;
    
    // 检查是否全为数字
    if (!RegExp(r'^\d+$').hasMatch(cleanNumber)) return false;
    
    // Luhn算法验证
    return _luhnCheck(cleanNumber);
  }

  /// 验证中文姓名
  static bool isValidChineseName(String name) {
    if (name.isEmpty) return false;
    
    // 中文字符正则
    final chineseRegex = RegExp(r'^[\u4e00-\u9fa5]{2,10}$');
    return chineseRegex.hasMatch(name);
  }

  /// 验证车牌号
  static bool isValidLicensePlate(String plate) {
    if (plate.isEmpty) return false;
    
    // 新能源车牌
    final newEnergyRegex = RegExp(r'^[京津沪渝冀豫云辽黑湘皖鲁新苏浙赣鄂桂甘晋蒙陕吉闽贵粤青藏川宁琼使领][A-Z][A-HJ-NP-Z0-9]{4}[A-HJ-NP-Z0-9挂学警港澳]$');
    
    // 普通车牌
    final normalRegex = RegExp(r'^[京津沪渝冀豫云辽黑湘皖鲁新苏浙赣鄂桂甘晋蒙陕吉闽贵粤青藏川宁琼使领][A-Z][A-HJ-NP-Z0-9]{5}$');
    
    return newEnergyRegex.hasMatch(plate) || normalRegex.hasMatch(plate);
  }

  /// 验证IP地址
  static bool isValidIP(String ip) {
    if (ip.isEmpty) return false;
    
    final parts = ip.split('.');
    if (parts.length != 4) return false;
    
    for (final part in parts) {
      final num = int.tryParse(part);
      if (num == null || num < 0 || num > 255) return false;
    }
    
    return true;
  }

  /// 验证MAC地址
  static bool isValidMAC(String mac) {
    if (mac.isEmpty) return false;
    
    final macRegex = RegExp(r'^([0-9A-Fa-f]{2}[:-]){5}([0-9A-Fa-f]{2})$');
    return macRegex.hasMatch(mac);
  }

  /// Luhn算法验证（用于银行卡号验证）
  static bool _luhnCheck(String cardNumber) {
    var sum = 0;
    var alternate = false;
    
    for (var i = cardNumber.length - 1; i >= 0; i--) {
      var digit = int.parse(cardNumber[i]);
      
      if (alternate) {
        digit *= 2;
        if (digit > 9) {
          digit = (digit % 10) + 1;
        }
      }
      
      sum += digit;
      alternate = !alternate;
    }
    
    return sum % 10 == 0;
  }

  /// 验证身份证校验码
  static bool _validateIdCardChecksum(String idCard) {
    if (idCard.length != 18) return false;
    
    const weights = [7, 9, 10, 5, 8, 4, 2, 1, 6, 3, 7, 9, 10, 5, 8, 4, 2];
    const checkCodes = ['1', '0', 'X', '9', '8', '7', '6', '5', '4', '3', '2'];
    
    var sum = 0;
    for (var i = 0; i < 17; i++) {
      sum += int.parse(idCard[i]) * weights[i];
    }
    
    final checkCode = checkCodes[sum % 11];
    return idCard[17].toUpperCase() == checkCode;
  }

  /// 获取密码强度等级
  static PasswordStrength getPasswordStrength(String password) {
    if (password.isEmpty) return PasswordStrength.none;
    
    var score = 0;
    
    // 长度
    if (password.length >= 8) score++;
    if (password.length >= 12) score++;
    
    // 包含小写字母
    if (RegExp('[a-z]').hasMatch(password)) score++;
    
    // 包含大写字母
    if (RegExp('[A-Z]').hasMatch(password)) score++;
    
    // 包含数字
    if (RegExp(r'\d').hasMatch(password)) score++;
    
    // 包含特殊字符
    if (RegExp(r'[!@#$%^&*(),.?":{}|<>]').hasMatch(password)) score++;
    
    if (score <= 2) return PasswordStrength.weak;
    if (score <= 4) return PasswordStrength.medium;
    return PasswordStrength.strong;
  }
}

/// 密码强度枚举
enum PasswordStrength {
  none,
  weak,
  medium,
  strong,
}
