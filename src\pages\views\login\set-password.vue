<template>
  <view class="set-password">
    <view class="header">
      <view class="title">设置密码</view>
      <view class="subtitle">请为您的账户设置一个安全的密码</view>
    </view>

    <view class="form-container">
      <view class="form-item">
        <van-icon name="closed-eye" size="20" color="#999" />
        <input
          v-model="password"
          type="text"
          password
          placeholder="请设置密码"
          placeholder-class="placeholder"
          class="input"
          cursor-spacing="20"
        />
      </view>
      <view class="password-tips">
        <text>密码长度8-20位，包含字母和数字</text>
      </view>

      <view class="form-item">
        <van-icon name="closed-eye" size="20" color="#999" />
        <input
          v-model="confirmPassword"
          type="text"
          password
          placeholder="请确认密码"
          placeholder-class="placeholder"
          class="input"
          cursor-spacing="20"
        />
      </view>

      <view class="form-item">
        <van-icon name="gift-o" size="20" color="#999" />
        <input
          v-model="invitationCode"
          type="number"
          placeholder="邀请码（选填，8位数字）"
          placeholder-class="placeholder"
          class="input"
          cursor-spacing="20"
          maxlength="8"
        />
      </view>

      <button
        class="submit-button"
        :disabled="!isFormValid || loading"
        @tap="register"
        hover-class="button-hover"
      >
        {{ loading ? "注册中..." : "完成注册" }}
      </button>
    </view>
  </view>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from "vue"
import JSEncrypt from "jsencrypt"
import {
  checkVerificationCode,
  getPublicKeyOptimized,
  login,
  POST_REGISTER_USER
} from "@/api/api"
import store from "@/store"

// 不再需要pinia
const password = ref("")
const confirmPassword = ref("")
const loading = ref(false)
const phone = ref("")
const code = ref("")
const publicKey = ref("")
const invitationCode = ref("")

// 从缓存获取手机号和验证码
onMounted(() => {
  phone.value = uni.getStorageSync("registrationPhone") || ""
  code.value = uni.getStorageSync("registrationCode") || ""

  if (!phone.value || !code.value) {
    uni.showToast({
      title: "注册信息不完整，请返回重试",
      icon: "none",
      duration: 2000
    })

    setTimeout(() => {
      uni.navigateBack()
    }, 2000)
  }

  // 获取公钥
  getPublicKey()
})

// 表单验证
const isFormValid = computed(() => {
  // 密码规则：8-20位，包含字母和数字
  const passwordRegex = /^(?=.*[A-Za-z])(?=.*\d)[A-Za-z\d]{8,20}$/
  const isPasswordValid = passwordRegex.test(password.value)
  const isConfirmMatch = password.value === confirmPassword.value
  // 邀请码规则：选填，6位数字
  const isInvitationValid =
    invitationCode.value === "" || /^\d{8}$/.test(invitationCode.value)

  return (
    isPasswordValid &&
    isConfirmMatch &&
    password.value.length > 0 &&
    isInvitationValid
  )
})

// 获取公钥用于加密密码
const getPublicKey = async () => {
  try {
    const res = await getPublicKeyOptimized()
    console.log("获取公钥结果:", res)

    // 适配axios的返回结构
    if (res.code === 200) {
      publicKey.value = res.data.publicKey
      console.log("公钥设置成功")
      return true
    } else {
      console.error("未能获取有效公钥")
      showToast("获取加密密钥失败")
      return false
    }
  } catch (error) {
    console.error("获取公钥异常:", error)
    showToast("网络异常，请重试")
    return false
  }
}

// 加密密码
const encryptPassword = (pwd) => {
  if (!publicKey.value) return pwd

  try {
    const encrypt = new JSEncrypt()
    encrypt.setPublicKey(publicKey.value)
    const encrypted = encrypt.encrypt(pwd)
    return encrypted
  } catch (error) {
    console.error("密码加密失败:", error)
    return pwd
  }
}

// 显示提示
const showToast = (message) => {
  uni.showToast({
    title: message,
    icon: "none",
    duration: 2000
  })
}

// 注册
const register = async () => {
  if (!isFormValid.value) {
    if (password.value.length < 8 || password.value.length > 20) {
      showToast("密码长度应为8-20位")
    } else if (!/(?=.*[A-Za-z])(?=.*\d)/.test(password.value)) {
      showToast("密码需包含字母和数字")
    } else if (password.value !== confirmPassword.value) {
      showToast("两次密码输入不一致")
    } else if (invitationCode.value && !/^\d{8}$/.test(invitationCode.value)) {
      showToast("邀请码应为8位数字")
    }
    return
  }

  if (!phone.value || !code.value) {
    showToast("注册信息不完整，请返回重试")
    return
  }

  loading.value = true
  uni.showLoading({
    title: "注册中..."
  })

  try {
    // 验证验证码有效性
    const verifyData = {
      mobile: phone.value,
      code: code.value
    }

    const verifyResult = await checkVerificationCode(verifyData)
    if (verifyResult.code !== 200) {
      showToast("验证码已失效，请返回重新获取")
      loading.value = false
      uni.hideLoading()
      return
    }

    // 注册用户
    const res = await POST_REGISTER_USER({
      phone: phone.value,
      password: encryptPassword(password.value),
      username: phone.value, // 默认使用手机号作为用户名
      code: code.value,
      invitationCode: invitationCode.value
    })

    if (res.code === 200) {
      // 注册成功后自动登录
      handleLogin()
    } else {
      showToast("注册失败")
    }
  } catch (error) {
    console.error("注册过程出错:", error)
    showToast("网络异常，请重试")
  } finally {
    loading.value = false
    uni.hideLoading()
  }
}

const handleLogin = async () => {
  const params = {
    username: phone.value,
    password: encryptPassword(password.value),
    loginType: "password"
  }

  const res = await store.dispatch("login", params)
  if (res.success) {
    showToast("登录成功")
    // 清除缓存的注册信息
    uni.removeStorageSync("registrationPhone")
    uni.removeStorageSync("registrationCode")
    // 延迟跳转，让用户看到成功提示
    setTimeout(() => {
      uni.reLaunch({
        url: "/pages/views/home/<USER>"
      })
    }, 1000)
  } else {
    showToast(res.message || "登录失败")
  }
}
</script>

<style scoped lang="scss">
$primary-color: #9c4400;
$text-color: #333333;
$placeholder-color: #999999;
$border-color: #e5e5e5;

.set-password {
  width: 100%;
  min-height: 100vh;
  background-color: #ffffff;
  display: flex;
  flex-direction: column;
  padding: 0 24px;
  box-sizing: border-box;
}

.header {
  margin-top: 60px;
  margin-bottom: 40px;
}

.title {
  font-size: 28px;
  font-weight: bold;
  color: $text-color;
  margin-bottom: 10px;
}

.subtitle {
  font-size: 16px;
  color: $placeholder-color;
}

.form-container {
  width: 100%;
  flex-grow: 1;
}

.form-item {
  height: 50px;
  border-bottom: 1px solid $border-color;
  display: flex;
  align-items: center;
  margin-bottom: 12px;
}

.input {
  flex: 1;
  height: 100%;
  font-size: 16px;
  color: $text-color;
  margin-left: 12px;
}

.placeholder {
  color: $placeholder-color;
}

.password-tips {
  font-size: 12px;
  color: $placeholder-color;
  margin-bottom: 20px;
  padding-left: 5px;
}

.submit-button {
  width: 100%;
  height: 48px;
  background-color: $primary-color;
  color: #ffffff;
  font-size: 16px;
  font-weight: bold;
  border-radius: 24px;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-top: 40px;
  border: none;
}

.submit-button[disabled] {
  background-color: #cccccc;
}

.button-hover {
  opacity: 0.9;
  transform: scale(0.98);
}
</style>
