import 'package:freezed_annotation/freezed_annotation.dart';

import 'package:soko/core/enums/app_enums.dart';

part 'payment_request.freezed.dart';
part 'payment_request.g.dart';

/// 支付请求
@freezed
class PaymentRequest with _$PaymentRequest {
  const factory PaymentRequest({
    /// 订单ID
    required String orderId,
    
    /// 支付金额（分）
    required int amount,
    
    /// 支付方式
    required PaymentMethod paymentMethod,
    
    /// 支付标题
    required String subject,
    
    /// 支付描述
    String? body,
    
    /// 回调地址
    String? notifyUrl,
    
    /// 返回地址
    String? returnUrl,
    
    /// 超时时间（分钟）
    @Default(30) int timeoutMinutes,
    
    /// 是否沙盒环境
    @Default(true) bool isSandbox,
    
    /// 扩展参数
    Map<String, dynamic>? extraParams,
  }) = _PaymentRequest;

  factory PaymentRequest.fromJson(Map<String, dynamic> json) =>
      _$PaymentRequestFromJson(json);
}

/// 支付响应
@freezed
class PaymentResponse with _$PaymentResponse {
  const factory PaymentResponse({
    /// 支付订单号
    required String paymentOrderNo,
    
    /// 支付方式
    required PaymentMethod paymentMethod, /// 支付状态
    required PaymentStatus status, /// 支付金额（分）
    required int amount, /// 第三方支付订单号
    String? thirdPartyOrderNo,
    
    /// 支付时间
    DateTime? payTime,
    
    /// 支付参数（用于调起支付）
    Map<String, dynamic>? paymentParams,
    
    /// 错误信息
    String? errorMessage,
    
    /// 扩展信息
    Map<String, dynamic>? extraInfo,
  }) = _PaymentResponse;

  factory PaymentResponse.fromJson(Map<String, dynamic> json) =>
      _$PaymentResponseFromJson(json);
}

/// 支付状态枚举
enum PaymentStatus {
  @JsonValue('pending')
  pending, // 待支付
  
  @JsonValue('processing')
  processing, // 支付中
  
  @JsonValue('success')
  success, // 支付成功
  
  @JsonValue('failed')
  failed, // 支付失败
  
  @JsonValue('cancelled')
  cancelled, // 支付取消
  
  @JsonValue('timeout')
  timeout, // 支付超时
}

/// 支付状态扩展
extension PaymentStatusExtension on PaymentStatus {
  String get displayName {
    switch (this) {
      case PaymentStatus.pending:
        return '待支付';
      case PaymentStatus.processing:
        return '支付中';
      case PaymentStatus.success:
        return '支付成功';
      case PaymentStatus.failed:
        return '支付失败';
      case PaymentStatus.cancelled:
        return '支付取消';
      case PaymentStatus.timeout:
        return '支付超时';
    }
  }

  bool get isCompleted {
    return this == PaymentStatus.success ||
           this == PaymentStatus.failed ||
           this == PaymentStatus.cancelled ||
           this == PaymentStatus.timeout;
  }

  bool get isSuccess {
    return this == PaymentStatus.success;
  }
}
