<template>
  <view class="flex flex-col min-h-screen bg-slate-50">
    <!-- 使用自定义NavBar组件 -->
    <NavBar
      title="售后服务"
      bgColor="#FFFFFF"
      color="#333333"
      :showBack="true"
      :fixed="true"
    >
    </NavBar>

    <!-- 售后状态标签 -->
    <scroll-view
      scroll-x
      class="bg-white border-b whitespace-nowrap"
      :show-scrollbar="false"
      :scroll-into-view="`tab-${activeTab}`"
      scroll-with-animation
    >
      <view class="flex px-4 h-12 items-center flex-nowrap">
        <view
          v-for="(tab, index) in tabs"
          :key="index"
          :id="`tab-${tab.value}`"
          :class="[
            'px-4 py-1.5 mx-1 rounded-full inline-block whitespace-nowrap transition-all duration-300',
            activeTab === tab.value
              ? 'bg-primary text-white shadow-sm'
              : 'text-text-secondary bg-slate-100'
          ]"
          @click="filterListByTab(tab.value)"
        >
          <text class="text-sm font-medium whitespace-nowrap">{{
            tab.label
          }}</text>
        </view>
      </view>
    </scroll-view>

    <!-- 主内容区域 -->
    <van-pull-refresh v-model="refreshLoading" @refresh="onRefresh">
      <view class="flex-1 p-4">
        <view class="max-w-2xl mx-auto w-full">
          <!-- 无售后状态 -->
          <view
            class="flex flex-col items-center justify-center py-10"
            v-if="!filteredAfterSales.length"
          >
            <view
              class="w-24 h-24 bg-slate-100 rounded-full flex items-center justify-center mb-6 shadow-inner"
            >
              <van-icon name="after-sale" size="36" color="#666666" />
            </view>
            <text class="text-xl font-semibold mb-2 text-text-primary"
              >暂无售后</text
            >
            <text class="text-text-secondary text-sm text-center mb-8 max-w-xs"
              >您还没有符合条件的售后单，浏览已购商品</text
            >
            <view
              @click="goToOrders"
              class="bg-gradient-to-r from-heroic to-villain text-white rounded-full px-8 py-3 shadow-lg shadow-villain/20 active:translate-y-0.5 transition-transform"
            >
              <text class="text-sm font-medium">查看订单</text>
            </view>
          </view>

          <!-- 售后列表 -->
          <view class="space-y-4 pb-safe" v-else>
            <scroll-view
              class="w-full"
              scroll-y
              @scrolltolower="onReachBottom"
              :style="{ height: scrollViewHeight + 'px' }"
            >
              <view
                v-for="(afterSale, index) in filteredAfterSales"
                :key="afterSale.id"
              >
                <view
                  class="bg-white rounded-xl overflow-hidden shadow-sm active:bg-slate-50 transition-colors"
                  :class="{ 'mb-3': index < filteredAfterSales.length - 1 }"
                  @click="goToAfterSaleDetail(afterSale.id)"
                >
                  <!-- 顶部 售后单号和申请时间 -->
                  <view
                    class="flex justify-between items-center px-4 pt-3 pb-1"
                  >
                    <text class="text-xs text-text-secondary"
                      >售后单号：{{ afterSale.afterSaleNo || "-" }}</text
                    >
                    <text class="text-xs text-text-secondary">{{
                      afterSale.applyTime
                        ? moment(afterSale.applyTime).format("YYYY-MM-DD")
                        : "-"
                    }}</text>
                  </view>
                  <view class="h-px bg-slate-100 mx-4"></view>
                  <!-- 商品信息和状态 -->
                  <view class="flex px-4 py-3 items-center">
                    <view
                      v-if="afterSale.fileUrl"
                      class="w-16 h-16 mr-3 rounded-md overflow-hidden bg-slate-100 flex-shrink-0"
                    >
                      <image
                        :src="getFileUrl(afterSale.fileUrl)"
                        mode="aspectFill"
                        class="w-full h-full"
                      ></image>
                    </view>
                    <view class="flex-1 min-w-0">
                      <view class="flex items-center justify-between">
                        <text
                          class="text-sm text-text-primary font-semibold truncate"
                          >{{ afterSale.productName || "商品信息" }}</text
                        >
                        <view
                          :class="
                            getStatusBadgeClass(afterSale.afterSaleStatus)
                          "
                          class="ml-2 flex-shrink-0"
                        >
                          <van-icon
                            :name="getStatusIconName(afterSale.afterSaleStatus)"
                            class="mr-1.5"
                            size="12"
                          />
                          <text class="text-xs font-medium">{{
                            getStatusText(afterSale.afterSaleStatus)
                          }}</text>
                        </view>
                      </view>
                      <text
                        v-if="afterSale.specification"
                        class="text-xs text-text-secondary block mt-1 truncate"
                        >{{
                          formatSpecification(afterSale.specification)
                        }}</text
                      >
                      <text class="text-base text-primary font-bold block mt-1"
                        >¥{{ afterSale.amount?.toFixed(2) ?? "0.00" }}</text
                      >
                    </view>
                  </view>
                  <view class="flex justify-end items-center px-4 pb-3 pt-1">
                    <view
                      :class="getActionButtonClass(afterSale.afterSaleStatus)"
                      @click.stop="handleActionButton(afterSale)"
                    >
                      <text>{{
                        getActionButtonText(afterSale.afterSaleStatus)
                      }}</text>
                    </view>
                  </view>
                </view>
              </view>

              <!-- 加载状态 -->
              <view
                v-if="listLoading"
                class="py-3 flex justify-center items-center"
              >
                <u-loading-icon
                  size="28"
                  mode="circle"
                  color="#909399"
                ></u-loading-icon>
                <text class="ml-2 text-text-secondary text-sm">加载中...</text>
              </view>

              <!-- 没有更多数据提示 -->
              <view
                v-if="finished && filteredAfterSales.length > 0"
                class="py-3 flex justify-center"
              >
                <text class="text-text-secondary text-sm">没有更多了</text>
              </view>
            </scroll-view>
          </view>
        </view>
      </view>
    </van-pull-refresh>
  </view>
</template>

<script lang="ts" setup>
import { getMyAfterSaleList } from "@/api/api.js"
import NavBar from "@/components/NavBar.vue"
import { onLoad } from "@dcloudio/uni-app"
import moment from "moment"
import { reactive, ref } from "vue"

// 售后状态类型
type AfterSaleStatus =
  | "PENDING"
  | "REJECTED"
  | "RETURN_PENDING"
  | "RETURNING"
  | "RETURNED"
  | "REFUNDED"
  | "CLOSED"

// 售后单类型
interface AfterSale {
  id: string
  afterSaleNo: string
  orderNo: string
  applyTime: number
  afterSaleStatus: AfterSaleStatus
  amount: number
  description: string
  afterSaleType: string
  reason: string
  remark: string
  productName?: string
  fileUrl?: string
  specification?: string
  returnQuantity?: number
  returnAmount?: number
  returnReceivedTime?: number
  expressCompany?: string
  expressNo?: string
  returnTime?: number
  refundTime?: number
  returnAddress?: string
  returnName?: string
  returnPhone?: string
  images?: string
}

// 标签选项
const tabs = [
  { value: "", label: "全部" },
  { value: "PROCESSING", label: "待处理" }
]

// 响应式状态
const activeTab = ref("")
const isLoading = ref(true)
const filteredAfterSales = ref<AfterSale[]>([])
const pageNoParams = reactive({
  page: 1,
  size: 10
})
const totalAfterSales = ref(0)
const listLoading = ref(false)
const finished = ref(false)
const scrollViewHeight = ref(500)
const refreshLoading = ref(false)

onLoad((option) => {
  activeTab.value = option.status || ""
  calculateScrollViewHeight()
  loadList()
})

const filterListByTab = (tabValue: string) => {
  activeTab.value = tabValue
  pageNoParams.page = 1
  finished.value = false
  fetchAfterSales(pageNoParams)
}

const loadList = () => {
  if (!finished.value && !listLoading.value) {
    listLoading.value = true
    fetchAfterSales(pageNoParams)
  }
}

async function fetchAfterSales(pageParams: { page: number; size: number }) {
  const params: {
    page: number
    size: number
    afterSaleStatus?: string
  } = { ...pageParams }

  // 处理状态筛选
  if (activeTab.value === "PROCESSING") {
    // 待处理是一个自定义分类，前端处理
    // 不设置afterSaleStatus参数，后续在前端筛选
  } else if (activeTab.value) {
    // 如果选择了具体状态，则直接传给后端
    params.afterSaleStatus = activeTab.value
  }

  listLoading.value = true
  const res = await getMyAfterSaleList(params)
  const resData = res?.data

  // 兼容后端返回结构
  if (resData) {
    // 根据activeTab进行前端筛选
    let filteredRecords = resData.records || []

    if (activeTab.value === "PROCESSING") {
      // 待处理包含多种状态
      const processingStatuses = [
        "PENDING",
        "RETURN_PENDING",
        "RETURNING",
        "RETURNED"
      ]
      filteredRecords = filteredRecords.filter((item) =>
        processingStatuses.includes(item.afterSaleStatus)
      )
    }

    // 更新计数和列表
    totalAfterSales.value =
      activeTab.value === "PROCESSING"
        ? filteredRecords.length
        : resData.total || 0

    // 处理分页加载
    if (params.page === 1) {
      filteredAfterSales.value = filteredRecords
    } else {
      filteredAfterSales.value = [
        ...filteredAfterSales.value,
        ...filteredRecords
      ]
    }

    // 更新页码，为下一次加载做准备
    pageNoParams.page++
  } else {
    filteredAfterSales.value = []
    totalAfterSales.value = 0
  }

  listLoading.value = false
  finished.value = filteredAfterSales.value.length >= totalAfterSales.value
}

// 状态样式
function getStatusBadgeClass(status: AfterSaleStatus): string {
  switch (status) {
    case "PENDING":
      return "flex items-center border rounded-full px-2.5 py-1 border-warning text-warning"
    case "REJECTED":
      return "flex items-center border rounded-full px-2.5 py-1 border-destructive text-destructive"
    case "RETURN_PENDING":
      return "flex items-center border rounded-full px-2.5 py-1 border-primary text-primary"
    case "RETURNING":
      return "flex items-center border rounded-full px-2.5 py-1 border-accent text-accent"
    case "RETURNED":
      return "flex items-center border rounded-full px-2.5 py-1 border-heroic text-heroic"
    case "REFUNDED":
      return "flex items-center border rounded-full px-2.5 py-1 border-success text-success"
    case "CLOSED":
      return "flex items-center border rounded-full px-2.5 py-1 border-text-secondary text-text-secondary"
    default:
      return "flex items-center border rounded-full px-2.5 py-1 border-text-secondary text-text-secondary"
  }
}

// 状态图标名称
function getStatusIconName(status: AfterSaleStatus): string {
  switch (status) {
    case "PENDING":
      return "clock-o"
    case "REJECTED":
      return "close"
    case "RETURN_PENDING":
      return "logistics"
    case "RETURNING":
      return "send-gift-o"
    case "RETURNED":
      return "gold-coin-o"
    case "REFUNDED":
      return "passed"
    case "CLOSED":
      return "delete-o"
    default:
      return "question-o"
  }
}

// 状态文本
function getStatusText(status: AfterSaleStatus): string {
  switch (status) {
    case "PENDING":
      return "待处理"
    case "REJECTED":
      return "已拒绝"
    case "RETURN_PENDING":
      return "待寄回"
    case "RETURNING":
      return "退货中"
    case "RETURNED":
      return "待退款"
    case "REFUNDED":
      return "已完成"
    case "CLOSED":
      return "已关闭"
    default:
      return "未知状态"
  }
}

// 状态操作按钮样式
function getActionButtonClass(status: AfterSaleStatus): string {
  switch (status) {
    case "PENDING":
      return "py-1.5 px-4 bg-slate-100 text-text-secondary rounded-full text-sm"
    case "REJECTED":
      return "py-1.5 px-4 bg-slate-100 text-text-secondary rounded-full text-sm"
    case "RETURN_PENDING":
      return "py-1.5 px-4 bg-primary text-white rounded-full text-sm"
    case "RETURNING":
      return "py-1.5 px-4 bg-slate-100 text-text-secondary rounded-full text-sm"
    case "RETURNED":
      return "py-1.5 px-4 bg-slate-100 text-text-secondary rounded-full text-sm"
    case "REFUNDED":
      return "py-1.5 px-4 bg-success/10 text-success rounded-full text-sm"
    case "CLOSED":
      return "py-1.5 px-4 bg-slate-100 text-text-secondary rounded-full text-sm"
    default:
      return "py-1.5 px-4 bg-slate-100 text-text-secondary rounded-full text-sm"
  }
}

// 状态操作按钮文本
function getActionButtonText(status: AfterSaleStatus): string {
  switch (status) {
    case "PENDING":
      return "售后详情"
    case "REJECTED":
      return "查看详情"
    case "RETURN_PENDING":
      return "去寄回"
    case "RETURNING":
      return "查看进度"
    case "RETURNED":
      return "等待退款"
    case "REFUNDED":
      return "已完成"
    case "CLOSED":
      return "已关闭"
    default:
      return "查看详情"
  }
}

// 按钮处理
function handleActionButton(afterSale: AfterSale) {
  goToAfterSaleDetail(afterSale.id)
}

// 跳转到售后详情
function goToAfterSaleDetail(id: string) {
  uni.navigateTo({
    url: `/pages/views/after-sale/afterSale-detail?id=${id}`,
    fail: (err) => {
      console.error("导航到售后详情失败", err)
      uni.showToast({
        title: "访问详情失败",
        icon: "none"
      })
    }
  })
}

// 跳转到订单列表
function goToOrders() {
  uni.navigateTo({
    url: "/pages/views/order/list"
  })
}

// 获取文件URL，处理可能的相对路径
function getFileUrl(url: string): string {
  if (!url) return "/static/placeholder.png"

  if (url.startsWith("http://") || url.startsWith("https://")) {
    return url
  } else {
    // 根据实际情况处理相对路径
    // 使用环境变量，修复import.meta.env的linter错误
    return process.env.VITE_APP_BASE_API + url
  }
}

// 计算scroll-view的高度
function calculateScrollViewHeight() {
  const info = uni.getSystemInfoSync()
  // 获取状态栏高度
  const statusBarHeight = info.statusBarHeight || 0
  // 导航栏高度一般为44px
  const navBarHeight = 44
  // 搜索框和状态标签的高度估算约为120px
  const searchAndTabsHeight = 120
  // 计算scroll-view高度 = 窗口高度 - 状态栏高度 - 导航栏高度 - 搜索和状态标签高度 - 底部安全区域高度
  const bottomSafeArea = info.screenHeight - info.safeArea.bottom || 0
  scrollViewHeight.value =
    info.windowHeight -
    statusBarHeight -
    navBarHeight -
    searchAndTabsHeight -
    bottomSafeArea
}

// 滚动到底部触发加载更多
function onReachBottom() {
  if (!finished.value && !listLoading.value) {
    loadList()
  }
}

// 规格友好展示
function formatSpecification(spec: string): string {
  if (!spec) return "-"
  try {
    const obj = JSON.parse(spec)
    if (typeof obj === "object" && obj !== null) {
      return Object.entries(obj)
        .map(([k, v]) => `${k}: ${v}`)
        .join("，")
    }
    return String(obj)
  } catch {
    return spec
  }
}

const onRefresh = () => {
  filterListByTab(activeTab.value)
  refreshLoading.value = false
}
</script>

<style scoped></style>
