import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:go_router/go_router.dart';

import 'package:soko/shared/presentation/widgets/custom_app_bar.dart';
import 'package:soko/shared/presentation/widgets/loading_widget.dart';
import 'package:soko/shared/presentation/widgets/error_retry_widget.dart';
import 'package:soko/features/recycle/presentation/providers/price_evaluation_provider.dart';
import 'package:soko/features/recycle/presentation/widgets/price_evaluation_form.dart';
import 'package:soko/features/recycle/presentation/widgets/price_evaluation_result_card.dart';
import 'package:soko/features/recycle/presentation/widgets/price_factors_section.dart';
import 'package:soko/features/recycle/presentation/widgets/market_trend_section.dart';
import 'package:soko/features/recycle/domain/entities/recycle_models.dart';

/// 价格评估页面
class PriceEvaluationPage extends ConsumerStatefulWidget {
  const PriceEvaluationPage({super.key});

  @override
  ConsumerState<PriceEvaluationPage> createState() => _PriceEvaluationPageState();
}

class _PriceEvaluationPageState extends ConsumerState<PriceEvaluationPage>
    with TickerProviderStateMixin {
  late TabController _tabController;
  final PageController _pageController = PageController();

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 2, vsync: this);
  }

  @override
  void dispose() {
    _tabController.dispose();
    _pageController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final evaluationState = ref.watch(priceEvaluationProvider);

    return Scaffold(
      appBar: CustomAppBar(
        title: '价格评估',
        showBackButton: true,
        bottom: TabBar(
          controller: _tabController,
          onTap: (index) {
            _pageController.animateToPage(
              index,
              duration: const Duration(milliseconds: 300),
              curve: Curves.easeInOut,
            );
          },
          tabs: const [
            Tab(text: '智能评估'),
            Tab(text: '评估结果'),
          ],
        ),
      ),
      body: PageView(
        controller: _pageController,
        onPageChanged: (index) {
          _tabController.animateToIndex(index);
        },
        children: [
          // 智能评估页面
          _buildEvaluationForm(),
          
          // 评估结果页面
          _buildEvaluationResult(evaluationState),
        ],
      ),
    );
  }

  /// 构建评估表单页面
  Widget _buildEvaluationForm() {
    return SingleChildScrollView(
      padding: EdgeInsets.all(16.w),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // 页面说明
          Container(
            padding: EdgeInsets.all(16.w),
            decoration: BoxDecoration(
              color: Colors.blue[50],
              borderRadius: BorderRadius.circular(12.r),
              border: Border.all(color: Colors.blue[200]!),
            ),
            child: Row(
              children: [
                Icon(
                  Icons.lightbulb_outline,
                  color: Colors.blue[600],
                  size: 20.w,
                ),
                SizedBox(width: 8.w),
                Expanded(
                  child: Text(
                    '填写设备详细信息，AI将为您提供精准的价格评估',
                    style: TextStyle(
                      fontSize: 12.sp,
                      color: Colors.blue[700],
                    ),
                  ),
                ),
              ],
            ),
          ),
          SizedBox(height: 20.h),
          
          // 评估表单
          PriceEvaluationForm(
            onSubmit: (request) => _submitEvaluation(request),
          ),
        ],
      ),
    );
  }

  /// 构建评估结果页面
  Widget _buildEvaluationResult(AsyncValue<PriceEvaluationResult?> state) {
    return state.when(
      data: (result) {
        if (result == null) {
          return _buildEmptyResult();
        }
        return _buildResultContent(result);
      },
      loading: () => const LoadingWidget(),
      error: (error, stackTrace) => ErrorRetryWidget(
        message: error.toString(),
        onRetry: () => ref.read(priceEvaluationProvider.notifier).retry(),
      ),
    );
  }

  /// 构建空结果状态
  Widget _buildEmptyResult() {
    return Center(
      child: Padding(
        padding: EdgeInsets.all(32.w),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.assessment_outlined,
              size: 80.w,
              color: Colors.grey[400],
            ),
            SizedBox(height: 24.h),
            Text(
              '暂无评估结果',
              style: TextStyle(
                fontSize: 18.sp,
                fontWeight: FontWeight.w600,
                color: Colors.grey[700],
              ),
            ),
            SizedBox(height: 8.h),
            Text(
              '请先在智能评估页面填写设备信息',
              style: TextStyle(
                fontSize: 14.sp,
                color: Colors.grey[500],
              ),
            ),
            SizedBox(height: 24.h),
            ElevatedButton(
              onPressed: () {
                _tabController.animateToIndex(0);
                _pageController.animateToPage(
                  0,
                  duration: const Duration(milliseconds: 300),
                  curve: Curves.easeInOut,
                );
              },
              style: ElevatedButton.styleFrom(
                backgroundColor: Theme.of(context).primaryColor,
                foregroundColor: Colors.white,
              ),
              child: const Text('开始评估'),
            ),
          ],
        ),
      ),
    );
  }

  /// 构建结果内容
  Widget _buildResultContent(PriceEvaluationResult result) {
    return RefreshIndicator(
      onRefresh: () => ref.read(priceEvaluationProvider.notifier).refresh(),
      child: SingleChildScrollView(
        padding: EdgeInsets.all(16.w),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // 价格评估结果卡片
            PriceEvaluationResultCard(result: result),
            SizedBox(height: 16.h),
            
            // 价格影响因素
            PriceFactorsSection(factors: result.factors),
            SizedBox(height: 16.h),
            
            // 市场趋势
            if (result.marketTrend != null)
              MarketTrendSection(trend: result.marketTrend!),
            if (result.marketTrend != null)
              SizedBox(height: 16.h),
            
            // 建议和说明
            if (result.recommendations != null && result.recommendations!.isNotEmpty)
              _buildRecommendationsSection(result.recommendations!),
            
            // 操作按钮
            SizedBox(height: 24.h),
            _buildActionButtons(result),
            
            // 底部安全距离
            SizedBox(height: 32.h),
          ],
        ),
      ),
    );
  }

  /// 构建建议部分
  Widget _buildRecommendationsSection(List<String> recommendations) {
    return Container(
      padding: EdgeInsets.all(16.w),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(12.r),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.05),
            blurRadius: 8,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(
                Icons.tips_and_updates,
                size: 20.w,
                color: Theme.of(context).primaryColor,
              ),
              SizedBox(width: 8.w),
              Text(
                '优化建议',
                style: TextStyle(
                  fontSize: 16.sp,
                  fontWeight: FontWeight.w600,
                ),
              ),
            ],
          ),
          SizedBox(height: 12.h),
          ...recommendations.map((recommendation) => Padding(
            padding: EdgeInsets.only(bottom: 8.h),
            child: Row(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Container(
                  width: 4.w,
                  height: 4.w,
                  margin: EdgeInsets.only(top: 8.h, right: 8.w),
                  decoration: BoxDecoration(
                    color: Theme.of(context).primaryColor,
                    shape: BoxShape.circle,
                  ),
                ),
                Expanded(
                  child: Text(
                    recommendation,
                    style: TextStyle(
                      fontSize: 14.sp,
                      color: Colors.grey[700],
                      height: 1.4,
                    ),
                  ),
                ),
              ],
            ),
          )),
        ],
      ),
    );
  }

  /// 构建操作按钮
  Widget _buildActionButtons(PriceEvaluationResult result) {
    return Column(
      children: [
        // 创建回收订单按钮
        SizedBox(
          width: double.infinity,
          height: 48.h,
          child: ElevatedButton.icon(
            onPressed: () => _createRecycleOrder(result),
            icon: const Icon(Icons.add_circle_outline),
            label: const Text('创建回收订单'),
            style: ElevatedButton.styleFrom(
              backgroundColor: Theme.of(context).primaryColor,
              foregroundColor: Colors.white,
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(8.r),
              ),
            ),
          ),
        ),
        SizedBox(height: 12.h),
        
        // 重新评估按钮
        SizedBox(
          width: double.infinity,
          height: 48.h,
          child: OutlinedButton.icon(
            onPressed: () {
              _tabController.animateToIndex(0);
              _pageController.animateToPage(
                0,
                duration: const Duration(milliseconds: 300),
                curve: Curves.easeInOut,
              );
            },
            icon: const Icon(Icons.refresh),
            label: const Text('重新评估'),
            style: OutlinedButton.styleFrom(
              foregroundColor: Theme.of(context).primaryColor,
              side: BorderSide(color: Theme.of(context).primaryColor),
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(8.r),
              ),
            ),
          ),
        ),
      ],
    );
  }

  /// 提交评估请求
  void _submitEvaluation(PriceEvaluationRequest request) {
    ref.read(priceEvaluationProvider.notifier).evaluate(request);
    
    // 自动切换到结果页面
    _tabController.animateToIndex(1);
    _pageController.animateToPage(
      1,
      duration: const Duration(milliseconds: 300),
      curve: Curves.easeInOut,
    );
  }

  /// 创建回收订单
  void _createRecycleOrder(PriceEvaluationResult result) {
    // TODO: 导航到创建回收订单页面，并传递评估结果
    context.push('/recycle/create?evaluationId=${result.id}');
  }
}
