import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:go_router/go_router.dart';

import 'package:soko/features/auth/presentation/pages/login_page.dart';
import 'package:soko/features/home/<USER>/pages/home_page.dart';
import 'package:soko/features/product/presentation/pages/product_detail_page.dart';
import 'package:soko/features/product/presentation/pages/product_list_page.dart';
import 'package:soko/features/cart/presentation/pages/cart_page.dart';
import 'package:soko/features/profile/presentation/pages/profile_page.dart';
import 'package:soko/features/recycle/presentation/pages/recycle_home_page.dart';
import 'package:soko/features/recycle/presentation/pages/create_recycle_order_page.dart';
import 'package:soko/features/order/presentation/pages/order_list_page.dart';
import 'package:soko/shared/presentation/pages/main_page.dart';
import 'package:soko/shared/presentation/pages/splash_page.dart';
import 'package:soko/core/router/app_routes.dart';

/// 路由配置提供者
final appRouterProvider = Provider<GoRouter>((ref) {
  return GoRouter(
    initialLocation: AppRoutes.splash,
    debugLogDiagnostics: true,
    routes: [
      // 启动页
      GoRoute(
        path: AppRoutes.splash,
        name: 'splash',
        builder: (context, state) => const SplashPage(),
      ),
      
      // 登录页
      GoRoute(
        path: AppRoutes.login,
        name: 'login',
        builder: (context, state) => const LoginPage(),
      ),
      
      // 主页面（包含底部导航）
      ShellRoute(
        builder: (context, state, child) => MainPage(child: child),
        routes: [
          // 首页
          GoRoute(
            path: AppRoutes.home,
            name: 'home',
            builder: (context, state) => const HomePage(),
          ),
          
          // 购物车
          GoRoute(
            path: AppRoutes.cart,
            name: 'cart',
            builder: (context, state) => const CartPage(),
          ),
          
          // 回收首页
          GoRoute(
            path: AppRoutes.recycle,
            name: 'recycle',
            builder: (context, state) => const RecycleHomePage(),
          ),
          
          // 个人中心
          GoRoute(
            path: AppRoutes.profile,
            name: 'profile',
            builder: (context, state) => const ProfilePage(),
          ),
        ],
      ),
      
      // 商品相关页面
      GoRoute(
        path: AppRoutes.productList,
        name: 'productList',
        builder: (context, state) {
          final categoryId = state.uri.queryParameters['categoryId'];
          final keyword = state.uri.queryParameters['keyword'];
          return ProductListPage(
            categoryId: categoryId,
            keyword: keyword,
          );
        },
      ),
      
      GoRoute(
        path: AppRoutes.productDetail,
        name: 'productDetail',
        builder: (context, state) {
          final productId = state.pathParameters['id']!;
          return ProductDetailPage(productId: productId);
        },
      ),
      
      // 回收相关页面
      GoRoute(
        path: AppRoutes.createRecycleOrder,
        name: 'createRecycleOrder',
        builder: (context, state) => const CreateRecycleOrderPage(),
      ),
      
      // 订单相关页面
      GoRoute(
        path: AppRoutes.orderList,
        name: 'orderList',
        builder: (context, state) {
          final type = state.uri.queryParameters['type'];
          return OrderListPage(orderType: type);
        },
      ),
    ],
    
    // 错误页面
    errorBuilder: (context, state) => Scaffold(
      appBar: AppBar(title: const Text('页面不存在')),
      body: Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            const Icon(Icons.error_outline, size: 64, color: Colors.grey),
            const SizedBox(height: 16),
            Text('页面不存在: ${state.uri.toString()}'),
            const SizedBox(height: 16),
            ElevatedButton(
              onPressed: () => context.go(AppRoutes.home),
              child: const Text('返回首页'),
            ),
          ],
        ),
      ),
    ),
    
    // 路由重定向
    redirect: (context, state) {
      // 这里可以添加登录状态检查等逻辑
      return null;
    },
  );
});
