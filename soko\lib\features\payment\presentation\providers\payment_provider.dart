import 'dart:async';

import 'package:flutter/foundation.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';

import 'package:soko/core/state/base_state.dart';
import 'package:soko/features/payment/domain/entities/payment_request.dart';
import 'package:soko/features/payment/domain/usecases/create_payment_usecase.dart';
import 'package:soko/features/payment/domain/usecases/query_payment_status_usecase.dart';

/// 支付状态
@immutable
class PaymentState {

  const PaymentState({
    this.createPaymentState = const AsyncState.idle(),
    this.queryPaymentState = const AsyncState.idle(),
    this.currentPaymentRequest,
    this.currentPaymentResponse,
    this.statusQueryTimer,
  });
  final AsyncState<PaymentResponse> createPaymentState;
  final AsyncState<PaymentResponse> queryPaymentState;
  final PaymentRequest? currentPaymentRequest;
  final PaymentResponse? currentPaymentResponse;
  final Timer? statusQueryTimer;

  PaymentState copyWith({
    AsyncState<PaymentResponse>? createPaymentState,
    AsyncState<PaymentResponse>? queryPaymentState,
    PaymentRequest? currentPaymentRequest,
    PaymentResponse? currentPaymentResponse,
    Timer? statusQueryTimer,
  }) {
    return PaymentState(
      createPaymentState: createPaymentState ?? this.createPaymentState,
      queryPaymentState: queryPaymentState ?? this.queryPaymentState,
      currentPaymentRequest: currentPaymentRequest ?? this.currentPaymentRequest,
      currentPaymentResponse: currentPaymentResponse ?? this.currentPaymentResponse,
      statusQueryTimer: statusQueryTimer ?? this.statusQueryTimer,
    );
  }

  /// 是否正在支付中
  bool get isPaymentInProgress {
    return currentPaymentResponse?.status == PaymentStatus.processing ||
           currentPaymentResponse?.status == PaymentStatus.pending;
  }

  /// 支付是否完成
  bool get isPaymentCompleted {
    return currentPaymentResponse?.status.isCompleted == true;
  }

  /// 支付是否成功
  bool get isPaymentSuccess {
    return currentPaymentResponse?.status.isSuccess == true;
  }
}

/// 支付状态管理器
class PaymentNotifier extends StateNotifier<PaymentState> {

  PaymentNotifier(
    this._createPaymentUseCase,
    this._queryPaymentStatusUseCase,
  ) : super(const PaymentState());
  final CreatePaymentUseCase _createPaymentUseCase;
  final QueryPaymentStatusUseCase _queryPaymentStatusUseCase;

  @override
  void dispose() {
    // 清理定时器
    state.statusQueryTimer?.cancel();
    super.dispose();
  }

  /// 创建支付
  Future<PaymentResponse?> createPayment(PaymentRequest request) async {
    state = state.copyWith(
      createPaymentState: const AsyncState.loading(),
      currentPaymentRequest: request,
    );

    try {
      final result = await _createPaymentUseCase(request);

      return result.fold(
        (failure) {
          state = state.copyWith(
            createPaymentState: AsyncState.error(failure.message),
          );
          return null;
        },
        (response) {
          state = state.copyWith(
            createPaymentState: AsyncState.success(response),
            currentPaymentResponse: response,
          );

          // 如果支付状态是处理中，开始轮询查询状态
          if (response.status == PaymentStatus.processing ||
              response.status == PaymentStatus.pending) {
            _startStatusPolling(response.paymentOrderNo);
          }

          return response;
        },
      );
    } catch (e) {
      state = state.copyWith(
        createPaymentState: AsyncState.error(e.toString()),
      );
      return null;
    }
  }

  /// 查询支付状态
  Future<PaymentResponse?> queryPaymentStatus(String paymentOrderNo) async {
    state = state.copyWith(queryPaymentState: const AsyncState.loading());

    try {
      final result = await _queryPaymentStatusUseCase(paymentOrderNo);

      return result.fold(
        (failure) {
          state = state.copyWith(
            queryPaymentState: AsyncState.error(failure.message),
          );
          return null;
        },
        (response) {
          state = state.copyWith(
            queryPaymentState: AsyncState.success(response),
            currentPaymentResponse: response,
          );

          // 如果支付完成，停止轮询
          if (response.status.isCompleted) {
            _stopStatusPolling();
          }

          return response;
        },
      );
    } catch (e) {
      state = state.copyWith(
        queryPaymentState: AsyncState.error(e.toString()),
      );
      return null;
    }
  }

  /// 开始状态轮询
  void _startStatusPolling(String paymentOrderNo) {
    // 先停止之前的轮询
    _stopStatusPolling();

    // 每3秒查询一次支付状态
    final timer = Timer.periodic(const Duration(seconds: 3), (timer) {
      queryPaymentStatus(paymentOrderNo);
    });

    state = state.copyWith(statusQueryTimer: timer);
  }

  /// 停止状态轮询
  void _stopStatusPolling() {
    state.statusQueryTimer?.cancel();
    state = state.copyWith();
  }

  /// 重置支付状态
  void resetPaymentState() {
    _stopStatusPolling();
    state = const PaymentState();
  }

  /// 清除创建支付状态
  void clearCreatePaymentState() {
    state = state.copyWith(createPaymentState: const AsyncState.idle());
  }

  /// 清除查询支付状态
  void clearQueryPaymentState() {
    state = state.copyWith(queryPaymentState: const AsyncState.idle());
  }
}

/// 支付状态管理器提供者
final paymentProvider = StateNotifierProvider<PaymentNotifier, PaymentState>((ref) {
  final createPaymentUseCase = ref.read(createPaymentUseCaseProvider);
  final queryPaymentStatusUseCase = ref.read(queryPaymentStatusUseCaseProvider);
  return PaymentNotifier(createPaymentUseCase, queryPaymentStatusUseCase);
});
