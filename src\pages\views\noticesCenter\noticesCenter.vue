<template>
  <view class="flex flex-col min-h-screen bg-slate-50">
    <!-- 使用自定义NavBar组件 -->
    <NavBar
      title="消息中心"
      bgColor="#FFFFFF"
      color="#333333"
      :showBack="true"
      :fixed="true"
    >
    </NavBar>

    <!-- 主内容区域 -->
    <view class="flex-1 p-4">
      <view class="max-w-2xl mx-auto w-full">
        <!-- 活动列表 -->
        <view class="space-y-4 pb-safe" v-if="noticesList.length">
          <view
            v-for="item in noticesList"
            :key="item.id"
            class="bg-white rounded-xl overflow-hidden shadow-sm active:bg-slate-50 transition-colors relative"
            :class="{
              'border-l-4 border-green-500': item.showType === 'success',
              'border-l-4 border-yellow-500': item.showType === 'warning',
              'border-l-4 border-blue-500': item.showType === 'info',
              'border-l-4 border-red-500': item.showType === 'error'
            }"
          >
            <!-- 未读标记 -->
            <view
              v-if="item.isRead === 0"
              class="absolute top-2 right-2 w-2 h-2 rounded-full bg-red-500"
            ></view>

            <!-- 标题区域 -->
            <view class="p-4 pb-2">
              <text
                class="text-base font-semibold"
                :class="{
                  'text-green-600': item.showType === 'success',
                  'text-yellow-600': item.showType === 'warning',
                  'text-blue-600': item.showType === 'info',
                  'text-red-600': item.showType === 'error'
                }"
              >
                {{ item.title }}
              </text>
            </view>

            <view class="px-4 pb-2 flex items-center gap-1">
              <van-icon name="manager-o" size="14" color="#666666" />
              <text class="text-xs text-text-secondary">
                发送人：{{ item.creator }}
              </text>
            </view>

            <view class="px-4 pb-2 flex items-center gap-1">
              <van-icon name="clock-o" size="14" color="#666666" />
              <text class="text-xs text-text-secondary">
                发起时间：{{
                  item.createdTime
                    ? moment(item.createdTime).format("YYYY-MM-DD")
                    : "-"
                }}
              </text>
            </view>

            <!-- 操作区域 -->
            <view class="p-4 pt-2" @click="openDetail(item.id)">
              <view class="flex justify-between items-center">
                <text class="text-xs text-text-secondary">点击查看详情</text>
                <van-icon name="arrow" color="#666666" />
              </view>
            </view>
          </view>
        </view>
        <!-- 无订单状态 -->
        <view class="flex flex-col items-center justify-center py-10" v-else>
          <text class="text-xl font-semibold mb-2 text-text-primary"
            >暂无消息</text
          >
        </view>
      </view>
    </view>
    <up-popup
      v-model:show="showPopup"
      mode="bottom"
      round="16"
      safe-area-inset-bottom
    >
      <!-- 标题区域 -->
      <view class="p-4 pb-2">
        <text class="text-base font-semibold text-text-primary">
          {{ noticesDetail.title }}
        </text>
      </view>

      <view class="px-4 pb-2 flex items-center gap-1">
        <van-icon name="manager-o" size="14" color="#666666" />
        <text class="text-xs text-text-secondary">
          发送人：{{ noticesDetail.creator }}
        </text>
      </view>

      <view class="px-4 pb-2 flex items-center gap-1">
        <van-icon name="clock-o" size="14" color="#666666" />
        <text class="text-xs text-text-secondary">
          发起时间：{{
            noticesDetail.createdTime
              ? moment(noticesDetail.createdTime).format("YYYY-MM-DD")
              : "-"
          }}
        </text>
      </view>

      <scroll-view scroll-y class="px-4 mt-2 max-h-[600rpx] min-h-[400rpx]">
        <p>{{ noticesDetail.content }}</p>
      </scroll-view>
    </up-popup>
  </view>
</template>

<script lang="ts" setup>
import { ref } from "vue"
import NavBar from "@/components/NavBar.vue"
import { onLoad } from "@dcloudio/uni-app"
import { GET_NOTICES_LIST, POST_READ_MESSAGE } from "@/api/api"
import moment from "moment"

interface NoticesDetail {
  id: string
  title: string
  content: string
  createdTime: string
  showType: string
  isRead: number
  creator: string
}

const noticesList = ref<any[]>([])
const showPopup = ref(false)
const noticesDetail = ref<NoticesDetail>({
  id: "",
  title: "",
  content: "",
  createdTime: "",
  showType: "",
  isRead: 0,
  creator: ""
})

onLoad(async (options) => {
  await getNoticesList()
  if (options.id) {
    openDetail(options.id)
  }
})

const openDetail = (id: string) => {
  showPopup.value = true
  getNoticesDetail(id)
}

const getNoticesDetail = (id: string) => {
  noticesDetail.value = noticesList.value.find((item) => item.id === id)
  sendRead()
}

const sendRead = async () => {
  const params = {
    ...noticesDetail.value,
    readTime: moment(new Date()).valueOf(),
    isRead: 1,
    messageId: noticesDetail.value.id
  }
  const res = await POST_READ_MESSAGE(params)
  if (res.code === 200) {
    noticesList.value = noticesList.value.map((item) =>
      item.id === params.id ? params : item
    )
  } else {
    uni.showToast({ title: res.message, icon: "none" })
  }
}

const getNoticesList = async () => {
  const res = await GET_NOTICES_LIST({ targetDevice: 2 })
  if (res.code === 200) {
    const resData = res.data
    noticesList.value = resData
  } else {
    uni.showToast({ title: res.message, icon: "none" })
  }
}
</script>

<style lang="scss" scoped></style>
