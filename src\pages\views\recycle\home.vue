<template>
  <view class="min-h-screen bg-gray-50">
    <!-- 自定义导航栏 -->
    <StatusBarPlaceholder />

    <!-- 顶部横幅 -->
    <view class="relative h-48 bg-gradient-to-r from-primary-500 to-primary-600 overflow-hidden">
      <image
        src="/static/recycle-banner.jpg"
        class="absolute inset-0 w-full h-full object-cover opacity-30"
        mode="aspectFill"
      />
      <view class="relative z-10 flex flex-col justify-center items-center h-full px-4 text-white">
        <text class="text-2xl font-bold mb-2">闲置变现，轻松回收</text>
        <text class="text-sm opacity-90">专业评估，价格透明</text>
      </view>
    </view>

    <!-- 快速回收按钮 -->
    <view class="px-4 -mt-6 relative z-20">
      <button
        class="w-full bg-white rounded-xl shadow-lg p-6 flex flex-col items-center space-y-2 active:scale-95 transition-transform"
        @click="startRecycle"
      >
        <text class="text-lg font-semibold text-gray-800">立即回收</text>
        <text class="text-sm text-gray-500">3分钟完成申请</text>
      </button>
    </view>

    <!-- 回收流程 -->
    <view class="px-4 mt-6">
      <text class="text-lg font-semibold text-gray-800 mb-4 block">回收流程</text>
      <view class="space-y-3">
        <view
          class="flex items-center space-x-4 bg-white rounded-lg p-4 shadow-sm"
          v-for="(step, index) in processSteps"
          :key="index"
        >
          <view class="w-8 h-8 bg-primary-500 rounded-full flex items-center justify-center">
            <text class="text-white text-sm font-medium">{{ index + 1 }}</text>
          </view>
          <view class="flex-1">
            <text class="text-base font-medium text-gray-800 block">{{ step.title }}</text>
            <text class="text-sm text-gray-500">{{ step.desc }}</text>
          </view>
        </view>
      </view>
    </view>

    <!-- 我的回收 -->
    <view class="px-4 mt-6 pb-6">
      <view class="flex justify-between items-center mb-4" @click="goToOrderList">
        <text class="text-lg font-semibold text-gray-800">我的回收</text>
        <text class="text-sm text-primary-500">查看全部 ></text>
      </view>

      <view v-if="recentOrders.length > 0" class="space-y-3">
        <view
          class="bg-white rounded-lg p-4 shadow-sm flex items-center space-x-3 active:bg-gray-50"
          v-for="order in recentOrders"
          :key="order.id"
          @click="goToOrderDetail(order.id)"
        >
          <image
            :src="order.mainImage || '/static/default-product.png'"
            class="w-16 h-16 rounded-lg object-cover"
            mode="aspectFill"
          />
          <view class="flex-1 min-w-0">
            <text class="text-base font-medium text-gray-800 truncate block">
              {{ order.brandName }} {{ order.model }}
            </text>
            <text class="text-sm text-gray-500 block">{{ order.orderStatusDesc }}</text>
            <text
              v-if="order.finalPrice"
              class="text-lg font-semibold text-green-600 mt-1 block"
            >
              ¥{{ order.finalPrice }}
            </text>
          </view>
        </view>
      </view>

      <view v-else class="bg-white rounded-lg p-8 text-center shadow-sm">
        <image src="/static/empty-recycle.png" class="w-20 h-20 mx-auto mb-4 opacity-50"/>
        <text class="text-gray-500 mb-4 block">暂无回收记录</text>
        <button
          class="bg-primary-500 text-white px-6 py-2 rounded-lg text-sm font-medium active:bg-primary-600"
          @click="startRecycle"
        >
          开始第一次回收
        </button>
      </view>
    </view>
  </view>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue'
import StatusBarPlaceholder from '@/components/StatusBarPlaceholder.vue'
import NavBar from '@/components/NavBar.vue'

// 类型定义
interface ProcessStep {
  title: string
  desc: string
}

interface RecentOrder {
  id: string
  brandName: string
  model: string
  orderStatusDesc: string
  finalPrice?: number
  mainImage?: string
}

// 响应式数据
const processSteps = ref<ProcessStep[]>([
  { title: '提交申请', desc: '填写商品信息并上传图片' },
  { title: '专业评估', desc: '24小时内完成评估报价' },
  { title: '确认寄送', desc: '满意报价后寄送商品' },
  { title: '完成交易', desc: '收货确认后立即打款' }
])

const recentOrders = ref<RecentOrder[]>([])

// 方法
const startRecycle = () => {
  uni.navigateTo({
    url: '/pages/views/recycle/create-order'
  })
}

const goToOrderList = () => {
  uni.navigateTo({
    url: '/pages/views/recycle/order-list'
  })
}

const goToOrderDetail = (orderId: string) => {
  uni.navigateTo({
    url: `/pages/views/recycle/order-detail?id=${orderId}`
  })
}

const loadRecentOrders = async () => {
  try {
    // TODO: 调用API获取最近的回收订单
    // const orders = await getRecentRecycleOrders()
    // recentOrders.value = orders.slice(0, 3)
  } catch (error) {
    console.error('加载最近订单失败:', error)
  }
}

// 生命周期
onMounted(() => {
  loadRecentOrders()
})
</script>

<style lang="scss" scoped>
// 自定义样式
</style>
