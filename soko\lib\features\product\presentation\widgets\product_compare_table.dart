import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

import 'package:soko/features/product/domain/entities/product.dart';
import 'package:soko/features/product/presentation/providers/product_compare_provider.dart';

/// 商品比较表格组件
class ProductCompareTable extends ConsumerWidget {
  const ProductCompareTable({
    super.key,
    required this.products,
  });

  final List<Product> products;

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    if (products.isEmpty) {
      return const SizedBox.shrink();
    }

    final comparisonData = ref.watch(comparisonDataProvider);
    final priceComparison = ref.watch(priceComparisonProvider);

    return Container(
      color: Colors.grey[50],
      child: Column(
        children: [
          // 价格对比摘要
          if (products.length > 1) _buildPriceSummary(context, priceComparison),
          
          // 比较表格
          Expanded(
            child: SingleChildScrollView(
              child: _buildComparisonTable(context, comparisonData),
            ),
          ),
        ],
      ),
    );
  }

  /// 构建价格摘要
  Widget _buildPriceSummary(BuildContext context, Map<String, dynamic> priceData) {
    if (priceData.isEmpty) return const SizedBox.shrink();

    return Container(
      margin: EdgeInsets.all(16.w),
      padding: EdgeInsets.all(16.w),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(12.r),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.05),
            blurRadius: 8,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            '价格对比',
            style: TextStyle(
              fontSize: 16.sp,
              fontWeight: FontWeight.w600,
              color: Colors.grey[800],
            ),
          ),
          SizedBox(height: 12.h),
          
          Row(
            children: [
              Expanded(
                child: _buildPriceItem(
                  '最低价',
                  '¥${(priceData['lowest'] as double).toStringAsFixed(0)}',
                  Colors.green,
                ),
              ),
              Expanded(
                child: _buildPriceItem(
                  '最高价',
                  '¥${(priceData['highest'] as double).toStringAsFixed(0)}',
                  Colors.red,
                ),
              ),
              Expanded(
                child: _buildPriceItem(
                  '平均价',
                  '¥${(priceData['average'] as double).toStringAsFixed(0)}',
                  Colors.blue,
                ),
              ),
              Expanded(
                child: _buildPriceItem(
                  '价差',
                  '¥${(priceData['difference'] as double).toStringAsFixed(0)}',
                  Colors.orange,
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  /// 构建价格项目
  Widget _buildPriceItem(String label, String value, Color color) {
    return Column(
      children: [
        Text(
          value,
          style: TextStyle(
            fontSize: 16.sp,
            fontWeight: FontWeight.w600,
            color: color,
          ),
        ),
        SizedBox(height: 4.h),
        Text(
          label,
          style: TextStyle(
            fontSize: 12.sp,
            color: Colors.grey[600],
          ),
        ),
      ],
    );
  }

  /// 构建比较表格
  Widget _buildComparisonTable(BuildContext context, Map<String, List<String>> data) {
    if (data.isEmpty) return const SizedBox.shrink();

    return Container(
      margin: EdgeInsets.symmetric(horizontal: 16.w),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(12.r),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.05),
            blurRadius: 8,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        children: [
          // 表格标题
          Container(
            padding: EdgeInsets.all(16.w),
            decoration: BoxDecoration(
              color: Theme.of(context).primaryColor.withValues(alpha: 0.1),
              borderRadius: BorderRadius.vertical(top: Radius.circular(12.r)),
            ),
            child: Row(
              children: [
                Icon(
                  Icons.compare_arrows,
                  size: 20.w,
                  color: Theme.of(context).primaryColor,
                ),
                SizedBox(width: 8.w),
                Text(
                  '详细对比',
                  style: TextStyle(
                    fontSize: 16.sp,
                    fontWeight: FontWeight.w600,
                    color: Theme.of(context).primaryColor,
                  ),
                ),
              ],
            ),
          ),
          
          // 表格内容
          ...data.entries.map((entry) => _buildTableRow(
            context,
            entry.key,
            entry.value,
            data.keys.toList().indexOf(entry.key) % 2 == 0,
          )),
        ],
      ),
    );
  }

  /// 构建表格行
  Widget _buildTableRow(BuildContext context, String label, List<String> values, bool isEven) {
    return Container(
      color: isEven ? Colors.grey[50] : Colors.white,
      padding: EdgeInsets.symmetric(horizontal: 16.w, vertical: 12.h),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // 标签列
          SizedBox(
            width: 80.w,
            child: Text(
              label,
              style: TextStyle(
                fontSize: 13.sp,
                fontWeight: FontWeight.w500,
                color: Colors.grey[700],
              ),
            ),
          ),
          
          // 值列
          Expanded(
            child: Row(
              children: values.asMap().entries.map((entry) {
                final index = entry.key;
                final value = entry.value;
                
                return Expanded(
                  child: Container(
                    padding: EdgeInsets.only(left: index > 0 ? 8.w : 0),
                    child: Text(
                      value,
                      style: TextStyle(
                        fontSize: 13.sp,
                        color: _getValueColor(label, value, values),
                        fontWeight: _isHighlightValue(label, value, values) 
                            ? FontWeight.w600 
                            : FontWeight.normal,
                      ),
                    ),
                  ),
                );
              }).toList(),
            ),
          ),
        ],
      ),
    );
  }

  /// 获取值的颜色
  Color _getValueColor(String label, String value, List<String> allValues) {
    if (label == '价格') {
      // 价格最低的显示绿色，最高的显示红色
      final prices = allValues
          .map((v) => double.tryParse(v.replaceAll('¥', '').replaceAll(',', '')) ?? 0)
          .toList();
      final currentPrice = double.tryParse(value.replaceAll('¥', '').replaceAll(',', '')) ?? 0;
      
      if (currentPrice == prices.reduce((a, b) => a < b ? a : b)) {
        return Colors.green;
      } else if (currentPrice == prices.reduce((a, b) => a > b ? a : b)) {
        return Colors.red;
      }
    }
    
    return Colors.grey[800]!;
  }

  /// 是否高亮显示值
  bool _isHighlightValue(String label, String value, List<String> allValues) {
    if (label == '价格') {
      final prices = allValues
          .map((v) => double.tryParse(v.replaceAll('¥', '').replaceAll(',', '')) ?? 0)
          .toList();
      final currentPrice = double.tryParse(value.replaceAll('¥', '').replaceAll(',', '')) ?? 0;
      
      return currentPrice == prices.reduce((a, b) => a < b ? a : b) ||
             currentPrice == prices.reduce((a, b) => a > b ? a : b);
    }
    
    return false;
  }
}
