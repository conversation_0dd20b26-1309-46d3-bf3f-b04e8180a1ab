/*
 * <AUTHOR> jry,jry
 * @Description  :
 * @version      : 3.0
 * @Date         : 2024-04-22 16:44:21
 * @LastAuthor   : jry
 * @lastTime     : 2024-08-20 14:20:58
 * @FilePath     : /uview-plus/libs/config/props/tag.js
 */
export default {
	// tag 组件
	tag: {
		type: 'primary',
		disabled: false,
		size: 'medium',
		shape: 'square',
		text: '',
		bgColor: '',
		color: '',
		borderColor: '',
		closeColor: '#C6C7CB',
		name: '',
		plainFill: false,
		plain: false,
		closable: false,
		show: true,
		icon: '',
		iconColor: ''
	}
} as UTSJSONObject