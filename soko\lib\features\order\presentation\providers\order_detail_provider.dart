import 'package:flutter_riverpod/flutter_riverpod.dart';

import 'package:soko/core/state/base_state.dart';
import 'package:soko/features/order/domain/entities/order.dart';
import 'package:soko/features/order/domain/usecases/get_order_detail_usecase.dart';

/// 订单详情状态管理器
class OrderDetailNotifier extends StateNotifier<AsyncState<Order>> {

  OrderDetailNotifier(
    this._getOrderDetailUseCase,
    this.orderId,
  ) : super(const AsyncState.idle());
  final GetOrderDetailUseCase _getOrderDetailUseCase;
  final String orderId;

  /// 加载订单详情
  Future<void> loadOrderDetail() async {
    state = const AsyncState.loading();

    try {
      final result = await _getOrderDetailUseCase(orderId);

      result.fold(
        (failure) {
          state = AsyncState.error(failure.message);
        },
        (order) {
          state = AsyncState.success(order);
        },
      );
    } catch (e) {
      state = AsyncState.error(e.toString());
    }
  }

  /// 刷新订单详情
  Future<void> refreshOrderDetail() async {
    await loadOrderDetail();
  }

  /// 更新订单状态（本地更新，用于操作后的即时反馈）
  void updateOrderStatus(OrderStatus newStatus) {
    state.maybeWhen(
      success: (order) {
        // 这里需要创建一个新的Order对象，但由于Order是不可变的，
        // 我们需要在Order类中添加copyWith方法
        // 暂时先重新加载数据
        loadOrderDetail();
      },
      orElse: () {},
    );
  }
}

/// 订单详情状态管理器提供者
final orderDetailProvider = StateNotifierProvider.family<OrderDetailNotifier, AsyncState<Order>, String>((ref, orderId) {
  final getOrderDetailUseCase = ref.read(getOrderDetailUseCaseProvider);
  return OrderDetailNotifier(getOrderDetailUseCase, orderId);
});
