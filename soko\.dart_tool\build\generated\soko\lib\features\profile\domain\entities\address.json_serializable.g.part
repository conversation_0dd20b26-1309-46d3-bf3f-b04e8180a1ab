// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

Address _$AddressFromJson(Map<String, dynamic> json) => Address(
      id: json['id'] as String,
      userId: json['userId'] as String,
      receiverName: json['receiverName'] as String,
      receiverPhone: json['receiverPhone'] as String,
      province: json['province'] as String,
      city: json['city'] as String,
      district: json['district'] as String,
      address: json['address'] as String,
      postalCode: json['postalCode'] as String?,
      addressType: json['addressType'] as String?,
      isDefault: json['isDefault'] as bool,
      createTime: (json['createTime'] as num).toInt(),
      updateTime: (json['updateTime'] as num).toInt(),
    );

Map<String, dynamic> _$AddressToJson(Address instance) => <String, dynamic>{
      'id': instance.id,
      'userId': instance.userId,
      'receiverName': instance.receiverName,
      'receiverPhone': instance.receiverPhone,
      'province': instance.province,
      'city': instance.city,
      'district': instance.district,
      'address': instance.address,
      'postalCode': instance.postalCode,
      'addressType': instance.addressType,
      'isDefault': instance.isDefault,
      'createTime': instance.createTime,
      'updateTime': instance.updateTime,
    };

CreateAddressRequest _$CreateAddressRequestFromJson(
        Map<String, dynamic> json) =>
    CreateAddressRequest(
      receiverName: json['receiverName'] as String,
      receiverPhone: json['receiverPhone'] as String,
      province: json['province'] as String,
      city: json['city'] as String,
      district: json['district'] as String,
      address: json['address'] as String,
      postalCode: json['postalCode'] as String?,
      addressType: json['addressType'] as String?,
      isDefault: json['isDefault'] as bool,
    );

Map<String, dynamic> _$CreateAddressRequestToJson(
        CreateAddressRequest instance) =>
    <String, dynamic>{
      'receiverName': instance.receiverName,
      'receiverPhone': instance.receiverPhone,
      'province': instance.province,
      'city': instance.city,
      'district': instance.district,
      'address': instance.address,
      'postalCode': instance.postalCode,
      'addressType': instance.addressType,
      'isDefault': instance.isDefault,
    };

UpdateAddressRequest _$UpdateAddressRequestFromJson(
        Map<String, dynamic> json) =>
    UpdateAddressRequest(
      receiverName: json['receiverName'] as String?,
      receiverPhone: json['receiverPhone'] as String?,
      province: json['province'] as String?,
      city: json['city'] as String?,
      district: json['district'] as String?,
      address: json['address'] as String?,
      postalCode: json['postalCode'] as String?,
      addressType: json['addressType'] as String?,
      isDefault: json['isDefault'] as bool?,
    );

Map<String, dynamic> _$UpdateAddressRequestToJson(
        UpdateAddressRequest instance) =>
    <String, dynamic>{
      'receiverName': instance.receiverName,
      'receiverPhone': instance.receiverPhone,
      'province': instance.province,
      'city': instance.city,
      'district': instance.district,
      'address': instance.address,
      'postalCode': instance.postalCode,
      'addressType': instance.addressType,
      'isDefault': instance.isDefault,
    };
