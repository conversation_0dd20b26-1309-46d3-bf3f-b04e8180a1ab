<template>
  <view class="shop-container">
    <!-- 搜索框部分 - 添加动态状态栏高度适配 -->
    <view
      class="search-box"
      :style="{ paddingTop: `calc(${statusBarHeight} + 16rpx)` }"
      style="
        padding-left: 24rpx;
        padding-right: 24rpx;
        padding-bottom: 16rpx;
        background-color: #f6f6f6;
        position: relative;
        z-index: 10;
      "
    >
      <view
        class="search-input-wrap"
        :class="{ 'search-input-active': isSearchActive }"
        @tap="handleSearchTap"
        style="
          width: 100%;
          height: 88rpx;
          background-color: #ffffff;
          border-radius: 100rpx;
          display: flex;
          align-items: center;
          padding: 0 24rpx;
          box-sizing: border-box;
          border: 1px solid #eeeeee;
          box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.05);
          transition: all 0.1s ease;
        "
      >
        <van-icon name="search" style="color: #666666; font-size: 28px" />
        <view
          class="search-placeholder"
          style="
            flex: 1;
            font-size: 28rpx;
            padding: 0 16rpx;
            color: #909399;
            line-height: 88rpx;
          "
          >搜索你想要的特摄模玩</view
        >
      </view>
    </view>

    <!-- 全部商品按钮 -->
    <view
      class="all-products-btn bg-white mb-2 px-4 py-3 flex justify-between items-center"
      @tap="goToAllProducts"
    >
      <view class="flex items-center">
        <i
          class="fas fa-shopping-bag text-primary mr-2"
          style="font-size: 18px"
        ></i>
        <text class="font-medium">全部商品</text>
      </view>
      <i
        class="fas fa-chevron-right text-text-secondary"
        style="font-size: 16px"
      ></i>
    </view>

    <!-- 加载状态 -->
    <view
      v-if="loading"
      class="loading-container flex justify-center items-center py-4"
    >
      <view class="loading-spinner"></view>
      <text class="ml-2 text-text-secondary">加载中...</text>
    </view>

    <!-- 空数据状态 -->
    <view
      v-else-if="list.length === 0"
      class="empty-container flex flex-col justify-center items-center py-10"
    >
      <i
        class="fas fa-box-open text-text-secondary mb-3"
        style="font-size: 48px"
      ></i>
      <text class="text-text-secondary">暂无分类数据</text>
    </view>

    <!-- 分类导航部分 -->
    <view v-else class="menu-wrap">
      <!-- 左侧分类菜单 -->
      <scroll-view
        scroll-y
        scroll-with-animation
        class="tab-view menu-scroll-view"
        :scroll-top="scrollTop"
        bounces="false"
      >
        <view
          v-for="(item, index) in list"
          :key="index"
          class="tab-item"
          :class="[current === index ? 'tab-item-active' : '']"
          @tap.stop="swichMenu(index)"
        >
          <text class="text-ellipsis">{{ item.name }}</text>
        </view>
      </scroll-view>

      <!-- 右侧商品展示区 -->
      <scroll-view
        :scroll-top="scrollRightTop"
        scroll-y
        scroll-with-animation
        class="right-box"
        @scroll="rightScroll"
        bounces="false"
      >
        <view class="page-view">
          <view
            class="class-item"
            :id="'item' + index"
            v-for="(item, index) in list"
            :key="index"
          >
            <view class="item-title">
              <text>{{ item.name }}</text>
            </view>
            <view class="item-container">
              <view
                class="thumb-box"
                v-for="(child, childIndex) in item.children"
                :key="childIndex"
                @tap="goToProductList(item, child)"
              >
                <view class="item-menu-image">
                  <CacheImgs :src="child.url"></CacheImgs>
                </view>

                <view class="item-menu-name">{{ child.name }}</view>
              </view>
            </view>
          </view>
          <!-- 底部占位区 -->
          <view class="bottom-placeholder"></view>
        </view>
      </scroll-view>
    </view>
  </view>
</template>

<script setup>
import CacheImgs from "@/components/CacheImgs.vue"
import { ref, nextTick, onBeforeMount } from "vue"
import { onShow } from "@dcloudio/uni-app"
import { listCategoryByType } from "@/api/api"

const scrollTop = ref(0)
const oldScrollTop = ref(0)
const current = ref(0)
const menuHeight = ref(0)
const menuItemHeight = ref(0)
const itemId = ref("")
const arr = ref([])
const scrollRightTop = ref(0)
const timer = ref(null)
const isUserScrolling = ref(false)
const isSearchActive = ref(false)
const loading = ref(false)
const statusBarHeight = ref("0px")

// 获取状态栏高度
onBeforeMount(() => {
  const systemInfo = uni.getSystemInfoSync()
  statusBarHeight.value = `${systemInfo.statusBarHeight}px`
})

// 处理搜索框点击效果
const handleSearchTap = () => {
  isSearchActive.value = true
  setTimeout(() => {
    isSearchActive.value = false
    goToSearch()
  }, 80)
}

const swichMenu = async (index) => {
  if (arr.value.length === 0) {
    await getMenuItemTop()
  }
  if (index === current.value) return

  isUserScrolling.value = true

  current.value = index

  scrollRightTop.value = arr.value[index]

  if (menuHeight.value > 0 && menuItemHeight.value > 0) {
    scrollTop.value =
      index * menuItemHeight.value +
      menuItemHeight.value / 2 -
      menuHeight.value / 2
  } else {
    leftMenuStatus(index)
  }

  setTimeout(() => {
    isUserScrolling.value = false
  }, 1000)
}

const goToSearch = () => {
  uni.navigateTo({
    url: "/pages/views/search/search"
  })
}

const goToProductList = (parentCategory, category) => {
  uni.navigateTo({
    url: `/pages/views/product/products?categoryId=${
      category.id
    }&categoryName=${encodeURIComponent(category.name)}&parentCode=${
      parentCategory.code || ""
    }`
  })
}

// 跳转到全部商品页面
const goToAllProducts = () => {
  uni.navigateTo({
    url: `/pages/views/product/products`
  })
}

const getElRect = (elClass, dataVal) => {
  return new Promise((resolve) => {
    const query = uni.createSelectorQuery().in(this)
    query
      .select("." + elClass)
      .fields(
        {
          size: true
        },
        (res) => {
          if (!res) {
            setTimeout(() => {
              getElRect(elClass, dataVal)
            }, 10)
            return
          }
          if (dataVal === "menuHeight") {
            menuHeight.value = res.height
          } else if (dataVal === "menuItemHeight") {
            menuItemHeight.value = res.height
          }
          resolve()
        }
      )
      .exec()
  })
}

const leftMenuStatus = async (index) => {
  current.value = index
  if (menuHeight.value === 0 || menuItemHeight.value === 0) {
    await getElRect("menu-scroll-view", "menuHeight")
    await getElRect("u-tab-item", "menuItemHeight")
  }
  scrollTop.value =
    index * menuItemHeight.value +
    menuItemHeight.value / 2 -
    menuHeight.value / 2
}

const getMenuItemTop = () => {
  return new Promise((resolve) => {
    const selectorQuery = uni.createSelectorQuery()
    selectorQuery
      .selectAll(".class-item")
      .boundingClientRect((rects) => {
        if (!rects || !rects.length) {
          setTimeout(() => {
            getMenuItemTop()
          }, 10)
          return
        }
        // 清空数组，防止重复添加
        arr.value = []
        rects.forEach((rect) => {
          arr.value.push(rect.top - rects[0].top)
        })
        resolve()
      })
      .exec()
  })
}

const rightScroll = (() => {
  let waiting = false
  return async (e) => {
    oldScrollTop.value = e.detail.scrollTop

    if (isUserScrolling.value || waiting) return

    waiting = true
    setTimeout(async () => {
      waiting = false

      if (arr.value.length === 0) {
        await getMenuItemTop()
      }

      if (!menuHeight.value) {
        await getElRect("menu-scroll-view", "menuHeight")
      }

      const scrollHeight = oldScrollTop.value + menuHeight.value / 2
      for (let i = 0; i < arr.value.length; i++) {
        const height1 = arr.value[i]
        const height2 = arr.value[i + 1]
        if (!height2 || (scrollHeight >= height1 && scrollHeight < height2)) {
          if (current.value != i) {
            current.value = i
            scrollTop.value =
              i * menuItemHeight.value +
              menuItemHeight.value / 2 -
              menuHeight.value / 2
          }
          return
        }
      }
    }, 150)
  }
})()

const list = ref([])

// 获取分类数据
const fetchCategoryData = () => {
  loading.value = true
  arr.value = [] // 重置坐标数组

  const data = {
    type: "CLASSIFICATION",
    status: "E"
  }

  listCategoryByType(data)
    .then((result) => {
      loading.value = false
      if (result.code === 200) {
        // 清空列表，避免重复添加
        list.value = []

        // 检查返回的数据是否为数组且不为空
        if (Array.isArray(result.data) && result.data.length > 0) {
          list.value = result.data

          // 数据加载完成后，计算元素位置
          nextTick(() => {
            setTimeout(() => {
              getMenuItemTop()
            }, 100)
          })
        }
      } else {
        // 返回错误码时显示错误信息
        uni.showToast({
          title: result.message || "获取分类数据失败",
          icon: "none",
          duration: 2000
        })
      }
    })
    .catch((error) => {
      loading.value = false
      uni.showToast({
        title: "获取数据异常，请稍后重试",
        icon: "none",
        duration: 2000
      })
      console.error("获取分类数据异常:", error)
    })
}

onShow(() => {
  setTimeout(() => {
    fetchCategoryData()
  }, 50)
})
</script>

<style lang="scss" scoped>
/* 设置特定于shop页面的容器样式 */
.shop-container {
  display: flex;
  flex-direction: column;
  height: 100vh;
  /* #ifdef H5 */
  height: calc(100vh - var(--window-top));
  /* #endif */
  background-color: #f6f6f6;
  overflow: hidden; /* 仅在此页面禁止滚动 */
}

/* 搜索框点击状态样式 */
.search-input-active {
  opacity: 0.9 !important;
  transform: scale(0.98) !important;
  box-shadow: 0 2rpx 12rpx rgba(239, 68, 68, 0.15) !important;
  border-color: rgba(239, 68, 68, 0.3) !important;
}

.search-box {
  padding-left: 24rpx;
  padding-right: 24rpx;
  padding-bottom: 16rpx;
  flex-shrink: 0;
  background-color: #f6f6f6;
  position: relative;
  z-index: 10;
}

/* 与搜索页一致的搜索框样式 */
.search-input-wrap {
  width: 100%;
  height: 88rpx;
  background-color: #ffffff;
  border-radius: 100rpx;
  display: flex;
  align-items: center;
  padding: 0 24rpx;
  box-sizing: border-box;
  border: 1px solid #eeeeee;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.05);
  transition: all 0.1s ease;
  position: relative;
}

.search-input-wrap:active {
  opacity: 0.95;
  transform: scale(0.99);
  box-shadow: 0 2rpx 12rpx rgba(239, 68, 68, 0.15);
  border-color: rgba(239, 68, 68, 0.2);
}

.search-placeholder {
  flex: 1;
  font-size: 28rpx;
  padding: 0 16rpx;
  color: #909399;
  line-height: 88rpx;
}

/* 全部商品按钮样式 */
.all-products-btn {
  border-radius: 12rpx;
  margin: 0 24rpx 16rpx;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.03);
  transition: all 0.2s;
  width: calc(100% - 48rpx); /* 确保宽度与搜索框一致 */
  box-sizing: border-box;
}

.all-products-btn:active {
  opacity: 0.9; /* 点击时的视觉反馈 */
  transform: scale(0.98);
}

.menu-wrap {
  flex: 1;
  display: flex;
  overflow: hidden;
  margin-top: 0;
}

.tab-view {
  width: 180rpx;
  height: 100%;
  background: #fff;
  border-radius: 8px 8px 0 8px;
  box-shadow: 2px 0 12px rgba(239, 68, 68, 0.04);
  margin-left: 8rpx;
  margin-top: 0;
  margin-bottom: 8rpx;
}

.tab-item {
  height: 96rpx;
  background: transparent;
  color: #a3a3a3;
  font-size: 14px;
  font-weight: 400;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 8px;
  margin: 4rpx 8rpx;
  transition: background 0.2s, color 0.2s;
}

.tab-item-active {
  color: #ef4444;
  background: #fff0f0;
  font-size: 15px;
  font-weight: 600;
  position: relative;
}

.tab-item-active::before {
  content: "";
  position: absolute;
  border-left: 8rpx solid #ef4444;
  height: 32rpx;
  left: 0;
  top: 32rpx;
  border-radius: 0 4rpx 4rpx 0;
}

.text-ellipsis {
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  width: 100%;
  text-align: center;
}

.right-box {
  background-color: #f6f6f6;
  flex: 1;
  will-change: transform;
  margin-top: 0;
}

.page-view {
  padding: 0 16rpx 16rpx 16rpx;
}

.class-item {
  margin-bottom: 24rpx;
  background: #fff;
  padding: 20rpx 16rpx 20rpx 16rpx;
  border-radius: 8px;
  box-shadow: 0 4px 16px rgba(239, 68, 68, 0.06);
}

.class-item:last-child {
  margin-bottom: 24rpx;
  padding-bottom: 24rpx;
}

.item-title {
  font-size: 16px;
  color: #ef4444;
  font-weight: 700;
  padding: 8rpx 0 16rpx;
  border-bottom: 1px solid #f6f6f6;
  margin-bottom: 8rpx;
}

.item-menu-name {
  font-weight: 600;
  font-size: 14px;
  color: #333;
  margin-top: 8rpx;
  text-align: center;
}

.item-container {
  display: flex;
  flex-wrap: wrap;
  margin: 0 -8rpx;
}

.thumb-box {
  width: 33.333333%;
  display: flex;
  align-items: center;
  justify-content: center;
  flex-direction: column;
  margin-top: 16rpx;
  padding: 8rpx;
  box-sizing: border-box;
}

.item-menu-image {
  width: 140rpx;
  height: 140rpx;
  border-radius: 12px;
  background: #f6f6f6;
  box-shadow: 0 2px 8px rgba(239, 68, 68, 0.08);
}

.bottom-placeholder {
  height: 24rpx;
  width: 100%;
}

.thumb-box:active {
  .item-menu-name {
    color: #ef4444;
  }
}

/* 加载状态样式 */
.loading-container {
  height: 100rpx;
}

.loading-spinner {
  width: 32rpx;
  height: 32rpx;
  border-radius: 50%;
  border: 3rpx solid rgba(239, 68, 68, 0.1);
  border-top-color: #ef4444;
  animation: spin 0.8s linear infinite;
}

@keyframes spin {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
}

/* 空数据状态样式 */
.empty-container {
  flex: 1;
  opacity: 0.7;
}
</style>
