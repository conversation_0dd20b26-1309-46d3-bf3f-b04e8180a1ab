<template>
  <view class="flex flex-col min-h-screen" style="background-color: #f5f5f5">
    <!-- #ifdef APP-PLUS || MP-WEIXIN -->
    <!-- <view class="status-bar" :style="{ height: statusBarHeight + 'px' }"></view> -->
    <!-- #endif -->

    <!-- 头部区域 -->
    <view class="sticky top-0 z-10">
      <!-- 渐变背景 -->
      <view
        class="header-gradient pb-4 relative overflow-hidden"
        :style="{ paddingTop: statusBarHeight + 'px' }"
      >
        <!-- 头部内容 -->
        <view class="relative z-10 px-4">
          <view class="flex items-center justify-between h-11">
            <view class="flex items-center">
              <view @tap="goBack" class="mr-2">
                <text class="fas fa-chevron-left text-white text-lg"></text>
              </view>
              <text class="text-xl font-bold text-white">确认订单</text>
            </view>
          </view>
        </view>

        <!-- 装饰背景元素 -->
        <view class="absolute right-4 -bottom-6 opacity-10">
          <view class="w-24 h-24 rounded-full">
            <text class="fas fa-shopping-bag text-white text-5xl"></text>
          </view>
        </view>

        <!-- 装饰圆形 -->
        <view class="header-circle"></view>
        <view class="header-circle header-circle-2"></view>
      </view>
    </view>

    <!-- 主要内容区域 -->
    <view class="content py-4 px-4 pb-40 space-y-4">
      <!-- 订单金额 -->
      <view class="bg-white rounded-xl shadow-app p-4">
        <view class="flex justify-between items-center py-2">
          <text class="text-sm text-gray-600">商品金额</text>
          <text class="text-sm">¥{{ productTotalAmount.toFixed(2) }}</text>
        </view>
        <view class="flex justify-between items-center py-2">
          <text class="text-sm text-gray-600">国内运费</text>
          <text class="text-sm"
            >¥{{ orderForm.domesticFreight.toFixed(2) }}</text
          >
        </view>
        <view class="flex justify-between items-center py-2">
          <text class="text-sm text-gray-600">国际运费</text>
          <text class="text-sm"
            >¥{{ orderForm.internationalFreight.toFixed(2) }}</text
          >
        </view>
        <view class="flex justify-between items-center py-2">
          <text class="text-sm text-gray-600">优惠金额</text>
          <text class="text-sm"
            >¥{{ orderForm.discountAmount.toFixed(2) }}</text
          >
        </view>
        <view class="border-t border-gray-100 mt-2 pt-3">
          <view class="flex justify-between items-center">
            <text class="font-semibold">订单总金额</text>
            <text class="text-lg font-bold text-primary"
              >¥{{ totalAmount.toFixed(2) }}</text
            >
          </view>
        </view>
      </view>

      <!-- 商品信息 -->
      <view class="bg-white rounded-xl shadow-app">
        <view class="p-4 border-b border-gray-100">
          <text class="text-base font-semibold">商品信息</text>
        </view>
        <view
          v-for="(item, index) in orderItems"
          :key="index"
          class="p-4 border-b border-gray-100 last:border-b-0"
        >
          <view class="flex">
            <view
              class="w-20 h-20 rounded-lg overflow-hidden flex-shrink-0 bg-gray-100"
            >
              <CacheImgs :src="item.image"></CacheImgs>
            </view>

            <view class="ml-3 flex-1 flex flex-col justify-between">
              <text class="text-sm font-medium text-gray-800 line-clamp-2">{{
                item.productName
              }}</text>
              <text class="text-xs text-gray-500 mt-1 block">{{
                formatSpecification(item.productSkuId, item.skus)
              }}</text>
              <view class="flex justify-between items-center mt-2">
                <text class="text-base font-bold text-primary"
                  >¥{{ Number(item.productPrice).toFixed(2) }}</text
                >
                <text class="text-sm text-gray-600">x{{ item.quantity }}</text>
              </view>
            </view>
          </view>
        </view>
      </view>

      <!-- 收货地址 -->
      <view class="bg-white rounded-xl shadow-app p-4">
        <view class="flex items-center mb-2">
          <text
            class="fas fa-map-marker-alt text-primary text-base mr-2"
          ></text>
          <text class="text-base font-semibold">收货地址</text>
        </view>

        <view
          v-if="selectedAddress"
          class="flex items-start mt-2 relative p-2 rounded-lg bg-gray-50"
          hover-class="bg-gray-100"
          @tap="showAddressSelector"
        >
          <view class="flex-1">
            <view class="flex items-center flex-wrap">
              <text class="text-base font-semibold mr-3">{{
                selectedAddress.name
              }}</text>
              <text class="text-base">{{ selectedAddress.phone }}</text>
              <view
                v-if="selectedAddress.iDefault"
                class="ml-2 bg-primary bg-opacity-10 rounded-full px-2 py-0.5"
              >
                <text class="text-2xs text-primary">默认</text>
              </view>
            </view>
            <view class="mt-2 text-sm text-gray-600 break-all pr-6">
              {{ formatAddress(selectedAddress) }}
            </view>
          </view>
          <view class="absolute right-2 top-1/2 transform -translate-y-1/2">
            <text class="fas fa-chevron-right text-gray-400"></text>
          </view>
        </view>

        <view
          v-else
          class="flex items-center justify-between p-3 mt-2 border border-dashed border-gray-200 rounded-lg"
          hover-class="bg-gray-50"
          @tap="showAddressSelector"
        >
          <view class="flex items-center">
            <text class="fas fa-plus text-primary mr-2"></text>
            <text class="text-base text-gray-600">添加收货地址</text>
          </view>
          <text class="fas fa-chevron-right text-gray-400"></text>
        </view>
      </view>

      <!-- 配送方式 -->
      <view class="bg-white rounded-xl shadow-app p-4">
        <view class="flex justify-between items-center">
          <text class="text-base font-semibold">配送方式</text>
          <view class="flex">
            <view
              class="px-3 py-1 rounded-full mr-2"
              :class="
                orderForm.deliveryType === 'NORMAL'
                  ? 'bg-gradient-to-r from-primary to-neon text-white'
                  : 'bg-gray-100 text-gray-600'
              "
              @tap="selectDeliveryType('NORMAL')"
            >
              <text class="text-sm">普通快递</text>
            </view>
            <view
              class="px-3 py-1 rounded-full"
              :class="
                orderForm.deliveryType === 'SF'
                  ? 'bg-gradient-to-r from-primary to-neon text-white'
                  : 'bg-gray-100 text-gray-600'
              "
              @tap="selectDeliveryType('SF')"
            >
              <text class="text-sm">顺丰快递</text>
            </view>
          </view>
        </view>
      </view>

      <!-- 销售类型 -->
      <view class="bg-white rounded-xl shadow-app p-4">
        <view class="flex justify-between items-center">
          <text class="text-base font-semibold">销售类型</text>
          <view class="flex">
            <view
              class="px-3 py-1 rounded-full"
              :class="
                orderForm.salesType === 'STOCK'
                  ? 'bg-gradient-to-r from-secondary to-green-400 text-white'
                  : 'bg-gradient-to-r from-heroic to-amber-400 text-white'
              "
            >
              <text class="text-sm">{{
                orderForm.salesType === "STOCK" ? "现货" : "预售"
              }}</text>
            </view>
          </view>
        </view>
      </view>
      <!-- 优惠券 -->
      <view class="bg-white rounded-xl shadow-app p-4">
        <view class="flex justify-between items-center">
          <text class="text-base font-semibold">优惠券</text>
          <view class="flex">
            <view class="flex items-center" @click="openProductPopup">
              <text class="text-sm text-gray-700" v-if="!hasSelectedCoupons">
                未选择优惠券
              </text>
              <text v-else>已选择优惠券</text>
              <text class="ml-auto"><van-icon name="arrow" /></text>
            </view>
          </view>
        </view>
      </view>
      <!-- 支付方式 -->
      <view class="bg-white rounded-xl shadow-app p-4">
        <view class="flex justify-between items-center">
          <text class="text-base font-semibold">支付方式</text>
          <view class="flex">
            <view
              class="px-3 py-1 rounded-full bg-gradient-to-r from-accent to-blue-400 text-white"
            >
              <text class="text-sm">支付宝</text>
            </view>
          </view>
        </view>
      </view>

      <!-- 订单备注 -->
      <view class="bg-white rounded-xl shadow-app p-4">
        <text class="text-base font-semibold mb-2 block">订单备注</text>
        <textarea
          v-model="orderForm.note"
          placeholder="请输入订单备注（选填）"
          maxlength="100"
          class="w-full bg-gray-100 rounded-lg p-2 text-sm"
          style="height: 80px; box-sizing: border-box"
        ></textarea>
      </view>
    </view>

    <!-- 底部操作栏 -->
    <view
      class="fixed bottom-0 left-0 right-0 bg-white border-t border-gray-200 pb-safe"
    >
      <view class="flex items-center px-4 py-3 h-14">
        <view class="flex-1 flex items-center">
          <text class="text-sm mr-1">合计：</text>
          <text class="text-lg font-bold text-primary"
            >¥{{ totalAmount.toFixed(2) }}</text
          >
        </view>
        <view class="flex justify-end">
          <button
            class="bg-gradient-to-r from-primary to-neon text-white rounded-full px-6 py-2 font-semibold text-sm shadow-button"
            :disabled="submitting"
            :class="submitting ? 'opacity-70' : ''"
            @tap="submitOrder"
          >
            {{ submitting ? "处理中..." : "立即支付" }}
          </button>
        </view>
      </view>
    </view>

    <!-- 收货地址选择弹窗 -->
    <up-popup :show="showAddressPopup" mode="bottom" round>
      <view class="bg-white rounded-t-xl p-4 max-h-[70vh]">
        <view
          class="flex justify-between items-center mb-4 pb-2 border-b border-gray-100"
        >
          <text class="text-lg font-bold">选择收货地址</text>
          <view
            @tap="showAddressPopup = false"
            class="p-1 w-8 h-8 flex items-center justify-center rounded-full hover:bg-gray-100"
          >
            <text class="fas fa-times text-gray-500"></text>
          </view>
        </view>

        <view
          v-if="addressList.length === 0"
          class="py-12 flex flex-col items-center justify-center"
        >
          <text
            class="fas fa-map-marker-alt text-gray-300 text-4xl mb-4"
          ></text>
          <text class="text-gray-500 mb-2">暂无收货地址</text>
          <text class="text-gray-400 text-sm mb-4"
            >添加地址以便我们准确送达</text
          >
        </view>

        <scroll-view
          v-else
          scroll-y
          style="max-height: 60vh"
          class="address-scroll"
        >
          <view
            v-for="address in addressList"
            :key="address.id"
            class="border border-gray-100 rounded-lg p-3 mb-3 relative"
            :class="
              selectedAddress && selectedAddress.id === address.id
                ? 'border-primary bg-primary bg-opacity-5'
                : ''
            "
            hover-class="border-primary"
            @tap="selectAddress(address)"
          >
            <view class="flex items-center">
              <text
                class="fas fa-map-marker-alt text-primary mr-2"
                :class="
                  selectedAddress && selectedAddress.id === address.id
                    ? 'text-primary'
                    : 'text-gray-400'
                "
              ></text>
              <text class="text-base font-semibold">{{ address.name }}</text>
              <text class="ml-3 text-base">{{ address.phone }}</text>
              <view
                v-if="address.iDefault"
                class="ml-2 bg-primary bg-opacity-10 rounded-full px-2 py-0.5"
              >
                <text class="text-2xs text-primary">默认</text>
              </view>
            </view>
            <view class="mt-2 text-sm text-gray-600 pl-6">
              {{ formatAddress(address) }}
            </view>
            <view class="absolute right-3 top-1/2 transform -translate-y-1/2">
              <view
                class="rounded-full w-6 h-6 border-2 flex items-center justify-center"
                :class="
                  selectedAddress && selectedAddress.id === address.id
                    ? 'bg-primary border-primary'
                    : 'border-gray-300'
                "
              >
                <text
                  class="fas fa-check text-xs text-white"
                  v-if="selectedAddress && selectedAddress.id === address.id"
                ></text>
              </view>
            </view>
          </view>
        </scroll-view>
        <view class="mt-4 pt-3 flex justify-center border-t border-gray-100">
          <button
            class="bg-gradient-to-r from-primary to-neon text-white rounded-full px-5 py-1.5 text-xs font-medium shadow-button"
            @tap="goToAddAddress"
          >
            <text class="fas fa-plus mr-1"></text> 添加新地址
          </button>
        </view>
      </view>
    </up-popup>

    <!-- 支付失败弹窗 -->
    <up-popup
      :show="showPaymentFailedPopup"
      @close="showPaymentFailedPopup = false"
      mode="center"
      round
      :closeable="true"
    >
      <view class="p-6 bg-white rounded-xl shadow-lg w-84 max-w-[85vw]">
        <view class="flex flex-col items-center">
          <view class="bg-yellow-50 p-4 rounded-full mb-4">
            <text
              class="fas fa-exclamation-circle text-yellow-500 text-4xl"
            ></text>
          </view>
          <text class="text-xl font-bold mb-3 text-gray-800"
            >未查询到支付结果</text
          >
          <text class="text-sm text-gray-600 text-center mb-6">
            如您已支付，请尝试刷新页面查询结果
            <br />
            如您未支付，请重新支付
          </text>
          <view class="flex w-full space-x-3">
            <van-button
              class="flex-1 bg-gray-100 text-gray-700 rounded-full py-3 font-medium text-sm border border-gray-200"
              @tap="retryPayment"
            >
              重新支付
            </van-button>
            <van-button
              class="flex-1 bg-gradient-to-r from-primary to-neon text-white rounded-full py-3 font-medium text-sm"
              @tap="refreshPaymentStatus"
            >
              刷新
            </van-button>
          </view>
        </view>
      </view>
    </up-popup>

    <!-- 优惠券弹出层 -->
    <up-popup
      :show="showCouponPopup"
      @close="clearCoupons"
      mode="bottom"
      round="16"
      safe-area-inset-bottom
    >
      <view class="p-4">
        <view class="text-lg font-bold mb-3">选择优惠券</view>
        <!-- 不可叠加优惠券 -->
        <view>
          <view
            class="inline-block bg-red-500/80 text-white text-xs font-medium px-2 py-0.5 rounded-full mr-2"
            >不可叠加</view
          >
          <text class="text-sm font-medium">不可叠加优惠券</text>
          <scroll-view scroll-y class="mt-2 max-h-[300rpx]">
            <view
              v-if="nonStackableCoupons.length === 0"
              class="py-4 text-center"
            >
              <text class="text-sm text-gray-500">暂无不可叠加优惠券</text>
            </view>
            <view
              v-for="(coupon, index) in nonStackableCoupons"
              :key="index"
              :class="[
                'flex items-center justify-between py-3 px-3 rounded-xl shadow-sm hover:shadow-md transition-shadow duration-200 mb-3',
                selectedNonStackableCouponId === coupon.id
                  ? 'bg-primary/10 border border-primary'
                  : 'bg-gray-100'
              ]"
              @click="selectNonStackableCoupon(coupon)"
            >
              <view class="flex-1">
                <text class="text-xs font-medium">{{ coupon.name }}</text>
                <view class="flex items-center mt-1">
                  <text class="text-xs text-gray-500">优惠券类型：</text>
                  <text class="text-xs font-medium">{{ coupon.type }}</text>
                </view>
                <view class="flex items-center mt-1">
                  <text class="text-xs text-gray-500">面值：</text>
                  <text class="text-xs font-medium">¥{{ coupon.value }}</text>
                </view>
                <view class="flex items-center mt-1">
                  <text class="text-xs text-gray-500">有效期：</text>
                  <text class="text-xs text-gray-700">{{
                    coupon.validStartTime && coupon.validEndTime
                      ? `${coupon.validStartTime} ~ ${coupon.validEndTime}`
                      : "无限"
                  }}</text>
                </view>
              </view>
              <view v-if="selectedNonStackableCouponId === coupon.id">
                <text
                  class="fas fa-check text-primary"
                  style="font-size: 14px"
                ></text>
              </view>
            </view>
          </scroll-view>
        </view>
        <view class="border-t bg-gray-400 my-3 h-px"></view>
        <!-- 可叠加优惠券 -->
        <view class="mb-4">
          <view
            class="inline-block bg-green-500/80 text-white text-xs font-medium px-2 py-0.5 rounded-full mr-2"
            >可叠加</view
          >
          <text class="text-sm font-medium text-gray-700">可叠加优惠券</text>
          <scroll-view scroll-y class="mt-2 max-h-[300rpx] space-y-3">
            <view v-if="stackableCoupons.length === 0" class="py-4 text-center">
              <text class="text-sm text-gray-500">暂无可叠加优惠券</text>
            </view>
            <view
              v-for="(coupon, index) in stackableCoupons"
              :key="index"
              class="flex items-center justify-between bg-gray-100 py-3 px-3 rounded-xl shadow-sm duration-200 mb-3"
            >
              <view class="flex-1">
                <text class="text-xs font-medium">{{ coupon.name }}</text>
                <view class="flex items-center mt-1">
                  <text class="text-xs text-gray-500">优惠券类型：</text>
                  <text class="text-xs font-medium">{{ coupon.type }}</text>
                </view>
                <view class="flex items-center mt-1">
                  <text class="text-xs text-gray-500">面值：</text>
                  <text class="text-xs font-medium">¥{{ coupon.value }}</text>
                </view>
                <view class="flex items-center mt-1">
                  <text class="text-xs text-gray-500">有效期：</text>
                  <text class="text-xs text-gray-700">{{
                    coupon.validStartTime && coupon.validEndTime
                      ? `${coupon.validStartTime} ~ ${coupon.validEndTime}`
                      : "无限"
                  }}</text>
                </view>
                <view class="flex items-center mt-1">
                  <text class="text-xs text-gray-500">使用规则：</text>
                  <text class="text-xs font-medium">{{
                    coupon.stackRuleName
                  }}</text>
                </view>
              </view>
              <van-switch
                :disabled="!!selectedNonStackableCouponId"
                active-color="#22c55ecc"
                v-model="coupon.selected"
                size="24px"
              />
            </view>
          </scroll-view>
        </view>

        <!-- 邮费优惠券 -->
        <view>
          <view
            class="inline-block bg-purple-500/80 text-white text-xs font-medium px-2 py-0.5 rounded-full mr-2"
            >邮费优惠</view
          >
          <text class="text-sm font-medium">邮费优惠券</text>
          <scroll-view scroll-y class="mt-2 max-h-[300rpx]">
            <view v-if="postageCoupons.length === 0" class="py-4 text-center">
              <text class="text-sm text-gray-500">暂无邮费优惠券</text>
            </view>
            <view
              v-for="(coupon, index) in postageCoupons"
              :key="index"
              :class="[
                'flex items-center justify-between py-3 px-3 rounded-xl shadow-sm hover:shadow-md transition-shadow duration-200 mb-3',
                selectedPostedCouponId === coupon.id
                  ? 'bg-primary/10 border border-primary'
                  : 'bg-gray-100'
              ]"
              @click="selectPostedCoupon(coupon)"
            >
              <view class="flex-1">
                <text class="text-xs font-medium">{{ coupon.name }}</text>
                <view class="flex items-center mt-1">
                  <text class="text-xs text-gray-500">优惠券类型：</text>
                  <text class="text-xs font-medium">{{ coupon.type }}</text>
                </view>
                <view class="flex items-center mt-1">
                  <text class="text-xs text-gray-500">面值：</text>
                  <text class="text-xs font-medium">¥{{ coupon.value }}</text>
                </view>
                <view class="flex items-center mt-1">
                  <text class="text-xs text-gray-500">有效期：</text>
                  <text class="text-xs text-gray-700">{{
                    coupon.validStartTime && coupon.validEndTime
                      ? `${coupon.validStartTime} ~ ${coupon.validEndTime}`
                      : "无限"
                  }}</text>
                </view>
                <view class="flex items-center mt-1">
                  <text class="text-xs text-gray-500">使用规则：</text>
                  <text class="text-xs font-medium">{{
                    coupon.stackRuleName
                  }}</text>
                </view>
              </view>
              <view v-if="selectedPostedCouponId === coupon.id">
                <text
                  class="fas fa-check text-primary"
                  style="font-size: 14px"
                ></text>
              </view>
            </view>
          </scroll-view>
        </view>

        <!-- 底部按钮 -->
        <view class="flex gap-2 mt-4">
          <button
            class="flex-1 bg-gray-100 text-gray-700 rounded-full py-2 text-sm"
            @click="clearCoupons"
          >
            不使用优惠券
          </button>
          <button
            v-if="
              nonStackableCoupons.length ||
              stackableCoupons.length ||
              postageCoupons.length
            "
            class="flex-1 bg-primary text-white rounded-full py-2 text-sm"
            @click="applyCoupons"
          >
            使用优惠券
          </button>
        </view>
      </view>
    </up-popup>

    <!-- 使用对应商品出层 -->
    <up-popup
      :show="showProductPopup"
      @close="showProductPopup = false"
      mode="bottom"
      round="16"
      safe-area-inset-bottom
    >
      <view class="p-4">
        <view class="text-lg font-bold mb-3">选择使用的商品</view>
        <view>
          <scroll-view scroll-y class="mt-2 max-h-[300rpx]">
            <view v-if="orderItems.length === 0" class="py-4 text-center">
              <text class="text-sm text-gray-500">暂无商品</text>
            </view>
            <view
              v-for="(choseItem, index) in orderItems"
              :key="index"
              :class="[
                'flex items-center justify-between py-3 px-3 rounded-xl shadow-sm hover:shadow-md transition-shadow duration-200 mb-3',
                selectedProductToSaleId === choseItem.id
                  ? 'bg-primary/10 border border-primary'
                  : 'bg-gray-100'
              ]"
              @click="selectProductToSale(choseItem)"
            >
              <view class="w-20 h-20 rounded-lg flex-shrink-0">
                <CacheImgs :src="choseItem.image" mode="aspectFill"></CacheImgs>
              </view>

              <view
                class="flex-1 ml-3 min-h-[80px] flex flex-col justify-between"
              >
                <view>
                  <text
                    class="text-sm font-medium text-[#333333] line-clamp-2 leading-tight cursor-pointer"
                    >{{ choseItem.name }}</text
                  >
                  <view class="flex items-center mt-2">
                    <text class="text-2xs text-gray-500 mr-1">{{
                      formatSpecification(
                        choseItem.productSkuId,
                        choseItem.skus
                      )
                    }}</text>
                  </view>
                </view>
                <view class="flex justify-between items-center mt-2">
                  <view class="flex flex-col">
                    <view
                      class="text-base font-bold bg-gradient-to-r from-primary to-neon bg-clip-text text-transparent"
                    >
                      ¥{{ choseItem.price?.toFixed(2) }}
                    </view>
                  </view>
                </view>
              </view>
            </view>
          </scroll-view>
        </view>
        <!-- 底部按钮 -->
        <view class="flex gap-2 mt-4">
          <button
            class="flex-1 bg-gray-100 text-gray-700 rounded-full py-2 text-sm"
            @click="showProductPopup = false"
          >
            返回
          </button>
          <button
            class="flex-1 bg-primary text-white rounded-full py-2 text-sm"
            @click="handleChosenProduct"
          >
            确定
          </button>
        </view>
      </view>
    </up-popup>
  </view>
</template>

<script setup lang="ts">
import CacheImgs from "@/components/CacheImgs.vue"
import { ref, computed } from "vue"
import { useStore } from "vuex"
import {
  getAddressList,
  createOrder,
  getProductDetail,
  payOrder,
  getOrderDetail,
  GET_COUPONS_PRODUCT
} from "@/api/api"
import { onShow } from "@dcloudio/uni-app"
import moment from "moment"
import { COUPONS_STACKRULE, COUPONS_TYPE } from "@/enum"
import {
  coutDiscountAmount,
  coutPriceByCouponsList
} from "@/util/common/coutPriceByCoupons"

const store = useStore()

// 页面参数
const orderItems = ref([])
const addressList = ref([])
const selectedAddress = ref(null)
const showAddressPopup = ref(false)
const submitting = ref(false)
const productDetails = ref([]) // 存储产品详情
const expressConfig = ref(null) // 存储系统配置的快递价格
const statusBarHeight = ref(0) // 状态栏高度
const showPaymentFailedPopup = ref(false) // 控制支付失败弹窗的显示
const currentOrderIdForPopup = ref(null) // 存储支付失败时的订单ID
const showCouponPopup = ref(false) // 控制优惠券弹窗的显示
const selectedNonStackableCouponId = ref(null) // 存储不可叠加优惠券
const selectedNonStackableCoupon = ref(null) // 存储不可叠加优惠券
const selectedPostedCouponId = ref(null) // 存储邮费优惠券
const selectedPostedCoupon = ref(null) // 存储邮费优惠券

const selectedProductToSaleId = ref(null) // 存储想要使用优惠券的商品ID
const selectedProductToSale = ref(null) // 存储想要使用优惠券的商品
// 订单表单
const orderForm = ref({
  cartId: "",
  receiverName: "",
  receiverPhone: "",
  receiverProvince: "",
  receiverCity: "",
  receiverDistrict: "",
  receiverAddress: "",
  note: "",
  payType: "ALIPAY",
  domesticFreight: 0,
  internationalFreight: 0,
  discountAmount: 0,
  salesType: "STOCK",
  deliveryType: "NORMAL",
  orderItems: []
})

const stackableCoupons = ref([])

const hasSelectedCoupons = computed(() => {
  return (
    stackableCoupons.value.some((c) => c.selected) ||
    selectedNonStackableCouponId.value ||
    selectedPostedCouponId.value
  )
})
const showProductPopup = ref(false)
const nonStackableCoupons = ref([])
const postageCoupons = ref([])
// 计算商品总金额
const productTotalAmount = computed(() => {
  return orderItems.value.reduce((total, item) => {
    const price = parseFloat(item.price) || 0
    const quantity = parseInt(item.quantity) || 1
    return total + price * quantity
  }, 0)
})

// 计算订单总金额
const totalAmount = computed(() => {
  const productTotal = productTotalAmount.value || 0
  const domestic = orderForm.value.domesticFreight || 0
  const international = orderForm.value.internationalFreight || 0
  const discount = orderForm.value.discountAmount || 0
  return productTotal + domestic + international - discount
})

// 页面初始化
onShow(() => {
  statusBarHeight.value = uni.getSystemInfoSync().statusBarHeight || 20
  // 从路由参数或缓存获取购物车选中项
  cartCartList()
  // 加载收货地址
  loadAddressList()
})

// 处理订单项
const processOrderItems = async (data) => {
  console.log(data, 1111)
  // 处理订单项
  orderItems.value = data
  orderForm.value.orderItems = data.map((item) => ({
    ...item,
    quantity: item.quantity || 1,
    productPrice: item.price.toString() || 0, //重置
    specification: getSpecificationJson(item.productSkuId, item.skus),
    totalPrice: (
      parseFloat(item.productPrice || 0) * (parseInt(item.quantity) || 1)
    ).toFixed(2),
    realPrice: parseFloat(item.productPrice || 0)
  }))
  // 确定销售类型
  determineOrderSalesType(data)
  await Promise.all([loadExpressConfig(), loadProductDetails()])
  // 重新计算运费
  calculateFreight()
  orderForm.value.discountAmount =
    orderForm.value.discountAmount + (selectedPostedCoupon.value?.value || 0)
}

// 在原来的地方调用
const cartCartList = async () => {
  try {
    const eventChannel = getOpenerEventChannel()
    eventChannel.on("selectedItems", async (data) => {
      console.log("接收到的购物车选中项:", data)
      // 验证商品数据完整性
      const invalidItems = data.filter(
        (item) => !item?.productId || !item?.productSkuId
      )
      if (invalidItems.length > 0) {
        uni.showToast({
          title: "商品数据不完整",
          icon: "none"
        })
        setTimeout(() => {
          uni.navigateBack()
        }, 1500)
        return
      }

      // 调用处理订单项的函数
      await processOrderItems(data)
    })
  } catch (error) {
    console.error("订单初始化失败:", error)
    uni.showToast({
      title: "订单初始化失败，请重试",
      icon: "none"
    })
  }
}

// 加载地址列表
const loadAddressList = async () => {
  try {
    const res = await getAddressList()
    if (res.code === 200) {
      addressList.value = res.data
      // 如果有默认地址，自动选择
      const defaultAddress = addressList.value.find(
        (addr) => addr.isDefault || addr.iDefault
      )
      if (defaultAddress) {
        selectAddress(defaultAddress)
      } else if (addressList.value.length > 0) {
        selectAddress(addressList.value[0])
      }
    }
  } catch (error) {
    console.error("获取地址列表失败", error)
    uni.showToast({
      title: "获取地址失败，请重试",
      icon: "none"
    })
  }
}

// 格式化地址
const formatAddress = (address) => {
  if (!address) return ""
  return `${address.province} ${address.city} ${address.district} ${address.detail}`
}

// 获取规格的JSON字符串
const getSpecificationJson = (skuId, skus) => {
  if (!skus || skus.length === 0) return JSON.stringify({})

  const sku = skus.find((s) => s.id === skuId)
  if (!sku || !sku.specification) return JSON.stringify({})

  return sku.specification // 直接返回原始JSON字符串
}

// 格式化规格
const formatSpecification = (skuId, skus) => {
  if (!skus || skus.length === 0) return "默认规格"

  const sku = skus.find((s) => s.id === skuId)
  if (!sku) return "默认规格"

  try {
    const spec = JSON.parse(sku.specification)
    const specStr = Object.entries(spec)
      .map(([key, value]) => `${key}:${value}`)
      .join(" ")
    return specStr || "默认规格"
  } catch (error) {
    console.error("解析规格错误", error)
    return "默认规格"
  }
}

// 显示地址选择器
const showAddressSelector = () => {
  showAddressPopup.value = true
}

// 选择地址
const selectAddress = (address) => {
  selectedAddress.value = address

  // 确保地址对象的属性存在后再赋值
  if (address) {
    orderForm.value.receiverName = address.name || ""
    orderForm.value.receiverPhone = address.phone || ""
    orderForm.value.receiverProvince = address.province || ""
    orderForm.value.receiverCity = address.city || ""
    orderForm.value.receiverDistrict = address.district || ""
    orderForm.value.receiverAddress = address.detail || ""
  }

  showAddressPopup.value = false
}

// 跳转到添加地址页面
const goToAddAddress = () => {
  showAddressPopup.value = false
  uni.navigateTo({
    url: "/pages/views/profile/address-edit",
    events: {
      // 监听地址添加成功事件
      addressAdded: () => {
        loadAddressList()
      }
    }
  })
}

// 返回上一页
const goBack = () => {
  uni.navigateBack()
}

// 获取页面打开事件通道
const getOpenerEventChannel = () => {
  const pages = getCurrentPages()
  const currentPage = pages[pages.length - 1] as any // 添加类型断言
  return currentPage.$page?.fullPath
    ? currentPage.getOpenerEventChannel()
    : null
}

// 根据商品确定订单销售类型
const determineOrderSalesType = (items) => {
  // 检查是否有预售商品
  const hasPresaleItems = items.some((item) => item.salesType === "PRESALE")
  // 如果存在预售商品，整个订单按预售处理，否则按现货处理
  orderForm.value.salesType = hasPresaleItems ? "PRESALE" : "STOCK"
}

// 选择配送方式
const selectDeliveryType = (type) => {
  orderForm.value.deliveryType = type
  calculateFreight()
}

// 获取快递价格配置
const loadExpressConfig = () => {
  //获取商品发货天数
  const expressPriceList = store.state.$systemConfig.filter(
    (item) => item.type === "express_price"
  )
  expressConfig.value = {
    normal_price:
      Number(expressPriceList.find((item) => item.code === "NORMAL")?.value) ||
      10, // 普通快递默认价格
    sf_price:
      Number(expressPriceList.find((item) => item.code === "SF")?.value) || 20 // 顺丰快递默认价格
  }
}

// 计算运费
const calculateFreight = async () => {
  // 先重置所有费用
  orderForm.value.domesticFreight = 0
  orderForm.value.internationalFreight = 0
  orderForm.value.discountAmount = 0

  // 确保已加载快递价格配置
  if (!expressConfig.value) {
    await loadExpressConfig()
  }

  // 根据产品详情计算运费
  let totalDomesticFreight = 0
  let totalInternationalFreight = 0

  // 遍历订单中的商品项
  for (const item of orderItems.value) {
    // 查找对应的产品详情
    const productDetail = productDetails.value.find(
      (p) => p.id === item.productId
    )

    if (productDetail) {
      // 从产品详情中获取运费信息
      let itemDomesticFreight = 0

      // 如果商品设置了运费且不为0，则使用商品运费
      if (productDetail.domesticFreight && productDetail.domesticFreight > 0) {
        itemDomesticFreight = productDetail.domesticFreight
      } else {
        // 否则使用系统配置的价格
        itemDomesticFreight =
          orderForm.value.deliveryType === "SF"
            ? expressConfig.value.sf_price
            : expressConfig.value.normal_price
      }

      // 计算国际运费
      let itemInternationalFreight = productDetail.internationalFreight || 0

      // 计算数量导致的运费增加: 每增加一件商品，运费增加5元
      const quantityAdditional = item.quantity > 1 ? (item.quantity - 1) * 5 : 0

      // 根据配送方式调整运费
      if (
        orderForm.value.deliveryType === "SF" &&
        productDetail.domesticFreight > 0
      ) {
        // 如果有设置商品运费，顺丰快递比普通快递多10元
        itemDomesticFreight += 10
      }

      // 增加数量导致的额外运费
      itemDomesticFreight += quantityAdditional

      // 累加运费
      totalDomesticFreight += itemDomesticFreight
      totalInternationalFreight += itemInternationalFreight
    } else {
      // 如果没有找到产品详情，使用默认值
      const defaultDomesticFreight =
        orderForm.value.deliveryType === "SF"
          ? expressConfig.value.sf_price
          : expressConfig.value.normal_price

      // 计算数量导致的运费增加
      const quantityAdditional = item.quantity > 1 ? (item.quantity - 1) * 5 : 0

      totalDomesticFreight += defaultDomesticFreight + quantityAdditional
      totalInternationalFreight += 0
    }
  }

  // 设置优惠金额
  orderForm.value.discountAmount = coutDiscountAmount(orderItems.value)

  // 更新订单表单中的运费
  orderForm.value.domesticFreight = Math.round(totalDomesticFreight * 100) / 100
  orderForm.value.internationalFreight =
    Math.round(totalInternationalFreight * 100) / 100

  console.log(
    "运费计算结果:",
    "国内:",
    orderForm.value.domesticFreight,
    "国际:",
    orderForm.value.internationalFreight,
    "优惠:",
    orderForm.value.discountAmount
  )
}

// 加载产品详情
const loadProductDetails = async () => {
  try {
    // 得到所有唯一的产品ID
    const productIds = Array.from(
      new Set(orderItems.value.map((item) => item.productId))
    )

    // 并行获取所有产品详情
    const promises = productIds.map((id) => getProductDetail({ id }))
    const results = await Promise.all(promises)

    // 提取产品详情
    const details = results
      .filter((res) => res && res.data)
      .map((res) => res.data)

    productDetails.value = details
    // 更新订单项中的价格（如果原始价格为0）
    orderItems.value.forEach((item) => {
      const productDetail = productDetails.value.find(
        (p) => p.id === item.productId
      )
      if (
        productDetail &&
        (!item.productPrice || parseFloat(item.productPrice) === 0)
      ) {
        const skuDetail = productDetail.skus?.find(
          (s) => s.id === item.productSkuId
        )
        item.productPrice = skuDetail?.price || productDetail.price || 0
      }
    })
  } catch (error) {
    console.error("获取产品详情失败:", error)
    uni.showToast({
      title: "获取产品信息失败",
      icon: "none"
    })
  }
}

// 提交订单
const submitOrder = async () => {
  // 下单前检查实名认证状态
  if (!isCertified.value) {
    uni.showModal({
      title: "实名认证提醒",
      content: "根据平台规定，您需要先完成实名认证才能进行下单操作。",
      confirmText: "前往认证",
      cancelText: "取消",
      success: (res) => {
        if (res.confirm) {
          uni.navigateTo({
            url: "/pages/views/certification/index"
          })
        }
      }
    })
    return // 阻止后续下单流程
  }

  if (!selectedAddress.value) {
    uni.showToast({
      title: "请选择收货地址",
      icon: "none"
    })
    return
  }

  // 验证收货信息
  if (
    !orderForm.value.receiverName ||
    !orderForm.value.receiverPhone ||
    !orderForm.value.receiverProvince ||
    !orderForm.value.receiverCity ||
    !orderForm.value.receiverDistrict ||
    !orderForm.value.receiverAddress
  ) {
    uni.showToast({
      title: "收货信息不完整，请检查",
      icon: "none"
    })
    return
  }

  // 验证手机号格式
  if (!/^1[3-9]\d{9}$/.test(orderForm.value.receiverPhone)) {
    uni.showToast({
      title: "请输入正确的手机号码",
      icon: "none"
    })
    return
  }

  // 验证订单项
  if (!orderForm.value.orderItems || orderForm.value.orderItems.length === 0) {
    uni.showToast({
      title: "订单中没有商品",
      icon: "none"
    })
    return
  }

  // 验证金额
  if (totalAmount.value <= 0) {
    uni.showToast({
      title: "订单金额异常，请重新确认",
      icon: "none"
    })
    return
  }

  // 确保订单项中包含价格信息和新增的必要字段
  orderForm.value.orderItems = orderForm.value.orderItems.map((item, index) => {
    const orderItem = orderItems.value[index]
    return {
      ...item,
      productName: orderItem.productName || "",
      productPrice: orderItem.productPrice || 0,
      totalPrice: (
        parseFloat(orderItem.productPrice || 0) * (parseInt(item.quantity) || 1)
      ).toFixed(2),
      specification: getSpecificationJson(
        orderItem.productSkuId,
        orderItem.skus
      ),
      fileUrl: orderItem.image || "",
      realPrice: parseFloat(orderItem.productPrice || 0)
    }
  })

  // 设置提交状态
  submitting.value = true

  //收集所有使用的优惠券
  const couponsList = [
    ...(stackableCoupons.value?.filter((c) => c.selected) || []),
    selectedNonStackableCoupon.value,
    selectedPostedCoupon.value
  ].filter(Boolean)

  // 处理金额字段，保留两位小数避免精度问题
  const orderData = {
    ...orderForm.value,
    domesticFreight: parseFloat(orderForm.value.domesticFreight.toFixed(2)),
    internationalFreight: parseFloat(
      orderForm.value.internationalFreight.toFixed(2)
    ),
    discountAmount: parseFloat(orderForm.value.discountAmount.toFixed(2)),
    totalAmount: parseFloat(totalAmount.value.toFixed(2)),
    productTotalAmount: parseFloat(productTotalAmount.value.toFixed(2)),
    orderTime: Date.now(),
    ...(couponsList.length > 0 && {
      coupons: couponsList.map((coupon) => ({
        id: coupon.id,
        orderId: coupon.orderId,
        productId: selectedProductToSale.value.productId,
        skuId: selectedProductToSale.value.productSkuId
      }))
    })
  }

  try {
    console.log("开始创建订单...")
    // 移除第二个参数以匹配类型定义，假设错误处理由API封装或默认行为处理
    const response = await createOrder(orderData)

    if (response.code === 200) {
      const orderId = response.data

      // 创建支付宝订单
      const alipayOrder = await payOrder({
        orderId: orderId,
        payType: "ALIPAY"
      })
      const result = alipayOrder[1]

      if (result.code === 200) {
        const platform = uni.getSystemInfoSync().platform
        if (
          platform === "android" ||
          platform === "ios" ||
          platform === "app-plus"
        ) {
          try {
            // 设置支付宝沙盒环境
            var EnvUtils = plus.android.importClass(
              "com.alipay.sdk.app.EnvUtils"
            ) as any // 添加类型断言
            EnvUtils.setEnv(EnvUtils.EnvEnum.SANDBOX)

            // 调用支付接口
            uni.requestPayment({
              provider: "alipay",
              orderInfo: result.data.trim(),
              success: (e) => {
                submitting.value = false // 重置提交状态
                uni.showToast({
                  icon: "success",
                  title: "支付成功！"
                })

                // 支付成功后延迟跳转到结果页
                setTimeout(() => {
                  // 获取支付方式名称
                  const getPaymentMethod = (payType: string): string => {
                    const payMap: { [key: string]: string } = {
                      ALIPAY: "支付宝",
                      WECHAT: "微信支付",
                      BANK: "银行卡"
                    }
                    return payMap[payType] || "未知方式"
                  }

                  // 构建订单详情数据
                  const orderDetailData = {
                    orderId: orderId,
                    payAmount: totalAmount.value.toFixed(2),
                    paymentMethod: getPaymentMethod("ALIPAY"),
                    payTime: Date.now()
                  }

                  uni.redirectTo({
                    url: `/pages/views/order/result?orderId=${orderId}&status=success&payAmount=${
                      orderDetailData.payAmount
                    }&paymentMethod=${encodeURIComponent(
                      orderDetailData.paymentMethod
                    )}&payTime=${orderDetailData.payTime}`
                  })
                }, 1500)
              },
              fail: (e) => {
                console.log("支付失败或取消:", e)
                uni.showToast({
                  title: "支付未完成",
                  icon: "none",
                  duration: 1500
                })
                // 直接跳转到订单详情页面
                setTimeout(() => {
                  uni.redirectTo({
                    url: `/pages/views/order/detail?id=${orderId}`
                  })
                }, 1500)
                submitting.value = false
              }
            })
          } catch (error) {
            console.error("支付过程异常:", error)
            uni.showToast({
              title: "支付异常",
              icon: "none",
              duration: 1500
            })
            // 直接跳转到订单详情页面
            setTimeout(() => {
              uni.redirectTo({
                url: `/pages/views/order/detail?id=${orderId}`
              })
            }, 1500)
            submitting.value = false
          }
        } else {
          // 非APP环境，直接跳转到订单详情
          uni.showToast({
            title: "当前环境不支持支付",
            icon: "none"
          })
          // 直接跳转到订单详情页面
          setTimeout(() => {
            uni.redirectTo({
              url: `/pages/views/order/detail?id=${orderId}`
            })
          }, 1500)
          submitting.value = false
        }
      } else {
        // 创建支付订单失败
        uni.showToast({
          title: "创建支付订单失败",
          icon: "none"
        })
        // 直接跳转到订单详情页面
        setTimeout(() => {
          uni.redirectTo({
            url: `/pages/views/order/detail?id=${orderId}`
          })
        }, 1500)
        submitting.value = false
      }
    } else {
      uni.showToast({
        title: response.message,
        icon: "none"
      })
      submitting.value = false
    }
  } catch (error) {
    const errorMsg =
      error?.data?.message || error?.message || "提交订单失败，请重试"
    uni.showToast({
      title: errorMsg,
      icon: "none"
    })
    // 提交订单接口失败，也显示支付失败弹窗，但此时 orderId 可能未生成
    // 为避免错误，检查 currentOrderIdForPopup 是否有值
    if (currentOrderIdForPopup.value) {
      setTimeout(() => {
        uni.redirectTo({
          url: `/pages/views/order/detail?id=${currentOrderIdForPopup.value}`
        })
      }, 1500)
    }
    submitting.value = false
  }
}

// 支付失败弹窗的"重新支付"按钮事件
const retryPayment = () => {
  showPaymentFailedPopup.value = false
  if (currentOrderIdForPopup.value) {
    // 跳转到订单详情页并传递needPay参数，表示需要调出支付
    uni.navigateTo({
      url: `/pages/views/order/detail?id=${currentOrderIdForPopup.value}&needPay=true`
    })
  } else {
    uni.showToast({
      title: "无法获取订单信息",
      icon: "none"
    })
  }
}

// 支付失败弹窗的"刷新"按钮事件
const refreshPaymentStatus = () => {
  showPaymentFailedPopup.value = false
  if (currentOrderIdForPopup.value) {
    // 跳转到订单详情页面查看订单状态
    uni.navigateTo({
      url: `/pages/views/profile/order-detail?id=${currentOrderIdForPopup.value}`
    })
  } else {
    uni.showToast({
      title: "无法获取订单信息",
      icon: "none"
    })
    // 如果没有订单信息，返回上一页
    setTimeout(() => {
      uni.navigateBack()
    }, 1500)
  }
}

// 查询支付状态
const queryPaymentStatus = async (orderId) => {
  try {
    // 显示加载提示
    uni.showLoading({
      title: "查询支付结果...",
      mask: true
    })

    // 延迟1秒再查询，等待后端处理支付回调
    await new Promise((resolve) => setTimeout(resolve, 1000))

    // 定义查询失败次数计数器
    let queryAttempts = 0
    const maxQueryAttempts = 4 // 增加到4次

    // 递归查询函数
    const checkOrderStatus = async () => {
      try {
        // 记录查询次数
        queryAttempts++
        console.log(`开始第${queryAttempts}次查询订单状态, 订单ID: ${orderId}`)

        // 使用封装的API查询订单状态
        const [statusError, statusRes] = await getOrderDetail(orderId)

        if (statusError) {
          console.error(`第${queryAttempts}次查询订单状态出错:`, statusError)
          throw new Error(statusError.message || "查询订单状态失败")
        }

        // 检查响应
        if (!statusRes) {
          console.error(`第${queryAttempts}次查询订单状态响应异常:`, statusRes)
          throw new Error("订单状态查询返回数据异常")
        }

        // 解析嵌套的数据结构
        let orderData
        if (statusRes.data && statusRes.data.data) {
          // 如果返回的是嵌套的 data.data 结构
          orderData = statusRes.data.data
        } else if (statusRes.data) {
          // 直接使用 data
          orderData = statusRes.data
        } else {
          // 数据格式异常
          console.error(
            `第${queryAttempts}次查询返回的数据格式异常:`,
            statusRes
          )
          throw new Error("订单数据格式异常")
        }

        console.log(`第${queryAttempts}次查询订单状态结果:`, orderData)

        // 检查支付状态 - 添加多种可能的状态值
        if (
          orderData.payStatus === "PAID" ||
          orderData.orderStatus === "PAID" ||
          orderData.orderStatus === "WAIT_DELIVER" ||
          orderData.orderStatus === "PAID_SUCCESS" ||
          orderData.payStatus === "SUCCESS"
        ) {
          uni.hideLoading()
          submitting.value = false // 重置提交状态
          uni.showToast({
            title: "支付成功",
            icon: "success"
          })

          // 跳转到支付成功页面
          setTimeout(() => {
            uni.redirectTo({
              url: `/pages/views/order/result?orderId=${orderId}&status=success`
            })
          }, 1500)
          return true
        }

        // 检查是否已取消
        if (
          orderData.orderStatus === "CANCELLED" ||
          orderData.orderStatus === "CANCEL" ||
          orderData.payStatus === "CANCEL"
        ) {
          uni.hideLoading()
          uni.showToast({
            title: "订单已取消",
            icon: "none"
          })

          setTimeout(() => {
            uni.navigateBack()
          }, 1500)
          return false
        }

        // 如果未达到最大查询次数，继续查询
        if (queryAttempts < maxQueryAttempts) {
          // 每次查询增加等待时间
          const waitTime = 1500 + queryAttempts * 500

          uni.showToast({
            title: `正在确认支付结果(${queryAttempts}/${maxQueryAttempts})`,
            icon: "none",
            duration: 1500
          })

          // 延迟后再次查询
          setTimeout(() => {
            checkOrderStatus()
          }, waitTime)
          return false
        }

        // 达到最大查询次数，仍未支付成功
        uni.hideLoading()
        uni.showModal({
          title: "支付提示",
          content: "支付结果确认中，请在订单页面查看最新状态",
          confirmText: "查看订单",
          cancelText: "返回",
          success: (res) => {
            if (res.confirm) {
              uni.redirectTo({
                url: `/pages/views/profile/order-detail?id=${orderId}`
              })
            } else {
              uni.navigateBack()
            }
          }
        })
        return false
      } catch (error) {
        console.error(`第${queryAttempts}次查询支付状态失败`, error)

        // 如果未达到最大查询次数，继续查询
        if (queryAttempts < maxQueryAttempts) {
          // 遇到错误，增加等待时间
          const waitTime = 2000 + queryAttempts * 1000

          uni.showToast({
            title: "重新查询支付结果...",
            icon: "none",
            duration: 1500
          })

          setTimeout(() => {
            checkOrderStatus()
          }, waitTime)
          return false
        }

        // 达到最大查询次数，显示错误
        uni.hideLoading()
        uni.showModal({
          title: "查询失败",
          content: "无法确认支付结果，请在订单页面查看",
          confirmText: "查看订单",
          cancelText: "返回",
          success: (res) => {
            if (res.confirm) {
              uni.redirectTo({
                url: `/pages/views/profile/order-detail?id=${orderId}`
              })
            } else {
              uni.navigateBack()
            }
          }
        })
        return false
      }
    }

    // 开始查询
    return await checkOrderStatus()
  } catch (error) {
    console.error("查询支付状态异常", error)
    uni.hideLoading()
    uni.showToast({
      title: "查询支付结果异常",
      icon: "none"
    })

    // 显示对话框，允许用户查看订单
    uni.showModal({
      title: "支付提示",
      content: "支付过程中出现异常，请在订单页面确认结果",
      confirmText: "查看订单",
      cancelText: "返回",
      success: (res) => {
        if (res.confirm) {
          uni.redirectTo({
            url: `/pages/views/profile/order-detail?id=${orderId}`
          })
        } else {
          uni.navigateBack()
        }
      }
    })
    return false
  }
}

// 开始轮询查询支付状态
const startPaymentStatusPolling = async (orderId) => {
  console.log("开始轮询查询支付状态, 订单ID:", orderId)

  // 显示加载提示
  uni.showLoading({
    title: "等待支付结果...",
    mask: true
  })

  // 轮询次数和间隔设置
  let pollCount = 0
  const maxPolls = 12 // 最多查询12次
  const pollInterval = 5000 // 每5秒查询一次

  // 创建轮询函数
  const pollPaymentStatus = async () => {
    try {
      // 记录查询次数
      pollCount++
      console.log(`进行第${pollCount}次支付状态轮询, 订单ID: ${orderId}`)

      // 使用封装的API查询订单状态
      const [statusError, statusRes] = await getOrderDetail(orderId)

      if (statusError) {
        console.error(`第${pollCount}次查询订单状态出错:`, statusError)
        if (pollCount >= maxPolls) {
          uni.hideLoading()
          uni.showModal({
            title: "订单支付",
            content: "无法获取支付结果，请在订单列表中查看",
            confirmText: "查看订单",
            cancelText: "返回",
            success: (res) => {
              if (res.confirm) {
                uni.redirectTo({
                  url: `/pages/views/profile/order-detail?id=${orderId}`
                })
              } else {
                uni.navigateBack()
              }
            }
          })
          return
        }

        // 继续下一次轮询
        setTimeout(pollPaymentStatus, pollInterval)
        return
      }

      // 解析订单数据
      let orderData
      if (statusRes.data && statusRes.data.data) {
        // 如果返回的是嵌套的 data.data 结构
        orderData = statusRes.data.data
      } else if (statusRes.data) {
        // 直接使用 data
        orderData = statusRes.data
      } else {
        console.error(`第${pollCount}次查询返回的数据格式异常:`, statusRes)
        if (pollCount >= maxPolls) {
          uni.hideLoading()
          uni.showModal({
            title: "订单支付",
            content: "无法获取支付结果，请在订单列表中查看",
            confirmText: "查看订单",
            cancelText: "返回",
            success: (res) => {
              if (res.confirm) {
                uni.redirectTo({
                  url: `/pages/views/profile/order-detail?id=${orderId}`
                })
              } else {
                uni.navigateBack()
              }
            }
          })
          return
        }

        // 继续下一次轮询
        setTimeout(pollPaymentStatus, pollInterval)
        return
      }

      console.log(`第${pollCount}次查询订单状态结果:`, orderData)

      // 检查支付状态
      if (
        orderData.payStatus === "PAID" ||
        orderData.orderStatus === "PAID" ||
        orderData.orderStatus === "WAIT_DELIVER" ||
        orderData.orderStatus === "PAID_SUCCESS" ||
        orderData.payStatus === "SUCCESS"
      ) {
        // 支付成功
        uni.hideLoading()
        submitting.value = false // 重置提交状态
        uni.showToast({
          title: "支付成功",
          icon: "success"
        })

        // 跳转到支付成功页面
        setTimeout(() => {
          uni.redirectTo({
            url: `/pages/views/order/result?orderId=${orderId}&status=success`
          })
        }, 1500)
        return
      }

      // 检查是否已取消
      if (
        orderData.orderStatus === "CANCELLED" ||
        orderData.orderStatus === "CANCEL" ||
        orderData.payStatus === "CANCEL"
      ) {
        uni.hideLoading()
        uni.showToast({
          title: "订单已取消",
          icon: "none"
        })

        setTimeout(() => {
          uni.navigateBack()
        }, 1500)
        return
      }

      // 如果未达到最大查询次数且未支付，继续查询
      if (pollCount < maxPolls) {
        // 每4次查询显示一次提示
        if (pollCount % 4 === 0) {
          uni.hideLoading()
          uni.showToast({
            title: "正在等待支付结果...",
            icon: "none",
            duration: 2000
          })

          setTimeout(() => {
            uni.showLoading({
              title: "等待支付...",
              mask: true
            })
          }, 2000)
        }

        // 继续下一次轮询
        setTimeout(pollPaymentStatus, pollInterval)
      } else {
        // 达到最大查询次数，仍未支付成功
        uni.hideLoading()
        uni.showModal({
          title: "订单支付",
          content: "未检测到支付完成，请在订单页面查看最新状态",
          confirmText: "查看订单",
          cancelText: "返回",
          success: (res) => {
            if (res.confirm) {
              uni.redirectTo({
                url: `/pages/views/profile/order-detail?id=${orderId}`
              })
            } else {
              uni.navigateBack()
            }
          }
        })
      }
    } catch (error) {
      console.error(`第${pollCount}次查询支付状态异常`, error)

      // 如果未达到最大查询次数，继续查询
      if (pollCount < maxPolls) {
        setTimeout(pollPaymentStatus, pollInterval)
      } else {
        // 达到最大查询次数，显示错误
        uni.hideLoading()
        uni.showModal({
          title: "查询失败",
          content: "无法确认支付结果，请在订单页面查看",
          confirmText: "查看订单",
          cancelText: "返回",
          success: (res) => {
            if (res.confirm) {
              uni.redirectTo({
                url: `/pages/views/profile/order-detail?id=${orderId}`
              })
            } else {
              uni.navigateBack()
            }
          }
        })
      }
    }
  }

  // 开始首次查询
  setTimeout(pollPaymentStatus, 3000) // 延迟3秒后开始首次查询
}

const openProductPopup = () => {
  showProductPopup.value = true
}

const getCouponsApp = async () => {
  const params = {
    productId: selectedProductToSale.value.productId,
    orderAmount: selectedProductToSale.value.price
  }
  const res = await GET_COUPONS_PRODUCT(params)
  uni.showLoading({
    title: "正在获取优惠券..."
  })
  if (res.code === 200) {
    let resData = res.data
    const formatCouponInfo = function (couponInfo, couponsItem) {
      // 查找原有优惠券的selected状态
      const existingCoupon = stackableCoupons.value?.find(
        (c) => c.id === couponInfo.id
      )
      return {
        ...couponInfo,
        id: couponsItem.id,
        validStartTime: couponInfo?.validStartTime
          ? moment(couponInfo?.validStartTime).format("YYYY-MM-DD")
          : "",
        validEndTime: couponInfo?.validEndTime
          ? moment(couponInfo?.validEndTime).format("YYYY-MM-DD")
          : "",
        type: COUPONS_TYPE.find((type) => type.value === couponInfo?.type)
          .label,
        stackRuleName: COUPONS_STACKRULE.find(
          (rule) => rule.value === couponInfo?.stackRule
        ).label,
        selected: existingCoupon ? existingCoupon.selected : false // 保留原有的selected状态
      }
    }

    // 不可叠加优惠券
    nonStackableCoupons.value = resData
      .filter(
        (item) =>
          item?.couponInfo?.stackRule === "NONE" &&
          item?.couponInfo?.type !== "SHIPPING"
      )
      .map((item) => formatCouponInfo(item.couponInfo, item))

    // 可叠加优惠券
    stackableCoupons.value = resData
      .filter(
        (item) =>
          item?.couponInfo?.stackRule !== "NONE" &&
          item?.couponInfo?.type !== "SHIPPING"
      )
      .map((item) => formatCouponInfo(item.couponInfo, item))

    // 邮费优惠券
    postageCoupons.value = resData
      .filter((item) => item?.couponInfo?.type === "SHIPPING")
      .map((item) => formatCouponInfo(item.couponInfo, item))
  } else {
    uni.showToast({
      title: res.data?.message || "获取优惠券失败",
      icon: "none"
    })
  }
  uni.hideLoading()
}

const selectProductToSale = (coupon) => {
  // 如果选择的是同一个优惠券，则取消选择
  if (selectedProductToSaleId.value === coupon.id) {
    selectedProductToSaleId.value = null
    selectedProductToSale.value = null
  } else {
    selectedProductToSaleId.value = coupon.id
    selectedProductToSale.value = coupon
  }
}

const selectNonStackableCoupon = (coupon) => {
  // 如果选择的是同一个优惠券，则取消选择
  if (selectedNonStackableCouponId.value === coupon.id) {
    selectedNonStackableCouponId.value = null
    selectedNonStackableCoupon.value = null
  } else {
    selectedNonStackableCouponId.value = coupon.id
    selectedNonStackableCoupon.value = coupon
    // 清空所有可叠加优惠券的选择
    stackableCoupons.value.forEach((c) => (c.selected = false))
  }
}

const selectPostedCoupon = (coupon) => {
  // 如果选择的是同一个优惠券，则取消选择
  if (selectedPostedCouponId.value === coupon.id) {
    selectedPostedCouponId.value = null
    selectedPostedCoupon.value = null
  } else {
    selectedPostedCouponId.value = coupon.id
    selectedPostedCoupon.value = coupon
  }
}

const clearCoupons = () => {
  selectedNonStackableCouponId.value = null
  selectedNonStackableCoupon.value = null
  selectedPostedCouponId.value = null
  selectedPostedCoupon.value = null
  stackableCoupons.value.forEach((c) => (c.selected = false))
  showCouponPopup.value = false

  clearChosenProduct()
}

const applyCoupons = () => {
  //可叠加优惠券选择数量
  if (!hasSelectedCoupons) {
    uni.showToast({
      title: "请选择优惠券",
      icon: "none"
    })
    return
  }

  // 优惠券计算逻辑
  coutPriceByCoupons()
  showCouponPopup.value = false
}

const clearChosenProduct = () => {
  showProductPopup.value = false
  selectedProductToSaleId.value = null

  // 重置价格
  orderItems.value = orderItems.value.map((item) => {
    item.productPrice = item.price.toString()
    return item
  })
  processOrderItems(orderItems.value)
}

const handleChosenProduct = () => {
  showProductPopup.value = false
  if (selectedProductToSaleId.value) {
    //加载优惠券
    getCouponsApp()
    showCouponPopup.value = true
  }
}

const coutPriceByCoupons = () => {
  console.log(orderItems.value, "before")
  // 计算总金额
  const options = {
    product: selectedProductToSale.value,
    couponsList: selectedNonStackableCouponId.value
      ? [selectedNonStackableCoupon.value]
      : stackableCoupons.value.filter((c) => c.selected)
  }
  const result = coutPriceByCouponsList(options)
  if (!result) {
    return
  } else {
    // 替换对应商品
    const index = orderItems.value?.findIndex((item) => item.id === result.id)
    const formIndex = orderForm.value.orderItems?.findIndex(
      (item) => item.id === result.id
    )
    if (index !== -1) {
      orderItems.value.splice(index, 1, result)
    }
    if (formIndex !== -1) {
      orderForm.value.orderItems.splice(formIndex, 1, result)
    }
  }
  console.log(orderItems, orderForm.value.orderItems, "after")
  processOrderItems(orderItems.value)
}

// 获取实名认证状态
const isCertified = computed(() => store.state.$userInfo.verified)
</script>

<style>
.header-gradient {
  background-image: linear-gradient(to right, #ef4444, #ec4899);
  height: 80rpx;
}

.header-circle {
  position: absolute;
  width: 180rpx;
  height: 180rpx;
  border-radius: 50%;
  background-color: rgba(255, 255, 255, 0.1);
  top: -60rpx;
  left: -60rpx;
}

.header-circle-2 {
  width: 140rpx;
  height: 140rpx;
  left: auto;
  right: -40rpx;
  top: -40rpx;
}
</style>
