export default {
	// 可以以页面为单位来写，比如首页的内容，写在index字段，个人中心写在center，共同部分写在common部分
	components: {
		desc: 'Numerous components cover the various requirements of the development process, and the components are rich in functions and compatible with multiple terminals. Let you integrate quickly, out of the box'
	},
	js: {
		desc: 'Numerous intimate gadgets are a weapon that you can call upon during the development process, allowing you to dart in your hand and pierce the Yang with a hundred steps'
	},
	template: {
		desc: 'Collection of many commonly used pages and layouts, reducing the repetitive work of developers, allowing you to focus on logic and get twice the result with half the effort'
	},
	nav: {
		components: 'Components',
		js: 'JS',
		template: 'Template'
	},
	common: {
		intro: 'UI framework for rapid development of multiple platforms',
		title: 'uview-plus',
	},
}
