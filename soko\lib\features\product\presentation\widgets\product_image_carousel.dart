import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:cached_network_image/cached_network_image.dart';

import 'package:soko/core/theme/app_colors.dart';
import 'package:soko/core/theme/app_text_styles.dart';
import 'package:soko/shared/presentation/widgets/loading_widget.dart';

/// 商品图片轮播组件
class ProductImageCarousel extends StatefulWidget {

  const ProductImageCarousel({
    super.key,
    required this.images,
    this.height,
    this.onImageTap,
  });
  final List<String> images;
  final double? height;
  final VoidCallback? onImageTap;

  @override
  State<ProductImageCarousel> createState() => _ProductImageCarouselState();
}

class _ProductImageCarouselState extends State<ProductImageCarousel> {
  final PageController _pageController = PageController();
  int _currentIndex = 0;

  @override
  void dispose() {
    _pageController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    if (widget.images.isEmpty) {
      return _buildPlaceholder();
    }

    return Container(
      height: widget.height ?? 300.h,
      color: Colors.white,
      child: Stack(
        children: [
          // 图片轮播
          PageView.builder(
            controller: _pageController,
            onPageChanged: (index) {
              setState(() {
                _currentIndex = index;
              });
            },
            itemCount: widget.images.length,
            itemBuilder: (context, index) {
              return GestureDetector(
                onTap: widget.onImageTap,
                child: CachedNetworkImage(
                  imageUrl: widget.images[index],
                  fit: BoxFit.cover,
                  placeholder: (context, url) => const LoadingWidget(),
                  errorWidget: (context, url, error) => _buildErrorWidget(),
                ),
              );
            },
          ),
          // 指示器
          if (widget.images.length > 1) _buildIndicator(),
          // 图片计数
          if (widget.images.length > 1) _buildImageCounter(),
        ],
      ),
    );
  }

  /// 构建占位符
  Widget _buildPlaceholder() {
    return Container(
      height: widget.height ?? 300.h,
      color: AppColors.grey100,
      child: Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.image_not_supported,
              size: 48.sp,
              color: AppColors.grey400,
            ),
            SizedBox(height: 8.h),
            Text(
              '暂无图片',
              style: AppTextStyles.bodyMedium.copyWith(
                color: AppColors.grey500,
              ),
            ),
          ],
        ),
      ),
    );
  }

  /// 构建错误组件
  Widget _buildErrorWidget() {
    return ColoredBox(
      color: AppColors.grey100,
      child: Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.broken_image,
              size: 48.sp,
              color: AppColors.grey400,
            ),
            SizedBox(height: 8.h),
            Text(
              '图片加载失败',
              style: AppTextStyles.bodyMedium.copyWith(
                color: AppColors.grey500,
              ),
            ),
          ],
        ),
      ),
    );
  }

  /// 构建指示器
  Widget _buildIndicator() {
    return Positioned(
      bottom: 16.h,
      left: 0,
      right: 0,
      child: Row(
        mainAxisAlignment: MainAxisAlignment.center,
        children: List.generate(
          widget.images.length,
          (index) => Container(
            margin: EdgeInsets.symmetric(horizontal: 2.w),
            width: 6.w,
            height: 6.w,
            decoration: BoxDecoration(
              shape: BoxShape.circle,
              color: index == _currentIndex
                  ? Colors.white
                  : Colors.white.withOpacity(0.5),
            ),
          ),
        ),
      ),
    );
  }

  /// 构建图片计数
  Widget _buildImageCounter() {
    return Positioned(
      top: 16.h,
      right: 16.w,
      child: Container(
        padding: EdgeInsets.symmetric(horizontal: 8.w, vertical: 4.h),
        decoration: BoxDecoration(
          color: Colors.black.withOpacity(0.6),
          borderRadius: BorderRadius.circular(12.r),
        ),
        child: Text(
          '${_currentIndex + 1}/${widget.images.length}',
          style: AppTextStyles.caption.copyWith(
            color: Colors.white,
            fontWeight: FontWeight.w500,
          ),
        ),
      ),
    );
  }
}

/// 商品图片预览弹窗
class ProductImagePreviewDialog extends StatefulWidget {

  const ProductImagePreviewDialog({
    super.key,
    required this.images,
    this.initialIndex = 0,
  });
  final List<String> images;
  final int initialIndex;

  @override
  State<ProductImagePreviewDialog> createState() => _ProductImagePreviewDialogState();
}

class _ProductImagePreviewDialogState extends State<ProductImagePreviewDialog> {
  late PageController _pageController;
  late int _currentIndex;

  @override
  void initState() {
    super.initState();
    _currentIndex = widget.initialIndex;
    _pageController = PageController(initialPage: widget.initialIndex);
  }

  @override
  void dispose() {
    _pageController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Dialog.fullscreen(
      backgroundColor: Colors.black,
      child: Stack(
        children: [
          // 图片预览
          PageView.builder(
            controller: _pageController,
            onPageChanged: (index) {
              setState(() {
                _currentIndex = index;
              });
            },
            itemCount: widget.images.length,
            itemBuilder: (context, index) {
              return Center(
                child: InteractiveViewer(
                  child: CachedNetworkImage(
                    imageUrl: widget.images[index],
                    fit: BoxFit.contain,
                    placeholder: (context, url) => const LoadingWidget(),
                    errorWidget: (context, url, error) => Icon(
                      Icons.broken_image,
                      size: 64.sp,
                      color: Colors.white54,
                    ),
                  ),
                ),
              );
            },
          ),
          // 顶部工具栏
          Positioned(
            top: MediaQuery.of(context).padding.top,
            left: 0,
            right: 0,
            child: Container(
              height: 56.h,
              padding: EdgeInsets.symmetric(horizontal: 16.w),
              child: Row(
                children: [
                  // 关闭按钮
                  IconButton(
                    onPressed: () => Navigator.of(context).pop(),
                    icon: Icon(
                      Icons.close,
                      color: Colors.white,
                      size: 24.sp,
                    ),
                  ),
                  const Spacer(),
                  // 图片计数
                  Text(
                    '${_currentIndex + 1}/${widget.images.length}',
                    style: AppTextStyles.bodyMedium.copyWith(
                      color: Colors.white,
                      fontWeight: FontWeight.w500,
                    ),
                  ),
                ],
              ),
            ),
          ),
          // 底部缩略图
          if (widget.images.length > 1) _buildThumbnails(),
        ],
      ),
    );
  }

  /// 构建缩略图
  Widget _buildThumbnails() {
    return Positioned(
      bottom: MediaQuery.of(context).padding.bottom + 16.h,
      left: 0,
      right: 0,
      child: SizedBox(
        height: 60.h,
        child: ListView.builder(
          scrollDirection: Axis.horizontal,
          padding: EdgeInsets.symmetric(horizontal: 16.w),
          itemCount: widget.images.length,
          itemBuilder: (context, index) {
            final isSelected = index == _currentIndex;
            return GestureDetector(
              onTap: () {
                _pageController.animateToPage(
                  index,
                  duration: const Duration(milliseconds: 300),
                  curve: Curves.easeInOut,
                );
              },
              child: Container(
                width: 60.w,
                height: 60.h,
                margin: EdgeInsets.only(right: 8.w),
                decoration: BoxDecoration(
                  border: Border.all(
                    color: isSelected ? Colors.white : Colors.transparent,
                    width: 2,
                  ),
                  borderRadius: BorderRadius.circular(8.r),
                ),
                child: ClipRRect(
                  borderRadius: BorderRadius.circular(6.r),
                  child: CachedNetworkImage(
                    imageUrl: widget.images[index],
                    fit: BoxFit.cover,
                    placeholder: (context, url) => Container(
                      color: Colors.grey[800],
                      child: const LoadingWidget(),
                    ),
                    errorWidget: (context, url, error) => Container(
                      color: Colors.grey[800],
                      child: Icon(
                        Icons.broken_image,
                        color: Colors.white54,
                        size: 20.sp,
                      ),
                    ),
                  ),
                ),
              ),
            );
          },
        ),
      ),
    );
  }
}
