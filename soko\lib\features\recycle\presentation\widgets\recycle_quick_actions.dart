import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

import 'package:soko/shared/presentation/widgets/custom_card.dart';

/// 回收快速操作组件
class RecycleQuickActions extends StatelessWidget {

  const RecycleQuickActions({
    super.key,
    required this.onCreateOrder,
    required this.onViewOrders,
  });
  final VoidCallback onCreateOrder;
  final VoidCallback onViewOrders;

  @override
  Widget build(BuildContext context) {
    return CustomCard(
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // 标题
          Text(
            '快速操作',
            style: TextStyle(
              fontSize: 18.sp,
              fontWeight: FontWeight.w600,
              color: Colors.black87,
            ),
          ),
          SizedBox(height: 16.h),

          // 操作按钮
          Row(
            children: [
              // 创建回收订单
              Expanded(
                child: _buildActionButton(
                  icon: Icons.add_circle_outline,
                  title: '创建回收订单',
                  subtitle: '快速创建新的回收订单',
                  color: Colors.green,
                  onTap: onCreateOrder,
                ),
              ),
              SizedBox(width: 12.w),

              // 查看我的订单
              Expanded(
                child: _buildActionButton(
                  icon: Icons.list_alt,
                  title: '我的订单',
                  subtitle: '查看所有回收订单',
                  color: Colors.blue,
                  onTap: onViewOrders,
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  /// 构建操作按钮
  Widget _buildActionButton({
    required IconData icon,
    required String title,
    required String subtitle,
    required Color color,
    required VoidCallback onTap,
  }) {
    return InkWell(
      onTap: onTap,
      borderRadius: BorderRadius.circular(12.r),
      child: Container(
        padding: EdgeInsets.all(16.w),
        decoration: BoxDecoration(
          color: color.withValues(alpha: 0.1),
          borderRadius: BorderRadius.circular(12.r),
          border: Border.all(color: color.withValues(alpha: 0.2)),
        ),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // 图标
            Container(
              width: 48.w,
              height: 48.w,
              decoration: BoxDecoration(
                color: color.withValues(alpha: 0.2),
                borderRadius: BorderRadius.circular(24.r),
              ),
              child: Icon(
                icon,
                color: color,
                size: 24.w,
              ),
            ),
            SizedBox(height: 12.h),

            // 标题
            Text(
              title,
              style: TextStyle(
                fontSize: 16.sp,
                fontWeight: FontWeight.w600,
                color: Colors.black87,
              ),
            ),
            SizedBox(height: 4.h),

            // 副标题
            Text(
              subtitle,
              style: TextStyle(
                fontSize: 12.sp,
                color: Colors.grey[600],
              ),
              maxLines: 2,
              overflow: TextOverflow.ellipsis,
            ),
          ],
        ),
      ),
    );
  }
}
