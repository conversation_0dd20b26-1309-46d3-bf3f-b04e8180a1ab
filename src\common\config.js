const serverConfigs = {
  local: "http://************:8080",
  development: "http://localhost:8080",
  丁少康: "http://***********:8080",
  test: "http://***********:8090/api"
}

export default {
  // 根据环境选择不同的API基础地址
  baseUrl:
    process.env.NODE_ENV === "development"
      ? serverConfigs["development"]
      : "https://api.soko.com/api", // 生产环境API地址，需要替换为实际地址

  // API超时时间设置(毫秒)
  timeout: 60000,

  // 是否允许跨域携带凭证
  withCredentials: true
}
