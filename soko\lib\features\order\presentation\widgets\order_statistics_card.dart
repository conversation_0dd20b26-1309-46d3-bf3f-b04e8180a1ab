import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

import 'package:soko/features/order/presentation/providers/order_management_provider.dart';

/// 订单统计卡片组件
class OrderStatisticsCard extends ConsumerWidget {
  const OrderStatisticsCard({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final statisticsAsync = ref.watch(orderStatisticsProvider);

    return statisticsAsync.when(
      data: (statistics) => _buildStatisticsContent(context, statistics),
      loading: () => _buildLoadingState(),
      error: (error, stackTrace) => const SizedBox.shrink(),
    );
  }

  /// 构建统计内容
  Widget _buildStatisticsContent(BuildContext context, OrderStatistics statistics) {
    return Container(
      margin: EdgeInsets.symmetric(horizontal: 16.w, vertical: 8.h),
      padding: EdgeInsets.all(16.w),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(12.r),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.05),
            blurRadius: 8,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // 标题
          Row(
            children: [
              Icon(
                Icons.analytics,
                size: 20.w,
                color: Theme.of(context).primaryColor,
              ),
              SizedBox(width: 8.w),
              Text(
                '订单统计',
                style: TextStyle(
                  fontSize: 16.sp,
                  fontWeight: FontWeight.w600,
                  color: Colors.grey[800],
                ),
              ),
            ],
          ),
          SizedBox(height: 16.h),
          
          // 今日数据
          Row(
            children: [
              Expanded(
                child: _buildStatItem(
                  '今日订单',
                  statistics.todayOrders.toString(),
                  '单',
                  Colors.blue,
                  Icons.today,
                ),
              ),
              Expanded(
                child: _buildStatItem(
                  '今日金额',
                  statistics.todayAmount.toStringAsFixed(0),
                  '元',
                  Colors.green,
                  Icons.attach_money,
                ),
              ),
            ],
          ),
          SizedBox(height: 16.h),
          
          // 订单状态统计
          Row(
            children: [
              Expanded(
                child: _buildStatItem(
                  '待付款',
                  statistics.pendingOrders.toString(),
                  '单',
                  Colors.orange,
                  Icons.pending_actions,
                ),
              ),
              Expanded(
                child: _buildStatItem(
                  '待发货',
                  statistics.paidOrders.toString(),
                  '单',
                  Colors.purple,
                  Icons.inventory,
                ),
              ),
              Expanded(
                child: _buildStatItem(
                  '已发货',
                  statistics.shippedOrders.toString(),
                  '单',
                  Colors.cyan,
                  Icons.local_shipping,
                ),
              ),
              Expanded(
                child: _buildStatItem(
                  '已完成',
                  statistics.completedOrders.toString(),
                  '单',
                  Colors.green,
                  Icons.check_circle,
                ),
              ),
            ],
          ),
          SizedBox(height: 12.h),
          
          // 总计信息
          Container(
            padding: EdgeInsets.all(12.w),
            decoration: BoxDecoration(
              color: Theme.of(context).primaryColor.withValues(alpha: 0.1),
              borderRadius: BorderRadius.circular(8.r),
            ),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Text(
                  '总订单数：${statistics.totalOrders}',
                  style: TextStyle(
                    fontSize: 14.sp,
                    fontWeight: FontWeight.w500,
                    color: Theme.of(context).primaryColor,
                  ),
                ),
                Text(
                  '总金额：¥${statistics.totalAmount.toStringAsFixed(0)}',
                  style: TextStyle(
                    fontSize: 14.sp,
                    fontWeight: FontWeight.w500,
                    color: Theme.of(context).primaryColor,
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  /// 构建统计项
  Widget _buildStatItem(
    String label,
    String value,
    String unit,
    Color color,
    IconData icon,
  ) {
    return Column(
      children: [
        Container(
          width: 40.w,
          height: 40.w,
          decoration: BoxDecoration(
            color: color.withValues(alpha: 0.1),
            borderRadius: BorderRadius.circular(20.r),
          ),
          child: Icon(
            icon,
            size: 20.w,
            color: color,
          ),
        ),
        SizedBox(height: 8.h),
        Text(
          value,
          style: TextStyle(
            fontSize: 16.sp,
            fontWeight: FontWeight.w600,
            color: color,
          ),
        ),
        SizedBox(height: 2.h),
        Text(
          label,
          style: TextStyle(
            fontSize: 11.sp,
            color: Colors.grey[600],
          ),
          textAlign: TextAlign.center,
        ),
      ],
    );
  }

  /// 构建加载状态
  Widget _buildLoadingState() {
    return Container(
      margin: EdgeInsets.symmetric(horizontal: 16.w, vertical: 8.h),
      padding: EdgeInsets.all(16.w),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(12.r),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.05),
            blurRadius: 8,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        children: [
          Row(
            children: [
              Container(
                width: 20.w,
                height: 20.w,
                decoration: BoxDecoration(
                  color: Colors.grey[300],
                  borderRadius: BorderRadius.circular(4.r),
                ),
              ),
              SizedBox(width: 8.w),
              Container(
                width: 80.w,
                height: 16.h,
                decoration: BoxDecoration(
                  color: Colors.grey[300],
                  borderRadius: BorderRadius.circular(8.r),
                ),
              ),
            ],
          ),
          SizedBox(height: 16.h),
          Row(
            children: List.generate(4, (index) => Expanded(
              child: Padding(
                padding: EdgeInsets.symmetric(horizontal: 8.w),
                child: Column(
                  children: [
                    Container(
                      width: 40.w,
                      height: 40.w,
                      decoration: BoxDecoration(
                        color: Colors.grey[300],
                        borderRadius: BorderRadius.circular(20.r),
                      ),
                    ),
                    SizedBox(height: 8.h),
                    Container(
                      width: 30.w,
                      height: 16.h,
                      decoration: BoxDecoration(
                        color: Colors.grey[300],
                        borderRadius: BorderRadius.circular(8.r),
                      ),
                    ),
                    SizedBox(height: 4.h),
                    Container(
                      width: 40.w,
                      height: 12.h,
                      decoration: BoxDecoration(
                        color: Colors.grey[300],
                        borderRadius: BorderRadius.circular(6.r),
                      ),
                    ),
                  ],
                ),
              ),
            )),
          ),
        ],
      ),
    );
  }
}
