import 'package:dio/dio.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:retrofit/retrofit.dart';

import 'package:soko/core/network/dio_client.dart';
import 'package:soko/features/payment/domain/entities/payment_request.dart';

part 'payment_api_service.g.dart';

/// 支付API服务
@RestApi()
abstract class PaymentApiService {
  factory PaymentApiService(Dio dio, {String baseUrl}) = _PaymentApiService;

  /// 创建支付订单
  @POST('/payment/create')
  Future<PaymentResponse> createPayment(@Body() Map<String, dynamic> paymentData);

  /// 查询支付状态
  @GET('/payment/query/{paymentOrderNo}')
  Future<PaymentResponse> queryPaymentStatus(@Path('paymentOrderNo') String paymentOrderNo);

  /// 取消支付
  @POST('/payment/cancel/{paymentOrderNo}')
  Future<void> cancelPayment(@Path('paymentOrderNo') String paymentOrderNo);

  /// 验证支付回调
  @POST('/payment/verify')
  Future<Map<String, dynamic>> verifyPaymentCallback(@Body() Map<String, dynamic> callbackData);
}

/// 支付API服务提供者
final paymentApiServiceProvider = Provider<PaymentApiService>((ref) {
  final dio = ref.read(dioClientProvider);
  return PaymentApiService(dio);
});
