<template>
  <view class="flex flex-col min-h-screen" style="background-color: #f5f5f5">
    <!-- 头部区域 -->
    <view class="sticky top-0 z-10">
      <!-- 渐变背景 -->
      <view class="header-gradient pb-6 relative overflow-hidden">
        <!-- 添加状态栏占位组件 -->
        <StatusBarPlaceholder bgColor="transparent" />
        <!-- 头部内容 -->
        <view class="relative z-10 px-4">
          <view class="flex items-center justify-between h-11">
            <view class="flex items-center">
              <text class="text-2xl font-bold text-white">购物车</text>
            </view>
            <view class="flex items-center space-x-3">
              <button
                class="flex items-center justify-center bg-white bg-opacity-20 rounded-full w-8 h-8 shadow-light"
              >
                <text class="fas fa-ellipsis-h text-white text-sm"></text>
              </button>
            </view>
          </view>
        </view>

        <!-- 装饰背景元素 -->
        <view class="absolute right-4 -bottom-6 opacity-10">
          <view class="w-24 h-24 rounded-full">
            <text class="fas fa-shopping-cart text-white text-4xl"></text>
          </view>
        </view>

        <!-- 装饰圆形 -->
        <view class="header-circle"></view>
        <view class="header-circle header-circle-2"></view>
      </view>

      <!-- 头部卡片信息 -->
      <view class="header-card-wrapper px-4">
        <view class="header-card flex items-center justify-between">
          <view class="flex items-center">
            <view
              class="w-8 h-8 rounded-full bg-primary bg-opacity-10 flex items-center justify-center mr-3"
            >
              <text class="fas fa-shopping-bag text-primary text-sm"></text>
            </view>
            <view>
              <text class="text-xs text-gray-500">购物车商品</text>
              <view class="flex items-baseline">
                <text class="text-lg font-bold text-[#333333] mr-1">{{
                  cartItems.length
                }}</text>
                <text class="text-xs text-gray-500">件</text>
              </view>
            </view>
          </view>
          <view class="flex items-center justify-end w-1/3">
            <button
              class="rounded-full bg-gradient-to-r from-primary to-neon text-white py-2 px-4 text-sm font-medium shadow-button"
              @tap="goToShop"
            >
              <text class="text-sm font-semibold">继续购物</text>
              <text class="fas fa-chevron-right ml-2 text-xs"></text>
            </button>
          </view>
        </view>
      </view>
    </view>

    <!-- 主要内容区域 -->
    <view class="cart-content py-4 px-0 pb-40">
      <template v-if="cartItems.length > 0">
        <!-- 全选区域 -->
        <view
          class="bg-white rounded-xl px-4 py-3 mx-4 mb-4 shadow-app flex justify-between items-center"
        >
          <view class="flex items-center">
            <up-checkbox
              :checked="allSelected"
              @change="toggleSelectAll"
              shape="circle"
              activeColor="#EF4444"
              label="全选"
              labelSize="14"
              labelColor="#333333"
              iconSize="18"
            ></up-checkbox>
          </view>
          <view class="text-xs text-gray-500"
            >共 {{ cartItems.length }} 件商品</view
          >
        </view>

        <!-- 商品列表 -->
        <view class="space-y-3 px-4">
          <view
            class="relative overflow-hidden rounded-xl"
            v-for="(item, index) in cartItems"
            :key="item.id"
            @touchstart="touchStart($event, index)"
            @touchmove="touchMove($event, index)"
            @touchend="touchEnd($event, index)"
            @tap="handleItemClick(index)"
          >
            <view
              class="bg-white p-4 rounded-xl shadow-gray flex items-start transition-all"
              :style="{ transform: `translateX(${slideOffset[index] || 0}px)` }"
            >
              <view class="flex items-center pr-3">
                <up-checkbox
                  :checked="item.selected"
                  @change="toggleItemSelect(index)"
                  shape="circle"
                  activeColor="#EF4444"
                  iconSize="18"
                  :disabled="isItemInvalid(item) || isSkuOutOfStock(item)"
                ></up-checkbox>
              </view>

              <view
                class="relative w-20 h-20 rounded-lg overflow-hidden flex-shrink-0"
              >
                <CacheImgs
                  :src="item.image"
                  mode="aspectFill"
                  class="w-full h-full"
                  @tap.stop="goToProductDetail(item.productId)"
                ></CacheImgs>

                <!-- 添加商品状态标签 -->
                <view
                  v-if="isProductInvalid(item)"
                  class="absolute top-0 left-0 w-full bg-black bg-opacity-60 text-white text-2xs py-1 text-center"
                  >商品已失效</view
                >
                <view
                  v-else-if="isSkuInvalid(item)"
                  class="absolute top-0 left-0 w-full bg-black bg-opacity-60 text-white text-2xs py-1 text-center"
                  >规格已失效</view
                >
                <view
                  v-else-if="isSkuOutOfStock(item)"
                  class="absolute top-0 left-0 w-full bg-red-500 bg-opacity-70 text-white text-2xs py-1 text-center"
                  >缺货</view
                >
              </view>

              <view
                class="flex-1 ml-3 min-h-[80px] flex flex-col justify-between"
                :class="{
                  'opacity-60': isItemInvalid(item) || isSkuOutOfStock(item)
                }"
              >
                <view>
                  <text
                    class="text-sm font-medium text-[#333333] line-clamp-2 leading-tight cursor-pointer"
                    @tap.stop="goToProductDetail(item.productId)"
                    >{{ item.name }}</text
                  >
                  <!-- 规格信息独占一行 -->
                  <view
                    class="flex items-center mt-2"
                    @tap.stop="openSpecsModal(index)"
                  >
                    <text class="text-2xs text-gray-500 mr-1">{{
                      formatSpecification(item.productSkuId, item.skus)
                    }}</text>
                    <text
                      class="fas fa-chevron-right text-3xs text-gray-400"
                    ></text>
                  </view>

                  <!-- 添加物流信息显示 -->
                  <view
                    v-if="item.isShipped && item.trackingNumber"
                    class="mt-2"
                  >
                    <view class="flex items-center text-2xs text-gray-500">
                      <text class="fas fa-truck mr-1"></text>
                      <text>{{ item.shippingCompany || "未知物流公司" }}</text>
                      <text class="mx-1">|</text>
                      <text>单号：{{ item.trackingNumber }}</text>
                    </view>
                  </view>
                </view>

                <view class="flex justify-between items-center mt-2">
                  <view class="flex flex-col">
                    <view
                      class="text-base font-bold bg-gradient-to-r from-primary to-neon bg-clip-text text-transparent"
                    >
                      ¥{{ item.price.toFixed(2) }}
                    </view>
                    <!-- 删除调试状态信息 -->
                  </view>

                  <view class="flex items-center">
                    <up-number-box
                      v-model="item.quantity"
                      :min="1"
                      :max="getNumberBoxMax(item)"
                      buttonSize="22"
                      bgColor="#f1f5f9"
                      shape="square"
                      @change="handleQuantityChange($event, index)"
                      :disabled="isItemInvalid(item) || isSkuOutOfStock(item)"
                    ></up-number-box>
                  </view>
                </view>
              </view>
            </view>

            <view
              class="absolute top-0 right-0 h-full w-20 bg-primary flex items-center justify-center text-white font-medium rounded-r-xl transition-all duration-300 shadow-delete"
              :style="{
                opacity: slideOffset[index] && slideOffset[index] < 0 ? 1 : 0,
                visibility:
                  slideOffset[index] && slideOffset[index] < 0
                    ? 'visible'
                    : 'hidden',
                transform:
                  slideOffset[index] && slideOffset[index] < 0
                    ? 'translateX(0)'
                    : 'translateX(100%)'
              }"
              @tap.stop="confirmDelete(index)"
            >
              删除
            </view>
          </view>
        </view>
      </template>

      <!-- 空购物车状态 -->
      <template v-else>
        <view
          class="bg-white rounded-xl p-8 shadow-app flex flex-col items-center justify-center mx-4"
        >
          <view
            class="w-32 h-32 rounded-full bg-slate-100 flex items-center justify-center mb-4"
          >
            <van-icon name="cart-o" size="60" color="#666666" />
          </view>
          <text class="text-base font-medium text-[#333333]"
            >您的购物车是空的</text
          >
          <button
            class="mt-6 rounded-full bg-gradient-to-r from-primary to-neon text-white px-6 py-2 text-sm font-medium shadow-button"
            @tap="goToShop"
          >
            去逛逛
          </button>
        </view>
      </template>
    </view>

    <!-- 悬浮结算区域 -->
    <view
      v-if="cartItems.length > 0"
      class="fixed left-0 right-0 z-50 pb-safe floating-checkout"
      style="bottom: 12px"
    >
      <view
        class="mx-4 mb-4 bg-white rounded-xl shadow-app p-4 flex items-center"
      >
        <view class="flex-1">
          <view class="flex items-baseline">
            <text class="text-xs mr-1 text-[#333333] font-medium">合计:</text>
            <text
              class="text-base font-bold bg-gradient-to-r from-primary to-neon bg-clip-text text-transparent"
            >
              ¥{{ totalPrice.toFixed(2) }}
            </text>
          </view>
          <view class="flex text-2xs text-gray-500 mt-1">
            <text class="mr-2">已选{{ selectedCount }}件</text>
          </view>
        </view>

        <view class="flex gap-2">
          <button
            class="rounded-lg bg-white border border-gray-300 text-[#333] font-semibold w-20 py-2.5 flex items-center justify-center text-xs whitespace-nowrap shadow-sm transition-all duration-150 hover:brightness-105 active:brightness-95"
            @tap="showOrderDetail"
            :disabled="selectedCount === 0"
            :class="{ 'opacity-50': selectedCount === 0 }"
          >
            <text class="fas fa-list-ul mr-1 text-xs"></text>查看明细
          </button>
          <button
            class="primary-btn rounded-lg bg-gradient-to-r from-primary to-neon text-white font-medium w-20 py-2.5 flex items-center justify-center text-xs"
            @tap="checkout"
            :disabled="selectedCount === 0"
            :class="{ 'opacity-50': selectedCount === 0 }"
          >
            结&nbsp;&nbsp;&nbsp;算
          </button>
        </view>
      </view>
    </view>

    <!-- 删除确认对话框 -->
    <up-modal
      :show="showDeleteModal"
      title="删除商品"
      @close="showDeleteModal = false"
      :showCancelButton="true"
      :showConfirmButton="true"
      cancelText="取消"
      confirmText="确定"
      confirmColor="#EF4444"
      @confirm="removeCartItem"
    >
      <view class="flex flex-col items-center py-4">
        <view
          class="w-12 h-12 rounded-full bg-red-100 flex items-center justify-center mb-4"
        >
          <text class="fas fa-exclamation-circle text-primary text-lg"></text>
        </view>
        <text class="text-center text-[#333333]">确定要删除这个商品吗？</text>
      </view>
    </up-modal>

    <!-- 订单明细弹出卡片 -->
    <view
      class="order-detail-popup fixed bottom-0 left-0 right-0 z-50 bg-black bg-opacity-50 transition-all duration-300"
      :class="
        showOrderDetailPopup ? 'h-screen' : 'h-0 pointer-events-none opacity-0'
      "
      @tap.stop="closeOrderDetail"
    >
      <view
        class="absolute left-0 right-0 bg-white rounded-t-xl pb-safe transition-transform transform popup-container duration-300"
        :class="showOrderDetailPopup ? 'translate-y-0' : 'translate-y-full'"
        :style="
          showOrderDetailPopup
            ? 'max-height: 75vh; bottom: 0'
            : 'max-height: 0; visibility: hidden; overflow: hidden'
        "
        @tap.stop
      >
        <view class="flex flex-col">
          <!-- 标题和关闭按钮 -->
          <view
            class="flex justify-between items-center px-4 py-3 border-b border-gray-100"
          >
            <text class="font-medium text-base text-[#333333]">订单明细</text>
            <text
              class="fas fa-times text-gray-500 p-2 text-sm"
              @tap="closeOrderDetail"
            ></text>
          </view>

          <!-- 内容区域 -->
          <view class="p-4">
            <!-- 商品列表 -->
            <view
              class="mb-4 max-h-60 overflow-y-auto"
              @touchmove.stop
              @scroll.stop
            >
              <view
                v-for="(item, index) in selectedItems"
                :key="item.id"
                class="relative"
              >
                <view class="flex items-center py-3">
                  <view
                    class="w-10 h-10 rounded-md overflow-hidden flex-shrink-0"
                  >
                    <CacheImgs
                      :src="item.image"
                      mode="aspectFill"
                      class="w-full h-full"
                    ></CacheImgs>
                  </view>
                  <view class="flex-1 ml-3">
                    <text class="text-xs text-[#333333] line-clamp-1">{{
                      item.name
                    }}</text>
                    <view class="flex justify-between mt-1">
                      <text class="text-2xs text-gray-500"
                        >x{{ item.quantity }}</text
                      >
                      <text class="text-xs font-medium text-[#333333]"
                        >¥{{ (item.price * item.quantity).toFixed(2) }}</text
                      >
                    </view>
                  </view>
                </view>
                <view
                  v-if="index < selectedItems.length - 1"
                  class="h-[0.5px] bg-gray-200 w-full opacity-60"
                ></view>
              </view>
            </view>

            <!-- 费用明细 -->
            <view class="rounded-xl bg-gray-50 p-3">
              <view class="fee-item">
                <text>商品总价</text>
                <text>¥{{ totalPrice.toFixed(2) }}</text>
              </view>
              <view class="fee-item">
                <text>国内运费</text>
                <text>¥{{ domesticShipping.toFixed(2) }}</text>
              </view>
              <view class="fee-item">
                <text>国际运费</text>
                <text>¥{{ internationalShipping.toFixed(2) }}</text>
              </view>
              <view
                class="fee-item font-medium border-t border-gray-200 pt-2 mt-2"
              >
                <text>合计</text>
                <text
                  class="text-lg bg-gradient-to-r from-primary to-neon bg-clip-text text-transparent font-bold"
                >
                  ¥{{ grandTotal.toFixed(2) }}
                </text>
              </view>
            </view>
          </view>

          <!-- 底部按钮 -->
          <view class="px-4 py-4 border-t border-gray-100">
            <button
              class="primary-btn rounded-lg bg-gradient-to-r from-primary to-neon text-white font-medium w-auto px-8 py-2 flex items-center justify-center mx-auto text-xs"
              @tap="checkout"
            >
              立即结算
            </button>
          </view>
        </view>
      </view>
    </view>

    <!-- 添加规格选择弹窗 -->
    <up-modal
      :show="showSpecsModal"
      title="修改规格"
      @close="closeSpecsModal"
      :showCancelButton="true"
      :showConfirmButton="true"
      cancelText="取消"
      confirmText="确定"
      confirmColor="#EF4444"
      @confirm="confirmChangeSpecs"
      @cancel="closeSpecsModal"
    >
      <view class="flex flex-col py-4 px-3">
        <!-- 商品信息展示区域 -->
        <view class="mb-6 bg-gray-50 p-3 rounded-xl">
          <view class="flex items-start">
            <view
              class="w-20 h-20 rounded-lg overflow-hidden flex-shrink-0 bg-gray-100"
            >
              <CacheImgs
                :src="currentItem?.image || ''"
                mode="aspectFill"
                class="w-full h-full"
              ></CacheImgs>
            </view>
            <view class="ml-3 flex-1">
              <text class="text-sm font-medium text-[#333333] line-clamp-2">{{
                currentItem?.productName || ""
              }}</text>
              <view v-if="selectedSku" class="mt-2 flex items-baseline">
                <text class="text-lg font-bold text-primary"
                  >¥{{ selectedSku.price.toFixed(2) }}</text
                >
                <text class="text-xs text-gray-400 line-through ml-2"
                  >¥{{ selectedSku.originalPrice.toFixed(2) }}</text
                >
                <view
                  class="ml-2 bg-red-50 text-primary text-2xs rounded-full px-2 py-0.5 flex-shrink-0"
                  v-if="selectedSku.discount"
                >
                  {{ ((1 - selectedSku.discount) * 10).toFixed(1) }}折
                </view>
              </view>
              <view v-if="selectedSku" class="mt-1.5 text-2xs text-gray-500">
                库存: {{ selectedSku.stock }} 件
                <text v-if="selectedSku.stock <= 0" class="text-red-500 ml-1"
                  >缺货</text
                >
              </view>
            </view>
          </view>
        </view>

        <!-- 规格选择区域 -->
        <template v-if="currentItem && specOptions.length > 0">
          <view
            v-for="(specGroup, groupIndex) in specOptions"
            :key="groupIndex"
            class="mb-5"
          >
            <view class="flex items-center mb-2">
              <view class="w-1 h-4 bg-primary rounded-full mr-2"></view>
              <text class="text-sm font-medium text-[#333333]">{{
                specGroup.name
              }}</text>
            </view>
            <view class="flex flex-wrap gap-2">
              <view
                v-for="(option, optionIndex) in specGroup.options"
                :key="optionIndex"
                class="px-4 py-2 rounded-full text-xs border transition-all duration-200"
                :class="{
                  'border-primary text-primary bg-primary bg-opacity-5 shadow-sm':
                    selectedSpecs[specGroup.name] === option &&
                    !isSkuOptionInvalid(specGroup.name, option),
                  'border-gray-200 text-gray-600':
                    selectedSpecs[specGroup.name] !== option &&
                    !isSkuOptionInvalid(specGroup.name, option),
                  'border-gray-200 text-gray-400 bg-gray-100 opacity-50':
                    isSkuOptionInvalid(specGroup.name, option)
                }"
                @tap="selectSpec(specGroup.name, option)"
              >
                {{ option }}
              </view>
            </view>
          </view>
        </template>
        <view
          v-else-if="isProductInvalid(currentItem)"
          class="py-10 text-center"
        >
          <view
            class="w-16 h-16 mx-auto rounded-full bg-gray-100 flex items-center justify-center mb-3"
          >
            <text
              class="fas fa-exclamation-circle text-gray-400 text-lg"
            ></text>
          </view>
          <text class="text-gray-500">商品已失效</text>
        </view>
        <view v-else class="py-10 text-center">
          <view
            class="w-16 h-16 mx-auto rounded-full bg-gray-100 flex items-center justify-center mb-3"
          >
            <text
              class="fas fa-exclamation-circle text-gray-400 text-lg"
            ></text>
          </view>
          <text class="text-gray-500">暂无可选规格</text>
        </view>
      </view>
    </up-modal>
  </view>
</template>

<script setup lang="ts">
// @ts-nocheck  // 忽略整个文件的类型检查
import CacheImgs from "@/components/CacheImgs.vue"
import { ref, computed, onBeforeMount, reactive } from "vue"
import {
  getCartList,
  updateCartQuantity,
  deleteCartItem,
  batchDeleteCartItems,
  clearCart,
  createOrder,
  getAddressList
} from "@/api/api"
import { useStore } from "vuex"
import StatusBarPlaceholder from "@/components/StatusBarPlaceholder.vue"
import { onLoad, onShow } from "@dcloudio/uni-app"

// 获取store
const store = useStore()

onShow(() => {
  loadCartData()
})

// 检查登录状态并处理未登录情况
const checkLogin = () => {
  const isLogin = store.getters.isLoggedIn
  if (!isLogin) {
    uni.showToast({
      title: "请先登录",
      icon: "none"
    })
    // 跳转到登录页面
    setTimeout(() => {
      uni.navigateTo({
        url: "/pages/views/login/login"
      })
    }, 1500)
    return false
  }
  return true
}

// 处理管理购物车
const handleManageCart = () => {
  uni.showActionSheet({
    itemList: ["清空购物车", "移除选中商品"],
    success: (res) => {
      if (res.tapIndex === 0) {
        // 清空购物车
        confirmClearCart()
      } else if (res.tapIndex === 1) {
        // 删除选中商品
        removeSelectedItems()
      }
    }
  })
}

// 确认清空购物车
const confirmClearCart = () => {
  uni.showModal({
    title: "清空购物车",
    content: "确定要清空购物车吗？此操作不可恢复",
    confirmColor: "#EF4444",
    success: (res) => {
      if (res.confirm) {
        handleClearCart()
      }
    }
  })
}

// 清空购物车
const handleClearCart = () => {
  if (cartItems.value.length === 0) {
    uni.showToast({
      title: "购物车已经是空的",
      icon: "none"
    })
    return
  }

  uni.showLoading({
    title: "正在清空..."
  })

  clearCart()
    .then((res) => {
      uni.hideLoading()
      uni.showToast({
        title: "购物车已清空",
        icon: "success"
      })
      cartItems.value = []
    })
    .catch((err) => {
      uni.hideLoading()
      uni.showToast({
        title: "操作失败，请重试",
        icon: "none"
      })
    })
}

// 移除选中的商品
const removeSelectedItems = () => {
  const selectedIds = cartItems.value
    .filter((item) => item.selected)
    .map((item) => item.id)

  if (selectedIds.length === 0) {
    uni.showToast({
      title: "请先选择商品",
      icon: "none"
    })
    return
  }

  uni.showLoading({
    title: "正在删除..."
  })

  batchDeleteCartItems(selectedIds)
    .then((res) => {
      // 从本地数组中删除选中的商品
      cartItems.value = cartItems.value.filter((item) => !item.selected)

      uni.hideLoading()
      uni.showToast({
        title: "删除成功",
        icon: "success"
      })
    })
    .catch((err) => {
      uni.hideLoading()
      uni.showToast({
        title: "删除失败，请重试",
        icon: "none"
      })
    })
}

// 购物车商品接口定义
interface CartItem {
  id: string
  name: string
  price: number
  quantity: number
  image: string
  productId: string
  productSkuId: string
  productName: string
  productPrice: string
  userId: string
  createTime: number
  selected: boolean // 前端添加的字段，用于选择状态
  skus: any[]
  version?: number
  // 添加物流相关字段
  trackingNumber?: string
  shippingCompany?: string
  isShipped?: boolean
}

// 购物车商品列表
const cartItems = ref<CartItem[]>([])

// 是否加载中
const loading = ref(false)
// 删除确认对话框
const showDeleteModal = ref(false)
const itemToDelete = ref<number | null>(null)

// 跳转到商店页面
const goToShop = () => {
  uni.switchTab({
    url: "/pages/views/shop/shop"
  })
}

// 切换选中状态 (单个)
const toggleItemSelect = (index: number) => {
  const item = cartItems.value[index]
  // 只允许选择有效的商品
  if (!isItemInvalid(item)) {
    item.selected = !item.selected
  } else if (item.selected) {
    // 如果商品已失效但之前是选中状态，则取消选中
    item.selected = false
  }
}

// 切换全选
const toggleSelectAll = (checked: boolean) => {
  // 只选择有效的商品
  cartItems.value.forEach((item) => {
    // 只有商品有效且库存充足时才允许选中
    if (!isItemInvalid(item) && !isSkuOutOfStock(item)) {
      item.selected = checked
    } else {
      // 失效商品始终不选中
      item.selected = false
    }
  })
}

// 处理数量变化 (步进器回调)
const handleQuantityChange = (event: { value: number }, index: number) => {
  // event.value 是变化后的值
  const item = cartItems.value[index]

  // 如果商品无效或缺货，不允许修改数量
  if (isItemInvalid(item) || isSkuOutOfStock(item)) {
    // 恢复到之前的数量
    item.quantity = item.quantity
    return
  }

  // 强制最大
  let newVal = event.value
  console.log(getIsMerChant.value, "222")
  if (!getIsMerChant.value && newVal > 2) {
    newVal = 2
    uni.showToast({
      title: "每个SKU单次限购2件",
      icon: "none"
    })
  }
  if (newVal < 1) newVal = 1
  item.quantity = newVal

  // 调用API更新购物车数量
  updateCartQuantity({
    id: item.id,
    quantity: item.quantity,
    version: item.version || 0
  })
    .then((res) => {
      // 更新version
      if (res.data && res.data.version) {
        item.version = res.data.version
      }
    })
    .catch((err) => {
      uni.showToast({
        title: "更新数量失败",
        icon: "none"
      })
    })
}

const getIsMerChant = computed(() => {
  return store.state.$userInfo.identity === "MERCHANT"
})

// 是否全部选中
const allSelected = computed(() => {
  if (cartItems.value.length === 0) return false
  // 只检查有效商品是否全部选中
  return cartItems.value
    .filter((item) => !isItemInvalid(item) && !isSkuOutOfStock(item))
    .every((item) => item.selected)
})

// 选中的商品数量（只计算有效且有库存的商品）
const selectedCount = computed(() => {
  return cartItems.value.filter(
    (item) => item.selected && !isItemInvalid(item) && !isSkuOutOfStock(item)
  ).length
})

// 选中商品的总价（只计算有效且有库存的商品）
const totalPrice = computed(() => {
  return cartItems.value
    .filter(
      (item) => item.selected && !isItemInvalid(item) && !isSkuOutOfStock(item)
    )
    .reduce((sum, item) => {
      const price =
        typeof item.price === "number"
          ? item.price
          : parseFloat(item.productPrice)
      return sum + price * item.quantity
    }, 0)
})

const getNumberBoxMax = (item) => {
  const sku = item.skus.find((s) => s.id === item.productSkuId)
  console.log(getIsMerChant.value, "111")
  return getIsMerChant.value && sku ? sku.stock : 2
}

// 结算
const checkout = () => {
  if (selectedCount.value === 0) {
    uni.showToast({
      title: "请选择要结算的商品",
      icon: "none"
    })
    return
  }

  // 只获取有效的选中商品
  const selectedItems = cartItems.value.filter(
    (item) => item.selected && !isItemInvalid(item) && !isSkuOutOfStock(item)
  )

  // 跳转到确认订单页面
  uni.navigateTo({
    url: "/pages/views/order/confirm",
    success: (res) => {
      // 通过eventChannel向确认订单页面传递选中的商品
      res.eventChannel.emit("selectedItems", selectedItems)
    }
  })
}

// 计算国内运费
const calculateDomesticFreight = (items) => {
  // 没有选中商品时不收取运费
  if (items.length === 0) return 0

  // 按商品ID分组，统计每种商品的数量
  const productGroups = {}

  items.forEach((item) => {
    // 使用productId作为分组依据
    const productId = item.productId
    if (!productGroups[productId]) {
      productGroups[productId] = 0
    }
    productGroups[productId] += item.quantity
  })

  // 计算总运费：每种商品第一件10元，之后每件+5元
  let totalShipping = 0
  Object.values(productGroups).forEach((quantity) => {
    // 基础运费10元
    let productShipping = 10
    // 超过1件的部分，每件+5元
    if (quantity > 1) {
      productShipping += (quantity - 1) * 5
    }
    totalShipping += productShipping
  })

  return totalShipping
}

// 计算国际运费
const calculateInternationalFreight = (items) => {
  // 这里可以添加真实的国际运费计算逻辑
  return items.length > 0 ? 20.0 : 0
}

// 加载购物车数据
const loadCartData = () => {
  loading.value = true
  getCartList()
    .then((res) => {
      let cartData = []

      // 处理各种可能的返回格式
      if (res.data) {
        // 情况1: res.data是数组
        if (Array.isArray(res.data)) {
          cartData = res.data
        }
        // 情况2: res.data.records是数组 (分页格式)
        else if (res.data.records && Array.isArray(res.data.records)) {
          cartData = res.data.records
        }

        if (cartData.length > 0) {
          // 转换后端数据格式为前端所需格式
          cartItems.value = cartData.map((item) => ({
            ...item,
            // 初始状态为未选中
            selected: false,
            // 如果价格是字符串，尝试转换为数字
            price:
              typeof item.productPrice === "string"
                ? parseFloat(item.productPrice)
                : item.productPrice,
            // 使用productName作为展示名称
            name: item.productName || item.name
          }))
        } else {
          cartItems.value = []
        }
      } else {
        cartItems.value = []
      }
    })
    .catch((err) => {
      uni.showToast({
        title: "获取购物车数据失败",
        icon: "none"
      })
    })
    .finally(() => {
      loading.value = false
    })
}

// 查看订单明细
const showOrderDetail = () => {
  if (selectedCount.value === 0) {
    uni.showToast({
      title: "请先选择商品",
      icon: "none"
    })
    return
  }

  // 显示订单明细弹窗
  showOrderDetailPopup.value = true
  // 添加body类以防止滚动穿透
  document.body.classList.add("popup-open")
}

// 关闭订单明细
const closeOrderDetail = () => {
  showOrderDetailPopup.value = false
  // 移除body类
  document.body.classList.remove("popup-open")
}

// 获取选中的商品项
const selectedItems = computed(() => {
  return cartItems.value.filter((item) => item.selected)
})

// 运费计算（示例）
const domesticShipping = computed(() => {
  // 没有选中商品时不收取运费
  if (selectedCount.value === 0) return 0

  // 按商品ID分组，统计每种商品的数量
  const productGroups: Record<string, number> = {}

  selectedItems.value.forEach((item) => {
    // 使用productId作为分组依据
    const productId = item.productId
    if (!productGroups[productId]) {
      productGroups[productId] = 0
    }
    productGroups[productId] += item.quantity
  })

  // 计算总运费：每种商品第一件10元，之后每件+5元
  let totalShipping = 0
  Object.values(productGroups).forEach((quantity) => {
    // 基础运费10元
    let productShipping = 10
    // 超过1件的部分，每件+5元
    if (quantity > 1) {
      productShipping += (quantity - 1) * 5
    }
    totalShipping += productShipping
  })

  return totalShipping
})

const internationalShipping = computed(() => {
  // 这里可以添加真实的国际运费计算逻辑
  return selectedCount.value > 0 ? 20.0 : 0
})

// 总计金额
const grandTotal = computed(() => {
  return totalPrice.value + domesticShipping.value + internationalShipping.value
})

// 滑动相关变量
const slideOffset = ref<Record<number, number>>({})
const startX = ref<Record<number, number>>({})
const currentIndex = ref<number | null>(null)
const deleteButtonWidth = 80 // 删除按钮宽度

// 触摸开始
const touchStart = (event: TouchEvent, index: number) => {
  startX.value[index] = event.touches[0].clientX
  currentIndex.value = index
}

// 触摸移动
const touchMove = (event: TouchEvent, index: number) => {
  if (currentIndex.value !== index) return

  const moveX = event.touches[0].clientX
  const offsetX = moveX - startX.value[index]

  // 允许向左滑动显示删除按钮，也允许向右滑动恢复原状
  if (offsetX < 0) {
    // 向左滑动，最大不超过删除按钮宽度
    const newOffset = Math.max(-deleteButtonWidth, offsetX)
    slideOffset.value[index] = newOffset
  } else if (slideOffset.value[index] && slideOffset.value[index] < 0) {
    // 向右滑动，但只有当已经左滑显示了删除按钮时才生效
    // 确保不会右滑超过初始位置
    const newOffset = Math.min(0, slideOffset.value[index] + offsetX)
    slideOffset.value[index] = newOffset

    // 更新起始位置，避免连续滑动时位置跳跃
    if (newOffset === 0) {
      startX.value[index] = moveX
    }
  }
}

// 触摸结束
const touchEnd = (event: TouchEvent, index: number) => {
  if (currentIndex.value !== index) return

  // 如果滑动超过一半宽度，则完全显示删除按钮，否则恢复原状
  const offset = slideOffset.value[index] || 0
  if (offset < -deleteButtonWidth / 2) {
    slideOffset.value[index] = -deleteButtonWidth
  } else {
    slideOffset.value[index] = 0
  }

  currentIndex.value = null
}

// 点击商品项时关闭所有打开的滑动项
const closeAllSlides = (exceptIndex?: number) => {
  Object.keys(slideOffset.value).forEach((key) => {
    const idx = Number(key)
    if (exceptIndex === undefined || idx !== exceptIndex) {
      slideOffset.value[idx] = 0
    }
  })
}

// 商品项点击处理
const handleItemClick = (index: number) => {
  // 如果当前项已滑动显示删除按钮，则点击时关闭它
  if (slideOffset.value[index] && slideOffset.value[index] < 0) {
    slideOffset.value[index] = 0
  } else {
    // 否则关闭其他所有已打开的项
    closeAllSlides()
  }
}

// 确认删除对话框
const confirmDelete = (index: number) => {
  itemToDelete.value = index
  showDeleteModal.value = true
}

// 删除购物车商品
const removeCartItem = () => {
  if (itemToDelete.value === null) return

  const itemIndex = itemToDelete.value
  const item = cartItems.value[itemIndex]

  uni.showLoading({
    title: "删除中..."
  })

  deleteCartItem(item.id)
    .then((res) => {
      // 从本地数组中删除
      cartItems.value.splice(itemIndex, 1)

      // 重置滑动状态
      if (itemIndex in slideOffset.value) {
        delete slideOffset.value[itemIndex]
      }

      uni.hideLoading()
      uni.showToast({
        title: "删除成功",
        icon: "success"
      })
    })
    .catch((err) => {
      uni.hideLoading()
      uni.showToast({
        title: "删除失败，请重试",
        icon: "none"
      })
    })
    .finally(() => {
      // 关闭对话框
      showDeleteModal.value = false
      itemToDelete.value = null
    })
}

// 订单明细弹窗状态
const showOrderDetailPopup = ref(false)

// 规格选择弹窗相关
const showSpecsModal = ref(false)
const currentItemIndex = ref<number | null>(null)
const currentItem = ref<CartItem | null>(null)
const selectedSpecs = reactive<Record<string, string>>({})
const selectedSku = ref<any>(null)

// 格式化规格
const formatSpecification = (skuId: string, skus: any[] | undefined) => {
  if (!skus || skus.length === 0) return "默认规格"

  const sku = skus.find((s) => s.id === skuId)
  if (!sku) return "默认规格"

  try {
    const spec = JSON.parse(sku.specification)
    const specStr = Object.entries(spec)
      .map(([key, value]) => `${key}:${value}`)
      .join(" ")
    return specStr || "默认规格"
  } catch (error) {
    return "默认规格"
  }
}

// 打开规格选择弹窗
const openSpecsModal = (index: number) => {
  currentItemIndex.value = index
  currentItem.value = cartItems.value[index]

  // 判断商品是否完全失效
  if (isProductInvalid(currentItem.value)) {
    uni.showToast({
      title: "该商品已失效",
      icon: "none"
    })
    return
  }

  // 解析当前选中的规格
  const currentSkuId = currentItem.value.productSkuId
  const skus = currentItem.value.skus || []

  if (skus.length === 0) {
    uni.showToast({
      title: "该商品无可选规格",
      icon: "none"
    })
    return
  }

  // 找到当前选中的规格
  const currentSku = skus.find((s) => s.id === currentSkuId)
  selectedSku.value = currentSku

  if (currentSku) {
    try {
      const specObj = JSON.parse(currentSku.specification)
      // 重置选择的规格
      Object.keys(selectedSpecs).forEach((key) => delete selectedSpecs[key])

      // 设置当前选中的规格
      Object.entries(specObj).forEach(([key, value]) => {
        selectedSpecs[key] = value as string
      })
    } catch (error) {
      console.error("解析规格错误", error)
    }
  }

  showSpecsModal.value = true
}

// 关闭规格选择弹窗
const closeSpecsModal = () => {
  showSpecsModal.value = false
  currentItemIndex.value = null
  currentItem.value = null
  selectedSku.value = null
  // 清空已选规格
  Object.keys(selectedSpecs).forEach((key) => delete selectedSpecs[key])
}

// 计算规格选项
const specOptions = computed(() => {
  if (
    !currentItem.value ||
    !currentItem.value.skus ||
    currentItem.value.skus.length === 0
  ) {
    return []
  }

  const skus = currentItem.value.skus
  const specGroups: Record<string, Set<string>> = {}

  // 提取所有可能的规格选项
  skus.forEach((sku) => {
    try {
      const spec = JSON.parse(sku.specification)
      Object.entries(spec).forEach(([key, value]) => {
        if (!specGroups[key]) {
          specGroups[key] = new Set()
        }
        specGroups[key].add(value as string)
      })
    } catch (error) {
      console.error("解析规格错误", error)
    }
  })

  // 转换为数组格式便于渲染
  return Object.entries(specGroups).map(([name, optionsSet]) => ({
    name,
    options: Array.from(optionsSet)
  }))
})

// 选择规格
const selectSpec = (specName: string, value: string) => {
  selectedSpecs[specName] = value

  // 根据已选规格更新当前选中的sku
  if (!currentItem.value || !currentItem.value.skus) return

  // 检查选中的规格组合是否匹配某个sku
  const matchedSku = findMatchingSku()
  if (matchedSku) {
    selectedSku.value = matchedSku
  }
}

// 查找匹配的SKU
const findMatchingSku = () => {
  if (!currentItem.value || !currentItem.value.skus) return null

  return currentItem.value.skus.find((sku) => {
    try {
      const skuSpec = JSON.parse(sku.specification)
      // 检查所有已选规格是否与该SKU匹配
      return (
        Object.entries(selectedSpecs).every(
          ([key, value]) => skuSpec[key] === value
        ) && Object.keys(skuSpec).length === Object.keys(selectedSpecs).length
      )
    } catch (error) {
      return false
    }
  })
}

// 确认修改规格
const confirmChangeSpecs = () => {
  if (currentItemIndex.value === null || !currentItem.value) return

  const matchedSku = findMatchingSku()
  if (!matchedSku) {
    uni.showToast({
      title: "请选择完整规格",
      icon: "none"
    })
    return
  }

  // 检查选择的SKU是否有效
  if (matchedSku.status !== "U") {
    uni.showToast({
      title: "所选规格已失效",
      icon: "none"
    })
    return
  }

  // 如果规格没有变化，则直接关闭弹窗
  if (matchedSku.id === currentItem.value.productSkuId) {
    closeSpecsModal()
    return
  }

  uni.showLoading({
    title: "更新中..."
  })

  // 调用API更新购物车规格 - 使用通用的cart更新接口
  updateCartQuantity({
    id: currentItem.value.id,
    productSkuId: matchedSku.id,
    quantity: currentItem.value.quantity, // 保持原数量不变
    version: currentItem.value.version || 0 // 添加version参数
  })
    .then((res) => {
      // 更新本地购物车数据
      const item = cartItems.value[currentItemIndex.value as number]
      item.productSkuId = matchedSku.id
      item.productPrice = matchedSku.price
      item.price = matchedSku.price
      // 更新version
      if (res.data && res.data.version) {
        item.version = res.data.version
      }

      uni.hideLoading()
      uni.showToast({
        title: "修改成功",
        icon: "success"
      })
      closeSpecsModal()

      // 重新加载购物车数据以刷新页面
      loadCartData()
    })
    .catch((err) => {
      uni.hideLoading()
      uni.showToast({
        title: "修改失败，请重试",
        icon: "none"
      })
    })
}

// 跳转到商品详情页
const goToProductDetail = (productId: string) => {
  if (!productId) {
    uni.showToast({
      title: "商品ID不存在",
      icon: "none"
    })
    return
  }

  uni.navigateTo({
    url: `/pages/views/product/detail?id=${productId}`
  })
}

// 判断商品是否完全失效（所有SKU都下架或无数据）
const isProductInvalid = (item: CartItem) => {
  // 防止item为undefined或null
  if (!item) return false

  // 检查skus是否有效
  const hasValidSkus =
    item.skus && Array.isArray(item.skus) && item.skus.length > 0
  if (!hasValidSkus) return true

  // 检查是否至少有一个SKU有效(status === "U")
  const hasActiveSkus = item.skus.some((sku) => sku && sku.status === "U")
  return !hasActiveSkus
}

// 判断当前SKU是否失效（当前SKU下架但有其他有效SKU）
const isSkuInvalid = (item: CartItem) => {
  // 防止item为undefined或null
  if (!item) return false

  // 如果整个商品都是无效的，则跳过单个SKU的判断
  if (isProductInvalid(item)) return false

  // 检查当前SKU的有效性
  const currentSku = item.skus?.find(
    (sku) => sku && sku.id === item.productSkuId
  )
  const isCurrentSkuActive = currentSku && currentSku.status === "U"

  // 如果当前SKU有效，则不是失效状态
  if (isCurrentSkuActive) return false

  // 检查是否存在其他有效SKU
  const hasOtherActiveSkus = item.skus?.some(
    (sku) => sku && sku.status === "U" && sku.id !== item.productSkuId
  )

  // 当前SKU无效但存在其他有效SKU，才显示"规格已失效"
  return hasOtherActiveSkus
}

// 判断当前SKU是否缺货（库存为0）
const isSkuOutOfStock = (item: CartItem) => {
  // 防止item为undefined或null
  if (!item) return false

  // 已经判断为商品或SKU无效的情况，不显示缺货
  if (isItemInvalid(item)) return false

  // 检查当前SKU的库存
  const currentSku = item.skus?.find(
    (sku) => sku && sku.id === item.productSkuId
  )
  return (
    currentSku &&
    currentSku.status === "U" &&
    typeof currentSku.stock === "number" &&
    currentSku.stock <= 0
  )
}

// 判断商品是否无效或缺货（用于禁用选择框和数量调整器）
const isItemInvalid = (item: CartItem) => {
  return isProductInvalid(item) || isSkuInvalid(item)
}

// 判断规格选项是否无效（选择此规格后会导致无效的SKU）
const isSkuOptionInvalid = (specName: string, optionValue: string) => {
  if (
    !currentItem.value ||
    !currentItem.value.skus ||
    currentItem.value.skus.length === 0
  ) {
    return true
  }

  // 获取选择了此规格后可能的SKU
  const possibleSkus = currentItem.value.skus.filter((sku) => {
    try {
      const spec = JSON.parse(sku.specification)
      return spec[specName] === optionValue
    } catch (error) {
      return false
    }
  })

  // 检查这些SKU是否都是无效的
  return (
    possibleSkus.length === 0 || possibleSkus.every((sku) => sku.status !== "U")
  )
}
</script>

<style scoped lang="scss">
.header-gradient {
  background: linear-gradient(to right, #ef4444, #ec4899);
  box-shadow: 0 4px 12px rgba(239, 68, 68, 0.2);
  position: relative;
}

.header-circle {
  position: absolute;
  width: 120px;
  height: 120px;
  border-radius: 60px;
  background: rgba(255, 255, 255, 0.1);
  top: -30px;
  left: -20px;
}

.header-circle-2 {
  width: 80px;
  height: 80px;
  bottom: -20px;
  left: 40%;
  top: auto;
}

.header-card-wrapper {
  margin-top: 15px;
  position: relative;
  z-index: 20;
}

.header-card {
  background: white;
  border-radius: 12px;
  padding: 12px 16px;
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.05);
}

.shadow-light {
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.shadow-app {
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.06);
}

.shadow-card {
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.06), 0 4px 12px rgba(0, 0, 0, 0.04);
}

.shadow-delete {
  box-shadow: -2px 0 8px rgba(239, 68, 68, 0.15);
}

.shadow-hero {
  box-shadow: 0 4px 16px rgba(245, 158, 11, 0.1),
    0 8px 24px rgba(239, 68, 68, 0.06);
}

.shadow-button {
  box-shadow: 0 4px 10px rgba(239, 68, 68, 0.25);
}

.space-y-3 > view:not(:first-child) {
  margin-top: 0.75rem;
}

.gap-3 > :not(:first-child) {
  margin-left: 0.75rem;
}

.checkout-btn {
  transition: transform 0.2s ease;
}

.checkout-btn:active {
  transform: scale(0.98);
}

.pb-safe {
  padding-bottom: env(safe-area-inset-bottom, 0);
}

.floating-checkout {
  bottom: 10%;
}

.outline-btn {
  position: relative;
  overflow: hidden;
  transition: all 0.2s ease;
  outline: 2px solid #e3e9f1;
  outline-offset: -2px;
}

.outline-btn:active {
  transform: scale(0.97);
  background-color: rgba(239, 68, 68, 0.05);
}

.primary-btn {
  position: relative;
  overflow: hidden;
  transition: all 0.2s ease;
  box-shadow: 0 4px 10px rgba(239, 68, 68, 0.25);
}

.primary-btn:active {
  transform: scale(0.97);
  box-shadow: 0 2px 5px rgba(239, 68, 68, 0.2);
}

.fee-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 0.5rem;
  font-size: 0.875rem;
  color: #666666;

  &:last-child {
    margin-bottom: 0;
  }
}

.order-detail-popup {
  -webkit-tap-highlight-color: transparent;
}

.popup-container {
  box-shadow: 0 -4px 12px rgba(0, 0, 0, 0.1);
  overflow-y: auto;
  -webkit-overflow-scrolling: touch;
}

body.popup-open {
  overflow: hidden;
  position: fixed;
  width: 100%;
}

/* 规格选择按钮样式 */
.spec-option {
  transition: all 0.2s ease;

  &.active {
    background-color: rgba(239, 68, 68, 0.05);
    border-color: #ef4444;
    color: #ef4444;
  }
}
</style>
