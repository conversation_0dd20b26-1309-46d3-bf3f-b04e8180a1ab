<template>
  <view class="min-h-screen pb-[50px] bg-gray-50">
    <StatusBarPlaceholder bgColor="#f9fafb" />
    <view class="p-4">
      <!-- 用户信息区域 - 登录状态 -->
      <view
        class="relative flex flex-col items-center justify-center p-8 mb-4 rounded-2xl shadow-md bg-white border border-gray-100 overflow-hidden"
        v-if="isLoggedIn"
      >
        <!-- 装饰圆（放在最下层） -->
        <view
          class="absolute -bottom-4 -right-4 w-24 h-24 rounded-full bg-primary/10 backdrop-blur-sm z-0"
        ></view>
        <view
          class="absolute -bottom-8 -right-8 w-16 h-16 rounded-full bg-secondary/10 backdrop-blur-sm z-0"
        ></view>
        <view class="flex items-center w-full z-10">
          <up-avatar
            :src="userAvatar || '/static/logo-write.png'"
            size="70"
          ></up-avatar>
          <view class="ml-4 flex flex-col justify-center">
            <view class="text-xl font-semibold text-gray-900">{{
              userName
            }}</view>
          </view>
        </view>
        <!-- 会员卡片 start -->
        <view
          class="w-full mt-4 mb-[-12px] p-4 rounded-xl bg-gray-50 border border-gray-100 shadow-lg flex flex-col z-10 relative custom-vip-shadow"
        >
          <view class="flex items-center justify-between mb-2">
            <view class="flex items-center">
              <van-icon
                name="diamond-o"
                size="18"
                color="#333333"
                class="mr-2"
              />
              <text class="text-base font-semibold text-gray-900">{{
                vip
              }}</text>
              <view
                v-if="vip"
                class="ml-2 px-2 py-0 rounded-full bg-primary flex items-center justify-center"
                style="display: inline-block"
              >
                <text class="text-xs text-white font-semibold">
                  {{
                    (!memberInfo || !memberInfo.memberType)
                      ? 'NORMAL'
                      : memberInfo.memberType === 'VIP'
                      ? 'PRO'
                      : memberInfo.memberType === 'SVIP'
                      ? 'Premium'
                      : memberInfo.memberType === 'NORMAL'
                      ? 'NORMAL'
                      : ''
                  }}
                </text>
              </view>
            </view>
            <van-icon name="arrow" size="16" color="#A3A3A3" @click="goToMemberCenter" />
          </view>
          <view class="flex items-center justify-between text-xs text-gray-900">
            <text class="text-gray-900">有效期至</text>
            <text class="text-gray-900">{{ userExpireDate || '--' }}</text>
          </view>
          <view
            class="flex items-center justify-between text-xs text-gray-900 mt-2"
          >
            <text class="text-gray-900">{{ userPhone || '未绑定手机号' }}</text>
          </view>
        </view>
        <!-- 会员卡片 end -->
        <view class="absolute top-3 right-3 p-2 z-20" @click="openSettings">
          <van-icon name="setting-o" size="20" color="#A3A3A3"></van-icon>
        </view>
      </view>

      <!-- 用户信息区域 - 未登录状态 -->
      <view
        class="flex flex-row justify-between items-center p-5 mb-4 rounded-2xl shadow-md border border-gray-100 bg-white cursor-pointer"
        v-else
        @click="goToLogin"
      >
        <view class="flex flex-row items-center">
          <up-avatar src="/static/logo-color.png" size="60"></up-avatar>
          <view class="ml-4">
            <view
              class="text-lg font-semibold text-gray-900 px-4 py-2 rounded-lg"
              >点击登录</view
            >
            <view class="text-xs text-gray-500 mt-1">登录后享受更多权益</view>
          </view>
        </view>
        <van-icon name="arrow" size="16" color="#A3A3A3" />
      </view>

      <!-- 我的订单区域 -->
      <view
        class="p-4 mb-4 rounded-2xl shadow-md border border-gray-100 bg-white"
        v-if="isLoggedIn"
      >
        <view class="flex justify-between items-center w-full mb-4">
          <view class="text-base font-semibold text-gray-900">我的订单</view>
          <view class="flex items-center" @click="navigateToAllOrders">
            <text class="text-sm text-gray-500 mr-1">全部订单</text>
            <van-icon name="arrow" size="12" color="#A3A3A3" />
          </view>
        </view>
        <view class="flex justify-between">
          <view
            class="flex flex-col items-center flex-1"
            v-for="(item, index) in orderItems"
            :key="index"
            @click="navigateToOrdersByStatus(item.status)"
          >
            <view
              class="w-11 h-11 rounded-full bg-primary-50 border border-primary-100 flex items-center justify-center mb-2"
            >
              <van-icon :name="item.icon" size="28" color="#EF4444" />
            </view>
            <text class="text-sm text-gray-900">{{ item.text }}</text>
          </view>
        </view>
      </view>

      <!-- 服务中心区域 -->
      <view
        class="p-4 mb-4 rounded-2xl shadow-md border border-gray-100 bg-white"
      >
        <view class="text-base font-semibold text-gray-900 mb-4">服务中心</view>
        <view class="space-y-3">
          <view
            class="flex justify-between items-center py-3"
            v-for="(item, index) in dynamicServiceItems"
            :key="index"
            @click="handleServiceClick(item.url)"
          >
            <view class="flex items-center">
              <van-icon
                :name="item.icon"
                size="18"
                color="#A3A3A3"
                class="mr-3"
              />
              <text class="text-base text-gray-900">{{ item.text }}</text>
            </view>
            <van-icon name="arrow" size="16" color="#A3A3A3" />
          </view>
        </view>
      </view>
    </view>

    <!-- 设置弹窗 -->
    <up-popup
      :show="showSettingsPopup"
      @close="showSettingsPopup = false"
      mode="bottom"
      round="16"
    >
      <view
        class="bg-white rounded-t-2xl overflow-hidden border-t border-gray-100"
      >
        <view
          class="text-base font-semibold py-4 text-center border-b border-gray-100 text-gray-900"
          >设置</view
        >
        <view class="max-h-[60vh] overflow-y-auto">
          <view
            class="flex justify-between items-center py-4 px-5 border-b border-gray-100"
            v-for="(option, index) in settingOptions"
            :key="index"
            @click="handleSettingOption(option)"
          >
            <view class="flex items-center">
              <van-icon
                :name="option.icon"
                size="18"
                color="#A3A3A3"
                class="mr-3"
              />
              <text class="text-base text-gray-900">{{ option.name }}</text>
            </view>
            <van-icon name="arrow" size="14" color="#A3A3A3" />
          </view>
        </view>
        <view
          class="text-base text-gray-900 py-4 text-center bg-gray-50 border-t border-gray-100"
          @click="showSettingsPopup = false"
          >取消</view
        >
      </view>
    </up-popup>
  </view>
</template>

<script setup lang="ts">
import { ref, computed } from 'vue'
import { useStore } from 'vuex'
import { onShow } from '@dcloudio/uni-app'
import { getUserInfo } from '@/api/api'
import { VIPSTATUS } from '@/enum'
import store from '@/store'
import StatusBarPlaceholder from '@/components/StatusBarPlaceholder.vue'

// 定义设置选项类型
interface SettingOption {
  icon: string
  name: string
}

// 定义订单项类型
interface OrderItem {
  icon: string
  text: string
  status: string // 订单状态码
}

// 定义服务项类型
interface ServiceItem {
  icon: string
  text: string
  url: string
}

const useMyStore = useStore()

// 从 store 获取用户登录状态
const isLoggedIn = computed(() => useMyStore.getters.isLoggedIn)
// 新增：获取实名认证状态
const isCertified = computed(() => useMyStore.getters.isCertified)

// 从 store 获取用户名，如果未登录或不存在则返回默认值
const userName = computed(() => useMyStore.getters.userName || '用户名')

// 会员信息直接从sstore获取
const memberInfo = computed(() => useMyStore.getters.userInfo?.member)

onShow(() => {
  refreshUserInfo()
})

// 会员类型
const vip = computed(() => {
  // 如果 memberInfo 为 null 或 memberType 不存在，则默认为 NORMAL
  if (!memberInfo.value || !memberInfo.value.memberType) {
    const normalStatus = VIPSTATUS.find((item) => item.value === 'NORMAL')
    return normalStatus ? normalStatus.label : ''
  }
  // 否则，按原逻辑查找
  return (
    VIPSTATUS.find((item) => item.value === memberInfo.value.memberType)
      ?.label || ''
  )
})
// 会员有效期（格式化时间戳）
const userExpireDate = computed(() => {
  if (!memberInfo.value?.endTime) return ''
  const date = new Date(memberInfo.value.endTime)
  const y = date.getFullYear()
  const m = (date.getMonth() + 1).toString().padStart(2, '0')
  const d = date.getDate().toString().padStart(2, '0')
  return `${y}-${m}-${d}`
})

// 从 store 获取用户头像URL，如果未登录或不存在则返回 null
const userAvatar = computed(() => useMyStore.state.$userInfo?.avatar || null)

// 用户手机号
const userPhone = computed(() => {
  const phone = useMyStore.getters.userInfo?.mobile
  return phone ? phone.replace(/^(\d{3})\d{4}(\d{4})$/, '$1****$2') : ''
})

// 控制设置弹窗显示
const showSettingsPopup = ref(false)

// 设置菜单项
const settingOptions = ref<SettingOption[]>([
  { icon: 'setting-o', name: '账户设置' },
  // { icon: 'comment-o', name: '消息通知' },
  // { icon: 'user-circle-o', name: '隐私安全' },
  // { icon: 'smile-comment-o', name: '帮助与反馈' },
  { icon: 'revoke', name: '退出登录' },
])

const orderItems = ref<OrderItem[]>([
  { icon: 'paid', text: '待付款', status: 'UNPAID' },
  // { icon: 'users', text: '拼团中' },
  { icon: 'clock-o', text: '待发货', status: 'PAID' },
  { icon: 'logistics', text: '待收货', status: 'SHIPPED' },
  { icon: 'passed', text: '已完成', status: 'COMPLETED' },
])

// 服务项
const serviceItems: ServiceItem[] = [
  {
    icon: 'chat-o',
    text: '联系客服',
    url: '/pages/views/profile/contact'
  },
  {
    icon: 'coupon-o',
    text: '我的优惠卷',
    url: '/pages/views/profile/voucher'
  },
  {
    icon: 'question-o',
    text: '售后服务',
    url: '/pages/views/after-sale/afterSale-list'
  }
]

// 新增：动态生成服务列表
const dynamicServiceItems = computed(() => {
  const baseItems = [...serviceItems]
  if (isLoggedIn.value) {
    baseItems.unshift({
      icon: 'user-circle-o',
      text: `实名认证 (${isCertified.value ? '已认证' : '去认证'})`,
      url: '/pages/views/certification/index'
    })
  }
  return baseItems
})

// 刷新用户信息
const refreshUserInfo = async () => {
  if (isLoggedIn) {
    try {
      const res = await getUserInfo()
      if (res.code === 200) {
        // 更新store中的用户信息
        useMyStore.commit('SET_USER_INFO', res.data)
      }
    } catch (error) {
      console.error('获取用户信息失败', error)
    }
  }
}

const navigateTo = (url: string) => {
  uni.navigateTo({
    url,
  })
}

// 处理服务点击，检查登录状态
const handleServiceClick = (url: string) => {
  // 首先检查登录状态
  if (!isLoggedIn) {
    goToLogin()
    return
  }
  navigateTo(url)
}

// 跳转到登录页
const goToLogin = () => {
  uni.navigateTo({
    url: '/pages/views/login/login',
  })
}

// 打开设置菜单
const openSettings = () => {
  showSettingsPopup.value = true
}

// 点击设置菜单项
const handleSettingOption = (option: SettingOption) => {
  showSettingsPopup.value = false

  // 根据不同的设置项执行不同的操作
  switch (option.name) {
    case '账户设置':
      uni.navigateTo({ url: '/pages/views/profile/settings' })
      break
    case '退出登录':
      uni.showModal({
        title: '提示',
        content: '确定要退出登录吗？',
        success: async (res) => {
          if (res.confirm) {
            // 清除本地存储的用户信息
            store.dispatch('logout')

            uni.showToast({
              title: '已退出登录',
              icon: 'success',
            })

            // 退出后返回首页
            setTimeout(() => {
              uni.switchTab({
                url: '/pages/views/home/<USER>',
              })
            }, 500)
          }
        },
      })
      break
    default:
      // 其他设置项，显示开发中提示
      uni.showToast({
        title: `${option.name}功能开发中`,
        icon: 'none',
      })
  }
}

// 跳转到全部订单页面
const navigateToAllOrders = () => {
  if (!isLoggedIn) {
    goToLogin()
    return
  }
  uni.navigateTo({
    url: '/pages/views/order/list',
  })
}

// 根据状态跳转到对应的订单列表页面
const navigateToOrdersByStatus = (status: string) => {
  if (!isLoggedIn) {
    goToLogin()
    return
  }
  uni.navigateTo({
    url: `/pages/views/order/list?status=${status}`,
  })
}

// 跳转到会员中心
const goToMemberCenter = () => {
  if (!isLoggedIn.value) {
    goToLogin()
    return
  }
  uni.navigateTo({
    url: '/pages/views/member/memberCenter',
  })
}
</script>

<style scoped lang="scss">
// 增强会员卡片阴影层次感
.custom-vip-shadow {
  box-shadow: 0 6px 24px 0 rgba(239, 68, 68, 0.1),
    0 1.5px 6px 0 rgba(0, 0, 0, 0.06);
}
</style>
