import 'package:json_annotation/json_annotation.dart';

part 'product_models.g.dart';

/// 商品信息
@JsonSerializable()
class Product {
  const Product({
    required this.id,
    required this.name,
    required this.brandId,
    required this.brandName,
    required this.categoryId,
    required this.categoryName,
    required this.price,
    required this.originalPrice,
    required this.condition,
    required this.conditionDesc,
    required this.images,
    required this.description,
    required this.specifications,
    required this.isAvailable,
    required this.stock,
    required this.sellerId,
    required this.sellerName,
    this.sellerRating,
    required this.location,
    required this.createTime,
    required this.updateTime,
    this.tags,
    this.discount,
    this.viewCount,
    this.favoriteCount,
  });

  factory Product.fromJson(Map<String, dynamic> json) =>
      _$ProductFromJson(json);

  @JsonKey(name: 'id')
  final String id;

  @JsonKey(name: 'name')
  final String name;

  @JsonKey(name: 'brandId')
  final String brandId;

  @<PERSON>son<PERSON>ey(name: 'brandName')
  final String brandName;

  @Json<PERSON>ey(name: 'categoryId')
  final String categoryId;

  @JsonKey(name: 'categoryName')
  final String categoryName;

  @JsonKey(name: 'price')
  final double price;

  @JsonKey(name: 'originalPrice')
  final double originalPrice;

  @JsonKey(name: 'condition')
  final String condition;

  @JsonKey(name: 'conditionDesc')
  final String conditionDesc;

  @JsonKey(name: 'images')
  final List<String> images;

  @JsonKey(name: 'description')
  final String description;

  @JsonKey(name: 'specifications')
  final Map<String, String> specifications;

  @JsonKey(name: 'isAvailable')
  final bool isAvailable;

  @JsonKey(name: 'stock')
  final int stock;

  @JsonKey(name: 'sellerId')
  final String sellerId;

  @JsonKey(name: 'sellerName')
  final String sellerName;

  @JsonKey(name: 'sellerRating')
  final double? sellerRating;

  @JsonKey(name: 'location')
  final String location;

  @JsonKey(name: 'createTime')
  final int createTime;

  @JsonKey(name: 'updateTime')
  final int updateTime;

  @JsonKey(name: 'tags')
  final List<String>? tags;

  @JsonKey(name: 'discount')
  final double? discount;

  @JsonKey(name: 'viewCount')
  final int? viewCount;

  @JsonKey(name: 'favoriteCount')
  final int? favoriteCount;

  Map<String, dynamic> toJson() => _$ProductToJson(this);

  /// 获取折扣百分比
  double get discountPercentage {
    if (discount == null || originalPrice <= 0) return 0;
    return (discount! / originalPrice * 100);
  }

  /// 是否有折扣
  bool get hasDiscount => discount != null && discount! > 0;

  /// 获取主图
  String get primaryImage => images.isNotEmpty ? images.first : '';

  /// 是否有库存
  bool get hasStock => stock > 0;

  /// 获取状况等级
  ProductCondition get conditionLevel {
    switch (condition.toLowerCase()) {
      case 'new':
        return ProductCondition.brandNew;
      case 'excellent':
        return ProductCondition.excellent;
      case 'good':
        return ProductCondition.good;
      case 'fair':
        return ProductCondition.fair;
      case 'poor':
        return ProductCondition.poor;
      default:
        return ProductCondition.unknown;
    }
  }
}

/// 商品分类
@JsonSerializable()
class ProductCategory {
  const ProductCategory({
    required this.id,
    required this.name,
    required this.icon,
    this.parentId,
    this.description,
    required this.productCount,
    required this.isActive,
    this.sortOrder,
    this.children,
  });

  factory ProductCategory.fromJson(Map<String, dynamic> json) =>
      _$ProductCategoryFromJson(json);

  @JsonKey(name: 'id')
  final String id;

  @JsonKey(name: 'name')
  final String name;

  @JsonKey(name: 'icon')
  final String icon;

  @JsonKey(name: 'parentId')
  final String? parentId;

  @JsonKey(name: 'description')
  final String? description;

  @JsonKey(name: 'productCount')
  final int productCount;

  @JsonKey(name: 'isActive')
  final bool isActive;

  @JsonKey(name: 'sortOrder')
  final int? sortOrder;

  @JsonKey(name: 'children')
  final List<ProductCategory>? children;

  Map<String, dynamic> toJson() => _$ProductCategoryToJson(this);

  /// 是否为顶级分类
  bool get isTopLevel => parentId == null;

  /// 是否有子分类
  bool get hasChildren => children != null && children!.isNotEmpty;
}

/// 商品搜索请求
@JsonSerializable()
class ProductSearchRequest {
  const ProductSearchRequest({
    this.keyword,
    this.categoryId,
    this.brandId,
    this.minPrice,
    this.maxPrice,
    this.condition,
    this.location,
    this.sortBy,
    this.sortOrder,
    this.page = 1,
    this.pageSize = 20,
  });

  factory ProductSearchRequest.fromJson(Map<String, dynamic> json) =>
      _$ProductSearchRequestFromJson(json);

  @JsonKey(name: 'keyword')
  final String? keyword;

  @JsonKey(name: 'categoryId')
  final String? categoryId;

  @JsonKey(name: 'brandId')
  final String? brandId;

  @JsonKey(name: 'minPrice')
  final double? minPrice;

  @JsonKey(name: 'maxPrice')
  final double? maxPrice;

  @JsonKey(name: 'condition')
  final String? condition;

  @JsonKey(name: 'location')
  final String? location;

  @JsonKey(name: 'sortBy')
  final String? sortBy;

  @JsonKey(name: 'sortOrder')
  final String? sortOrder;

  @JsonKey(name: 'page')
  final int page;

  @JsonKey(name: 'pageSize')
  final int pageSize;

  Map<String, dynamic> toJson() => _$ProductSearchRequestToJson(this);

  /// 复制并修改参数
  ProductSearchRequest copyWith({
    String? keyword,
    String? categoryId,
    String? brandId,
    double? minPrice,
    double? maxPrice,
    String? condition,
    String? location,
    String? sortBy,
    String? sortOrder,
    int? page,
    int? pageSize,
  }) {
    return ProductSearchRequest(
      keyword: keyword ?? this.keyword,
      categoryId: categoryId ?? this.categoryId,
      brandId: brandId ?? this.brandId,
      minPrice: minPrice ?? this.minPrice,
      maxPrice: maxPrice ?? this.maxPrice,
      condition: condition ?? this.condition,
      location: location ?? this.location,
      sortBy: sortBy ?? this.sortBy,
      sortOrder: sortOrder ?? this.sortOrder,
      page: page ?? this.page,
      pageSize: pageSize ?? this.pageSize,
    );
  }
}

/// 商品状况枚举
enum ProductCondition {
  brandNew,
  excellent,
  good,
  fair,
  poor,
  unknown,
}

/// 排序方式枚举
enum ProductSortBy {
  price,
  createTime,
  updateTime,
  viewCount,
  favoriteCount,
  relevance,
}

/// 排序顺序枚举
enum ProductSortOrder {
  asc,
  desc,
}
