<template>
	<view class="up-navbar" :class="[customClass]">
		<view
			class="up-navbar__placeholder"
			v-if="fixed && placeholder"
			:style="{
				height: placeholderHeight,
			}"
		></view>
		<view :class="[fixed ? 'up-navbar--fixed' : '']">
			<up-status-bar
				v-if="safeAreaInsetTop"
				:bgColor="bgColor"
			></up-status-bar>
			<view
				class="up-navbar__content"
				:class="[border ? 'up-border-bottom' : '']"
				:style="{
					height: addUnit(height),
					backgroundColor: bgColor,
				}"
			>
				<view
					class="up-navbar__content__left"
					hover-class="up-navbar__content__left--hover"
					hover-start-time="150"
					@tap="leftClick"
				>
					<slot name="left">
						<up-icon
							v-if="leftIcon"
							:name="leftIcon"
							:size="leftIconSize"
							:color="leftIconColor"
						></up-icon>
						<text
							v-if="leftText"
							:style="{
								color: leftIconColor
							}"
							class="up-navbar__content__left__text"
						>{{ leftText }}</text>
					</slot>
				</view>
				<slot name="center">
					<text
						class="up-line-1 up-navbar__content__title"
						:style="[{
							width: addUnit(titleWidth),
						}, addStyle(titleStyle)]"
					>{{ title }}</text>
				</slot>
				<view
					class="up-navbar__content__right"
					v-if="$slots['right'] != null || rightIcon != '' || rightText != ''"
					@tap="rightClick"
				>
					<slot name="right">
						<up-icon
							v-if="rightIcon"
							:name="rightIcon"
							size="20"
						></up-icon>
						<text
							v-if="rightText"
							class="up-navbar__content__right__text"
						>{{ rightText }}</text>
					</slot>
				</view>
			</view>
		</view>
	</view>
</template>

<script>
	import { propsNavbar } from './props';
	import { mpMixin } from '../../libs/mixin/mpMixin';
	import { mixin } from '../../libs/mixin/mixin';
	import { addUnit, addStyle, getPx, sys } from '../../libs/function/index';
	/**
	 * Navbar 自定义导航栏
	 * @<NAME_EMAIL>
	 * @description 此组件一般用于在特殊情况下，需要自定义导航栏的时候用到，一般建议使用uni-app带的导航栏。
	 * @tutorial https://ijry.github.io/uview-plus/components/navbar.html
	 * @property {Boolean}			safeAreaInsetTop	是否开启顶部安全区适配  （默认 true ）
	 * @property {Boolean}			placeholder			固定在顶部时，是否生成一个等高元素，以防止塌陷 （默认 false ）
	 * @property {Boolean}			fixed				导航栏是否固定在顶部 （默认 false ）
	 * @property {Boolean}			border				导航栏底部是否显示下边框 （默认 false ）
	 * @property {String}			leftIcon			左边返回图标的名称，只能为uView自带的图标 （默认 'arrow-left' ）
	 * @property {String}			leftText			左边的提示文字
	 * @property {String}			rightText			右边的提示文字
	 * @property {String}			rightIcon			右边返回图标的名称，只能为uView自带的图标
	 * @property {String}			title				导航栏标题，如设置为空字符，将会隐藏标题占位区域
	 * @property {String}			bgColor				导航栏背景设置 （默认 '#ffffff' ）
	 * @property {String | Number}	titleWidth			导航栏标题的最大宽度，内容超出会以省略号隐藏 （默认 '400rpx' ）
	 * @property {String | Number}	height				导航栏高度(不包括状态栏高度在内，内部自动加上)（默认 '44px' ）
	 * @property {String | Number}	leftIconSize		左侧返回图标的大小（默认 20px ）
	 * @property {String | Number}	leftIconColor		左侧返回图标的颜色（默认 #303133 ）
	 * @property {Boolean}	        autoBack			点击左侧区域(返回图标)，是否自动返回上一页（默认 false ）
	 * @property {Object | String}	titleStyle			标题的样式，对象或字符串
	 * @event {Function} leftClick		点击左侧区域
	 * @event {Function} rightClick		点击右侧区域
	 * @example <up-navbar title="剑未配妥，出门已是江湖" left-text="返回" right-text="帮助" @click-left="onClickBack" @click-right="onClickRight"></up-navbar>
	 */
	export default {
		name: 'up-navbar',
		mixins: [mpMixin, mixin, propsNavbar],
		data() {
			return {
				statusBarHeight: 0 as number,
				placeholderHeight: '0px' as string,
			}
		},
		emits: ["leftClick", "rightClick"],
		created() {
			this.statusBarHeight = uni.getSystemInfoSync().statusBarHeight
			this.placeholderHeight = addUnit(parseInt(getPx(this.height)) + this.statusBarHeight, 'px')
		},
		methods: {
			addStyle(val: any): any {
				return addStyle(val)
			},
			addUnit(val: any): string {
				return addUnit(val)
			},
			getPx(val: any): string {
				return getPx(val)
			},
			// 点击左侧区域
			leftClick(): void {
				// 如果配置了autoBack，自动返回上一页
				this.$emit('leftClick')
				if(this.autoBack) {
					uni.navigateBack()
				}
			},
			// 点击右侧区域
			rightClick(): void {
				this.$emit('rightClick')
			}
		}
	}
</script>

<style lang="scss" scoped>
	@import "../../libs/css/components.scss";

	.up-navbar {

		&--fixed {
			position: fixed;
			left: 0;
			right: 0;
			top: 0;
			z-index: 11;
		}

		&__content {
			@include flex(row);
			align-items: center;
			height: 44px;
			background-color: #9acafc;
			position: relative;
			justify-content: center;

			&__left,
			&__right {
				padding: 0 13px;
				position: absolute;
				top: 0;
				bottom: 0;
				@include flex(row);
				align-items: center;
			}

			&__left {
				left: 0;
				
				&--hover {
					opacity: 0.7;
				}

				&__text {
					font-size: 15px;
					margin-left: 3px;
				}
			}

			&__title {
				text-align: center;
				font-size: 16px;
				color: $up-main-color;
			}

			&__right {
				right: 0;

				&__text {
					font-size: 15px;
					margin-left: 3px;
				}
			}
		}
	}
</style>
