import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

import 'package:soko/features/recycle/domain/entities/recycle_order.dart';
import 'package:soko/core/enums/app_enums.dart';

/// 订单状态时间线组件
class OrderStatusTimeline extends StatelessWidget {
  const OrderStatusTimeline({
    super.key,
    required this.order,
  });

  final RecycleOrder order;

  @override
  Widget build(BuildContext context) {
    final steps = _getTimelineSteps();
    final currentStepIndex = _getCurrentStepIndex();

    return Container(
      padding: EdgeInsets.all(16.w),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(12.r),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.05),
            blurRadius: 8,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // 标题
          Row(
            children: [
              Icon(
                Icons.timeline,
                size: 20.w,
                color: Theme.of(context).primaryColor,
              ),
              SizedBox(width: 8.w),
              Text(
                '订单状态',
                style: TextStyle(
                  fontSize: 16.sp,
                  fontWeight: FontWeight.w600,
                ),
              ),
              const Spacer(),
              Container(
                padding: EdgeInsets.symmetric(horizontal: 8.w, vertical: 4.h),
                decoration: BoxDecoration(
                  color: _getStatusColor().withValues(alpha: 0.1),
                  borderRadius: BorderRadius.circular(12.r),
                  border: Border.all(
                    color: _getStatusColor().withValues(alpha: 0.3),
                  ),
                ),
                child: Text(
                  order.orderStatusDesc,
                  style: TextStyle(
                    fontSize: 12.sp,
                    color: _getStatusColor(),
                    fontWeight: FontWeight.w500,
                  ),
                ),
              ),
            ],
          ),
          SizedBox(height: 16.h),
          
          // 时间线
          Column(
            children: steps.asMap().entries.map((entry) {
              final index = entry.key;
              final step = entry.value;
              final isCompleted = index <= currentStepIndex;
              final isCurrent = index == currentStepIndex;
              final isLast = index == steps.length - 1;

              return _buildTimelineStep(
                step: step,
                isCompleted: isCompleted,
                isCurrent: isCurrent,
                isLast: isLast,
                context: context,
              );
            }).toList(),
          ),
        ],
      ),
    );
  }

  /// 构建时间线步骤
  Widget _buildTimelineStep({
    required TimelineStep step,
    required bool isCompleted,
    required bool isCurrent,
    required bool isLast,
    required BuildContext context,
  }) {
    final primaryColor = Theme.of(context).primaryColor;
    final greyColor = Colors.grey[400]!;

    return Row(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // 左侧图标和连接线
        Column(
          children: [
            // 状态图标
            Container(
              width: 24.w,
              height: 24.w,
              decoration: BoxDecoration(
                color: isCompleted ? primaryColor : Colors.white,
                border: Border.all(
                  color: isCompleted ? primaryColor : greyColor,
                  width: 2,
                ),
                shape: BoxShape.circle,
              ),
              child: isCompleted
                  ? Icon(
                      isCurrent ? Icons.radio_button_checked : Icons.check,
                      size: 14.w,
                      color: Colors.white,
                    )
                  : Icon(
                      Icons.radio_button_unchecked,
                      size: 14.w,
                      color: greyColor,
                    ),
            ),
            
            // 连接线
            if (!isLast)
              Container(
                width: 2.w,
                height: 40.h,
                color: isCompleted ? primaryColor : greyColor,
              ),
          ],
        ),
        SizedBox(width: 12.w),
        
        // 右侧内容
        Expanded(
          child: Padding(
            padding: EdgeInsets.only(bottom: isLast ? 0 : 16.h),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                // 步骤标题
                Text(
                  step.title,
                  style: TextStyle(
                    fontSize: 14.sp,
                    fontWeight: isCompleted ? FontWeight.w600 : FontWeight.normal,
                    color: isCompleted ? Colors.grey[800] : Colors.grey[500],
                  ),
                ),
                
                // 步骤描述
                if (step.description != null) ...[
                  SizedBox(height: 4.h),
                  Text(
                    step.description!,
                    style: TextStyle(
                      fontSize: 12.sp,
                      color: Colors.grey[600],
                    ),
                  ),
                ],
                
                // 时间
                if (step.time != null) ...[
                  SizedBox(height: 4.h),
                  Text(
                    _formatDateTime(step.time!),
                    style: TextStyle(
                      fontSize: 11.sp,
                      color: Colors.grey[500],
                    ),
                  ),
                ],
              ],
            ),
          ),
        ),
      ],
    );
  }

  /// 获取时间线步骤
  List<TimelineStep> _getTimelineSteps() {
    return [
      TimelineStep(
        title: '订单创建',
        description: '提交回收申请',
        time: order.createTime,
      ),
      TimelineStep(
        title: '等待审核',
        description: '专业评估师审核中',
        time: null,
      ),
      TimelineStep(
        title: '价格确认',
        description: '确认回收价格',
        time: null,
      ),
      TimelineStep(
        title: '寄送设备',
        description: '将设备寄送给我们',
        time: null,
      ),
      TimelineStep(
        title: '最终检测',
        description: '收到设备后进行最终检测',
        time: null,
      ),
      TimelineStep(
        title: '交易完成',
        description: '确认无误后转账到账',
        time: null,
      ),
    ];
  }

  /// 获取当前步骤索引
  int _getCurrentStepIndex() {
    switch (order.statusEnum) {
      case RecycleOrderStatus.created:
        return order.orderStatus == 'PENDING_APPROVAL' ? 1 : 0;
      case RecycleOrderStatus.confirmed:
        return 2;
      case RecycleOrderStatus.picked:
        return 3;
      case RecycleOrderStatus.evaluated:
        return 4;
      case RecycleOrderStatus.completed:
        return 5;
      case RecycleOrderStatus.cancelled:
        return 0; // 取消状态显示在第一步
    }
  }

  /// 获取状态颜色
  Color _getStatusColor() {
    switch (order.statusEnum) {
      case RecycleOrderStatus.created:
        return Colors.orange;
      case RecycleOrderStatus.confirmed:
        return Colors.blue;
      case RecycleOrderStatus.picked:
        return Colors.purple;
      case RecycleOrderStatus.evaluated:
        return Colors.indigo;
      case RecycleOrderStatus.completed:
        return Colors.green;
      case RecycleOrderStatus.cancelled:
        return Colors.red;
    }
  }

  /// 格式化日期时间
  String _formatDateTime(int timestamp) {
    final dateTime = DateTime.fromMillisecondsSinceEpoch(timestamp);
    return '${dateTime.month.toString().padLeft(2, '0')}-${dateTime.day.toString().padLeft(2, '0')} '
           '${dateTime.hour.toString().padLeft(2, '0')}:${dateTime.minute.toString().padLeft(2, '0')}';
  }
}

/// 时间线步骤数据模型
class TimelineStep {
  const TimelineStep({
    required this.title,
    this.description,
    this.time,
  });

  final String title;
  final String? description;
  final int? time;
}
