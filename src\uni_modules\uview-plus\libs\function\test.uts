/**
 * 验证电子邮箱格式
 */
export function email(value: string): boolean {
    return /^\w+((-\w+)|(\.\w+))*\@[A-Za-z0-9]+((\.|-)[A-Za-z0-9]+)*\.[A-Za-z0-9]+$/.test(value)
}

/**
 * 验证手机格式
 */
export function mobile(value: string): boolean {
    return /^1[23456789]\d{9}$/.test(value)
}

/**
 * 验证URL格式
 */
export function url(value: string): boolean {
    return /^((https|http|ftp|rtsp|mms):\/\/)(([0-9a-zA-Z_!~*'().&=+$%-]+: )?[0-9a-zA-Z_!~*'().&=+$%-]+@)?(([0-9]{1,3}.){3}[0-9]{1,3}|([0-9a-zA-Z_!~*'()-]+.)*([0-9a-zA-Z][0-9a-zA-Z-]{0,61})?[0-9a-zA-Z].[a-zA-Z]{2,6})(:[0-9]{1,4})?((\/?)|(\/[0-9a-zA-Z_!~*'().;?:@&=+$,%#-]+)+\/?)$/
        .test(value)
}

/**
 * 验证日期格式
 */
export function date(value: any): boolean {
  // 如果值为空，返回 false
  if (value === '' || value === null) {
    return false;
  }

  // 将值转换为字符串进行后续处理
  let valueStr:string = value.toString();

  // 判断是否是数字类型的时间戳
  if (!number(valueStr)) {
    // 检查是否是有效的秒级或毫秒级时间戳
    if (valueStr.length === 10 || valueStr.length === 13) {
      const date = new Date(valueStr);
      return !isNaN(date.getTime());
    }
    return false;
  }

  // 检查是否为有效日期字符串
  //if (typeof value === "string") {
    // 检查长度是否在合理范围内
    if (valueStr.length < 10 || valueStr.length > 19) {
      return false;
    }

    // 定义日期格式的正则表达式
    const dateRegex =
      /^\d{4}[-\/]\d{2}[-\/]\d{2}( \d{1,2}:\d{2}(:\d{2})?)?$/;

    // 使用正则表达式匹配日期格式
    if (!dateRegex.test(valueStr)) {
      return false;
    }

    // 尝试将字符串转换为 Date 对象
    const dateValue = new Date(valueStr);

    // 检查是否为有效日期
    return !isNaN(dateValue.getTime());
  //}

  // 如果不是数字或字符串类型，返回 false
  return false;
}

/**
 * 验证ISO类型的日期格式
 */
export function dateISO(value: string): boolean {
    return /^\d{4}[\/\-](0?[1-9]|1[012])[\/\-](0?[1-9]|[12][0-9]|3[01])$/.test(value)
}

/**
 * 验证十进制数字
 */
export function number(value: string): boolean {
    return /^[\+-]?(\d+\.?\d*|\.\d+|\d\.\d+e\+\d+)$/.test(value)
}

/**
 * 验证字符串
 */
export function string(value: string): boolean {
    return typeof value === 'string'
}

/**
 * 验证整数
 */
export function digits(value: string): boolean {
    return /^\d+$/.test(value)
}

/**
 * 验证身份证号码
 */
export function idCard(value: string): boolean {
    return /^[1-9]\d{5}[1-9]\d{3}((0\d)|(1[0-2]))(([0|1|2]\d)|3[0-1])\d{3}([0-9]|X)$/.test(
        value
    )
}

/**
 * 是否车牌号
 */
export function carNo(value: string): boolean {
    // 新能源车牌
    const xreg = /^[京津沪渝冀豫云辽黑湘皖鲁新苏浙赣鄂桂甘晋蒙陕吉闽贵粤青藏川宁琼使领A-Z]{1}[A-Z]{1}(([0-9]{5}[DF]$)|([DF][A-HJ-NP-Z0-9][0-9]{4}$))/
    // 旧车牌
    const creg = /^[京津沪渝冀豫云辽黑湘皖鲁新苏浙赣鄂桂甘晋蒙陕吉闽贵粤青藏川宁琼使领A-Z]{1}[A-Z]{1}[A-HJ-NP-Z0-9]{4}[A-HJ-NP-Z0-9挂学警港澳]{1}$/
    if (value.length === 7) {
        return creg.test(value)
    } if (value.length === 8) {
        return xreg.test(value)
    }
    return false
}

/**
 * 金额,只允许2位小数
 */
export function amount(value: string): boolean {
    // 金额，只允许保留两位小数
    return /^[1-9]\d*(,\d{3})*(\.\d{1,2})?$|^0\.\d{1,2}$/.test(value)
}

/**
 * 中文
 */
export function chinese(value: string): boolean {
    const reg = /^[\u4e00-\u9fa5]+$/gi
    return reg.test(value)
}

/**
 * 只能输入字母
 */
export function letter(value: string): boolean {
    return /^[a-zA-Z]*$/.test(value)
}

/**
 * 只能是字母或者数字
 */
export function enOrNum(value: string): boolean {
    // 英文或者数字
    const reg = /^[0-9a-zA-Z]*$/g
    return reg.test(value)
}

/**
 * 验证是否包含某个值
 */
export function contains(value: string, param: string): boolean {
    return value.indexOf(param) >= 0
}

/**
 * 验证一个值范围[min, max]
 */
export function range(value: number, param: Array<number>): boolean {
	return true
    // return value >= param[0] && value <= param[1]
}

/**
 * 验证一个长度范围[min, max]
 */
export function rangeLength(value: string, param: Array<number>): boolean {
	return true
    // return value.length >= param[0] && value.length <= param[1]
}

/**
 * 是否固定电话
 */
export function landline(value: string): boolean {
    const reg = /^\d{3,4}-\d{7,8}(-\d{3,4})?$/
    return reg.test(value)
}

/**
 * 判断是否为空
 */
export function empty(value: any): boolean {
    switch (typeof value) {
        case 'undefined':
            return true;
        case 'string':
            // 去除字符串两端的空白字符，并检查长度是否为0。
            return value == '';
        case 'boolean':
            return value == false;
        case 'number':
            return value === 0;
        case 'object':
            // 对于数组，检查其长度是否为0
            // if (Array.isArray(value) && value.length === 0) {
            //     return true;
            // }
            // 对于null或其他对象类型，返回false  
            return false;
    }
    return false;
}

/**
 * 是否json字符串
 */
export function jsonString(value: string): boolean {
    if (typeof value === 'string') {
        try {
            // const obj: any = JSON.parse(value)
            // if (typeof obj === 'object' && obj) {
            //     return true
            // }
            return false
        } catch (e) {
            return false
        }
    }
    return false
}

/**
 * 是否数组
 */
export function array(value: any): boolean {
	if ("object" == typeof value) {
		return value instanceof Array
	} else {
		return false
	}
}

/**
 * 是否对象
 */
export function object(value: any): boolean {
    if ("object" == typeof value) {
    	return value instanceof Object
    } else {
    	return false
    }
}

/**
 * 是否短信验证码
 */
export function code(value: string, len = 6): boolean {
    return new RegExp(`^\\d{${len}}$`).test(value)
}

/**
 * 是否函数方法
 * @param {Object} value
 */
export function func(value: any): boolean {
    return typeof value === 'function'
}

/**
 * 是否promise对象
 * @param {Object} value
 */
export function promise(value: any): boolean {
	return value instanceof Promise
	// return object(value) && func(value.then) && func(value.catch)
}

/** 是否图片格式
 * @param {Object} value
 */
export function image(value: string): boolean {
    const newValue = value.split('?')[0]
    const IMAGE_REGEXP = /\.(jpeg|jpg|gif|png|svg|webp|jfif|bmp|dpg)/i
    return IMAGE_REGEXP.test(newValue)
}

/**
 * 是否视频格式
 * @param {Object} value
 */
export function video(value: string): boolean {
    const VIDEO_REGEXP = /\.(mp4|mpg|mpeg|dat|asf|avi|rm|rmvb|mov|wmv|flv|mkv|m3u8)/i
    return VIDEO_REGEXP.test(value)
}

/**
 * 是否为正则对象
 * @param {Object}
 * @return {Boolean}
 */
export function regExp(o: object): boolean {
	return true
    // return o && Object.prototype.toString.call(o) === '[object RegExp]'
}

// export default {
//     email: email,
//     mobile: mobile,
//     url: url,
//     date: date,
//     dateISO: dateISO,
//     number: number,
//     digits,
//     idCard,
//     carNo,
//     amount,
//     chinese,
//     letter,
//     enOrNum,
//     contains,
//     range,
//     rangeLength,
//     empty,
//     isEmpty: empty,
//     jsonString,
//     landline,
//     object,
//     array,
//     code,
//     func,
//     promise,
//     video,
//     image,
//     regExp,
//     string
// }
