import { http, toast } from '@/uni_modules/uview-plus';
import store from '@/store';
const requestInterceptors = (vm) => {
  /**
   * 请求拦截
   * @param {Object} http
   */
  http.interceptors.request.use(
    (config) => {
      // 可使用async await 做异步操作
      // 初始化请求拦截器时，会执行此方法，此时data为undefined，赋予默认{}
      config.data = config.data || {};
      config.header['isAdmin'] = false;
      let token = uni.getStorageSync('token');
      if (token) {
        config.header['App-Authorization'] = 'FBearer ' + token;
      }
      return config;
    },
    (
      config // 可使用async await 做异步操作
    ) => Promise.reject(config)
  );
};
const responseInterceptors = (vm) => {
  /**
   * 响应拦截
   * @param {Object} http
   */

  // 定义错误处理映射
  const errorHandlers = {
    401: () => {
      uni.navigateTo({ url: '/pages/views/login/login' });
      toast('未授权，请重新登录');
    },
    701: () => {
      uni.navigateTo({ url: '/pages/views/login/login' });
      toast('登录已过期，请重新登录');
    },
    403: () => toast('禁止访问'),
    404: () => toast('请求的资源不存在'),
  };

  // 处理授权令牌和公钥的通用函数
  const handleHeaderValue = (key, storageKey, commitType) => {
    return (response) => {
      if (response.header[key]) {
        uni.setStorageSync(storageKey, response.header[key]);
        if (commitType) {
          store.commit(commitType, response.header[key]);
        }
      }
    };
  };

  http.interceptors.response.use(
    (response) => {
      const { data } = response;
      const custom = response.config?.custom;

      if (data.code !== 200) {
        const handler = errorHandlers[data.code];
        if (handler) {
          handler();
        } else {
          toast(`请求失败，状态码: ${data.code}`);
        }

        // 服务端返回的状态码不等于200，依然返回data
        if (custom?.toast !== false) {
          toast(data.message);
        }
      }

      // 处理授权令牌和公钥
      handleHeaderValue('app-authorization', 'token', 'SET_TOKEN')(response);
      handleHeaderValue('publickey', 'publickey')(response);

      if (response.header['publickey']) {
        // 设置定时任务，24小时后清除缓存
        setTimeout(() => {
          uni.removeStorageSync('publickey');
        }, 24 * 60 * 60 * 1000);
      }

      return data;
    },
    (response) => {
      const errMsg = response.errMsg || '';
      const config = response.config || {};
      const errorMessages = {
        'Failed to connect': '网络连接失败，请检查API服务是否正常运行',
        timeout: '请求超时，请稍后重试',
      };

      if (response.statusCode && response.statusCode === 401) {
        errorHandlers[401]();
      }

      const message =
        response?.data?.message ||
        errorMessages[errMsg] ||
        '请求失败，请稍后重试';
      toast(message);

      if (errMsg.includes('Failed to connect')) {
        console.error('API连接失败:', config.fullPath);
      }

      // 错误情况下也返回一个包含错误信息的data对象
      const errorData = {
        code: response?.statusCode || -1,
        message: message,
      };
      return errorData;
    }
  );
};

export { requestInterceptors, responseInterceptors };
