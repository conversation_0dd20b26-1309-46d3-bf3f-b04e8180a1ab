<template>
  <view class="flex flex-col min-h-screen bg-slate-50 custom-nav-page">
    <!-- 自定义导航栏 -->
    <NavBar title="交易快照详情" bgColor="#FFFFFF" :showBack="true" />

    <!-- 主内容区域 -->
    <view class="flex-1 px-4 pt-0 pb-3 space-y-3 -mt-8">
      <!-- 说明文字 -->
      <view class="bg-white rounded-lg shadow-sm p-4">
        <view class="flex items-start">
          <view class="mr-2 mt-0.5">
            <van-icon name="info-o" size="16" color="#6B7280" />
          </view>
          <text class="text-sm text-text-secondary leading-relaxed">
            交易快照包含订单创建时的商品描述和下单信息，买卖双方和平台在发生交易争议时，该页面作为判定依据。
          </text>
        </view>
      </view>

      <!-- 商品基本信息 -->
      <view v-if="snapshotItem" class="bg-white rounded-lg shadow-sm">
        <view class="p-4 border-b border-slate-100 flex items-center">
          <view class="w-1 h-4 bg-primary rounded-sm mr-2"></view>
          <text class="text-base font-medium text-text-primary">商品基本信息</text>
        </view>

        <view class="p-4">
          <!-- 商品图片和名称区域 -->
          <view class="flex gap-3 mb-6">
            <!-- 商品图片 -->
            <view class="relative h-20 w-20 bg-slate-50 rounded-md overflow-hidden flex-shrink-0 border border-slate-100 shadow-sm">
              <image
                :src="snapshotItem.image || '/static/logo-back.png'"
                :alt="snapshotItem.name"
                class="w-full h-full object-contain"
                mode="aspectFit"
              />
            </view>

            <!-- 商品名称和规格 -->
            <view class="flex-1 min-w-0 flex flex-col justify-center">
              <text class="text-sm font-medium text-text-primary line-clamp-2 mb-1">{{ snapshotItem.name || '未知商品' }}</text>
              <view v-if="snapshotItem.options" class="mb-1">
                <text v-if="!isValidJSON(snapshotItem.options)" class="text-xs text-text-secondary line-clamp-1">{{ snapshotItem.options }}</text>
                <view v-else class="flex flex-wrap">
                  <text class="text-xs text-text-secondary line-clamp-1">
                    <text v-for="(value, key, index) in parseSpecification(snapshotItem.options)" :key="index">
                      {{ index > 0 ? ' | ' : '' }}{{ key }}: {{ value }}
                    </text>
                  </text>
                </view>
              </view>
            </view>
          </view>

          <!-- 商品详情信息表格 -->
          <view class="space-y-3">
            <view class="flex justify-between py-2 border-b border-slate-100">
              <text class="text-text-secondary text-sm">商品价格</text>
              <text class="text-text-primary text-sm font-medium">¥{{ formatPrice(snapshotItem.price) }}</text>
            </view>
            <view class="flex justify-between py-2 border-b border-slate-100">
              <text class="text-text-secondary text-sm">购买数量</text>
              <text class="text-text-primary text-sm font-medium">{{ snapshotItem.quantity || 1 }}</text>
            </view>
            <view v-if="snapshotItem.originalPrice" class="flex justify-between py-2 border-b border-slate-100">
              <text class="text-text-secondary text-sm">原价</text>
              <text class="text-text-primary text-sm font-medium line-through">¥{{ formatPrice(snapshotItem.originalPrice) }}</text>
            </view>
            <view v-if="snapshotItem.salesType" class="flex justify-between py-2 border-b border-slate-100">
              <text class="text-text-secondary text-sm">销售类型</text>
              <text class="text-text-primary text-sm font-medium">{{ formatSalesType(snapshotItem.salesType) }}</text>
            </view>
            <view class="flex justify-between py-2 border-b border-slate-100">
              <text class="text-text-secondary text-sm">下单时间</text>
              <text class="text-text-primary text-sm font-medium">{{ orderTime }}</text>
            </view>
          </view>
        </view>
      </view>

      <!-- 商品描述 -->
      <view v-if="snapshotItem && snapshotItem.description" class="bg-white rounded-lg shadow-sm">
        <view class="p-4 border-b border-slate-100 flex items-center">
          <view class="w-1 h-4 bg-primary rounded-sm mr-2"></view>
          <text class="text-base font-medium text-text-primary">商品描述</text>
        </view>
        
        <view class="p-4">
          <!-- 如果是Delta格式则解析显示，否则直接显示文本 -->
          <view v-if="isDeltaFormat(snapshotItem.description)" class="text-sm text-text-secondary leading-relaxed rich-text-content" v-html="parseDelta(snapshotItem.description)"></view>
          <text v-else class="text-sm text-text-secondary leading-relaxed">{{ snapshotItem.description }}</text>
        </view>
      </view>

      <!-- 无商品信息 -->
      <view v-if="!snapshotItem" class="bg-white rounded-lg shadow-sm py-10 px-6 text-center">
        <van-icon name="warning-o" size="32" color="#9CA3AF" />
        <text class="text-sm text-text-secondary block mt-2">未能获取快照详情</text>
        <view class="mt-4">
          <view class="inline-block bg-slate-100 text-text-secondary rounded-full px-4 py-1.5 text-xs" @click="goBack">
            返回列表
          </view>
        </view>
      </view>
    </view>
  </view>
</template>

<script lang="ts" setup>
import { ref } from 'vue';
import { onShow } from '@dcloudio/uni-app';
import NavBar from '@/components/NavBar.vue';

// 定义商品快照数据类型
interface SnapshotOrderItem {
  id: string | number;
  name?: string;
  image?: string;
  options?: string;
  description?: string;
  price?: number | string;
  originalPrice?: number | string;
  quantity?: number;
  totalPrice?: number | string;
  realPrice?: number | string;
  salesType?: string;
  snapshotId?: string | number;
}

// 销售类型枚举
enum SalesType {
  STOCK = 'STOCK',
  PRESALE = 'PRESALE'
}

const snapshotItem = ref<SnapshotOrderItem | null>(null);
const orderId = ref('');
const orderTime = ref(formatDate(new Date()));

onShow(() => {
  const pages = getCurrentPages();
  const currentPage = pages[pages.length - 1];
  const pageOptions = (currentPage as any)?.$page?.options;
  
  if (pageOptions) {
    if (pageOptions.orderId) {
      orderId.value = pageOptions.orderId;
    }
    
    if (pageOptions.itemData) {
      try {
        const parsedData = JSON.parse(decodeURIComponent(pageOptions.itemData));
        snapshotItem.value = parsedData;
      } catch (error) {
        uni.showToast({
          title: '加载数据失败',
          icon: 'none'
        });
      }
    }
  }
});

// 检查字符串是否为有效的JSON
function isValidJSON(str: string): boolean {
  try {
    JSON.parse(str);
    return true;
  } catch (e) {
    return false;
  }
}

// 解析规格JSON字符串
function parseSpecification(jsonStr: string): Record<string, string> {
  try {
    const parsed = JSON.parse(jsonStr);
    
    // 如果是对象格式 {key1: value1, key2: value2}
    if (parsed && typeof parsed === 'object' && !Array.isArray(parsed)) {
      return parsed;
    }
    
    // 如果是数组格式 [{key: 'key1', value: 'value1'}, {key: 'key2', value: 'value2'}]
    if (Array.isArray(parsed)) {
      const result: Record<string, string> = {};
      parsed.forEach(item => {
        if (item.key && item.value) {
          result[item.key] = item.value;
        }
      });
      return result;
    }
    
    return {};
  } catch (e) {
    return {};
  }
}

// 格式化价格
function formatPrice(price: string | number | undefined): string {
  if (price === undefined || price === null) {
    return '0.00';
  }
  if (typeof price === 'string') {
    price = parseFloat(price) || 0;
  }
  return price.toFixed(2);
}

// 格式化销售类型
function formatSalesType(type?: string): string {
  if (!type) return '未知';
  
  switch(type) {
    case SalesType.STOCK:
      return '现货';
    case SalesType.PRESALE:
      return '预售';
    default:
      return type;
  }
}

// 格式化日期
function formatDate(date: Date): string {
  const year = date.getFullYear();
  const month = (date.getMonth() + 1).toString().padStart(2, '0');
  const day = date.getDate().toString().padStart(2, '0');
  const hour = date.getHours().toString().padStart(2, '0');
  const minute = date.getMinutes().toString().padStart(2, '0');
  return `${year}-${month}-${day} ${hour}:${minute}`;
}

// 返回上一页
function goBack() {
  // 检查是否有orderId，如果有则返回到快照列表页面
  if (orderId.value) {
    uni.redirectTo({
      url: `/pages/views/order/snapshot-list?orderId=${orderId.value}`
    });
  } else {
    // 没有orderId则返回上一页
    uni.navigateBack();
  }
}

// 检查是否为Delta格式
function isDeltaFormat(content: string | undefined): boolean {
  if (!content) return false;
  try {
    const parsed = JSON.parse(content);
    // Delta格式通常有ops属性
    return parsed && typeof parsed === 'object' && Array.isArray(parsed.ops);
  } catch (e) {
    return false;
  }
}

// 解析Delta格式为HTML
function parseDelta(deltaString: string | undefined): string {
  if (!deltaString) return '';
  try {
    const delta = JSON.parse(deltaString);
    if (!delta || !delta.ops || !Array.isArray(delta.ops)) {
      return deltaString;
    }

    let html = '';
    delta.ops.forEach((op: any) => {
      if (typeof op.insert !== 'string') {
        return;
      }

      let text = op.insert.replace(/</g, '&lt;').replace(/>/g, '&gt;');
      
      // 处理换行
      if (text.endsWith('\n')) {
        text = text.slice(0, -1) + '<br/>';
      }

      // 处理样式
      if (op.attributes) {
        if (op.attributes.bold) {
          text = `<strong>${text}</strong>`;
        }
        if (op.attributes.italic) {
          text = `<em>${text}</em>`;
        }
        if (op.attributes.underline) {
          text = `<u>${text}</u>`;
        }
        if (op.attributes.color) {
          text = `<span style="color:${op.attributes.color}">${text}</span>`;
        }
        if (op.attributes.background) {
          text = `<span style="background-color:${op.attributes.background}">${text}</span>`;
        }
        if (op.attributes.header) {
          text = `<h${op.attributes.header}>${text}</h${op.attributes.header}>`;
        }
        if (op.attributes.link) {
          text = `<a href="${op.attributes.link}" target="_blank">${text}</a>`;
        }
      }

      html += text;
    });

    return html;
  } catch (e) {
    return deltaString || '';
  }
}
</script>

<style scoped>
/* 页面特定样式 */
.custom-nav-page {
  padding-top: var(--status-bar-height); /* 适配状态栏 */
}

/* 富文本内容样式 */
:deep(.rich-text-content) {
  line-height: 1.6;
}

:deep(.rich-text-content p) {
  margin-bottom: 0.5rem;
}

:deep(.rich-text-content h1) {
  font-size: 1.5rem;
  font-weight: bold;
  margin: 0.75rem 0;
}

:deep(.rich-text-content h2) {
  font-size: 1.25rem;
  font-weight: bold;
  margin: 0.75rem 0;
}

:deep(.rich-text-content h3) {
  font-size: 1.125rem;
  font-weight: bold;
  margin: 0.5rem 0;
}

:deep(.rich-text-content a) {
  color: #3b82f6;
  text-decoration: underline;
}

:deep(.rich-text-content ul),
:deep(.rich-text-content ol) {
  padding-left: 1.5rem;
  margin: 0.5rem 0;
}

:deep(.rich-text-content ul li) {
  list-style-type: disc;
}

:deep(.rich-text-content ol li) {
  list-style-type: decimal;
}

/* 文本截断样式 */
.line-clamp-2 {
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
  text-overflow: ellipsis;
}

.line-clamp-1 {
  display: -webkit-box;
  -webkit-line-clamp: 1;
  -webkit-box-orient: vertical;
  overflow: hidden;
  text-overflow: ellipsis;
}
</style> 