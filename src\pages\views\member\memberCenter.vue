<template>
  <view class="min-h-screen bg-white text-sm">
    <NavBar title="会员中心" :showBack="true" :showRight="false" />

    <view class="px-4 py-6">
      <!-- 会员状态卡片 -->
      <view class="rounded-2xl bg-white shadow mb-8 p-6">
        <template v-if="memberData.memberType && (memberData.memberType === 'VIP' || memberData.memberType === 'SVIP' || memberData.memberType.startsWith('VIP') || memberData.memberType.startsWith('SVIP'))">
          <view class="flex items-center justify-between mb-6">
            <view class="flex items-center gap-4">
              <view class="w-14 h-14 bg-black rounded-full flex items-center justify-center">
                <van-icon name="diamond-o" size="32" color="#fff" />
              </view>
              <view>
                <text class="text-lg font-bold text-gray-900">{{ getMemberTypeLabel(memberData.memberType) }}</text>
                <view class="text-xs text-gray-500 mt-1">享受全部专属特权</view>
              </view>
            </view>
            <view class="bg-black text-white rounded-full px-3 py-1 text-xs font-medium">{{ memberData.memberType }}</view>
          </view>
          <view class="space-y-3">
            <view class="flex items-center justify-between text-xs">
              <text class="text-gray-500">会员有效期</text>
              <text class="font-medium text-gray-900">至 {{ formatDate(memberData.endTime) }}</text>
            </view>
            <view class="flex items-center justify-between text-xs">
              <text class="text-gray-500">剩余天数</text>
              <text class="font-medium text-gray-900">{{ calcDaysLeft(memberData.endTime) }} 天</text>
            </view>
            <view class="w-full bg-gray-100 rounded h-2 overflow-hidden">
              <view
                class="bg-black h-2 rounded"
                :style="{ width: (calcDaysLeft(memberData.endTime) / 365 * 100) + '%' }"
              ></view>
            </view>
          </view>
          <view class="flex gap-3 mt-6">
            <button
              class="flex-1 w-full bg-black hover:bg-gray-800 text-white rounded-full py-3 font-bold text-xs"
              @click="goToPlans"
            >续费会员</button>
          </view>
        </template>
        <template v-else>
          <view class="text-center py-8">
            <view class="w-16 h-16 bg-gray-100 rounded-full flex items-center justify-center mx-auto mb-4">
              <van-icon name="diamond-o" size="32" color="#ccc" />
            </view>
            <text class="text-xl font-bold text-gray-900 mb-2">{{ getMemberTypeLabel(memberData.memberType) }}</text>
            <view class="text-gray-500 mb-6">升级会员，解锁专属特权</view>
            <button
              class="bg-black hover:bg-gray-800 text-white rounded-full px-8 py-3 font-bold text-xs"
              @click="goToPlans"
            >立即升级</button>
          </view>
        </template>
      </view>

      <!-- 会员权益 -->
      <text class="text-lg font-bold text-gray-900 mb-4 block">会员权益</text>
      <view class="space-y-3">
        <view
          v-for="(benefit, index) in benefits"
          :key="benefit.id || index"
          class="rounded-2xl bg-white shadow p-4 flex items-center justify-between"
        >
          <view class="flex items-center gap-3">
            <view class="w-10 h-10 rounded-full flex items-center justify-center" :class="mapPrivilegeBg(benefit.code)">
              <van-icon :name="mapPrivilegeIcon(benefit.code)" size="22" color="#fff" />
            </view>
            <view>
              <text class="font-bold text-gray-900">{{ benefit.name }}</text>
              <view class="text-xs text-gray-500 mt-0.5">{{ benefit.description }}</view>
            </view>
          </view>
          <view
            class="rounded-full px-3 py-1 text-xs font-bold bg-green-50 text-green-600 border border-green-200"
          >已开通</view>
        </view>
      </view>
    </view>
  </view>
</template>

<script setup lang="ts">
import { ref, onMounted } from "vue"
import NavBar from '@/components/NavBar.vue'
import { getMemberInfo, getPrivilegesByType } from '@/api/api.js'

// 会员信息字段严格按API文档
const memberData = ref({
  id: '',
  version: 0,
  creatorId: '',
  creator: '',
  createdTime: 0,
  modifierId: '',
  modifier: '',
  modifiedTime: 0,
  userId: '',
  memberType: 'NORMAL', // 默认设置为普通会员
  startTime: 0,
  endTime: 0,
  autoRenew: false,
  originPrice: 0,
  realPrice: 0,
  payTime: 0,
  payOrderNo: '',
  status: '',
  inviterId: '',
  couponId: ''
})

// 权益列表严格按API文档
const benefits = ref<any[]>([])

onMounted(async () => {
  try {
    // 获取会员信息
    const res = await getMemberInfo()
    
    // 如果获取到有效的会员信息，则更新会员数据
    if (res && res.data) {
      Object.assign(memberData.value, res.data)
    } else {
      // 如果没有获取到会员信息或会员信息为空，确保会员类型为NORMAL
      memberData.value.memberType = 'NORMAL'
    }
    
    // 无论是否获取到会员信息，都根据当前会员类型获取对应权益
    if (memberData.value.memberType) {
      const benRes = await getPrivilegesByType(memberData.value.memberType)
      if (benRes && Array.isArray(benRes.data)) {
        benefits.value = benRes.data
      }
    }
  } catch (error) {
    console.error('获取会员信息失败:', error)
    // 发生错误时也确保会员类型为NORMAL
    memberData.value.memberType = 'NORMAL'
    // 获取普通会员权益
    try {
      const benRes = await getPrivilegesByType('NORMAL')
      if (benRes && Array.isArray(benRes.data)) {
        benefits.value = benRes.data
      }
    } catch (err) {
      console.error('获取会员权益失败:', err)
    }
  }
})

function getMemberTypeLabel(type: string) {
  if (!type) return '普通会员'
  
  if (type === 'SVIP' || type.startsWith('SVIP')) return '超级会员'
  if (type === 'VIP' || type.startsWith('VIP')) return '高级会员'
  if (type === 'NORMAL') return '普通会员'
  
  return '未知'
}
function formatDate(ts: number | string) {
  const date = typeof ts === 'number' ? new Date(ts) : new Date(Number(ts))
  if (isNaN(date.getTime())) return ''
  return `${date.getFullYear()}-${String(date.getMonth()+1).padStart(2,'0')}-${String(date.getDate()).padStart(2,'0')}`
}
function calcDaysLeft(endTime: number | string) {
  const now = Date.now()
  const end = typeof endTime === 'number' ? endTime : Number(endTime)
  if (!end) return 0
  return Math.max(0, Math.ceil((end - now) / (1000 * 60 * 60 * 24)))
}
function mapPrivilegeIcon(code: string) {
  switch (code) {
    case 'DISCOUNT': return 'smile-o'
    case 'QUALITY': return 'smile-o'
    case 'EXCLUSIVE': return 'smile-o'
    default: return 'smile-o'
  }
}
function mapPrivilegeBg(code: string) {
  // 统一深浅风格且不包含白色的色块集合
  const bgs = [
    'bg-primary',
    'bg-secondary',
    'bg-heroic',
    'bg-accent',
    'bg-error'
  ]
  switch (code) {
    case 'DISCOUNT': return 'bg-primary'
    case 'QUALITY': return 'bg-secondary'
    case 'EXCLUSIVE': return 'bg-heroic'
    default:
      // code 不存在时随机一个
      return bgs[Math.floor(Math.random() * bgs.length)]
  }
}
function goToPlans() {
  uni.navigateTo({ url: "/pages/views/member/memberPlan" })
}
</script>

<style scoped>
/* 细节微调可在此补充 */
</style>
