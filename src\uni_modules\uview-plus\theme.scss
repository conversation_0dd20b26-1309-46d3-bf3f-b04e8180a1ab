// 此文件为uview-plus的主题变量，这些变量目前只能通过uni.scss引入才有效，另外由于
// uni.scss中引入的样式会同时混入到全局样式文件和单独每一个页面的样式中，造成微信程序包太大，
// 故uni.scss只建议放scss变量名相关样式，其他的样式可以通过main.js或者App.vue引入

$up-main-color: #303133;
$up-content-color: #606266;
$up-tips-color: #909193;
$up-light-color: #c0c4cc;
$up-border-color: #dadbde;
$up-bg-color: #f3f4f6;
$up-disabled-color: #c8c9cc;

$up-primary: #EF4444; // 奥特曼红
$up-primary-dark: #DC2626; // 暗红
$up-primary-disabled: #FCA5A5; // 浅红
$up-primary-light: #FEF2F2; // 极浅红

$up-warning: #F59E0B; // 英雄金
$up-warning-dark: #D97706;
$up-warning-disabled: #FDE68A;
$up-warning-light: #FFFBEB;

$up-success: #10B981; // 假面骑士绿
$up-success-dark: #059669;
$up-success-disabled: #A7F3D0;
$up-success-light: #ECFDF5;

$up-error: #FF4D4F; // 错误红
$up-error-dark: #DC2626;
$up-error-disabled: #FCA5A5;
$up-error-light: #FEF2F2;

$up-info: #6B7280; // 机械灰
$up-info-dark: #4B5563;
$up-info-disabled: #D1D5DB;
$up-info-light: #F9FAFB;

$u-main-color: $up-main-color;
$u-content-color: $up-content-color;
$u-tips-color: $up-tips-color;
$u-light-color: $up-light-color;
$u-border-color: $up-border-color;
$u-bg-color: $up-bg-color;
$u-disabled-color: $up-disabled-color;

$u-primary: $up-primary;
$u-primary-dark: $up-primary-dark;
$u-primary-disabled: $up-primary-disabled;
$u-primary-light: $up-primary-light;

$u-warning: $up-warning;
$u-warning-dark: $up-warning-dark;
$u-warning-disabled: $up-warning-disabled;
$u-warning-light: $up-warning-light;

$u-success: $up-success;
$u-success-dark: $up-success-dark;
$u-success-disabled: $up-success-disabled;
$u-success-light: $up-success-light;

$u-error: $up-error;
$u-error-dark: $up-error-dark;
$u-error-disabled: $up-error-disabled;
$u-error-light: $up-error-light;

$u-info: $up-info;
$u-info-dark: $up-info-dark;
$u-info-disabled: $up-info-disabled;
$u-info-light: $up-info-light;

// scss混入，为了少写几行#ifndef
@mixin flex($direction: row) {
	/* #ifndef APP-NVUE */
	display: flex;
	/* #endif */
	flex-direction: $direction;
}
