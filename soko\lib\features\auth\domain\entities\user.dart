import 'package:json_annotation/json_annotation.dart';

import 'package:soko/core/enums/app_enums.dart';

part 'user.g.dart';

/// 用户实体类
@JsonSerializable()
class User {

  const User({
    required this.id,
    this.username,
    this.nickname,
    this.email,
    this.phone,
    this.avatar,
    this.gender,
    this.birthday,
    this.realName,
    this.idCard,
    this.memberLevel,
    this.memberExpireTime,
    this.balance,
    this.points,
    required this.status,
    required this.createTime,
    required this.updateTime,
    this.lastLoginTime,
    this.loginCount,
  });

  factory User.fromJson(Map<String, dynamic> json) => _$UserFromJson(json);
  @Json<PERSON>ey(name: 'id')
  final String id;

  @<PERSON><PERSON><PERSON><PERSON>(name: 'username')
  final String? username;

  @Json<PERSON><PERSON>(name: 'nickname')
  final String? nickname;

  @Json<PERSON>ey(name: 'email')
  final String? email;

  @<PERSON>son<PERSON><PERSON>(name: 'phone')
  final String? phone;

  @<PERSON>son<PERSON><PERSON>(name: 'avatar')
  final String? avatar;

  @<PERSON><PERSON><PERSON><PERSON>(name: 'gender')
  final String? gender;

  @<PERSON>son<PERSON><PERSON>(name: 'birthday')
  final String? birthday;

  @Json<PERSON><PERSON>(name: 'realName')
  final String? realName;

  @Json<PERSON>ey(name: 'idCard')
  final String? idCard;

  @JsonKey(name: 'memberLevel')
  final String? memberLevel;

  @JsonKey(name: 'memberExpireTime')
  final int? memberExpireTime;

  @JsonKey(name: 'balance')
  final double? balance;

  @JsonKey(name: 'points')
  final int? points;

  @JsonKey(name: 'status')
  final String status;

  @JsonKey(name: 'createTime')
  final int createTime;

  @JsonKey(name: 'updateTime')
  final int updateTime;

  @JsonKey(name: 'lastLoginTime')
  final int? lastLoginTime;

  @JsonKey(name: 'loginCount')
  final int? loginCount;

  Map<String, dynamic> toJson() => _$UserToJson(this);

  /// 获取显示名称
  String get displayName {
    if (nickname != null && nickname!.isNotEmpty) return nickname!;
    if (username != null && username!.isNotEmpty) return username!;
    if (phone != null && phone!.isNotEmpty) {
      return '${phone!.substring(0, 3)}****${phone!.substring(7)}';
    }
    return '用户$id';
  }

  /// 是否为VIP会员
  bool get isVip => memberLevel == MemberLevel.vip.value || memberLevel == MemberLevel.svip.value;

  /// 是否为SVIP会员
  bool get isSvip => memberLevel == MemberLevel.svip.value;

  /// 会员是否过期
  bool get isMemberExpired {
    if (memberExpireTime == null) return true;
    return DateTime.now().millisecondsSinceEpoch > memberExpireTime!;
  }

  /// 获取性别枚举
  Gender get genderEnum {
    switch (gender) {
      case 'male':
        return Gender.male;
      case 'female':
        return Gender.female;
      default:
        return Gender.unknown;
    }
  }

  /// 获取用户状态枚举
  UserStatus get statusEnum {
    switch (status) {
      case 'active':
        return UserStatus.active;
      case 'inactive':
        return UserStatus.inactive;
      case 'banned':
        return UserStatus.banned;
      case 'deleted':
        return UserStatus.deleted;
      default:
        return UserStatus.inactive;
    }
  }

  /// 获取会员等级枚举
  MemberLevel get memberLevelEnum {
    switch (memberLevel) {
      case 'vip':
        return MemberLevel.vip;
      case 'svip':
        return MemberLevel.svip;
      default:
        return MemberLevel.normal;
    }
  }

  /// 复制并更新用户信息
  User copyWith({
    String? id,
    String? username,
    String? nickname,
    String? email,
    String? phone,
    String? avatar,
    String? gender,
    String? birthday,
    String? realName,
    String? idCard,
    String? memberLevel,
    int? memberExpireTime,
    double? balance,
    int? points,
    String? status,
    int? createTime,
    int? updateTime,
    int? lastLoginTime,
    int? loginCount,
  }) {
    return User(
      id: id ?? this.id,
      username: username ?? this.username,
      nickname: nickname ?? this.nickname,
      email: email ?? this.email,
      phone: phone ?? this.phone,
      avatar: avatar ?? this.avatar,
      gender: gender ?? this.gender,
      birthday: birthday ?? this.birthday,
      realName: realName ?? this.realName,
      idCard: idCard ?? this.idCard,
      memberLevel: memberLevel ?? this.memberLevel,
      memberExpireTime: memberExpireTime ?? this.memberExpireTime,
      balance: balance ?? this.balance,
      points: points ?? this.points,
      status: status ?? this.status,
      createTime: createTime ?? this.createTime,
      updateTime: updateTime ?? this.updateTime,
      lastLoginTime: lastLoginTime ?? this.lastLoginTime,
      loginCount: loginCount ?? this.loginCount,
    );
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is User && other.id == id;
  }

  @override
  int get hashCode => id.hashCode;

  @override
  String toString() {
    return 'User(id: $id, username: $username, nickname: $nickname, phone: $phone)';
  }
}
