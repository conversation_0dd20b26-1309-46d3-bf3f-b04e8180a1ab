<template>
  <view v-if="show" class="fixed inset-0 z-50 flex items-center justify-center px-4">
    <!-- 遮罩 -->
    <view
      class="absolute inset-0 bg-black opacity-30"
      @tap="onClose"
    ></view>
    <!-- 弹窗内容 -->
    <view
      class="relative z-10 p-8 bg-white rounded-2xl shadow-lg w-full max-w-sm animate-popup"
    >
      <view class="flex flex-col items-center">
        <text class="fas fa-exclamation-circle text-warning text-4xl mb-4"></text>
        <text class="text-xl font-bold mb-2">未查询到支付结果</text>
        <text class="text-sm text-text-secondary text-center mb-6">
          如您已支付，请尝试刷新页面查询结果
          <br />
          如您未支付，请重新支付
        </text>
        <view class="flex w-full space-x-4 mt-2">
          <button
            class="flex-1 bg-white text-primary border border-solid border-primary rounded-xl py-2 px-2 text-xs font-medium active:opacity-80 active:translate-y-0.5 transition-transform min-h-9 flex items-center justify-center hover:bg-primary/5 active:border-primary-600"
            @tap="$emit('retry')"
          >
            重新支付
          </button>
          <button
            class="flex-1 bg-gradient-to-r from-primary to-neon text-white rounded-xl py-2 text-xs font-medium shadow-lg active:opacity-80 active:translate-y-0.5 transition-transform min-h-9 flex items-center justify-center"
            @tap="$emit('refresh')"
          >
            刷新
          </button>
        </view>
        <!-- 右上角关闭按钮 -->
        <button
          class="absolute top-3 right-3 p-2 text-2xl text-gray-400 hover:text-gray-600 active:scale-95 transition-transform"
          style="border:0; outline:0; background:none; appearance:none;"
          @tap="onClose"
          aria-label="关闭"
        >
          <van-icon name="close" size="24px" />
        </button>
      </view>
    </view>
  </view>
</template>

<script setup lang="ts">
defineProps<{ show: boolean }>()
const emit = defineEmits(['close', 'retry', 'refresh'])
const onClose = () => emit('close')
</script>

<style scoped>
.fixed {
  position: fixed;
}
.inset-0 {
  top: 0; right: 0; bottom: 0; left: 0;
}
.z-50 {
  z-index: 50;
}
.animate-popup {
  animation: popup-in 0.25s cubic-bezier(.4,2,.6,1) both;
}
@keyframes popup-in {
  0% { transform: scale(0.8); opacity: 0; }
  100% { transform: scale(1); opacity: 1; }
}
</style> 