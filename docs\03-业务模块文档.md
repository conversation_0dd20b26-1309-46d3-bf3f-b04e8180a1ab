# 中古虾(SOKO) 业务模块文档

## 📋 业务模块概览

中古虾平台包含以下核心业务模块：

```mermaid
graph TD
    A[中古虾平台] --> B[电商模块]
    A --> C[回收模块]
    A --> D[用户模块]
    A --> E[基础模块]
    
    B --> B1[首页展示]
    B --> B2[商品浏览]
    B --> B3[购物车]
    B --> B4[订单管理]
    
    C --> C1[回收首页]
    C --> C2[创建回收订单]
    C --> C3[回收订单管理]
    C --> C4[物流跟踪]
    
    D --> D1[个人中心]
    D --> D2[会员体系]
    D --> D3[地址管理]
    D --> D4[认证系统]
    
    E --> E1[登录注册]
    E --> E2[消息中心]
    E --> E3[客服系统]
    E --> E4[设计系统]
```

## 🏠 首页模块 (Home)

### 功能概述
首页作为平台入口，展示核心内容和引导用户进入各个业务流程。

### 页面结构
- **导航栏**: 自定义导航栏，包含消息和公告入口
- **轮播图**: 展示活动和推广内容
- **分类导航**: ACG分类快速入口
- **新品推荐**: 最新上架商品展示
- **优惠券**: 可领取优惠券展示

### 核心功能
```typescript
// 首页数据获取
const getBannerList = async () => {
  const res = await getBanners()
  if (res.code === 200) {
    bannerList.value = res.data.map((item) => {
      return { ...item, url: item.imageUrl }
    })
  }
}

const getnewProductInfo = async () => {
  const res = await getNewProductList({ limit: 10 })
  if (res.code === 200) {
    // 处理新品数据
  }
}
```

### 页面跳转关系
- **公告中心**: `/pages/views/announcement/announcement`
- **消息中心**: `/pages/views/noticesCenter/noticesCenter`
- **商品详情**: `/pages/views/product/detail`
- **商品列表**: `/pages/views/product/products`

## 🛍️ 电商模块

### 1. 商品详情页 (Product Detail)

#### 功能特性
- **商品展示**: 多图轮播、规格参数、详情描述
- **规格选择**: SKU规格选择、库存查询
- **购买操作**: 立即购买、加入购物车
- **收藏功能**: 商品收藏和取消收藏

#### 核心业务逻辑
```typescript
// 商品详情获取
async fetchProductDetails(id) {
  const res = await getProductDetail({ id, status: "U" })
  if (res.data) {
    this.product = res.data
    // 默认选择第一个有效的SKU
    if (this.product.skus && this.product.skus.length > 0) {
      const validSku = this.product.skus.find(sku => sku.status === "U")
      if (validSku) {
        this.selectedSkuId = validSku.id
      }
    }
  }
}

// 规格选择处理
selectSku(skuId) {
  this.selectedSkuId = skuId
  const sku = this.product.skus.find(s => s.id === skuId)
  if (sku) {
    this.initSelectedSpecs(sku)
  }
}
```

#### 数据流向
```mermaid
sequenceDiagram
    participant U as 用户
    participant P as 商品详情页
    participant A as API接口
    participant S as 后端服务
    
    U->>P: 访问商品详情
    P->>A: 获取商品信息
    A->>S: 查询商品数据
    S-->>A: 返回商品信息
    A-->>P: 商品数据
    P-->>U: 展示商品详情
    
    U->>P: 选择规格
    P->>A: 查询SKU库存
    A->>S: 库存查询
    S-->>A: 库存信息
    A-->>P: 库存数据
    P-->>U: 更新价格和库存
```

### 2. 购物车模块 (Cart)

#### 功能特性
- **商品管理**: 商品列表、数量调整、删除操作
- **批量操作**: 全选、批量删除、清空购物车
- **价格计算**: 实时计算总价、优惠金额
- **订单创建**: 选中商品生成订单

#### 核心组件
```vue
<template>
  <view class="cart-container">
    <!-- 购物车列表 -->
    <view v-for="item in cartList" :key="item.id" class="cart-item">
      <checkbox :checked="item.selected" @change="toggleSelect(item)" />
      <image :src="item.productImage" class="product-image" />
      <view class="product-info">
        <text class="product-name">{{ item.productName }}</text>
        <text class="product-price">¥{{ item.price }}</text>
      </view>
      <view class="quantity-control">
        <button @click="decreaseQuantity(item)">-</button>
        <input v-model="item.quantity" type="number" />
        <button @click="increaseQuantity(item)">+</button>
      </view>
    </view>
    
    <!-- 底部操作栏 -->
    <view class="bottom-bar">
      <checkbox :checked="allSelected" @change="toggleSelectAll">全选</checkbox>
      <text class="total-price">合计: ¥{{ totalPrice }}</text>
      <button @click="createOrder" class="checkout-btn">结算</button>
    </view>
  </view>
</template>
```

#### 状态管理
```typescript
// 购物车状态计算
const selectedItems = computed(() => {
  return cartList.value.filter(item => item.selected)
})

const totalPrice = computed(() => {
  return selectedItems.value.reduce((total, item) => {
    return total + (item.price * item.quantity)
  }, 0)
})

const selectedCount = computed(() => {
  return selectedItems.value.length
})
```

## ♻️ 回收模块 (核心特色业务)

### 1. 回收首页 (Recycle Home)

#### 功能概述
回收模块的入口页面，展示回收流程和用户的回收记录。

#### 页面结构
- **流程介绍**: 4步回收流程展示
- **快速入口**: 开始回收按钮
- **最近订单**: 用户最近的回收订单
- **空状态**: 无回收记录时的引导

#### 核心数据结构
```typescript
interface ProcessStep {
  title: string
  desc: string
}

interface RecentOrder {
  id: string
  brandName: string
  model: string
  orderStatusDesc: string
  finalPrice?: number
  mainImage?: string
}

const processSteps = ref<ProcessStep[]>([
  { title: '提交申请', desc: '填写商品信息并上传图片' },
  { title: '专业评估', desc: '24小时内完成评估报价' },
  { title: '确认寄送', desc: '满意报价后寄送商品' },
  { title: '完成交易', desc: '收货确认后立即打款' }
])
```

### 2. 创建回收订单 (Create Order)

#### 业务流程
```mermaid
flowchart TD
    A[开始回收] --> B[选择商品分类]
    B --> C[填写商品信息]
    C --> D[上传商品图片]
    D --> E[填写联系信息]
    E --> F[提交回收申请]
    F --> G[等待专业评估]
    G --> H[收到评估报价]
    H --> I{是否接受报价}
    I -->|接受| J[确认寄送]
    I -->|拒绝| K[订单结束]
    J --> L[寄送商品]
    L --> M[平台收货检验]
    M --> N[确认最终价格]
    N --> O[完成交易打款]
```

#### 数据模型
```typescript
export interface CreateOrderRequest {
  productName: string
  productDesc: string
  productModel?: string
  productCategory: string
  contactPerson: string
  contactPhone: string
  expectedPrice: number
  condition: string
  imageFiles: string[]
}

export interface RecycleOrder {
  id: string
  userId: string
  brandName: string
  model: string
  categoryName: string
  conditionDescription: string
  estimatedPrice: number
  finalPrice?: number
  orderStatus: string
  orderStatusDesc: string
  createTime: number
  updateTime: number
  files?: Array<{
    id: string
    url: string
    thumbnailUrl: string
  }>
}
```

### 3. 回收订单管理

#### 订单状态流转
```mermaid
stateDiagram-v2
    [*] --> PENDING: 提交申请
    PENDING --> EVALUATING: 开始评估
    EVALUATING --> QUOTED: 评估完成
    QUOTED --> CONFIRMED: 确认寄送
    QUOTED --> CANCELLED: 拒绝报价
    CONFIRMED --> SHIPPED: 用户寄送
    SHIPPED --> RECEIVED: 平台收货
    RECEIVED --> COMPLETED: 交易完成
    RECEIVED --> RETURN_REQUESTED: 申请退回
    RETURN_REQUESTED --> RETURNED: 退回完成
    
    PENDING --> CANCELLED: 用户取消
    CONFIRMED --> CANCELLED: 取消寄送
```

#### 核心业务逻辑 (Composables)
```typescript
// src/composables/useRecycle.ts
export const useRecycle = () => {
  const loading = ref(false)
  const currentOrder = ref<RecycleOrder | null>(null)

  // 创建回收订单
  const createOrder = async (orderData: CreateOrderRequest): Promise<boolean> => {
    loading.value = true
    try {
      const response = await recycleApi.createOrder(orderData)
      if (response.code === 200) {
        uni.showToast({
          title: '提交成功，等待评估',
          icon: 'success'
        })
        return true
      }
    } catch (error: any) {
      uni.showToast({
        title: error.message || '提交失败',
        icon: 'none'
      })
    } finally {
      loading.value = false
    }
    return false
  }

  // 取消订单
  const cancelOrder = async (orderId: string): Promise<boolean> => {
    try {
      await recycleApi.cancelOrder(orderId)
      uni.showToast({
        title: '订单已取消',
        icon: 'success'
      })
      return true
    } catch (error: any) {
      uni.showToast({
        title: error.message || '取消失败',
        icon: 'none'
      })
      return false
    }
  }

  // 确认寄送
  const confirmShipment = async (orderId: string): Promise<boolean> => {
    try {
      await recycleApi.confirmShipment(orderId)
      if (currentOrder.value && currentOrder.value.id === orderId) {
        currentOrder.value.orderStatus = 'CONFIRMED'
        currentOrder.value.orderStatusDesc = '已确认寄送'
      }
      return true
    } catch (error: any) {
      uni.showToast({
        title: error.message || '确认失败',
        icon: 'none'
      })
      return false
    }
  }

  return {
    loading,
    currentOrder,
    createOrder,
    cancelOrder,
    confirmShipment
  }
}
```

### 4. 回收业务组件

#### RecycleOrderCard 组件
```vue
<template>
  <view class="recycle-order-card">
    <view class="order-header">
      <text class="order-id">订单号: {{ order.id }}</text>
      <RecycleStatusBadge :status="order.orderStatus" />
    </view>
    
    <view class="order-content">
      <image v-if="order.mainImage" :src="order.mainImage" class="product-image" />
      <view class="product-info">
        <text class="product-name">{{ order.brandName }} {{ order.model }}</text>
        <text class="category">{{ order.categoryName }}</text>
        <text class="condition">{{ order.conditionDescription }}</text>
      </view>
    </view>
    
    <view class="order-footer">
      <text class="price">
        <text v-if="order.finalPrice">最终价格: ¥{{ order.finalPrice }}</text>
        <text v-else>预估价格: ¥{{ order.estimatedPrice }}</text>
      </text>
      <text class="time">{{ formatTime(order.updateTime) }}</text>
    </view>
  </view>
</template>
```

#### RecycleImageUploader 组件
专门用于回收订单的图片上传组件，支持多图上传、图片预览、删除等功能。

## 👤 用户模块

### 1. 个人中心 (Profile)

#### 功能结构
- **用户信息**: 头像、昵称、会员等级
- **订单管理**: 购买订单、回收订单入口
- **账户设置**: 地址管理、密码修改、手机号修改
- **服务功能**: 客服联系、意见反馈、关于我们

### 2. 会员体系 (Member)

#### 会员等级
- **普通用户**: 基础功能
- **VIP会员**: 专享优惠、优先客服
- **SVIP会员**: 更多特权和服务

#### 会员权益
- **积分系统**: 购买和回收获得积分
- **优惠券**: 专属优惠券和折扣
- **专属服务**: VIP客服、快速处理

### 3. 地址管理

#### 功能特性
- **地址列表**: 显示所有收货地址
- **默认地址**: 设置默认收货地址
- **地址编辑**: 新增、修改、删除地址
- **地区选择**: 省市区三级联动选择

## 🔐 登录注册模块

### 登录方式
- **密码登录**: 用户名/手机号 + 密码
- **验证码登录**: 手机号 + 短信验证码
- **第三方登录**: 微信、QQ等社交账号

### 安全机制
```typescript
// 密码加密登录
export const loginWithPassword = async (params) => {
  const { username, password } = params
  
  try {
    // 获取公钥加密密码
    const publicKey = await publicKeyManager.getPublicKey()
    const encrypt = new JSEncrypt()
    encrypt.setPublicKey(publicKey)
    const encryptedPassword = encrypt.encrypt(password)
    
    // 调用登录接口
    return login({
      username,
      password: encryptedPassword
    })
  } catch (error) {
    console.error("登录失败", error)
    return {
      data: {
        success: false,
        message: error.message || "登录失败"
      }
    }
  }
}
```

## 📊 数据流向和状态管理

### 全局状态 (Vuex Store)
```javascript
const store = createStore({
  state: {
    // 用户状态
    $userInfo: getInitialUserInfo(),
    $isLoggedIn: false,
    $token: uni.getStorageSync("token"),
    
    // 业务状态
    $certificationStatus: null,
    $systemConfig: [],
    
    // 通信状态
    $websocketConnected: false,
    $warningBadge: 0
  },
  
  mutations: {
    SET_USER_INFO(state, userInfo) {
      state.$userInfo = userInfo
    },
    SET_LOGIN_STATUS(state, status) {
      state.$isLoggedIn = status
    }
  }
})
```

### 页面间数据传递
- **路由参数**: 通过URL参数传递简单数据
- **全局状态**: 通过Vuex管理共享状态
- **本地存储**: 持久化重要数据
- **事件总线**: 组件间通信

---

*本文档最后更新时间: 2025年6月*
