import 'dart:io';

import 'package:dio/dio.dart';
import 'package:flutter/foundation.dart';

import 'package:soko/core/exceptions/app_exception.dart';
import 'package:soko/core/exceptions/network_exception.dart';

/// 错误处理工具类
class ErrorHandler {
  // 私有构造函数
  ErrorHandler._();

  /// 处理异常并返回用户友好的错误信息
  static String handleError(dynamic error) {
    if (error is AppException) {
      return error.message;
    }

    if (error is NetworkException) {
      return error.userFriendlyMessage;
    }

    if (error is DioException) {
      return _handleDioError(error);
    }

    if (error is SocketException) {
      return '网络连接失败，请检查网络设置';
    }

    if (error is FormatException) {
      return '数据格式错误';
    }

    if (error is TypeError) {
      return '数据类型错误';
    }

    // 在调试模式下显示详细错误信息
    if (kDebugMode) {
      return error.toString();
    }

    return '操作失败，请稍后重试';
  }

  /// 处理Dio错误
  static String _handleDioError(DioException error) {
    switch (error.type) {
      case DioExceptionType.connectionTimeout:
        return '连接超时，请检查网络连接';
      case DioExceptionType.sendTimeout:
        return '请求发送超时';
      case DioExceptionType.receiveTimeout:
        return '响应接收超时';
      case DioExceptionType.badResponse:
        return _handleResponseError(error);
      case DioExceptionType.cancel:
        return '请求已取消';
      case DioExceptionType.connectionError:
        return '网络连接失败，请检查网络设置';
      case DioExceptionType.badCertificate:
        return 'SSL证书验证失败';
      case DioExceptionType.unknown:
      default:
        return '网络请求失败';
    }
  }

  /// 处理HTTP响应错误
  static String _handleResponseError(DioException error) {
    final statusCode = error.response?.statusCode;
    final responseData = error.response?.data;

    // 尝试从响应中获取错误信息
    if (responseData is Map<String, dynamic>) {
      final message = responseData['message'] ?? responseData['msg'];
      if (message != null && message.toString().isNotEmpty) {
        return message.toString();
      }
    }

    // 根据状态码返回默认错误信息
    switch (statusCode) {
      case 400:
        return '请求参数错误';
      case 401:
        return '未授权，请重新登录';
      case 403:
        return '禁止访问';
      case 404:
        return '请求的资源不存在';
      case 405:
        return '请求方法不允许';
      case 408:
        return '请求超时';
      case 409:
        return '请求冲突';
      case 422:
        return '请求参数验证失败';
      case 429:
        return '请求过于频繁，请稍后再试';
      case 500:
        return '服务器内部错误';
      case 502:
        return '网关错误';
      case 503:
        return '服务暂时不可用';
      case 504:
        return '网关超时';
      default:
        return '网络请求失败（$statusCode）';
    }
  }

  /// 记录错误日志
  static void logError(dynamic error, {StackTrace? stackTrace}) {
    if (kDebugMode) {
      print('🔴 Error: $error');
      if (stackTrace != null) {
        print('🔴 StackTrace: $stackTrace');
      }
    }

    // 在生产环境中，可以将错误发送到错误监控服务
    // 例如：Crashlytics、Sentry等
  }

  /// 将异常转换为AppException
  static AppException convertToAppException(dynamic error) {
    if (error is AppException) {
      return error;
    }

    if (error is NetworkException) {
      return BusinessException(
        message: error.userFriendlyMessage,
        code: error.code,
        data: error.data,
      );
    }

    if (error is DioException) {
      return BusinessException(
        message: _handleDioError(error),
        code: error.response?.statusCode?.toString(),
        data: error.response?.data,
      );
    }

    if (error is SocketException) {
      return const BusinessException(
        message: '网络连接失败，请检查网络设置',
        code: 'SOCKET_ERROR',
      );
    }

    if (error is FormatException) {
      return const ParseException(
        message: '数据格式错误',
        code: 'FORMAT_ERROR',
      );
    }

    return UnknownException(
      message: kDebugMode ? error.toString() : '操作失败，请稍后重试',
      code: 'UNKNOWN_ERROR',
    );
  }

  /// 检查是否为网络错误
  static bool isNetworkError(dynamic error) {
    return error is NetworkException ||
        error is SocketException ||
        (error is DioException &&
            (error.type == DioExceptionType.connectionError ||
                error.type == DioExceptionType.connectionTimeout ||
                error.type == DioExceptionType.receiveTimeout ||
                error.type == DioExceptionType.sendTimeout));
  }

  /// 检查是否为认证错误
  static bool isAuthError(dynamic error) {
    if (error is AuthException) return true;
    if (error is NetworkException) return error.isAuthError;
    if (error is DioException) {
      return error.response?.statusCode == 401 || error.response?.statusCode == 403;
    }
    return false;
  }

  /// 检查是否为服务器错误
  static bool isServerError(dynamic error) {
    if (error is NetworkException) return error.isServerError;
    if (error is DioException) {
      final statusCode = error.response?.statusCode;
      return statusCode != null && statusCode >= 500;
    }
    return false;
  }
}
