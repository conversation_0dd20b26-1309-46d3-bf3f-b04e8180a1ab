<template>
  <view class="flex flex-col min-h-screen bg-slate-50">
    <NavBar title="优惠卷" bgColor="#FFFFFF" :showBack="false">
      <!-- 自定义左侧内容 -->
      <template #left>
        <view class="rounded-full p-2" @tap="goBack">
          <i class="fas fa-arrow-left text-text-primary"></i>
        </view>
      </template>
    </NavBar>

    <!-- 主内容区域 -->
    <van-pull-refresh v-model="refreshLoading" @refresh="onRefresh">
      <view class="flex-1 p-4 space-y-4">
        <scroll-view
          scroll-x
          class="bg-white border-b whitespace-nowrap"
          :show-scrollbar="false"
        >
          <view class="flex px-4 h-12 items-center flex-nowrap">
            <view
              v-for="(tab, index) in tabs"
              :key="index"
              :id="`tab-${tab.value}`"
              :class="[
                'px-4 py-1.5 mx-1 rounded-full inline-block whitespace-nowrap transition-all duration-300',
                activeTab === tab.value
                  ? 'bg-primary text-white shadow-sm'
                  : 'text-text-secondary bg-slate-100'
              ]"
              @click="filterListByTab(tab.value)"
            >
              <text class="text-sm font-medium whitespace-nowrap">{{
                tab.label
              }}</text>
            </view>
          </view>
        </scroll-view>

        <!-- 空状态 -->
        <view
          v-if="!vouchers.length"
          class="flex flex-col items-center justify-center py-10"
        >
          <view
            class="w-32 h-32 rounded-full bg-slate-100 flex items-center justify-center mb-4"
          >
            <van-icon name="refund-o" size="60" color="#666666" />
          </view>

          <text class="text-xl font-semibold mb-2 text-text-primary"
            >暂无优惠券</text
          >
          <text class="text-text-secondary text-center mb-6"
            >您还没有可用的优惠券</text
          >
          <view
            @click="goToShop"
            class="bg-primary text-white rounded-full px-6 py-2 font-medium"
          >
            去商城逛逛
          </view>
        </view>

        <!-- 优惠券列表 -->
        <view v-else class="space-y-4 pb-safe">
          <van-list
            v-model="listLoading"
            finished-text="没有更多了"
            :finished="finished"
            @load="loadList()"
          >
            <view
              v-for="voucher in vouchers"
              :key="voucher.id"
              class="bg-white rounded-lg overflow-hidden shadow-sm hover:shadow-md transition-shadow duration-300 mb-4"
            >
              <!-- 优惠券头部 -->
              <view class="p-4 border-b border-slate-100 pb-0">
                <text class="text-lg font-semibold text-text-primary mb-2">
                  {{ voucher.name || "优惠券" }}
                </text>
                <view class="flex justify-between items-center mb-2">
                  <text class="text-2xl font-bold text-primary"
                    >¥{{ voucher.value || "-" }}</text
                  >
                  <view
                    :class="[
                      'px-3 py-1 rounded-full text-sm font-medium',
                      getStatusClass(voucher.statusName)
                    ]"
                  >
                    {{ voucher.statusName }}
                  </view>
                </view>
                <text class="text-sm text-text-secondary">{{
                  voucher.description
                }}</text>
              </view>

              <!-- 优惠券信息 -->
              <view class="p-4 space-y-2">
                <view class="flex items-center">
                  <van-icon
                    name="clock-o"
                    class="mr-2"
                    size="14"
                    color="#666666"
                  />
                  <text class="text-sm text-text-secondary"
                    >有效期至：{{
                      voucher.validEndTime
                        ? moment(voucher.validEndTime).format("YYYY-MM-DD")
                        : "-"
                    }}</text
                  >
                </view>
                <view class="flex items-center">
                  <text class="text-sm text-text-secondary">{{
                    voucher.condition
                  }}</text>
                </view>
                <view class="flex items-center">
                  <van-icon
                    name="paid"
                    class="mr-2"
                    size="14"
                    color="#666666"
                  />
                  <text class="text-sm text-text-secondary"
                    >使用范围：{{ `${voucher.useRangeName}` || "-" }}</text
                  >
                </view>
                <view
                  class="flex items-center flex-wrap"
                  v-if="voucher.useRangeName === '分类'"
                >
                  <van-icon
                    name="tag-o"
                    class="mr-2"
                    size="14"
                    color="#666666"
                  />
                  <text
                    class="text-sm text-text-secondary whitespace-normal break-words"
                  >
                    {{ `(${voucher.category})` || "-" }}
                  </text>
                </view>
              </view>

              <!-- 使用按钮 -->
              <view
                v-if="voucher.statusName === '已领取'"
                class="p-4 border-t border-slate-100"
              >
                <view
                  @click="useVoucher(voucher)"
                  class="bg-primary hover:bg-primary-dark text-white rounded-full px-6 py-2 text-center text-sm font-medium transition-colors duration-300"
                >
                  立即使用
                </view>
              </view>
            </view>
          </van-list>
        </view>
      </view>
    </van-pull-refresh>
  </view>
</template>

<script setup lang="ts">
import { reactive, ref } from "vue"
import moment from "moment"
import NavBar from "@/components/NavBar.vue"
import { getCouponsPage } from "@/api/api"
import { onLoad } from "@dcloudio/uni-app"
import { USERANGE_TYPE, VOUCHERSTATUS_TYPE } from "@/enum"
// 优惠券状态类型
type VoucherStatus =
  | "未领取"
  | "已领取"
  | "已使用"
  | "已过期"
  | "已冻结"
  | "已作废"

// 优惠券类型
interface Voucher {
  id: string
  name: string
  value: number
  description: string
  validEndTime: number
  condition: string
  statusName: VoucherStatus
  useRangeName: string
  category: string
}

const vouchers = ref<Voucher[]>([])

const pageNoParams = reactive({
  pageNum: 0,
  pageSize: 10
})
const listLoading = ref(false)
const finished = ref(false)
const totalNum = ref(0)
const activeTab = ref("CLAIMED")
const refreshLoading = ref(false)
// 标签选项
const tabs = [
  { value: "CLAIMED", label: "已领取" },
  { value: "UNCLAIMED", label: "未领取" },
  { value: "USED", label: "已使用" },
  { value: "EXPIRED", label: "已过期" },
  { value: "FROZEN", label: "已冻结" },
  { value: "VOID", label: "售后/已作废" }
]
// 获取状态样式
function getStatusClass(statusName: VoucherStatus): string {
  switch (statusName) {
    case "已领取":
      return "bg-success text-white"
    case "已使用":
      return "bg-gray-200 text-text-disabled"
    case "已过期":
      return "bg-destructive text-white"
    default:
      return "bg-gray-200 text-text-disabled"
  }
}

onLoad(() => {
  loadList()
})

// 使用优惠券
function useVoucher(voucher: Voucher) {
  uni.switchTab({
    url: "/pages/views/shop/shop"
  })
}

// 导航方法
function goBack() {
  uni.navigateBack()
}

function goToShop() {
  uni.switchTab({
    url: "/pages/views/shop/shop"
  })
}

const loadList = () => {
  if (!finished.value && !listLoading.value) {
    listLoading.value = true
    pageNoParams.pageNum++
    getCouponsList(pageNoParams)
  }
}

const filterListByTab = (tabValue: string) => {
  activeTab.value = tabValue
  finished.value = false
  pageNoParams.pageNum = 0
  pageNoParams.pageSize = 10
  if (!finished.value && !listLoading.value) {
    listLoading.value = true
    pageNoParams.pageNum++
    getCouponsList(pageNoParams)
  }
}

const getCouponsList = async (pageParams: {
  pageNum: number
  pageSize: number
}) => {
  listLoading.value = true
  const params = {
    ...pageParams,
    status: activeTab.value
  }
  const res = await getCouponsPage(params)
  const resData = res?.data
  if (res.code === 200) {
    totalNum.value = resData.total
    const coverList = resData.records?.map((item: any) => {
      return {
        ...item.couponInfo,
        // description: `满${item.couponInfo.value || "-"}元可用`,
        statusName: VOUCHERSTATUS_TYPE.find(
          (statusItem) => statusItem.value === item.status
        )?.label,
        useRangeName: USERANGE_TYPE.find(
          (statusItem) => statusItem.value === item.couponInfo.useRange
        )?.label,
        category:
          item.couponInfo.couponRanges
            ?.map((range: any) => range.rangeName)
            .join(", ") || "-"
      }
    })
    vouchers.value =
      pageParams.pageNum === 1 ? coverList : [...vouchers.value, ...coverList]
    listLoading.value = false
    if (vouchers.value.length >= totalNum.value) {
      finished.value = true
    }
  } else {
    uni.showToast({
      title: res.message,
      icon: "none"
    })
  }
}

const onRefresh = () => {
  filterListByTab(activeTab.value)
  refreshLoading.value = false
}
</script>

<style scoped lang="scss"></style>
