<template>
	<view class="up-avatar-group">
		<view
		    class="up-avatar-group__item"
		    v-for="(item, index) in showUrl"
		    :key="index"
		    :style="{
				marginLeft: index === 0 ? 0 : addUnit(-parseFloat(size.toString()) * parseFloat(gap.toString()))
			}"
		>
			<up-avatar
			    :size="size"
			    :shape="shape"
			    :mode="mode"
			    :src="src(item)"
			></up-avatar>
			<view
			    class="up-avatar-group__item__show-more"
			    v-if="showMore && index === showUrl.length - 1 && (urls.length > parseInt(maxCount.toString()) || parseInt(extraValue.toString()) > 0)"
				@tap="clickHandler"
			>
				<up-text
				    color="#ffffff"
				    :size="parseInt(size.toString()) * 0.4"
				    :text="textStr"
					align="center"
					:style="{justifyContent: 'center'}"
				></up-text>
			</view>
		</view>
	</view>
</template>

<script>
	import { propsAvatarGroup } from './props';
	import { mpMixin } from '../../libs/mixin/mpMixin';
	import { mixin } from '../../libs/mixin/mixin';
	import { addUnit, addStyle } from '../../libs/function/index';
	/**
	 * AvatarGroup  头像组
	 * @description 本组件一般用于展示头像的地方，如个人中心，或者评论列表页的用户头像展示等场所。
	 * @tutorial https://ijry.github.io/uview-plus/components/avatar.html
	 * 
	 * @property {Array}           urls     头像图片组 （默认 [] ）
	 * @property {String | Number} maxCount 最多展示的头像数量 （ 默认 5 ）
	 * @property {String}          shape    头像形状（ 'circle' (默认) | 'square' ）
	 * @property {String}          mode     图片裁剪模式（默认 'scaleToFill' ）
	 * @property {Boolean}         showMore 超出maxCount时是否显示查看更多的提示 （默认 true ）
	 * @property {String | Number} size      头像大小 （默认 40 ）
	 * @property {String}          keyName  指定从数组的对象元素中读取哪个属性作为图片地址 
	 * @property {String | Number} gap      头像之间的遮挡比例（0.4代表遮挡40%）  （默认 0.5 ）
	 * @property {String | Number} extraValue  需额外显示的值
	 * @event    {Function}        showMore 头像组更多点击
	 * @example  <up-avatar-group :urls="urls" size="35" gap="0.4" ></up-avatar-group>
	 */
	export default {
		name: 'up-avatar-group',
		mixins: [mpMixin, mixin, propsAvatarGroup],
		data() {
			return {

			}
		},
		computed: {
			textStr(): string {
				let str = this.extraValue.toString() != ''
					? this.extraValue.toString()
					: (this.urls.length - this.showUrl.length).toString()
				return '+' + str
			},
			showUrl(): Array<any> {
				return this.urls.slice(0, parseInt(this.maxCount.toString()))
			}
		},
		emits: ["showMore"],
		methods: {
			addUnit(val: any): string {
				return addUnit(val)
			},
			src(item: any): string {
				if (typeof item == 'object') {
					let item1 = item as UTSJSONObject
					return (this.keyName != '' && item1.getString(this.keyName) != null)
						? item1.getString(this.keyName)!
						: item1.getString('url')!
				} else {
					return item.toString()
				}
			},
			clickHandler() {
				this.$emit('showMore')
			}
		},
	}
</script>

<style lang="scss" scoped>
	@import "../../libs/css/components.scss";

	.up-avatar-group {
		@include flex;

		&__item {
			margin-left: -10px;
			position: relative;

			&--no-indent {
				// 如果你想质疑作者不会使用:first-child，说明你太年轻，因为nvue不支持
				margin-left: 0;
			}

			&__show-more {
				position: absolute;
				top: 0;
				bottom: 0;
				left: 0;
				right: 0;
				background-color: rgba(0, 0, 0, 0.3);
				@include flex;
				align-items: center;
				justify-content: center;
				border-radius: 100px;
			}
		}
	}
</style>
