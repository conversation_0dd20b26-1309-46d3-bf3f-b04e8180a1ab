// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

CategoryItem _$CategoryItemFromJson(Map<String, dynamic> json) => CategoryItem(
      id: json['id'] as String,
      name: json['name'] as String,
      description: json['description'] as String?,
      icon: json['icon'] as String?,
      sort: (json['sort'] as num).toInt(),
      isActive: json['isActive'] as bool,
    );

Map<String, dynamic> _$CategoryItemToJson(CategoryItem instance) =>
    <String, dynamic>{
      'id': instance.id,
      'name': instance.name,
      'description': instance.description,
      'icon': instance.icon,
      'sort': instance.sort,
      'isActive': instance.isActive,
    };

ConditionOption _$ConditionOptionFromJson(Map<String, dynamic> json) =>
    ConditionOption(
      id: json['id'] as String,
      name: json['name'] as String,
      description: json['description'] as String?,
      priceMultiplier: (json['priceMultiplier'] as num).toDouble(),
      sort: (json['sort'] as num).toInt(),
    );

Map<String, dynamic> _$ConditionOptionToJson(ConditionOption instance) =>
    <String, dynamic>{
      'id': instance.id,
      'name': instance.name,
      'description': instance.description,
      'priceMultiplier': instance.priceMultiplier,
      'sort': instance.sort,
    };

BrandInfo _$BrandInfoFromJson(Map<String, dynamic> json) => BrandInfo(
      id: json['id'] as String,
      name: json['name'] as String,
      logo: json['logo'] as String?,
      categoryId: json['categoryId'] as String,
      isPopular: json['isPopular'] as bool,
      sort: (json['sort'] as num).toInt(),
    );

Map<String, dynamic> _$BrandInfoToJson(BrandInfo instance) => <String, dynamic>{
      'id': instance.id,
      'name': instance.name,
      'logo': instance.logo,
      'categoryId': instance.categoryId,
      'isPopular': instance.isPopular,
      'sort': instance.sort,
    };

ProductModel _$ProductModelFromJson(Map<String, dynamic> json) => ProductModel(
      id: json['id'] as String,
      name: json['name'] as String,
      brandId: json['brandId'] as String,
      categoryId: json['categoryId'] as String,
      basePrice: (json['basePrice'] as num).toDouble(),
      specifications: json['specifications'] as Map<String, dynamic>?,
      isPopular: json['isPopular'] as bool,
      sort: (json['sort'] as num).toInt(),
    );

Map<String, dynamic> _$ProductModelToJson(ProductModel instance) =>
    <String, dynamic>{
      'id': instance.id,
      'name': instance.name,
      'brandId': instance.brandId,
      'categoryId': instance.categoryId,
      'basePrice': instance.basePrice,
      'specifications': instance.specifications,
      'isPopular': instance.isPopular,
      'sort': instance.sort,
    };
