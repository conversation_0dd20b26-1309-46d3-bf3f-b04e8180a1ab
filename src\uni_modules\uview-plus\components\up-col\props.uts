/*
 * <AUTHOR> jry
 * @Description  : uview-plus component's props mixin file
 * @version      : 4.0
 * @Date         : 2024-04-22 16:44:21
 * @LastAuthor   : jry
 * @lastTime     : 2024-08-28 12:27:20
 */
import { defineMixin } from '../../libs/vue'
import defProps from './col'
let crtProp = defProps['col'] as UTSJSONObject

export const propsCol = defineMixin({
    props: {
        // 占父容器宽度的多少等分，总分为12份
        span: {
            type: [String, Number],
            default: crtProp['span']
        },
        // 指定栅格左侧的间隔数(总12栏)
        offset: {
            type: [String, Number],
            default: crtProp['offset']
        },
        // 水平排列方式，可选值为`start`(或`flex-start`)、`end`(或`flex-end`)、`center`、`around`(或`space-around`)、`between`(或`space-between`)
        justify: {
            type: String,
            default: crtProp['justify']
        },
        // 垂直对齐方式，可选值为top、center、bottom、stretch
        align: {
            type: String,
            default: crtProp['align']
        },
        // 文字对齐方式
        textAlign: {
            type: String,
            default: crtProp['textAlign']
        }
    }
})
