import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:go_router/go_router.dart';

import 'package:soko/shared/presentation/widgets/custom_app_bar.dart';
import 'package:soko/shared/presentation/widgets/custom_button.dart';
import 'package:soko/features/recycle/presentation/providers/recycle_create_provider.dart';
import 'package:soko/features/recycle/presentation/widgets/recycle_product_step.dart';
import 'package:soko/features/recycle/presentation/widgets/recycle_create_images_step.dart';
import 'package:soko/features/recycle/presentation/widgets/recycle_create_contact_step.dart';
import 'package:soko/features/recycle/presentation/widgets/recycle_create_summary_step.dart';

/// 回收订单创建页面
class RecycleCreatePage extends ConsumerStatefulWidget {

  const RecycleCreatePage({
    super.key,
    this.categoryId,
  });
  final String? categoryId;

  @override
  ConsumerState<RecycleCreatePage> createState() => _RecycleCreatePageState();
}

class _RecycleCreatePageState extends ConsumerState<RecycleCreatePage> {
  final PageController _pageController = PageController();

  @override
  void initState() {
    super.initState();
    // 如果有预选分类，设置到状态中
    if (widget.categoryId != null) {
      WidgetsBinding.instance.addPostFrameCallback((_) {
        ref
            .read(recycleCreateProvider.notifier)
            .setCategoryId(widget.categoryId!);
      });
    }
  }

  @override
  void dispose() {
    _pageController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final createState = ref.watch(recycleCreateProvider);

    return Scaffold(
      appBar: CustomAppBar(
        title: '创建回收订单',
        actions: [
          // 步骤指示器
          Padding(
            padding: EdgeInsets.only(right: 16.w),
            child: Center(
              child: Text(
                '${createState.currentStep + 1}/4',
                style: TextStyle(
                  fontSize: 14.sp,
                  color: Colors.grey[600],
                ),
              ),
            ),
          ),
        ],
      ),
      body: Column(
        children: [
          // 进度指示器
          _buildProgressIndicator(createState),

          // 步骤内容
          Expanded(
            child: PageView(
              controller: _pageController,
              physics: const NeverScrollableScrollPhysics(), // 禁用滑动
              children: [
                // 第1步：商品信息
                RecycleCreateProductStep(
                  productName: createState.formData.productName,
                  productDescription: createState.formData.productDescription,
                  onProductInfoChanged: (name, description) {
                    ref
                        .read(recycleCreateProvider.notifier)
                        .setProductInfo(name, description);
                  },
                ),

                // 第2步：上传图片
                RecycleCreateImagesStep(
                  imageFiles: createState.formData.imageFiles,
                  onImagesChanged: (images) {
                    ref
                        .read(recycleCreateProvider.notifier)
                        .setImageFiles(images);
                  },
                ),

                // 第3步：联系信息
                RecycleCreateContactStep(
                  contactName: createState.formData.contactName,
                  contactPhone: createState.formData.contactPhone,
                  pickupAddress: createState.formData.pickupAddress,
                  onContactInfoChanged: (name, phone, address) {
                    ref
                        .read(recycleCreateProvider.notifier)
                        .setContactInfo(name, phone, address);
                  },
                ),

                // 第4步：订单摘要
                RecycleCreateSummaryStep(
                  formData: createState.formData,
                ),
              ],
            ),
          ),

          // 底部操作栏
          _buildBottomActions(createState),
        ],
      ),
    );
  }

  /// 构建进度指示器
  Widget _buildProgressIndicator(RecycleCreateState state) {
    return Container(
      padding: EdgeInsets.all(16.w),
      child: Row(
        children: List.generate(4, (index) {
          final isActive = index <= state.currentStep;
          final isCompleted = index < state.currentStep;

          return Expanded(
            child: Row(
              children: [
                // 步骤圆点
                Container(
                  width: 24.w,
                  height: 24.w,
                  decoration: BoxDecoration(
                    color: isCompleted
                        ? Colors.green
                        : isActive
                            ? Colors.blue
                            : Colors.grey[300],
                    borderRadius: BorderRadius.circular(12.r),
                  ),
                  child: Center(
                    child: isCompleted
                        ? Icon(
                            Icons.check,
                            color: Colors.white,
                            size: 14.w,
                          )
                        : Text(
                            '${index + 1}',
                            style: TextStyle(
                              fontSize: 12.sp,
                              color: isActive ? Colors.white : Colors.grey[600],
                              fontWeight: FontWeight.w500,
                            ),
                          ),
                  ),
                ),

                // 连接线
                if (index < 3)
                  Expanded(
                    child: Container(
                      height: 2.h,
                      color: isCompleted ? Colors.green : Colors.grey[300],
                      margin: EdgeInsets.symmetric(horizontal: 4.w),
                    ),
                  ),
              ],
            ),
          );
        }),
      ),
    );
  }

  /// 构建底部操作栏
  Widget _buildBottomActions(RecycleCreateState state) {
    return Container(
      padding: EdgeInsets.all(16.w),
      decoration: BoxDecoration(
        color: Colors.white,
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.1),
            blurRadius: 4,
            offset: const Offset(0, -2),
          ),
        ],
      ),
      child: SafeArea(
        child: Row(
          children: [
            // 上一步按钮
            if (state.currentStep > 0)
              Expanded(
                child: CustomButton(
                  text: '上一步',
                  onPressed: _previousStep,
                  type: ButtonType.outline,
                ),
              ),

            if (state.currentStep > 0) SizedBox(width: 12.w),

            // 下一步/提交按钮
            Expanded(
              flex: state.currentStep > 0 ? 1 : 2,
              child: CustomButton(
                text: state.currentStep == 3 ? '提交订单' : '下一步',
                onPressed: state.currentStep == 3
                    ? _submitOrder
                    : _canProceedToNextStep(state)
                        ? _nextStep
                        : null,
                loading: state.createOrderState.isLoading,
              ),
            ),
          ],
        ),
      ),
    );
  }

  /// 检查是否可以进入下一步
  bool _canProceedToNextStep(RecycleCreateState state) {
    switch (state.currentStep) {
      case 0: // 商品信息
        return state.formData.customProductName?.isNotEmpty ?? false;
      case 1: // 图片上传
        return state.formData.imageFiles.isNotEmpty;
      case 2: // 联系信息
        return (state.formData.contactName?.isNotEmpty ?? false) &&
            (state.formData.contactPhone?.isNotEmpty ?? false) &&
            (state.formData.pickupAddress?.isNotEmpty ?? false);
      case 3: // 订单摘要
        return state.formData.isValid;
      default:
        return false;
    }
  }

  /// 下一步
  void _nextStep() {
    final currentStep = ref.read(recycleCreateProvider).currentStep;
    if (currentStep < 3) {
      ref.read(recycleCreateProvider.notifier).setCurrentStep(currentStep + 1);
      _pageController.nextPage(
        duration: const Duration(milliseconds: 300),
        curve: Curves.easeInOut,
      );
    }
  }

  /// 上一步
  void _previousStep() {
    final currentStep = ref.read(recycleCreateProvider).currentStep;
    if (currentStep > 0) {
      ref.read(recycleCreateProvider.notifier).setCurrentStep(currentStep - 1);
      _pageController.previousPage(
        duration: const Duration(milliseconds: 300),
        curve: Curves.easeInOut,
      );
    }
  }

  /// 提交订单
  Future<void> _submitOrder() async {
    final success =
        await ref.read(recycleCreateProvider.notifier).createOrder();

    if (success && mounted) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(content: Text('回收订单创建成功')),
      );
      context.pop(); // 返回上一页
    } else if (mounted) {
      final errorMessage =
          ref.read(recycleCreateProvider).createOrderState.maybeWhen(
                error: (error) => error,
                orElse: () => '订单创建失败',
              );

      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(content: Text(errorMessage)),
      );
    }
  }
}
