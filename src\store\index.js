import { createStore } from "vuex"
// 导入你的 API 函数
import {
  loginWithCode,
  loginWithPassword,
  updateUser,
  getUserInfo,
  getSystemParam
} from "@/api/api"

import { useStorage } from "@/util/common/storage"

const savePluginInfo = (store) => {
  store.state.$hasAlreadyShowModal = useStorage.get()
  store.subscribe((mutation, state) => {
    useStorage.set(state.$hasAlreadyShowModal)
  })
}

// 初始化用户信息
const getInitialUserInfo = () => {
  try {
    // 尝试从本地存储获取用户信息
    const userInfoStr = uni.getStorageSync("userInfo")
    return userInfoStr ? JSON.parse(userInfoStr) : null
  } catch (e) {
    return null
  }
}

import { useWebSocket } from "@/util/common/useWebSocket"

const store = createStore({
  // 为了不和页面或组件的data中的造成混淆，state中的变量前面建议加上$符号
  state: {
    //首次弹窗
    $hasAlreadyShowModal: false,
    // 用户信息
    $userInfo: getInitialUserInfo(),
    // 登录状态
    $isLoggedIn: false,
    // token
    $token: uni.getStorageSync("token"),

    // 用户实名认证状态
    $certificationStatus: null,

    // 系统配置信息
    $systemConfig: [],
    $websocketConnected: false,
    $warningBadge: 0,
    $warningMessage: {
      message: "",
      showMessage: false,
      id: ""
    }
  },

  getters: {
    // 获取用户信息
    userInfo: (state) => state.$userInfo,
    // 获取登录状态
    isLoggedIn: (state) => state.$isLoggedIn,
    // 获取用户ID
    userId: (state) => (state.$userInfo ? state.$userInfo.id : null),
    // 获取用户名
    userName: (state) =>
      state.$userInfo
        ? state.$userInfo.nickname || state.$userInfo.username
        : "游客",
    // 获取token
    token: (state) => state.$token,
    // 获取用户实名认证状态
    isCertified: (state) => state.$certificationStatus === "VERIFIED",
    // 获取系统配置信息
    systemConfig: (state) => state.$systemConfig
  },

  mutations: {
    SET_UNREAD_COUNT(state, count) {
      state.$warningBadge = count
    },

    SET_WEBSOCKET_CONNECTED(state, connected) {
      state.$websocketConnected = connected
    },

    // 设置用户信息
    GET_STSTEMCONFIG(state) {
      getSystemParam().then((res) => {
        const resData = res.data
        if (res.code === 200) {
          state.$systemConfig = resData
        } else {
          // console.error("刷新用户信息失败", res?.message) // 保留注释或按需删除
        }
      })
    },

    SET_USER_INFO(state, userInfo) {
      state.$userInfo = userInfo
      state.$isLoggedIn = true
      state.$token = userInfo.token
      
      try {
        uni.setStorageSync("userInfo", JSON.stringify(userInfo))
        uni.setStorageSync("isLoggedIn", true)
      } catch (e) {
        // console.error("更新用户信息到本地失败", e) // 保留注释或按需删除
      }
    },

    // 更新用户信息(部分更新)
    UPDATE_USER_INFO(state, partialInfo) {
      if (state.$userInfo) {
        state.$userInfo = { ...state.$userInfo, ...partialInfo }
        
        // 更新存储的信息
        try {
          uni.setStorageSync("userInfo", JSON.stringify(state.$userInfo))
        } catch (e) {
          // console.error("更新用户信息到本地失败", e) // 保留注释或按需删除
        }
      }
    },

    // 重新获取用户信息
    REFRESH_USER_INFO({ commit }) {
      // 调用实际接口获取最新用户信息
      getUserInfo().then((response) => {
        if (response.code === 200) {
          const updatedInfo = response.data.data // 从响应获取最新用户信息
          commit("SET_USER_INFO", updatedInfo) // 更新 store 和本地存储
        } else {
          // console.error("刷新用户信息失败", response.data?.message) // 保留注释或按需删除
        }
      })
    },

    // 清除用户信息(登出)
    CLEAR_USER_INFO(state) {
      state.$userInfo = null
      state.$isLoggedIn = false
      state.$token = ""
      state.$hasAlreadyShowModal = false
      state.$certificationStatus = null // 清除实名认证状态
      useStorage.set(false)
      // 从本地存储删除用户信息
      try {
        uni.clearStorageSync()
      } catch (e) {
        // console.error("清除本地用户信息失败", e) // 保留注释或按需删除
      }
    },

    // 设置token
    SET_TOKEN(state, token) {
      state.$token = token
    },
    SET_HAS_ALREADY_SHOW_MODAL(state, value) {
      state.$hasAlreadyShowModal = value
      useStorage.set(value)
    }
  },

  actions: {
    // ws滚动弹窗操作

    addMessage({ commit, state }, data) {
      state.$warningMessage = {
        message: data.content,
        showMessage: true,
        id: data.id
      }
      setTimeout(() => {
        state.$warningMessage = {
          message: "",
          showMessage: false,
          id: ""
        }
      }, 15000)
    },

    //ws操作
    connectWebSocket({ commit, state }) {
      if (state.$websocketConnected) {
        return
      }
      const { connect } = useWebSocket({
        url: "ws://110.42.70.6:43210/ws",
        token: state.$token
      })
      connect()
    },

    // 登录操作 (调用实际接口)
    async login({ commit }, loginData) {
      let response
      let result
      if (loginData.password) {
        response = await loginWithPassword(loginData) // 调用密码登录接口
      } else if (loginData.code) {
        response = await loginWithCode(loginData) // 调用验证码登录接口
      } else {
        throw new Error("无效的登录参数")
      }
      if (response.code === 200) {
        // 获取用户信息
        const res = await getUserInfo()
        if (!res.data) {
          throw new Error("登录响应缺少用户信息")
        }
        commit("SET_USER_INFO", res.data)
        result = { success: true, userInfo: res.data }
      } else {
        // 处理登录失败的情况
        const errorMessage = response.message || "登录失败，请重试" // 修正为 response.message
        result = { success: false, message: errorMessage }
      }
      return result
    },

    // 退出登录
    async logout({ commit, state }) {
      if (state.$userInfo?.token && api.logout) {
        await api.logout()
      }

      uni.closeSocket()
      commit("SET_WEBSOCKET_CONNECTED", false)
      commit("CLEAR_USER_INFO")
      return { success: true }
    },

    // 刷新用户信息
    async refreshUserInfo({ commit, state }) {
      try {
        if (!state.$userInfo || !state.$token) {
          return { success: false, error: "未登录" }
        }

        // 调用获取用户信息的 API
        const response = await getUserInfo()

        if (response.code === 200) {
          const updatedInfo = response.data // 从响应获取最新用户信息
          commit("SET_USER_INFO", updatedInfo) // 该mutation已包含更新实名状态的逻辑
          return { success: true, userInfo: updatedInfo }
        } else {
          throw new Error(response?.message || "获取用户信息失败")
        }
      } catch (error) {
        // console.error("刷新用户信息 Action 失败", error) // 保留注释或按需删除
        
        if (error.response?.status === 401) {
          // 检查未授权状态码
          uni.closeSocket()
          commit("SET_WEBSOCKET_CONNECTED", false)
          commit("CLEAR_USER_INFO") // 清除无效信息
          return { success: false, error: "登录已过期，请重新登录" }
        }
        const errorMessage = error.message || "刷新用户信息失败"
        return { success: false, error: errorMessage }
      }
    },

    // 更新用户资料 (调用实际接口)
    async updateProfile({ commit, state }, profileData) {
      try {
        // 调用更新用户资料的 API
        const response = await updateUser(profileData)
        if (response.code === 200) {
          // 只从接口返回数据中获取 version，其他字段保持原有值或使用 profileData 中的更新
          const updatedUserInfo = {
            ...state.$userInfo,  // 保持原有的用户信息
            ...profileData,      // 使用传入的更新字段
            version: response.data.version  // 只使用接口返回的 version
          };
          commit("UPDATE_USER_INFO", updatedUserInfo);
          return { success: true, userInfo: state.$userInfo } // 返回更新后的 store 信息
        } else {
          throw new Error(response?.message || "更新资料失败")
        }
      } catch (error) {
        // console.error("更新用户资料 Action 失败", error) // 保留注释或按需删除
        const errorMessage = error.message || "更新用户资料失败"
        return { success: false, error: errorMessage }
      }
    }
  },
  plugins: [savePluginInfo]
})

export default store
