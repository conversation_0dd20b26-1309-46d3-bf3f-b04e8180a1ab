import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

import 'package:soko/core/theme/app_colors.dart';
import 'package:soko/core/theme/app_text_styles.dart';

/// 空状态组件
class EmptyWidget extends StatelessWidget {

  const EmptyWidget({
    super.key,
    required this.message,
    this.description,
    this.icon,
    this.customIcon,
    this.onRetry,
    this.retryText,
  });
  final String message;
  final String? description;
  final IconData? icon;
  final Widget? customIcon;
  final VoidCallback? onRetry;
  final String? retryText;

  @override
  Widget build(BuildContext context) {
    return Center(
      child: Padding(
        padding: EdgeInsets.all(32.w),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            // 图标
            if (customIcon != null)
              customIcon!
            else
              Icon(
                icon ?? Icons.inbox_outlined,
                size: 64.sp,
                color: AppColors.grey400,
              ),
            SizedBox(height: 16.h),
            // 主要消息
            Text(
              message,
              style: AppTextStyles.bodyLarge.copyWith(
                color: AppColors.grey600,
                fontWeight: FontWeight.w600,
              ),
              textAlign: TextAlign.center,
            ),
            // 描述信息
            if (description != null) ...[
              SizedBox(height: 8.h),
              Text(
                description!,
                style: AppTextStyles.bodyMedium.copyWith(
                  color: AppColors.grey500,
                ),
                textAlign: TextAlign.center,
              ),
            ],
            // 重试按钮
            if (onRetry != null) ...[
              SizedBox(height: 24.h),
              ElevatedButton(
                onPressed: onRetry,
                child: Text(retryText ?? '重试'),
              ),
            ],
          ],
        ),
      ),
    );
  }
}
