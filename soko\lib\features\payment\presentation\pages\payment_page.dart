import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:go_router/go_router.dart';

import 'package:soko/core/enums/app_enums.dart';
import 'package:soko/shared/presentation/widgets/custom_app_bar.dart';
import 'package:soko/shared/presentation/widgets/custom_button.dart';
import 'package:soko/shared/presentation/widgets/loading_widget.dart';
import 'package:soko/features/payment/domain/entities/payment_request.dart';
import 'package:soko/features/payment/presentation/providers/payment_provider.dart';
import 'package:soko/features/payment/presentation/widgets/payment_method_selector.dart';
import 'package:soko/features/payment/presentation/widgets/payment_order_info.dart';
import 'package:soko/features/payment/presentation/widgets/payment_status_widget.dart';

/// 支付页面
class PaymentPage extends ConsumerStatefulWidget {

  const PaymentPage({
    super.key,
    required this.orderId,
    required this.amount,
    required this.subject,
  });
  final String orderId;
  final double amount;
  final String subject;

  @override
  ConsumerState<PaymentPage> createState() => _PaymentPageState();
}

class _PaymentPageState extends ConsumerState<PaymentPage> {
  PaymentMethod? selectedPaymentMethod;

  @override
  void initState() {
    super.initState();
    // 页面初始化时重置支付状态
    WidgetsBinding.instance.addPostFrameCallback((_) {
      ref.read(paymentProvider.notifier).resetPaymentState();
    });
  }

  @override
  void dispose() {
    // 页面销毁时清理资源
    ref.read(paymentProvider.notifier).resetPaymentState();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final paymentState = ref.watch(paymentProvider);

    return Scaffold(
      appBar: const CustomAppBar(title: '支付订单'),
      body: _buildPaymentContent(paymentState),
      bottomNavigationBar: _buildBottomBar(paymentState),
    );
  }

  /// 构建支付内容
  Widget _buildPaymentContent(PaymentState state) {
    // 如果有支付响应，显示支付状态
    if (state.currentPaymentResponse != null) {
      return _buildPaymentStatus(state.currentPaymentResponse!);
    }

    // 否则显示支付选择界面
    return SingleChildScrollView(
      padding: EdgeInsets.all(16.w),
      child: Column(
        children: [
          // 订单信息
          PaymentOrderInfo(
            orderId: widget.orderId,
            amount: widget.amount,
            subject: widget.subject,
          ),
          SizedBox(height: 24.h),

          // 支付方式选择
          PaymentMethodSelector(
            selectedMethod: selectedPaymentMethod,
            onMethodSelected: (method) {
              setState(() {
                selectedPaymentMethod = method;
              });
            },
          ),
          SizedBox(height: 24.h),

          // 支付说明
          _buildPaymentNotice(),
        ],
      ),
    );
  }

  /// 构建支付状态
  Widget _buildPaymentStatus(PaymentResponse response) {
    return PaymentStatusWidget(
      paymentResponse: response,
      onRetry: _createPayment,
      onBackToOrder: _backToOrder,
    );
  }

  /// 构建支付说明
  Widget _buildPaymentNotice() {
    return Container(
      width: double.infinity,
      padding: EdgeInsets.all(16.w),
      decoration: BoxDecoration(
        color: Colors.blue.withValues(alpha: 0.05),
        borderRadius: BorderRadius.circular(8.r),
        border: Border.all(color: Colors.blue.withValues(alpha: 0.2)),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(
                Icons.info_outline,
                color: Colors.blue,
                size: 16.w,
              ),
              SizedBox(width: 8.w),
              Text(
                '支付说明',
                style: TextStyle(
                  fontSize: 14.sp,
                  fontWeight: FontWeight.w500,
                  color: Colors.blue[700],
                ),
              ),
            ],
          ),
          SizedBox(height: 8.h),
          Text(
            '• 当前为沙盒测试环境，不会产生真实扣费\n'
            '• 支付完成后订单状态将自动更新\n'
            '• 如遇支付问题，请联系客服处理',
            style: TextStyle(
              fontSize: 12.sp,
              color: Colors.blue[600],
              height: 1.5,
            ),
          ),
        ],
      ),
    );
  }

  /// 构建底部操作栏
  Widget _buildBottomBar(PaymentState state) {
    // 如果已有支付响应，不显示底部栏
    if (state.currentPaymentResponse != null) {
      return const SizedBox.shrink();
    }

    return Container(
      padding: EdgeInsets.all(16.w),
      decoration: BoxDecoration(
        color: Colors.white,
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.1),
            blurRadius: 4,
            offset: const Offset(0, -2),
          ),
        ],
      ),
      child: SafeArea(
        child: Row(
          children: [
            // 支付金额显示
            Expanded(
              child: Column(
                mainAxisSize: MainAxisSize.min,
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    '支付金额',
                    style: TextStyle(
                      fontSize: 12.sp,
                      color: Colors.grey[600],
                    ),
                  ),
                  Text(
                    '¥${widget.amount.toStringAsFixed(2)}',
                    style: TextStyle(
                      fontSize: 20.sp,
                      fontWeight: FontWeight.w600,
                      color: Colors.red,
                    ),
                  ),
                ],
              ),
            ),

            // 立即支付按钮
            SizedBox(
              width: 120.w,
              child: CustomButton(
                text: state.createPaymentState.isLoading ? '支付中...' : '立即支付',
                onPressed: selectedPaymentMethod != null && !state.createPaymentState.isLoading
                    ? _createPayment
                    : null,
                loading: state.createPaymentState.isLoading,
              ),
            ),
          ],
        ),
      ),
    );
  }

  /// 创建支付
  Future<void> _createPayment() async {
    if (selectedPaymentMethod == null) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(content: Text('请选择支付方式')),
      );
      return;
    }

    final request = PaymentRequest(
      orderId: widget.orderId,
      amount: (widget.amount * 100).toInt(), // 转换为分
      paymentMethod: selectedPaymentMethod!,
      subject: widget.subject,
      body: '订单支付',
    );

    final response = await ref.read(paymentProvider.notifier).createPayment(request);

    if (response != null && mounted) {
      // 根据支付方式处理支付流程
      await _handlePaymentFlow(response);
    } else if (mounted) {
      // 显示错误信息
      final errorMessage = ref.read(paymentProvider).createPaymentState.maybeWhen(
        error: (error) => error,
        orElse: () => '支付创建失败',
      );

      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(content: Text(errorMessage)),
      );
    }
  }

  /// 处理支付流程
  Future<void> _handlePaymentFlow(PaymentResponse response) async {
    switch (response.paymentMethod) {
      case PaymentMethod.alipay:
        await _handleAlipayPayment(response);
      case PaymentMethod.wechat:
        await _handleWechatPayment(response);
      case PaymentMethod.unionpay:
        await _handleUnionpayPayment(response);
      case PaymentMethod.balance:
        await _handleBalancePayment(response);
    }
  }

  /// 处理支付宝支付
  Future<void> _handleAlipayPayment(PaymentResponse response) async {
    // TODO(alipay): 集成支付宝SDK
    // 这里模拟支付宝支付流程
    _showPaymentDialog('支付宝支付', '正在跳转到支付宝...');
  }

  /// 处理微信支付
  Future<void> _handleWechatPayment(PaymentResponse response) async {
    // TODO(wechat): 集成微信支付SDK
    // 这里模拟微信支付流程
    _showPaymentDialog('微信支付', '正在跳转到微信...');
  }

  /// 处理银联支付
  Future<void> _handleUnionpayPayment(PaymentResponse response) async {
    // TODO(unionpay): 集成银联支付SDK
    // 这里模拟银联支付流程
    _showPaymentDialog('银联支付', '正在跳转到银联...');
  }

  /// 处理余额支付
  Future<void> _handleBalancePayment(PaymentResponse response) async {
    // 余额支付通常是同步的
    if (response.status == PaymentStatus.success) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(content: Text('余额支付成功')),
      );
    }
  }

  /// 显示支付对话框
  void _showPaymentDialog(String title, String message) {
    showDialog<void>(
      context: context,
      barrierDismissible: false,
      builder: (context) => AlertDialog(
        title: Text(title),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            const CircularProgressIndicator(),
            SizedBox(height: 16.h),
            Text(message),
          ],
        ),
      ),
    );

    // 模拟支付流程，3秒后关闭对话框
    Future.delayed(const Duration(seconds: 3), () {
      if (mounted) {
        Navigator.of(context).pop();
      }
    });
  }

  /// 返回订单页面
  void _backToOrder() {
    context.pop();
  }
}
