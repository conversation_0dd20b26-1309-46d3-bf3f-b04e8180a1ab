import { ref, computed } from 'vue'
import { recycleApi, recycleUtils } from '@/api/recycle'
import type { RecycleOrder, OrderQueryParams, CreateOrderRequest, ShippingInfoRequest } from '@/api/recycle'

// 订单列表管理
export function useRecycleOrderList() {
  const orderList = ref<RecycleOrder[]>([])
  const loading = ref(false)
  const hasMore = ref(true)
  const currentPage = ref(1)
  const currentStatus = ref('')
  const pageSize = 10

  // 计算属性
  const orderCount = computed(() => orderList.value.length)
  
  const pendingOrders = computed(() =>
    orderList.value.filter(order => order.orderStatus === 'PENDING_APPROVAL')
  )
  
  const completedOrders = computed(() =>
    orderList.value.filter(order => order.orderStatus === 'COMPLETED')
  )

  // 加载订单列表
  const loadOrderList = async (options: { refresh?: boolean; status?: string } = {}) => {
    const { refresh = false, status = '' } = options

    if (refresh) {
      currentPage.value = 1
      hasMore.value = true
      currentStatus.value = status
    }

    if (!hasMore.value && !refresh) return
    if (loading.value) return

    loading.value = true

    try {
      const params: OrderQueryParams = {
        page: currentPage.value,
        size: pageSize,
        orderStatus: status || currentStatus.value
      }

      const res = await recycleApi.getUserOrders(params)
      const orders = res.data.records || []

      if (refresh) {
        orderList.value = orders
      } else {
        orderList.value = [...orderList.value, ...orders]
      }

      hasMore.value = orders.length === pageSize
      currentPage.value++

    } catch (error: any) {
      uni.showToast({
        title: error.message || '获取订单失败',
        icon: 'none'
      })
      throw error
    } finally {
      loading.value = false
    }
  }

  // 刷新列表
  const refreshList = () => loadOrderList({ refresh: true })

  // 加载更多
  const loadMore = () => {
    if (hasMore.value && !loading.value) {
      loadOrderList()
    }
  }

  // 按状态筛选
  const filterByStatus = (status: string) => {
    loadOrderList({ refresh: true, status })
  }

  // 重置状态
  const resetState = () => {
    orderList.value = []
    loading.value = false
    hasMore.value = true
    currentPage.value = 1
    currentStatus.value = ''
  }

  return {
    // 状态
    orderList,
    loading,
    hasMore,
    currentPage,
    currentStatus,
    
    // 计算属性
    orderCount,
    pendingOrders,
    completedOrders,
    
    // 方法
    loadOrderList,
    refreshList,
    loadMore,
    filterByStatus,
    resetState
  }
}

// 订单详情管理
export function useRecycleOrderDetail() {
  const orderDetail = ref<RecycleOrder | null>(null)
  const loading = ref(false)

  // 获取订单详情
  const fetchOrderDetail = async (orderId: string): Promise<RecycleOrder | null> => {
    loading.value = true

    try {
      const res = await recycleApi.getOrderDetail(orderId)
      orderDetail.value = res.data
      return res.data
    } catch (error: any) {
      uni.showToast({
        title: error.message || '获取订单详情失败',
        icon: 'none'
      })
      return null
    } finally {
      loading.value = false
    }
  }

  // 更新订单状态
  const updateOrderStatus = (status: string, statusDesc: string) => {
    if (orderDetail.value) {
      orderDetail.value.orderStatus = status
      orderDetail.value.orderStatusDesc = statusDesc
      orderDetail.value.updateTime = Date.now()
    }
  }

  return {
    orderDetail,
    loading,
    fetchOrderDetail,
    updateOrderStatus
  }
}

// 订单操作管理
export function useRecycleOrderActions() {
  const loading = ref(false)

  // 创建订单
  const createOrder = async (orderData: CreateOrderRequest): Promise<string | null> => {
    loading.value = true
    uni.showLoading({ title: '提交中...' })

    try {
      const res = await recycleApi.createOrder(orderData)
      
      uni.hideLoading()
      uni.showToast({
        title: '提交成功',
        icon: 'success'
      })

      return res.data
    } catch (error: any) {
      uni.hideLoading()
      uni.showToast({
        title: error.message || '提交失败',
        icon: 'none'
      })
      return null
    } finally {
      loading.value = false
    }
  }

  // 取消订单
  const cancelOrder = async (orderId: string): Promise<boolean> => {
    try {
      const res = await uni.showModal({
        title: '确认取消',
        content: '确定要取消这个回收订单吗？'
      })

      if (!res.confirm) return false

      loading.value = true
      await recycleApi.cancelOrder(orderId)

      uni.showToast({
        title: '订单已取消',
        icon: 'success'
      })
      
      return true
    } catch (error: any) {
      uni.showToast({
        title: error.message || '取消订单失败',
        icon: 'none'
      })
      return false
    } finally {
      loading.value = false
    }
  }

  // 确认寄送
  const confirmShipment = async (shippingInfo: ShippingInfoRequest): Promise<boolean> => {
    loading.value = true
    
    try {
      await recycleApi.confirmShipment(shippingInfo)

      uni.showToast({
        title: '确认寄送成功',
        icon: 'success'
      })
      
      return true
    } catch (error: any) {
      uni.showToast({
        title: error.message || '确认寄送失败',
        icon: 'none'
      })
      return false
    } finally {
      loading.value = false
    }
  }

  // 申请退回
  const requestReturn = async (orderId: string): Promise<boolean> => {
    try {
      const res = await uni.showModal({
        title: '申请退回',
        content: '确定要申请退回商品吗？'
      })

      if (!res.confirm) return false

      loading.value = true
      await recycleApi.requestReturn(orderId)

      uni.showToast({
        title: '申请退回成功',
        icon: 'success'
      })
      
      return true
    } catch (error: any) {
      uni.showToast({
        title: error.message || '申请退回失败',
        icon: 'none'
      })
      return false
    } finally {
      loading.value = false
    }
  }

  return {
    loading,
    createOrder,
    cancelOrder,
    confirmShipment,
    requestReturn
  }
}

// 工具函数
export function useRecycleUtils() {
  return {
    formatOrderStatus: recycleUtils.formatOrderStatus,
    formatCondition: recycleUtils.formatCondition,
    getStatusClass: recycleUtils.getStatusClass,
    formatTime: recycleUtils.formatTime,
    formatPrice: recycleUtils.formatPrice,
    validatePhone: recycleUtils.validatePhone,
    validateTrackingNumber: recycleUtils.validateTrackingNumber
  }
}
