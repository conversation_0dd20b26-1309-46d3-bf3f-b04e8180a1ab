<template>
  <view class="page min-h-screen bg-white">
    <NavBar title="修改密码" :showBack="true"></NavBar>

    <div class="edit-content">
      <!-- 验证方式切换 -->
      <view class="verify-tabs">
        <view
          class="tab-item"
          :class="{ active: verifyMethod === 'password' }"
          @click="switchVerifyMethod('password')"
        >
          旧密码验证
        </view>
        <view
          class="tab-item"
          :class="{ active: verifyMethod === 'sms' }"
          @click="switchVerifyMethod('sms')"
        >
          手机验证码
        </view>
      </view>

      <!-- 旧密码验证表单 -->
      <view class="form-section" v-if="verifyMethod === 'password'">
        <view class="form-item">
          <input
            class="password-input"
            v-model="oldPassword"
            type="password"
            placeholder="请输入当前密码"
            password
            @input="handleInput('oldPassword')"
            @focus="setFocus('oldPassword', true)"
            @blur="setFocus('oldPassword', false)"
            :class="{
              'input-error': touchedFields.oldPassword && errors.oldPassword,
              'input-focus': focusStates.oldPassword && !errors.oldPassword,
            }"
          />
          <text
            v-if="touchedFields.oldPassword && errors.oldPassword"
            class="error-tips"
            >{{ errors.oldPassword }}
          </text>
        </view>

        <view class="form-item">
          <input
            class="password-input"
            v-model="newPassword"
            type="password"
            placeholder="请输入新密码"
            password
            @input="handleInput('newPassword')"
            @focus="setFocus('newPassword', true)"
            @blur="setFocus('newPassword', false)"
            :class="{
              'input-error': touchedFields.newPassword && errors.newPassword,
              'input-focus': focusStates.newPassword && !errors.newPassword,
            }"
          />
          <text class="input-tips">密码长度8-20位，包含字母和数字</text>
          <text
            v-if="touchedFields.newPassword && errors.newPassword"
            class="error-tips"
            >{{ errors.newPassword }}
          </text>
        </view>

        <view class="form-item">
          <input
            class="password-input"
            v-model="confirmPassword"
            type="password"
            placeholder="请确认新密码"
            password
            @input="handleInput('confirmPassword')"
            @focus="setFocus('confirmPassword', true)"
            @blur="setFocus('confirmPassword', false)"
            :class="{
              'input-error':
                touchedFields.confirmPassword && errors.confirmPassword,
              'input-focus':
                focusStates.confirmPassword && !errors.confirmPassword,
            }"
          />
          <text
            v-if="touchedFields.confirmPassword && errors.confirmPassword"
            class="error-tips"
            >{{ errors.confirmPassword }}
          </text>
        </view>

        <view class="form-item">
          <button
            class="submit-btn"
            @click="savePasswordByOldPassword"
            :disabled="!isPasswordFormValid"
          >
            确认修改
          </button>
        </view>
      </view>

      <!-- 手机验证码表单 -->
      <view class="form-section" v-else>
        <!-- 步骤1：验证手机验证码 -->
        <view v-if="smsStep === 1">
          <view class="form-item">
            <view class="phone-display">
              <text v-if="!userPhone">请先绑定手机号</text>
              <text v-else class="phone-info">
                <view class="phone-number">
                  <text class="phone-label">当前手机号：</text>{{ maskPhone }}
                </view>
                <view v-if="countDown > 0" class="verification-sent">
                  验证码已发送
                </view>
                <view v-else class="verification-tip">
                  请点击"获取验证码"按钮
                </view>
              </text>
            </view>
          </view>

          <view class="form-item verification-code">
            <input
              class="code-input"
              v-model="smsCode"
              type="number"
              placeholder="请输入验证码"
              maxlength="6"
              @input="handleInput('smsCode')"
              @focus="setFocus('smsCode', true)"
              @blur="setFocus('smsCode', false)"
              :class="{
                'input-error': touchedFields.smsCode && errors.smsCode,
                'input-focus': focusStates.smsCode && !errors.smsCode,
              }"
            />
            <button
              class="code-btn"
              :disabled="countDown > 0"
              @click="sendVerificationCode"
            >
              {{ countDown > 0 ? `${countDown}秒后重新获取` : '获取验证码' }}
            </button>
          </view>
          <text
            v-if="touchedFields.smsCode && errors.smsCode"
            class="error-tips sms-error"
            >{{ errors.smsCode }}
          </text>

          <view class="form-item">
            <button
              class="submit-btn"
              @click="verifySmsCode"
              :disabled="!isSmsCodeValid"
            >
              验证
            </button>
          </view>
        </view>

        <!-- 步骤2：设置新密码 -->
        <view v-else>
          <view class="form-item">
            <view class="phone-display">
              <text class="phone-info">
                <view class="phone-number">
                  <text class="phone-label">当前手机号：</text>{{ maskPhone }}
                </view>
                <view class="verification-success"> 验证成功 </view>
              </text>
            </view>
          </view>

          <view class="form-item">
            <input
              class="password-input"
              v-model="newPasswordSms"
              type="password"
              placeholder="请输入新密码"
              password
              @input="handleInput('newPasswordSms')"
              @focus="setFocus('newPasswordSms', true)"
              @blur="setFocus('newPasswordSms', false)"
              :class="{
                'input-error':
                  touchedFields.newPasswordSms && errors.newPasswordSms,
                'input-focus':
                  focusStates.newPasswordSms && !errors.newPasswordSms,
              }"
            />
            <text class="input-tips">密码长度8-20位，包含字母和数字</text>
            <text
              v-if="touchedFields.newPasswordSms && errors.newPasswordSms"
              class="error-tips"
              >{{ errors.newPasswordSms }}
            </text>
          </view>

          <view class="form-item">
            <input
              class="password-input"
              v-model="confirmPasswordSms"
              type="password"
              placeholder="请确认新密码"
              password
              @input="handleInput('confirmPasswordSms')"
              @focus="setFocus('confirmPasswordSms', true)"
              @blur="setFocus('confirmPasswordSms', false)"
              :class="{
                'input-error':
                  touchedFields.confirmPasswordSms && errors.confirmPasswordSms,
                'input-focus':
                  focusStates.confirmPasswordSms && !errors.confirmPasswordSms,
              }"
            />
            <text
              v-if="
                touchedFields.confirmPasswordSms && errors.confirmPasswordSms
              "
              class="error-tips"
              >{{ errors.confirmPasswordSms }}
            </text>
          </view>

          <view class="form-item">
            <button
              class="submit-btn"
              @click="savePasswordBySms"
              :disabled="!isNewPasswordValid"
            >
              确认修改
            </button>
          </view>
        </view>
      </view>
    </div>
  </view>
</template>

<script setup lang="ts">
import { computed, ref } from 'vue';
import NavBar from '@/components/NavBar.vue';
import {
  checkVerificationCode,
  getPublicKeyOptimized,
  sendVerificationCode as sendSmsCode,
  updateUserPassword,
} from '@/api/api';
import { useStore } from 'vuex';
import JSEncrypt from 'jsencrypt';

// 验证方式
const verifyMethod = ref('password'); // 'password' 或 'sms'

// 短信验证步骤：1=验证验证码，2=设置新密码
const smsStep = ref(1);

// 旧密码验证表单数据
const oldPassword = ref('');
const newPassword = ref('');
const confirmPassword = ref('');

// 手机验证码表单数据
const smsCode = ref('');
const newPasswordSms = ref('');
const confirmPasswordSms = ref('');
const countDown = ref(0); // 验证码倒计时

// 错误提示状态
const errors = ref({
  oldPassword: '',
  newPassword: '',
  confirmPassword: '',
  smsCode: '',
  newPasswordSms: '',
  confirmPasswordSms: '',
});

// 输入框焦点状态
const focusStates = ref({
  oldPassword: false,
  newPassword: false,
  confirmPassword: false,
  smsCode: false,
  newPasswordSms: false,
  confirmPasswordSms: false,
});

// 输入框交互状态（用于记录用户是否已经与输入框交互过）
const touchedFields = ref({
  oldPassword: false,
  newPassword: false,
  confirmPassword: false,
  smsCode: false,
  newPasswordSms: false,
  confirmPasswordSms: false,
});

// 设置焦点状态
const setFocus = (field: string, isFocused: boolean) => {
  focusStates.value[field as keyof typeof focusStates.value] = isFocused;

  // 当输入框失去焦点时，标记该字段已被交互
  if (!isFocused) {
    touchedFields.value[field as keyof typeof touchedFields.value] = true;
  }
};

// 标记字段已被交互
const markFieldAsTouched = (field: string) => {
  touchedFields.value[field as keyof typeof touchedFields.value] = true;
};

// 处理输入事件
const handleInput = (field: string) => {
  markFieldAsTouched(field);
  validateInput();
};

// 切换验证方式
const switchVerifyMethod = (method: string) => {
  verifyMethod.value = method;

  // 如果切换到短信验证，重置步骤
  if (method === 'sms') {
    smsStep.value = 1;
  }

  // 清空错误提示
  Object.keys(errors.value).forEach((key) => {
    errors.value[key as keyof typeof errors.value] = '';
  });

  // 重置交互状态
  Object.keys(touchedFields.value).forEach((key) => {
    touchedFields.value[key as keyof typeof touchedFields.value] = false;
  });
};

const userStore = useStore();

// 密码规则验证
const isPasswordValid = (password: string) => {
  // 密码需要包含字母和数字，长度8-20位
  const passwordRegex = /^(?=.*[A-Za-z])(?=.*\d)[A-Za-z\d]{8,20}$/;
  return passwordRegex.test(password);
};

// 验证输入并更新错误信息
const validateInput = () => {
  // 旧密码验证
  if (oldPassword.value.length === 0) {
    errors.value.oldPassword = '请输入当前密码';
  } else {
    errors.value.oldPassword = '';
  }

  // 新密码验证（旧密码方式）
  if (newPassword.value.length === 0) {
    errors.value.newPassword = '请输入新密码';
  } else if (!isPasswordValid(newPassword.value)) {
    errors.value.newPassword = '密码需包含字母和数字，长度8-20位';
  } else {
    errors.value.newPassword = '';
  }

  // 确认密码验证（旧密码方式）
  if (confirmPassword.value.length === 0) {
    errors.value.confirmPassword = '请确认新密码';
  } else if (newPassword.value !== confirmPassword.value) {
    errors.value.confirmPassword = '两次密码输入不一致';
  } else {
    errors.value.confirmPassword = '';
  }

  // 验证码验证
  if (smsCode.value.length === 0) {
    errors.value.smsCode = '请输入验证码';
  } else if (smsCode.value.length !== 6) {
    errors.value.smsCode = '验证码应为6位数字';
  } else {
    errors.value.smsCode = '';
  }

  // 新密码验证（短信方式）
  if (newPasswordSms.value.length === 0) {
    errors.value.newPasswordSms = '请输入新密码';
  } else if (!isPasswordValid(newPasswordSms.value)) {
    errors.value.newPasswordSms = '密码需包含字母和数字，长度8-20位';
  } else {
    errors.value.newPasswordSms = '';
  }

  // 确认密码验证（短信方式）
  if (confirmPasswordSms.value.length === 0) {
    errors.value.confirmPasswordSms = '请确认新密码';
  } else if (newPasswordSms.value !== confirmPasswordSms.value) {
    errors.value.confirmPasswordSms = '两次密码输入不一致';
  } else {
    errors.value.confirmPasswordSms = '';
  }
};

// 监听输入变化，实时验证
const watchInputs = () => {
  // 在输入变化时验证
  oldPassword.value && validateInput();
  newPassword.value && validateInput();
  confirmPassword.value && validateInput();
  smsCode.value && validateInput();
  newPasswordSms.value && validateInput();
  confirmPasswordSms.value && validateInput();
};

// 旧密码验证表单有效性
const isPasswordFormValid = computed(() => {
  watchInputs();

  const oldPasswordValid = oldPassword.value.length > 0;
  const newPasswordValid = isPasswordValid(newPassword.value);
  const passwordsMatch = newPassword.value === confirmPassword.value;

  return oldPasswordValid && newPasswordValid && passwordsMatch;
});

// 验证码有效性（第一步）
const isSmsCodeValid = computed(() => {
  watchInputs();
  return smsCode.value.length === 6;
});

// 新密码有效性（第二步）
const isNewPasswordValid = computed(() => {
  watchInputs();

  const newPasswordValid = isPasswordValid(newPasswordSms.value);
  const passwordsMatch = newPasswordSms.value === confirmPasswordSms.value;

  return newPasswordValid && passwordsMatch;
});

// 注意：原有的isSmsFormValid已被拆分为isSmsCodeValid和isNewPasswordValid

// 获取用户手机号
const userPhone = computed(() => {
  return userStore.state.$userInfo?.mobile || '';
});

// 修改手机号显示格式，中间四位用*代替
const maskPhone = computed(() => {
  const phone = userPhone.value;
  if (phone && phone.length === 11) {
    return phone.replace(/(\d{3})(\d{4})(\d{4})/, '$1****$3');
  }
  return '未绑定手机号';
});

// 发送验证码
const sendVerificationCode = async () => {
  if (!userPhone.value) {
    uni.showToast({
      title: '请先绑定手机号',
      icon: 'none',
    });
    return;
  }

  try {
    const res = await sendSmsCode(userPhone.value);

    if (res.code === 200) {
      uni.showToast({
        title: '验证码已发送',
        icon: 'success',
      });

      // 开始倒计时
      countDown.value = 60;
      const timer = setInterval(() => {
        countDown.value--;
        if (countDown.value <= 0) {
          clearInterval(timer);
        }
      }, 1000);
    } else {
      uni.showToast({
        title: (res && (res.message || res.data?.message)) || '发送验证码失败',
        icon: 'none',
      });
    }
  } catch (error: any) {
    uni.showToast({
      title: error.message || '发送验证码失败，请稍后再试',
      icon: 'none',
    });
  }
};

// 通过旧密码修改
const savePasswordByOldPassword = async () => {
  // 进行表单验证
  validateInput();

  if (!isPasswordFormValid.value) {
    // 显示第一个错误信息
    if (errors.value.oldPassword) {
      uni.showToast({
        title: errors.value.oldPassword,
        icon: 'none',
      });
    } else if (errors.value.newPassword) {
      uni.showToast({
        title: errors.value.newPassword,
        icon: 'none',
      });
    } else if (errors.value.confirmPassword) {
      uni.showToast({
        title: errors.value.confirmPassword,
        icon: 'none',
      });
    }
    return;
  }

  // 调用后端 API
  try {
    // 检查用户信息是否存在
    if (!userStore.state.$userInfo) {
      uni.showToast({
        title: '用户信息获取失败，请重新登录',
        icon: 'none',
      });
      return;
    }

    const pubres = await getPublicKeyOptimized();
    const publicKey = pubres.data.publicKey;
    const encrypt = new JSEncrypt();
    encrypt.setPublicKey(publicKey);
    const params = {
      id: userStore.state.$userInfo.id,
      password: encrypt.encrypt(oldPassword.value), // 加密旧密码
      new_password: encrypt.encrypt(newPassword.value),
      new_password_repeat: encrypt.encrypt(confirmPassword.value),
      version: userStore.state.$userInfo.version,
      verifyType: 'password', // 表明使用密码验证方式
    };
    const res = await updateUserPassword(params);

    if (res.code === 200) {
      uni.showToast({
        title: '密码修改成功',
        icon: 'success',
      });

      // 更新本地存储的用户信息
      userStore.commit('REFRESH_USER_INFO');

      // 清空表单
      oldPassword.value = '';
      newPassword.value = '';
      confirmPassword.value = '';

      // 返回上一页
      setTimeout(() => {
        uni.navigateBack();
      }, 1000);
    } else {
      uni.showToast({
        title: (res && res.message) || '密码修改失败',
        icon: 'none',
      });
    }
  } catch (error: any) {
    console.error('修改密码失败:', error);
    uni.showToast({
      title: error.message || '请求失败，请稍后再试',
      icon: 'none',
    });
  }
};

// 验证短信验证码
const verifySmsCode = async () => {
  // 进行表单验证
  validateInput();

  if (!isSmsCodeValid.value) {
    uni.showToast({
      title: errors.value.smsCode || '请输入有效的验证码',
      icon: 'none',
    });
    return;
  }

  try {
    uni.showLoading({ title: '验证中...' });

    // 验证验证码
    const verifyParams = {
      mobile: userPhone.value,
      code: smsCode.value,
    };

    const verifyRes = await checkVerificationCode(verifyParams);

    uni.hideLoading();

    if (verifyRes.code === 200) {
      // 验证成功，进入第二步
      smsStep.value = 2;

      // 重置新密码相关字段的交互状态
      touchedFields.value.newPasswordSms = false;
      touchedFields.value.confirmPasswordSms = false;

      uni.showToast({
        title: '验证成功，请设置新密码',
        icon: 'success',
      });
    } else {
      uni.showToast({
        title: (verifyRes && verifyRes.message) || '验证码验证失败',
        icon: 'none',
      });
    }
  } catch (error: any) {
    uni.hideLoading();
    console.error('验证码验证失败:', error);
    uni.showToast({
      title: error.message || '验证失败，请稍后再试',
      icon: 'none',
    });
  }
};

// 通过短信验证码修改
const savePasswordBySms = async () => {
  // 进行表单验证
  validateInput();

  // 确保已经通过了验证码验证
  if (smsStep.value !== 2) {
    uni.showToast({
      title: '请先验证手机验证码',
      icon: 'none',
    });
    return;
  }

  if (!isNewPasswordValid.value) {
    // 显示第一个错误信息
    if (errors.value.newPasswordSms) {
      uni.showToast({
        title: errors.value.newPasswordSms,
        icon: 'none',
      });
    } else if (errors.value.confirmPasswordSms) {
      uni.showToast({
        title: errors.value.confirmPasswordSms,
        icon: 'none',
      });
    }
    return;
  }

  try {
    uni.showLoading({ title: '修改中...' });

    // 检查用户信息是否存在
    if (!userStore.state.$userInfo) {
      uni.hideLoading();
      uni.showToast({
        title: '用户信息获取失败，请重新登录',
        icon: 'none',
      });
      return;
    }

    // 获取公钥并加密密码
    const pubres = await getPublicKeyOptimized();
    const publicKey = pubres.data.publicKey;
    const encrypt = new JSEncrypt();
    encrypt.setPublicKey(publicKey);

    // 调用修改密码 API
    const updateParams = {
      id: userStore.state.$userInfo.id,
      password: '', // 传空字符串表示使用验证码验证
      new_password: newPasswordSms.value,
      new_password_repeat: confirmPasswordSms.value,
      verifyType: 'sms', // 用于后端区分验证方式
    } as any; // 临时使用 any 类型避免类型错误

    const updateRes = await updateUserPassword(updateParams);

    uni.hideLoading();

    if (updateRes.code === 200) {
      uni.showToast({
        title: '密码修改成功',
        icon: 'success',
      });

      // 更新本地存储的用户信息
      userStore.commit('REFRESH_USER_INFO');

      // 清空表单
      smsCode.value = '';
      newPasswordSms.value = '';
      confirmPasswordSms.value = '';
      smsStep.value = 1; // 重置步骤

      // 返回上一页
      setTimeout(() => {
        uni.navigateBack();
      }, 1500);
    } else {
      uni.showToast({
        title: (updateRes && updateRes.message) || '密码修改失败',
        icon: 'none',
      });
    }
  } catch (error: any) {
    uni.hideLoading();
    console.error('修改密码失败:', error);
    uni.showToast({
      title: error.message || '请求失败，请稍后再试',
      icon: 'none',
    });
  }
};
</script>

<style scoped lang="scss">
.edit-content {
  padding: 16px;
}

.verify-tabs {
  display: flex;
  background-color: #fff;
  border-radius: 8px;
  margin-bottom: 24px;
  overflow: hidden;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
}

.tab-item {
  flex: 1;
  text-align: center;
  padding: 16px 0;
  font-size: 14px;
  color: #666666;
  position: relative;
  transition: all 0.3s ease;
}

.tab-item.active {
  color: #ef4444;
  font-weight: 600;
}

.tab-item.active::after {
  content: '';
  position: absolute;
  left: 30%;
  right: 30%;
  bottom: 0;
  height: 3px;
  background-color: #ef4444;
  border-radius: 3px;
}

.form-section {
  margin-top: 16px;
}

.form-item {
  margin-bottom: 16px;
}

.password-input,
.code-input {
  background-color: #fff;
  border-radius: 8px;
  padding: 12px 16px;
  height: 48px;
  font-size: 16px;
  width: 100%;
  box-sizing: border-box;
  border: 1px solid #ededed;
  outline: none;
  transition: all 0.15s ease;
}

.password-input:focus,
.code-input:focus {
  border: 2px solid #333333 !important;
  box-shadow: 0 0 5px rgba(51, 51, 51, 0.3) !important;
}

.input-focus {
  border: 2px solid #333333 !important;
  box-shadow: 0 0 5px rgba(51, 51, 51, 0.3) !important;
}

.input-tips {
  font-size: 12px;
  color: #666666;
  margin-top: 8px;
  padding-left: 4px;
}

.error-tips {
  font-size: 12px;
  color: #ff4d4f;
  margin-top: 4px;
  padding-left: 4px;
  display: block;
}

.sms-error {
  margin-top: -8px;
  margin-bottom: 16px;
}

.input-error {
  border: 1px solid #ff4d4f !important;
}

.input-error:focus {
  border: 2px solid #ff4d4f !important;
}

.phone-display,
.verification-success {
  background-color: #fff;
  border-radius: 8px;
  padding: 16px;
  font-size: 14px;
  color: #333333;
  text-align: center;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
}

.verification-success {
  color: #52c41a;
  font-size: 12px;
  font-weight: 600;
  margin-top: 4px;
}

.verification-next-step {
  color: #333333;
  font-size: 14px;
  text-align: center;
  margin-top: 12px;
}

.phone-info {
  display: flex;
  flex-direction: column;
  align-items: center;
}

.phone-number {
  margin-bottom: 4px;
}

.phone-label {
  color: #666666;
  margin-right: 4px;
}

.verification-sent {
  color: #52c41a;
  font-size: 12px;
  margin-top: 4px;
}

.verification-tip {
  color: #faad14;
  font-size: 12px;
  margin-top: 4px;
}

.verification-code {
  display: flex;
  gap: 12px;
  align-items: stretch;
}

.code-input {
  flex: 1;
}

.code-btn {
  min-width: 120px;
  height: 48px;
  line-height: 48px;
  padding: 0 16px;
  font-size: 14px;
  background: linear-gradient(to right, #3b82f6, #06b6d4);
  color: #fff;
  border-radius: 8px;
  display: flex;
  align-items: center;
  justify-content: center;
  white-space: nowrap;
  transition: all 0.3s ease;
}

.code-btn[disabled] {
  background: #a3a3a3;
  color: #fff;
}

.submit-btn {
  background: linear-gradient(to right, #10b981, #52c41a);
  color: #fff;
  border-radius: 8px;
  font-size: 16px;
  font-weight: 500;
  height: 48px;
  line-height: 48px;
  width: 100%;
  margin-top: 24px;
  transition: all 0.3s ease;
}

.submit-btn[disabled] {
  background: #a3a3a3;
  color: #fff;
}
</style>
