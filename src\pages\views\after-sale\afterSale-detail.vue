<template>
  <view class="flex flex-col min-h-screen bg-slate-50">
    <!-- 顶部导航栏 -->
    <NavBar
      title="售后详情"
      bgColor="#FFFFFF"
      color="#333333"
      :showBack="true"
      :fixed="true"
    />

    <!-- 加载/错误/内容区 -->
    <view class="flex-1 flex flex-col">
      <view v-if="loading" class="flex-1 flex items-center justify-center">
        <van-loading type="spinner" color="#1989fa" />
      </view>
      <view v-else-if="error" class="flex-1 flex items-center justify-center">
        <view class="flex flex-col items-center px-4 py-8">
          <view class="mb-4">
            <van-icon name="clear" size="40" color="#ee0a24" />
          </view>
          <text class="text-base font-medium text-text-primary mb-2">加载失败</text>
          <text class="text-sm text-text-secondary text-center mb-6">{{ errorMessage }}</text>
          <view
            @click="handleLoadAfterSaleDetail"
            class="px-5 py-2 bg-primary text-white rounded-full text-sm font-medium shadow-sm active:opacity-80"
          >
            重新加载
          </view>
        </view>
      </view>
      <view v-else class="flex-1 flex flex-col px-2 pt-3 pb-safe">
        <view class="max-w-2xl mx-auto w-full flex flex-col gap-4">
          <!-- 售后单状态卡片 -->
          <view class="bg-white rounded-xl md:rounded-2xl overflow-hidden shadow-base">
            <view
              class="p-4 flex justify-between items-center"
              :class="getStatusBackgroundClass(afterSaleDetail.afterSaleStatus)"
            >
              <view class="flex items-center">
                <van-icon
                  :name="getStatusIconName(afterSaleDetail.afterSaleStatus)"
                  size="20"
                  :color="getStatusIconColor()"
                  class="mr-2"
                />
                <text class="text-base font-medium text-white">
                  {{ getStatusText(afterSaleDetail.afterSaleStatus) }}
                </text>
              </view>
              <text class="text-white text-xs opacity-80">
                {{ formatDate(afterSaleDetail.applyTime) }}
              </text>
            </view>
            <!-- 售后基本信息 -->
            <view class="p-4 flex flex-col gap-2">
              <view class="flex justify-between items-center">
                <text class="text-sm font-medium text-text-primary">订单号</text>
                <view class="flex items-center gap-1.5">
                  <text class="text-sm text-text-secondary">{{ afterSaleDetail.orderNo || "-" }}</text>
                  <view
                    class="px-2 py-0.5 rounded bg-slate-100 active:bg-slate-200 cursor-pointer"
                    @click="handleCopy(afterSaleDetail.orderNo, '订单号')"
                  >
                    <text class="text-xs text-text-secondary">复制</text>
                  </view>
                </view>
              </view>
              <view class="flex justify-between items-center">
                <text class="text-sm font-medium text-text-primary">售后单号</text>
                <view class="flex items-center gap-1.5">
                  <text class="text-sm text-text-secondary">{{ afterSaleDetail.afterSaleNo || "-" }}</text>
                  <view
                    class="px-2 py-0.5 rounded bg-slate-100 active:bg-slate-200 cursor-pointer"
                    @click="handleCopy(afterSaleDetail.afterSaleNo, '售后单号')"
                  >
                    <text class="text-xs text-text-secondary">复制</text>
                  </view>
                </view>
              </view>
              <view class="flex justify-between items-center">
                <text class="text-sm font-medium text-text-primary">申请时间</text>
                <text class="text-sm text-text-secondary">{{ formatDate(afterSaleDetail.applyTime) }}</text>
              </view>
              <view class="flex justify-between items-center">
                <text class="text-sm font-medium text-text-primary">退款金额</text>
                <text class="text-sm text-primary font-bold">¥{{ formatPrice(afterSaleDetail.amount) }}</text>
              </view>
            </view>
          </view>

          <!-- 售后商品卡片 -->
          <view class="bg-white rounded-xl md:rounded-2xl overflow-hidden shadow-base flex items-center gap-4 p-4">
            <view class="relative h-16 w-16 bg-slate-50 rounded-lg overflow-hidden flex-shrink-0 border border-slate-100">
              <CacheImgs
                :src="getFileUrl(afterSaleDetail.fileUrl)"
                :alt="afterSaleDetail.productName"
                class="w-full h-full object-contain"
              />
            </view>
            <view class="flex-1 min-w-0 flex flex-col justify-between gap-1">
              <text class="text-sm font-medium truncate block text-text-primary">
                {{ afterSaleDetail.productName }}
              </text>
              <view class="flex justify-between items-center">
                <text class="text-sm font-medium text-primary">
                  ¥{{ formatPrice(afterSaleDetail.returnAmount / (afterSaleDetail.returnQuantity || 1)) }}
                </text>
                <text class="text-xs text-text-secondary">×{{ afterSaleDetail.returnQuantity }}</text>
              </view>
            </view>
          </view>

          <!-- 售后进度卡片 -->
          <view class="bg-white rounded-xl md:rounded-2xl overflow-hidden shadow-base p-4">
            <view class="mb-3 flex items-center justify-between w-full">
              <text class="font-medium text-sm text-text-primary">售后进度</text>
              <button
                class="text-primary text-xs px-2 py-1 rounded focus:outline-none active:bg-slate-100"
                style="background:transparent;"
                @click="progressExpanded = !progressExpanded"
              >
                {{ progressExpanded ? '收起进度' : '查看进度' }}
              </button>
            </view>
            <view v-if="progressExpanded" class="relative">
              <view class="absolute left-3 top-6 bottom-3 w-0.5 bg-slate-200 z-0 pointer-events-none"></view>
              <view v-for="(step, idx) in progressSteps" :key="idx" class="relative flex items-start mb-8 last:mb-0 z-10">
                <view class="relative z-10">
                  <view :class="['w-6 h-6 rounded-full flex items-center justify-center border-2', step.active ? (step.success ? 'bg-success border-success' : 'bg-primary border-primary') : 'bg-slate-300 border-slate-300']">
                    <text class="text-white text-xs">{{ idx + 1 }}</text>
                  </view>
                </view>
                <view class="ml-4 flex-1 min-w-0">
                  <text class="text-sm font-medium block mb-1" :class="step.active ? 'text-text-primary' : 'text-text-secondary'">{{ step.title }}</text>
                  <text v-if="step.time" class="text-xs text-text-secondary">{{ step.time }}</text>
                  <view v-if="step.desc" class="mt-1.5 text-xs" :class="step.success ? 'text-success' : (step.danger ? 'text-destructive' : 'text-text-secondary')">
                    <text>{{ step.desc }}</text>
                  </view>
                </view>
              </view>
            </view>
          </view>

          <!-- 退货原因卡片 -->
          <view class="bg-white rounded-xl md:rounded-2xl overflow-hidden shadow-base p-4">
            <view class="mb-3">
              <text class="font-medium text-sm text-text-primary">退货原因</text>
            </view>
            <view class="rounded-lg bg-slate-50 p-3">
              <text class="text-sm text-text-primary leading-relaxed">
                99天无理由退货
              </text>
            </view>
          </view>

          <!-- 问题描述卡片 -->
          <view
            v-if="afterSaleDetail.description"
            class="bg-white rounded-xl md:rounded-2xl overflow-hidden shadow-base p-4"
          >
            <view class="mb-3">
              <text class="font-medium text-sm text-text-primary">问题描述</text>
            </view>
            <view class="rounded-lg bg-slate-50 p-3">
              <text class="text-sm text-text-primary leading-relaxed">
                {{ afterSaleDetail.description }}
              </text>
            </view>
          </view>

          <!-- 问题图片卡片 -->
          <view
            v-if="afterSaleDetail.images && afterSaleDetail.images.length > 0"
            class="bg-white rounded-xl md:rounded-2xl overflow-hidden shadow-base p-4"
          >
            <view class="mb-3">
              <text class="font-medium text-sm text-text-primary">问题凭证</text>
            </view>
            <view class="grid grid-cols-3 sm:grid-cols-4 gap-2">
              <view
                v-for="(image, index) in parseImagesArray(afterSaleDetail.images)"
                :key="index"
                class="aspect-square bg-slate-50 rounded-lg overflow-hidden cursor-pointer"
                @click="handlePreviewImage(image, parseImagesArray(afterSaleDetail.images))"
              >
                <CacheImgs
                  :src="getFileUrl(image)"
                  class="w-full h-full object-cover"
                  mode="aspectFill"
                />
              </view>
            </view>
          </view>
        </view>
      </view>

      <!-- 底部操作按钮（流式布局，放内容最底部） -->
      <view
        v-if="afterSaleDetail && (afterSaleDetail.afterSaleStatus === 'RETURN_PENDING' || afterSaleDetail.afterSaleStatus === 'PENDING')"
        class="w-full max-w-2xl mx-auto flex gap-2 mt-4 mb-4 px-1 justify-center"
      >
        <view
          v-if="afterSaleDetail.afterSaleStatus === 'RETURN_PENDING'"
          class="w-24 py-0.5 bg-gradient-to-r from-primary to-neon text-white rounded-xl flex items-center justify-center shadow-base text-xs font-medium active:opacity-90 min-h-[28px] cursor-pointer"
          @click="handleGoToDelivery"
          style="min-width: 28px; min-height: 28px"
        >
          <text class="font-medium text-xs">填写退货物流</text>
        </view>
        <view
          class="w-24 py-0.5 bg-destructive text-white rounded-xl flex items-center justify-center shadow-base text-xs font-medium active:bg-red-700 min-h-[28px] cursor-pointer border border-destructive transition-colors duration-150"
          @click="handleCancelAfterSale"
          style="min-width: 28px; min-height: 28px"
        >
          <text class="font-medium text-xs">取消售后</text>
        </view>
      </view>
    </view>

    <!-- 填写物流信息抽屉 -->
    <transition name="drawer-slide-up">
      <van-popup
        v-model:show="showDeliveryDrawer"
        position="bottom"
        :round="true"
        :closeable="true"
        class="!rounded-t-xl"
        style="min-height: 260px;"
      >
        <view class="p-5 flex flex-col gap-4">
          <text class="text-base font-bold mb-2">填写退货物流</text>
          <view class="flex flex-col gap-2">
            <view>
              <text class="text-sm font-medium mb-1 block">快递公司</text>
              <view @click="showExpressPicker = true"
                :style="inputPickerStyle"
                class="w-full cursor-pointer flex items-center justify-between transition-colors duration-150"
              >
                <span :class="deliveryCompany ? 'text-destructive' : 'text-text-secondary'">
                  {{ deliveryCompany || '请选择快递公司' }}
                </span>
                <span class="ml-2 text-xs text-slate-400">&#9662;</span>
              </view>
              <up-picker
                :show="showExpressPicker"
                :modelValue="[deliveryCompany]"
                :columns="expressPickerColumns"
                title="选择快递公司"
                @confirm="handleExpressConfirm"
                @cancel="showExpressPicker = false"
                confirmColor="#ef4444"
                :defaultIndex="deliveryCompany ? [expressOptions.findIndex(i => i.value === deliveryCompany)] : [0]"
              />
            </view>
            <view>
              <text class="text-sm font-medium mb-1 block">快递单号</text>
              <up-input
                v-model="deliveryNo"
                placeholder="请输入快递单号"
                maxlength="32"
                border="surround"
                clearable
                :customStyle="inputPickerStyle"
                @input="onDeliveryNoInput"
                type="text"
                inputAlign="left"
                :focusStyle="{ borderColor: 'var(--color-primary)' }"
              />
            </view>
          </view>
          <view class="flex gap-3 mt-4">
            <button
              class="flex-1 py-2 rounded-xl bg-primary text-white text-sm font-medium active:opacity-90"
              @click="handleSubmitDelivery"
              :disabled="deliveryLoading"
            >
              {{ deliveryLoading ? '提交中...' : '确认提交' }}
            </button>
            <button
              class="flex-1 py-2 rounded-xl bg-slate-100 text-text-primary text-sm font-medium active:bg-slate-200"
              @click="showDeliveryDrawer = false"
            >
              取消
            </button>
          </view>
        </view>
      </van-popup>
    </transition>
  </view>
</template>

<script lang="ts" setup>
import { getAfterSaleDetail, cancelAfterSale, submitReturnLogistics } from "@/api/api.js";
import CacheImgs from "@/components/CacheImgs.vue";
import NavBar from "@/components/NavBar.vue";
import { onLoad } from "@dcloudio/uni-app";
import moment from "moment";
import { ref, computed } from "vue";

interface AfterSaleDetail {
  id: string;
  afterSaleNo: string;
  orderNo: string;
  applyTime: number;
  afterSaleStatus: AfterSaleStatus;
  amount: number;
  orderItemId: string;
  productName: string;
  fileUrl: string;
  returnQuantity: number;
  returnAmount: number;
  description: string;
  images: string;
  reason?: string;
  logisticsInfo?: {
    company: string;
    trackingNumber: string;
    deliveryTime: number;
  };
  orderAfterSaleStatusLogs?: {
    toStatus?: string;
    status?: string;
    createdTime?: number;
    remark?: string;
  }[];
}

type AfterSaleStatus =
  | "PENDING"
  | "REJECTED"
  | "RETURN_PENDING"
  | "RETURNING"
  | "RETURNED"
  | "REFUNDED"
  | "CLOSED";

const afterSaleDetail = ref<AfterSaleDetail>({
  id: "",
  afterSaleNo: "",
  orderNo: "",
  applyTime: 0,
  afterSaleStatus: "PENDING",
  amount: 0,
  orderItemId: "",
  productName: "",
  fileUrl: "",
  returnQuantity: 0,
  returnAmount: 0,
  description: "",
  images: "",
});

const loading = ref(true);
const error = ref(false);
const errorMessage = ref("");
const afterSaleId = ref("");
const progressExpanded = ref(false);
const cancelLoading = ref(false);

// 抽屉相关状态
const showDeliveryDrawer = ref(false);
const deliveryCompany = ref("");
const deliveryNo = ref("");
const deliveryLoading = ref(false);
const showExpressPicker = ref(false);

// 快递公司选项
const expressOptions = [
  { label: '顺丰速运', value: '顺丰速运' },
  { label: '中通快递', value: '中通快递' },
  { label: '圆通速递', value: '圆通速递' },
  { label: '申通快递', value: '申通快递' },
  { label: '韵达快递', value: '韵达快递' },
  { label: '京东物流', value: '京东物流' },
  { label: '德邦快递', value: '德邦快递' },
  { label: 'EMS', value: 'EMS' },
  { label: '百世快递', value: '百世快递' },
  { label: '极兔速递', value: '极兔速递' },
];
const expressPickerColumns = [expressOptions.map(item => item.value)];

// 统一输入框和选择框样式
const inputPickerStyle = 'border-radius: 0.75rem; border-width: 2px; border-color: var(--color-primary); font-size: 0.875rem; padding: 0.5rem 0.75rem; background: #fff;';

onLoad((option) => {
  if (option.id) {
    afterSaleId.value = option.id;
    handleLoadAfterSaleDetail();
  } else {
    error.value = true;
    errorMessage.value = "未找到售后单ID";
    loading.value = false;
  }
});

const handleLoadAfterSaleDetail = async () => {
  loading.value = true;
  error.value = false;
  try {
    const [err, res] = await getAfterSaleDetail(afterSaleId.value);
    if (err) {
      error.value = true;
      errorMessage.value = err.message || "获取售后详情失败";
      return;
    }
    if (res && typeof res === "object") {
      afterSaleDetail.value = res;
    } else {
      error.value = true;
      errorMessage.value = "售后详情数据异常";
    }
  } catch (e) {
    error.value = true;
    errorMessage.value = "售后详情加载失败";
  } finally {
    loading.value = false;
  }
};

const handleCopy = (text: string, label: string) => {
  if (!text) return;
  uni.setClipboardData({
    data: text,
    success: () => {
      uni.showToast({ title: `${label}已复制`, icon: "none" });
    },
  });
};

const handleGoToDelivery = () => {
  showDeliveryDrawer.value = true;
};

const handleCancelAfterSale = () => {
  if (cancelLoading.value) return;
  uni.showModal({
    title: "取消售后",
    content: "确定要取消本次售后申请吗？",
    confirmText: "确定",
    cancelText: "再想想",
    success: async (res) => {
      if (res.confirm) {
        if (!afterSaleId.value) {
          uni.showToast({ title: "售后单ID缺失", icon: "none" });
          return;
        }
        cancelLoading.value = true;
        uni.showLoading({ title: "正在取消..." });
        const [err] = await cancelAfterSale(afterSaleId.value);
        uni.hideLoading();
        cancelLoading.value = false;
        if (err) {
          uni.showToast({ title: err.message || "取消失败", icon: "none" });
        } else {
          uni.showToast({ title: "已取消售后", icon: "success" });
          handleLoadAfterSaleDetail();
        }
      }
    },
  });
};

const formatDate = (timestamp: number): string =>
  timestamp ? moment(timestamp).format("YYYY-MM-DD HH:mm:ss") : "-";
const formatPrice = (price: number): string => price?.toFixed(2) || "0.00";

const getStatusBackgroundClass = (status: AfterSaleStatus): string => {
  switch (status) {
    case "PENDING":
      return "bg-warning";
    case "REJECTED":
      return "bg-destructive";
    case "RETURN_PENDING":
      return "bg-primary";
    case "RETURNING":
      return "bg-accent";
    case "RETURNED":
      return "bg-heroic";
    case "REFUNDED":
      return "bg-success";
    case "CLOSED":
      return "bg-text-secondary";
    default:
      return "bg-text-secondary";
  }
};

const getStatusIconName = (status: AfterSaleStatus): string => {
  switch (status) {
    case "PENDING":
      return "clock-o";
    case "REJECTED":
      return "close";
    case "RETURN_PENDING":
      return "logistics";
    case "RETURNING":
      return "send-gift-o";
    case "RETURNED":
      return "gold-coin-o";
    case "REFUNDED":
      return "passed";
    case "CLOSED":
      return "delete-o";
    default:
      return "question-o";
  }
};

const getStatusIconColor = () => "#FFFFFF";

const getStatusText = (status: AfterSaleStatus): string => {
  switch (status) {
    case "PENDING":
      return "待处理";
    case "REJECTED":
      return "已拒绝";
    case "RETURN_PENDING":
      return "待寄回";
    case "RETURNING":
      return "退货中";
    case "RETURNED":
      return "待退款";
    case "REFUNDED":
      return "已完成";
    case "CLOSED":
      return "已关闭";
    default:
      return "未知状态";
  }
};

const getProgressStepIndex = (status: AfterSaleStatus): number => {
  switch (status) {
    case "PENDING":
      return 0;
    case "REJECTED":
      return 1;
    case "RETURN_PENDING":
      return 1;
    case "RETURNING":
      return 2;
    case "RETURNED":
      return 3;
    case "REFUNDED":
      return 4;
    case "CLOSED":
      return 0;
    default:
      return 0;
  }
};

const parseImagesArray = (imagesStr: string): string[] => {
  if (!imagesStr) return [];
  try {
    return JSON.parse(imagesStr);
  } catch {
    return imagesStr ? [imagesStr] : [];
  }
};

const handlePreviewImage = (current: string, urls: string[]) => {
  uni.previewImage({
    urls: urls.map((url) => getFileUrl(url)),
    current: getFileUrl(current),
  });
};

const getFileUrl = (url: string): string => {
  if (!url) return "/static/placeholder.png";
  if (url.startsWith("http://") || url.startsWith("https://")) {
    return url;
  } else {
    // 兼容uView Plus baseURL
    const baseURL = (uni.$u && (uni.$u.http as any) && (uni.$u.http as any).config && (uni.$u.http as any).config.baseURL) ? (uni.$u.http as any).config.baseURL : "";
    return baseURL + url;
  }
};

// 售后业务流转顺序
const statusFlow: string[] = [
  "PENDING",        // 申请售后
  "RETURN_PENDING", // 待商家确认
  "RETURNING",      // 退货寄回
  "RETURNED",       // 商家收货
  "REFUNDED",       // 退款完成
];

const statusStepMap: Record<string, { title: string; desc: string }> = {
  PENDING: { title: "申请售后", desc: "您的售后申请已提交，商家正在处理中" },
  RETURN_PENDING: { title: "待商家确认", desc: "等待商家审核售后申请" },
  RETURNING: { title: "退货寄回", desc: "请按照退货地址寄回商品" },
  RETURNED: { title: "商家收货", desc: "商家已确认收到退货" },
  REFUNDED: { title: "退款完成", desc: "退款已完成，资金将原路返回" },
  REJECTED: { title: "已拒绝", desc: "商家已拒绝您的售后申请" },
  CLOSED: { title: "已关闭", desc: "售后单已关闭" },
};

function getNextStatus(current: string): string | null {
  const idx = statusFlow.indexOf(current);
  if (idx >= 0 && idx < statusFlow.length - 1) {
    return statusFlow[idx + 1];
  }
  return null;
}

const progressSteps = computed(() => {
  const logs = afterSaleDetail.value.orderAfterSaleStatusLogs || [];
  const closedIdx = logs.findIndex(log => (log.toStatus || log.status) === "CLOSED");
  // 1. 日志驱动已完成节点
  let steps: any[] = logs.map((log) => {
    const status = log.toStatus || log.status;
    const base = statusStepMap[status] || { title: status, desc: "" };
    return {
      status,
      title: base.title,
      time: log.createdTime ? formatDate(log.createdTime) : "",
      desc: log.remark || base.desc,
      active: true,
      success: true,
      danger: status === "REJECTED" || status === "CLOSED",
    };
  });
  // 2. 售后关闭时，直接展示到已关闭节点
  if (String(afterSaleDetail.value.afterSaleStatus) === "CLOSED" || closedIdx !== -1) {
    if (!steps.some(s => s.status === "CLOSED")) {
      steps.push({
        status: "CLOSED",
        title: statusStepMap.CLOSED.title,
        time: "",
        desc: statusStepMap.CLOSED.desc,
        active: true,
        success: false,
        danger: true,
      });
    }
    // 只展示到已关闭节点
    const closedStepIdx = steps.findIndex(s => s.status === "CLOSED");
    return steps.slice(0, closedStepIdx + 1);
  }
  // 3. 当前状态高亮（如未在日志中出现，补充）
  if (
    afterSaleDetail.value.afterSaleStatus &&
    !steps.some(s => s.status === afterSaleDetail.value.afterSaleStatus)
  ) {
    const status = afterSaleDetail.value.afterSaleStatus;
    const base = statusStepMap[status] || { title: status, desc: "" };
    steps.push({
      status,
      title: base.title,
      time: "",
      desc: base.desc,
      active: true,
      success: false,
      danger: status === "REJECTED" || status === "CLOSED",
    });
  }
  // 4. 预测下一个节点（仅未关闭时）
  const current = afterSaleDetail.value.afterSaleStatus;
  const next = getNextStatus(current);
  if (next && !steps.some(s => s.status === next)) {
    const base = statusStepMap[next] || { title: next, desc: "" };
    steps.push({
      status: next,
      title: base.title,
      time: "",
      desc: base.desc,
      active: false,
      success: false,
      danger: false,
    });
  }
  return steps;
});

// 提交物流信息（对接API）
async function handleSubmitDelivery() {
  if (!deliveryCompany.value.trim()) {
    uni.showToast({ title: "请填写快递公司", icon: "none" });
    return;
  }
  if (!deliveryNo.value.trim()) {
    uni.showToast({ title: "请填写快递单号", icon: "none" });
    return;
  }
  deliveryLoading.value = true;
  try {
    const payload = {
      afterSaleId: afterSaleDetail.value.id,
      orderId: afterSaleDetail.value.orderNo,
      expressCompany: deliveryCompany.value,
      expressNo: deliveryNo.value,
      returnTime: Date.now()
    };
    const res = await submitReturnLogistics(payload);
    // 兼容不同后端返回结构
    if (res && (res.success === true || res.code === 0 || res === true)) {
      showDeliveryDrawer.value = false;
      uni.showToast({ title: "物流信息已提交", icon: "success" });
      handleLoadAfterSaleDetail();
    } else {
      uni.showToast({ title: (res && res.message) || '提交失败', icon: 'none' });
    }
  } catch (err) {
    uni.showToast({ title: err.message || "提交失败", icon: "none" });
  } finally {
    deliveryLoading.value = false;
  }
}

// 快递单号输入优化：仅允许数字和字母，自动去除空格
function onDeliveryNoInput(e: Event) {
  const target = e.target as HTMLInputElement;
  let val = target.value.replace(/\s+/g, "");
  val = val.replace(/[^a-zA-Z0-9]/g, "");
  deliveryNo.value = val;
}

// up-picker选择快递公司回调，类型安全
function handleExpressConfirm(val: { value: string[] }) {
  deliveryCompany.value = val.value[0] || '';
  showExpressPicker.value = false;
}
</script>

<style scoped>
/* 可能需要的任何特定样式，但大多数应该由Tailwind CSS类处理 */
/* 抽屉弹出/收回动效 */
.drawer-slide-up-enter-active,
.drawer-slide-up-leave-active {
  transition: transform 0.3s cubic-bezier(0.4,0,0.2,1), opacity 0.3s;
}
.drawer-slide-up-enter-from,
.drawer-slide-up-leave-to {
  transform: translateY(100%);
  opacity: 0;
}
.drawer-slide-up-enter-to,
.drawer-slide-up-leave-from {
  transform: translateY(0);
  opacity: 1;
}
</style>
