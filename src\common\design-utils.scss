/* SOKO 特摄模玩应用设计系统工具类 */

/**
 * 此文件定义了基于设计系统的常用工具类
 * 配合Tailwind使用，提供一些Tailwind没有直接覆盖的特殊样式
 */

/* ===== 特摄主题颜色类 ===== */
:root {
  /* 主题色 */
  --primary: #{$uni-primary}; /* 奥特曼红 */
  --secondary: #{$uni-secondary}; /* 骑士绿 */
  --accent: #{$uni-accent}; /* 战队蓝 */
  
  /* 辅助色 */
  --heroic: #{$uni-heroic}; /* 英雄金 */
  --villain: #{$uni-villain}; /* 反派紫 */
  --neon: #{$uni-neon}; /* 霓虹粉 */
  --cyber: #{$uni-cyber}; /* 赛博蓝 */
  --energy: #{$uni-energy}; /* 能量绿 */
  --mecha: #{$uni-mecha}; /* 机械灰 */
}

/* ===== 主题渐变类 ===== */

/* 红粉渐变 - 主渐变 */
.bg-gradient-primary {
  background: linear-gradient(to right, var(--primary), var(--neon));
}

/* 绿色渐变 - 次要渐变 */
.bg-gradient-secondary {
  background: linear-gradient(to right, var(--secondary), var(--energy));
}

/* 蓝色渐变 - 强调渐变 */
.bg-gradient-accent {
  background: linear-gradient(to right, var(--accent), var(--cyber));
}

/* 金紫渐变 - 特殊渐变 */
.bg-gradient-special {
  background: linear-gradient(to right, var(--heroic), var(--villain));
}

/* ===== 圆角变体 ===== */

/* 特摄风格的不规则圆角 */
.rounded-tokusatsu {
  border-radius: 8px 16px 8px 16px;
}

/* 英雄面具风格的圆角 */
.rounded-hero-mask {
  border-radius: 24px 24px 8px 8px;
}

/* 反派风格的锐利圆角 */
.rounded-villain {
  border-radius: 4px 16px 4px 16px;
}

/* ===== 特殊阴影 ===== */

/* 英雄光晕阴影 */
.shadow-hero {
  box-shadow: 0 0 15px rgba(239, 68, 68, 0.5);
}

/* 机械边缘阴影 */
.shadow-mecha {
  box-shadow: 4px 4px 0 rgba(107, 114, 128, 0.8);
}

/* 能量发光阴影 */
.shadow-energy {
  box-shadow: 0 0 12px rgba(22, 163, 74, 0.6);
}

/* ===== 特摄风格边框 ===== */

/* 机械风格边框 */
.border-mecha {
  border: 2px solid var(--mecha);
  position: relative;
  
  &::before {
    content: '';
    position: absolute;
    top: -4px;
    left: -4px;
    right: 8px;
    bottom: 8px;
    border: 1px dashed var(--mecha);
    z-index: -1;
  }
}

/* 英雄风格边框 */
.border-hero {
  border: 2px solid var(--primary);
  box-shadow: inset 0 0 8px rgba(239, 68, 68, 0.3);
}

/* ===== 特摄风格动画 ===== */

/* 变身闪光动画 */
.animate-transform {
  animation: transform-flash 1.5s ease-in-out;
}

@keyframes transform-flash {
  0%, 100% { opacity: 1; }
  25% { opacity: 0.3; filter: brightness(3); }
  50% { opacity: 1; filter: brightness(1.5) saturate(1.5); }
  75% { opacity: 0.7; filter: brightness(2); }
}

/* 能量脉冲动画 */
.animate-energy-pulse {
  animation: energy-pulse 2s infinite;
}

@keyframes energy-pulse {
  0%, 100% { box-shadow: 0 0 5px rgba(22, 163, 74, 0.5); }
  50% { box-shadow: 0 0 20px rgba(22, 163, 74, 0.8); }
}

/* 反派出场动画 */
.animate-villain-appear {
  animation: villain-appear 0.8s cubic-bezier(0.25, 0.46, 0.45, 0.94);
}

@keyframes villain-appear {
  0% { transform: scale(0.8); opacity: 0; filter: saturate(0.5); }
  50% { transform: scale(1.1); filter: saturate(1.2); }
  100% { transform: scale(1); opacity: 1; filter: saturate(1); }
}

/* ===== 安全区域工具类 ===== */

.pt-safe {
  padding-top: env(safe-area-inset-top);
}

.pb-safe {
  padding-bottom: env(safe-area-inset-bottom);
}

.pl-safe {
  padding-left: env(safe-area-inset-left);
}

.pr-safe {
  padding-right: env(safe-area-inset-right);
}

/* ===== 响应式工具类 ===== */

/* 移动设备上的列表网格 */
.grid-list-mobile {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 8px;
  
  @media (min-width: 430px) {
    grid-template-columns: repeat(3, 1fr);
  }
  
  @media (min-width: 768px) {
    grid-template-columns: repeat(4, 1fr);
    gap: 16px;
  }
}

/* 标准页面容器 */
.page-container {
  padding: 16px;
  padding-bottom: calc(16px + env(safe-area-inset-bottom));
  
  @media (min-width: 430px) {
    padding: 24px;
    padding-bottom: calc(24px + env(safe-area-inset-bottom));
  }
}

/* ===== 特殊文本效果 ===== */

/* 英雄标题样式 */
.text-hero-title {
  font-weight: 700;
  background: linear-gradient(to right, var(--primary), var(--heroic));
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
  letter-spacing: 0.5px;
}

/* 机械文本样式 */
.text-mecha {
  font-family: monospace;
  font-weight: 600;
  color: var(--mecha);
  letter-spacing: 1px;
  text-transform: uppercase;
} 