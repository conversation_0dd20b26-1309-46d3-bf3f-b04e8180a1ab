import 'package:soko/features/cart/domain/entities/cart_item.dart';
import 'package:soko/features/cart/domain/entities/coupon.dart';

/// 价格计算服务
class PriceCalculationService {
  /// 基础运费
  static const double baseShippingFee = 10;
  
  /// 包邮门槛
  static const double freeShippingThreshold = 99;

  /// 计算购物车价格
  static PriceCalculation calculateCartPrice({
    required List<CartItem> items,
    Coupon? coupon,
    MemberDiscount? memberDiscount,
  }) {
    // 过滤选中且可用的商品
    final selectedItems = items.where((item) => item.selected && item.available).toList();
    
    if (selectedItems.isEmpty) {
      return const PriceCalculation(
        originalAmount: 0,
        productAmount: 0,
        memberDiscount: 0,
        couponDiscount: 0,
        shippingFee: 0,
        finalAmount: 0,
      );
    }

    // 计算商品原价总额
    final originalAmount = selectedItems.fold<double>(0, (sum, item) {
      final originalPrice = item.originalPrice ?? item.price;
      return sum + (originalPrice * item.quantity);
    });

    // 计算商品实际金额（已包含商品本身的折扣）
    final productAmount = selectedItems.fold<double>(0, (sum, item) {
      return sum + item.totalPrice;
    });

    // 计算会员折扣
    double memberDiscountAmount = 0;
    var afterMemberAmount = productAmount;
    
    if (memberDiscount != null) {
      memberDiscountAmount = memberDiscount.calculateDiscount(productAmount);
      afterMemberAmount = productAmount - memberDiscountAmount;
    }

    // 计算优惠券折扣
    double couponDiscountAmount = 0;
    var afterCouponAmount = afterMemberAmount;
    
    if (coupon != null && coupon.isAvailable) {
      // 检查优惠券是否适用于购物车中的商品
      final applicableItems = selectedItems.where((item) {
        // 这里需要商品的分类信息，暂时简化处理
        return coupon.isApplicableToProduct(item.productId, null);
      }).toList();

      if (applicableItems.isNotEmpty) {
        final applicableAmount = applicableItems.fold<double>(0, (sum, item) {
          return sum + item.totalPrice;
        });
        
        couponDiscountAmount = coupon.calculateDiscount(applicableAmount);
        afterCouponAmount = afterMemberAmount - couponDiscountAmount;
      }
    }

    // 计算运费
    var shippingFee = _calculateShippingFee(
      amount: afterCouponAmount,
      memberDiscount: memberDiscount,
      coupon: coupon,
    );

    // 计算最终金额
    final finalAmount = afterCouponAmount + shippingFee;

    return PriceCalculation(
      originalAmount: originalAmount,
      productAmount: productAmount,
      memberDiscount: memberDiscountAmount,
      couponDiscount: couponDiscountAmount,
      shippingFee: shippingFee,
      finalAmount: finalAmount,
      appliedCoupon: coupon,
      memberInfo: memberDiscount,
    );
  }

  /// 计算运费
  static double _calculateShippingFee({
    required double amount,
    MemberDiscount? memberDiscount,
    Coupon? coupon,
  }) {
    // 检查包邮券
    if (coupon?.type == CouponType.shipping && coupon!.isAvailable) {
      return 0;
    }

    // 检查会员包邮
    if (memberDiscount?.isFreeShipping(amount) == true) {
      return 0;
    }

    // 检查普通包邮门槛
    if (amount >= freeShippingThreshold) {
      return 0;
    }

    return baseShippingFee;
  }

  /// 获取可用优惠券列表
  static List<Coupon> getAvailableCoupons({
    required List<CartItem> items,
    required List<Coupon> allCoupons,
  }) {
    final selectedItems = items.where((item) => item.selected && item.available).toList();
    
    if (selectedItems.isEmpty) return [];

    final totalAmount = selectedItems.fold<double>(0, (sum, item) {
      return sum + item.totalPrice;
    });

    return allCoupons.where((coupon) {
      // 检查优惠券是否可用
      if (!coupon.isAvailable) return false;

      // 检查最低消费金额
      if (coupon.minAmount != null && totalAmount < coupon.minAmount!) {
        return false;
      }

      // 检查是否适用于购物车中的商品
      final hasApplicableItems = selectedItems.any((item) {
        return coupon.isApplicableToProduct(item.productId, null);
      });

      return hasApplicableItems;
    }).toList();
  }

  /// 选择最优优惠券
  static Coupon? selectBestCoupon({
    required List<CartItem> items,
    required List<Coupon> availableCoupons,
    MemberDiscount? memberDiscount,
  }) {
    if (availableCoupons.isEmpty) return null;

    Coupon? bestCoupon;
    double maxDiscount = 0;

    for (final coupon in availableCoupons) {
      final calculation = calculateCartPrice(
        items: items,
        coupon: coupon,
        memberDiscount: memberDiscount,
      );

      final totalDiscount = calculation.couponDiscount;
      if (totalDiscount > maxDiscount) {
        maxDiscount = totalDiscount;
        bestCoupon = coupon;
      }
    }

    return bestCoupon;
  }

  /// 计算节省金额
  static double calculateSavings({
    required List<CartItem> items,
    Coupon? coupon,
    MemberDiscount? memberDiscount,
  }) {
    final calculation = calculateCartPrice(
      items: items,
      coupon: coupon,
      memberDiscount: memberDiscount,
    );

    return calculation.originalAmount - calculation.finalAmount;
  }

  /// 计算距离包邮还需多少金额
  static double calculateAmountToFreeShipping({
    required List<CartItem> items,
    MemberDiscount? memberDiscount,
  }) {
    final selectedItems = items.where((item) => item.selected && item.available).toList();
    
    if (selectedItems.isEmpty) return freeShippingThreshold;

    final totalAmount = selectedItems.fold<double>(0, (sum, item) {
      return sum + item.totalPrice;
    });

    // 检查会员包邮门槛
    var threshold = freeShippingThreshold;
    if (memberDiscount?.freeShippingThreshold != null) {
      threshold = memberDiscount!.freeShippingThreshold!;
    }

    if (totalAmount >= threshold) return 0;

    return threshold - totalAmount;
  }

  /// 验证优惠券是否可用
  static bool validateCoupon({
    required Coupon coupon,
    required List<CartItem> items,
  }) {
    // 检查优惠券基本状态
    if (!coupon.isAvailable) return false;

    final selectedItems = items.where((item) => item.selected && item.available).toList();
    
    if (selectedItems.isEmpty) return false;

    // 检查最低消费金额
    final totalAmount = selectedItems.fold<double>(0, (sum, item) {
      return sum + item.totalPrice;
    });

    if (coupon.minAmount != null && totalAmount < coupon.minAmount!) {
      return false;
    }

    // 检查是否适用于购物车中的商品
    final hasApplicableItems = selectedItems.any((item) {
      return coupon.isApplicableToProduct(item.productId, null);
    });

    return hasApplicableItems;
  }

  /// 格式化金额显示
  static String formatAmount(double amount) {
    return '¥${amount.toStringAsFixed(2)}';
  }

  /// 格式化折扣显示
  static String formatDiscount(double discount) {
    if (discount <= 0) return '';
    return '-${formatAmount(discount)}';
  }

  /// 计算积分奖励
  static int calculatePoints({
    required double amount,
    MemberDiscount? memberDiscount,
  }) {
    final basePoints = (amount / 10).floor(); // 每10元1积分
    
    if (memberDiscount != null) {
      return (basePoints * memberDiscount.pointsMultiplier).floor();
    }
    
    return basePoints;
  }
}
