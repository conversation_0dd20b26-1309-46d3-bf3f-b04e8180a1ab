// 回收模块类型定义

export interface RecyclingOrderVO {
  id: string
  userId: string
  userPhone?: string
  brandName: string
  model: string
  categoryName: string
  productDesc?: string
  conditionDescription: string
  estimatedPrice: number
  finalPrice?: number
  orderStatus: string
  orderStatusDesc: string
  reviewedPrice?: number
  shippingInfo?: string
  buyerAddress?: string
  createTime: number
  updateTime: number
  files?: RecyclingOrderFile[]
  reviews?: OrderReview[]
}

export interface RecyclingOrderFile {
  id: string
  recyclingOrderId: string
  fileId: string
  url: string
  thumbnailUrl: string
  sort: number
  isMain: number
}

export interface OrderReview {
  id: string
  orderId: string
  reviewerId: string
  reviewComments: string
  createTime: number
}

export interface CreateOrderRequest {
  productName: string
  productDesc: string
  productModel?: string
  productCategory: string
  contactPerson: string
  contactPhone: string
  expectedPrice: number
  condition: string
  imageFiles: string[]
}

export interface OrderQueryParams {
  page: number
  size: number
  orderStatus?: string
  productName?: string
  startDate?: string
  endDate?: string
}

export interface ShippingInfo {
  courierCompany: string
  trackingNumber: string
  senderName: string
  senderPhone: string
  senderAddress: string
}

export interface CategoryItem {
  name: string
  value: string
}

export interface ConditionOption {
  value: string
  label: string
  desc: string
}

export interface ImageItem {
  fileId: string
  url: string
  thumbnailUrl: string
}

export interface ProcessStep {
  title: string
  desc: string
}

export interface OrderAction {
  key: string
  label: string
  type: 'primary' | 'danger' | 'warning' | 'default'
}

// API响应类型
export interface ApiResponse<T = any> {
  success: boolean
  data: T
  message: string
  code: number
}

export interface PageResult<T> {
  records: T[]
  total: number
  size: number
  current: number
  pages: number
}

// 订单状态枚举
export enum RecycleStatus {
  DRAFT = 'DRAFT',
  PENDING_APPROVAL = 'PENDING_APPROVAL',
  PRICE_QUOTED = 'PRICE_QUOTED',
  SHIPPING_CONFIRMED = 'SHIPPING_CONFIRMED',
  CANCELLED = 'CANCELLED',
  RECEIVED = 'RECEIVED',
  PAYMENT_CONFIRMED = 'PAYMENT_CONFIRMED',
  RETURN_REQUESTED = 'RETURN_REQUESTED',
  COMPLETED = 'COMPLETED',
  RETURNED = 'RETURNED'
}

// 商品状况枚举
export enum ProductCondition {
  EXCELLENT = 'excellent',
  GOOD = 'good',
  FAIR = 'fair',
  POOR = 'poor'
}

// 商品类别枚举
export enum ProductCategory {
  MOBILE = 'mobile',
  COMPUTER = 'computer',
  APPLIANCE = 'appliance',
  OTHER = 'other'
}
