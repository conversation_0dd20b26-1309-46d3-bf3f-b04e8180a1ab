import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

import 'package:soko/core/enums/app_enums.dart';
import 'package:soko/shared/presentation/widgets/custom_card.dart';

/// 订单创建支付方式组件
class OrderCreatePaymentSection extends StatelessWidget {

  const OrderCreatePaymentSection({
    super.key,
    this.selectedMethod,
    required this.onMethodSelected,
  });
  final PaymentMethod? selectedMethod;
  final ValueChanged<PaymentMethod> onMethodSelected;

  @override
  Widget build(BuildContext context) {
    return CustomCard(
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // 标题
          Row(
            children: [
              Icon(
                Icons.payment,
                color: Colors.purple,
                size: 20.w,
              ),
              SizedBox(width: 8.w),
              Text(
                '支付方式',
                style: TextStyle(
                  fontSize: 16.sp,
                  fontWeight: FontWeight.w600,
                ),
              ),
            ],
          ),
          SizedBox(height: 16.h),

          // 支付方式列表
          ...PaymentMethod.values.map(_buildPaymentOption),
        ],
      ),
    );
  }

  /// 构建支付方式选项
  Widget _buildPaymentOption(PaymentMethod method) {
    final isSelected = selectedMethod == method;

    return Padding(
      padding: EdgeInsets.only(bottom: 12.h),
      child: InkWell(
        onTap: () => onMethodSelected(method),
        borderRadius: BorderRadius.circular(8.r),
        child: Container(
          width: double.infinity,
          padding: EdgeInsets.all(12.w),
          decoration: BoxDecoration(
            color: isSelected 
                ? Colors.blue.withValues(alpha: 0.05)
                : Colors.transparent,
            borderRadius: BorderRadius.circular(8.r),
            border: Border.all(
              color: isSelected 
                  ? Colors.blue 
                  : Colors.grey.withValues(alpha: 0.3),
            ),
          ),
          child: Row(
            children: [
              // 支付方式图标
              Container(
                width: 40.w,
                height: 40.w,
                decoration: BoxDecoration(
                  color: _getPaymentMethodColor(method).withValues(alpha: 0.1),
                  borderRadius: BorderRadius.circular(8.r),
                ),
                child: Icon(
                  _getPaymentMethodIcon(method),
                  color: _getPaymentMethodColor(method),
                  size: 20.w,
                ),
              ),
              SizedBox(width: 12.w),

              // 支付方式信息
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      _getPaymentMethodName(method),
                      style: TextStyle(
                        fontSize: 14.sp,
                        fontWeight: FontWeight.w500,
                        color: Colors.black87,
                      ),
                    ),
                    SizedBox(height: 4.h),
                    Text(
                      _getPaymentMethodDescription(method),
                      style: TextStyle(
                        fontSize: 12.sp,
                        color: Colors.grey[600],
                      ),
                    ),
                  ],
                ),
              ),

              // 选择指示器
              Container(
                width: 20.w,
                height: 20.w,
                decoration: BoxDecoration(
                  color: isSelected ? Colors.blue : Colors.transparent,
                  border: Border.all(
                    color: isSelected ? Colors.blue : Colors.grey,
                    width: 2,
                  ),
                  borderRadius: BorderRadius.circular(10.r),
                ),
                child: isSelected
                    ? Icon(
                        Icons.check,
                        color: Colors.white,
                        size: 12.w,
                      )
                    : null,
              ),
            ],
          ),
        ),
      ),
    );
  }

  /// 获取支付方式图标
  IconData _getPaymentMethodIcon(PaymentMethod method) {
    switch (method) {
      case PaymentMethod.alipay:
        return Icons.account_balance_wallet;
      case PaymentMethod.wechat:
        return Icons.chat;
      case PaymentMethod.unionpay:
        return Icons.credit_card;
      case PaymentMethod.balance:
        return Icons.account_balance;
    }
  }

  /// 获取支付方式颜色
  Color _getPaymentMethodColor(PaymentMethod method) {
    switch (method) {
      case PaymentMethod.alipay:
        return const Color(0xFF1677FF);
      case PaymentMethod.wechat:
        return const Color(0xFF07C160);
      case PaymentMethod.unionpay:
        return const Color(0xFFE60012);
      case PaymentMethod.balance:
        return const Color(0xFFFF6B35);
    }
  }

  /// 获取支付方式名称
  String _getPaymentMethodName(PaymentMethod method) {
    switch (method) {
      case PaymentMethod.alipay:
        return '支付宝';
      case PaymentMethod.wechat:
        return '微信支付';
      case PaymentMethod.unionpay:
        return '银联支付';
      case PaymentMethod.balance:
        return '余额支付';
    }
  }

  /// 获取支付方式描述
  String _getPaymentMethodDescription(PaymentMethod method) {
    switch (method) {
      case PaymentMethod.alipay:
        return '使用支付宝账户余额或绑定银行卡支付';
      case PaymentMethod.wechat:
        return '使用微信钱包余额或绑定银行卡支付';
      case PaymentMethod.unionpay:
        return '使用银联卡直接支付';
      case PaymentMethod.balance:
        return '使用账户余额支付，余额不足时无法使用';
    }
  }
}
