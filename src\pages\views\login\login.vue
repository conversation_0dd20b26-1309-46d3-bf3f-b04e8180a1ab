<template>
  <view class="login-container">
    <!-- 状态栏适配 -->
    <view
      class="status-bar"
      :style="{
        height: statusBarHeight
          ? `${statusBarHeight}px`
          : 'var(--status-bar-height)'
      }"
    ></view>

    <!-- 顶部导航栏 -->
    <view class="login-header">
      <view class="back-button" @tap="backToPage">
        <van-icon name="arrow-left" size="16" color="#333" />
      </view>
    </view>

    <!-- 品牌区域 -->
    <view class="brand-container">
      <view class="brand-logo">
        <image
          src="/static/logo.png"
          class="brand-logo-image"
          mode="aspectFit"
        ></image>
      </view>
      <view class="brand-desc">
        <text class="brand-slogan">登录探索好价中古模玩</text>
      </view>
    </view>

    <!-- 主体区域 -->
    <view class="login-main">
      <view class="login-content">
        <!-- 表单区域 -->
        <view class="form-container">
          <view class="form-card">
            <view class="form-header">
              <text class="form-title">{{
                type ? "验证码登录" : "密码登录"
              }}</text>
              <text class="form-subtitle" v-if="type"
                >\n未注册的手机号将自动创建新账号</text
              >
              <text class="form-subtitle" v-else>\n新用户请用验证码登录</text>
            </view>

            <up-form class="login-form">
              <view>
                <up-form-item>
                  <up-input
                    custom-style="height: 55rpx; line-height: 55rpx;"
                    v-model="form.username"
                    type="number"
                    :maxlength="11"
                    placeholder="请输入手机号码"
                    :clearable="true"
                    @input="validatePhone"
                  ></up-input>
                </up-form-item>
              </view>

              <view v-if="type">
                <up-form-item>
                  <up-input
                    custom-style="height: 55rpx; line-height: 55rpx;"
                    v-model="form.code"
                    type="number"
                    :maxlength="6"
                    placeholder="请输入验证码"
                    :clearable="true"
                  >
                    <template #suffix>
                      <view class="code-button-wrapper">
                        <up-button
                          :text="codeButtonText"
                          type="primary"
                          size="mini"
                          :custom-style="codeButtonStyle"
                          :disabled="!isPhoneValid || codeButtonDisabled"
                          @click="getVerificationCode"
                        ></up-button>
                      </view>
                    </template>
                  </up-input>
                </up-form-item>
              </view>

              <view v-else>
                <up-form-item>
                  <up-input
                    custom-style="height: 55rpx; line-height: 55rpx;"
                    v-model="form.password"
                    type="password"
                    placeholder="请输入密码"
                    :clearable="true"
                  ></up-input>
                </up-form-item>
              </view>
            </up-form>

            <view class="switch-login-type" @tap="changeType">
              <view class="switch-login-text">
                <text>{{ type ? "密码登录" : "验证码登录" }}</text>
                <van-icon name="arrow" size="14" color="#666" />
              </view>
            </view>

            <up-button
              class="login-button"
              type="primary"
              :loading="loading"
              :text="
                loading
                  ? '登录中...'
                  : isNewUser && type
                  ? '注册并登录'
                  : '登录'
              "
              @click="login"
              shape="circle"
              :disabled="!canLogin"
            ></up-button>
          </view>
        </view>

        <!-- 底部协议区域 -->
        <view class="agreement-container">
          <view class="agreement-inner">
            <up-checkbox
              v-model="agreement"
              shape="circle"
              activeColor="#EF4444"
              @change="toggleAgreement"
            ></up-checkbox>
            <view class="agreement-text-container">
              <text class="agreement-text">我已阅读并同意</text>
              <text class="agreement-link" @tap.stop="goToServiceAgreement"
                >《服务协议》</text
              >
              <text class="agreement-text">和</text>
              <text class="agreement-link" @tap.stop="goToPrivacyAgreement"
                >《隐私政策》</text
              >
            </view>
          </view>
        </view>
      </view>
    </view>
  </view>
</template>

<script setup lang="ts">
import JSEncrypt from "jsencrypt"
import { computed, onMounted, onUnmounted, reactive, ref } from "vue"
import * as api from "@/api/api"
import { useStore } from "vuex"

// 定义类型
interface UserForm {
  username: string
  password: string
  code: string
}

const store = useStore()
const agreement = ref(false)
const type = ref(true)
const loading = ref(false)
const sendingCode = ref(false)
const isNewUser = ref(false)
const codeButtonDisabled = ref(false)
const codeButtonText = ref("获取验证码")
const countdownTimer = ref<ReturnType<typeof setInterval> | null>(null)

// 获取系统状态栏高度
const statusBarHeight = ref(0)

// 验证码按钮样式
const codeButtonStyle = {
  height: "50rpx",
  width: "140rpx",
  fontSize: "30rpx",
  borderRadius: "20rpx",
  marginRight: "10rpx",
  fontWeight: "500",
  boxShadow: "0 4rpx 8rpx rgba(239, 68, 68, 0.2)",
  background: "linear-gradient(45deg, #EF4444, #EC4899)",
  color: "#FFFFFF"
}

const form = reactive<UserForm>({
  username: "",
  password: "",
  code: ""
})

// 计算属性：验证手机号是否有效
const isPhoneValid = computed(() => {
  return (
    form.username &&
    form.username.length === 11 &&
    /^1[3-9]\d{9}$/.test(form.username)
  )
})

// 计算是否可以登录
const canLogin = computed(() => {
  if (!agreement.value) return false

  if (type.value) {
    // 验证码登录
    return isPhoneValid.value && form.code.length === 6
  } else {
    // 密码登录
    return isPhoneValid.value && form.password.length >= 8
  }
})

// 验证手机号格式
const validatePhone = (value: string) => {
  if (value && value.length === 11) {
    const valid = /^1[3-9]\d{9}$/.test(value)
    if (!valid) {
      showNotify("请输入正确的手机号码", "warning")
    }
    return valid
  }
  return false
}

const changeType = () => {
  type.value = !type.value
  form.password = ""
  form.code = ""
  isNewUser.value = false
}

// 验证验证码或密码
const validateForm = async () => {
  if (!isPhoneValid.value) {
    showNotify("请输入正确的手机号码", "warning")
    return false
  }

  if (type.value && form.code.length !== 6) {
    showNotify("请输入6位验证码", "warning")
    return false
  }

  if (!type.value && form.password.length < 8) {
    showNotify("密码长度至少8位", "warning")
    return false
  }

  if (!agreement.value) {
    showNotify("请阅读并同意服务协议和隐私政策", "warning")
    return false
  }

  return true
}

// 获取验证码
const getVerificationCode = async () => {
  if (codeButtonDisabled.value) return

  if (!isPhoneValid.value) {
    showNotify("请输入正确的手机号码", "warning")
    return
  }

  // 检查用户是否存在
  try {
    sendingCode.value = true
    const checkRes = await api.checkUser({ username: form.username })
    isNewUser.value = !checkRes.data.exists
  } catch (error) {
    console.error("检查用户是否存在失败", error)
    sendingCode.value = false
    return
  }

  try {
    // 确保使用 api. 前缀调用，并直接传递手机号字符串
    const res = await api.sendVerificationCode(form.username)
    if (res.code === 200) {
      showNotify("验证码已发送", "success")
      startCountdown()
    } else {
      showNotify(res.data?.message || "验证码发送失败", "error")
    }
  } catch (error) {
    console.error("发送验证码失败", error)
    showNotify("验证码发送失败", "error")
  } finally {
    sendingCode.value = false
  }
}

// 验证码倒计时
const startCountdown = () => {
  let countdown = 60
  codeButtonDisabled.value = true
  codeButtonText.value = `${countdown}秒`

  countdownTimer.value = setInterval(() => {
    countdown--
    codeButtonText.value = `${countdown}秒`

    if (countdown <= 0) {
      if (countdownTimer.value) {
        clearInterval(countdownTimer.value)
      }
      codeButtonDisabled.value = false
      codeButtonText.value = "获取验证码"
    }
  }, 1000)
}

// 登录操作
const login = async () => {
  if (loading.value) return
  if (!(await validateForm())) return
  loading.value = true

  try {
    if (type.value) {
      // 验证码登录流程
      // 先检查用户是否存在
      const checkRes = await api.checkUser({ username: form.username })

      if (!checkRes.data) {
        // 用户不存在，跳转到设置密码页面
        loading.value = false
        uni.navigateTo({
          url: `/pages/views/register/set-password?mobile=${form.username}&code=${form.code}`
        })
        return
      } else {
        // 用户存在，调用短信登录接口
        const loginRes = await api.loginWithCode({
          username: form.username,
          code: form.code
        })

        if (loginRes.code === 200) {
          // 登录成功，保存用户信息到store
          await store.dispatch("setUserInfo", loginRes.data)
          showNotify("登录成功", "success")
          setTimeout(() => {
            uni.switchTab({
              url: "/pages/views/home/<USER>"
            })
          }, 1000)
        } else {
          showNotify(loginRes.message || "登录失败", "error")
        }
      }
    } else {
      // 密码登录流程
      const data = {
        username: form.username,
        password: form.password
      }
      const res = await store.dispatch("login", data)
      if (res.success) {
        showNotify("登录成功", "success")
        setTimeout(() => {
          uni.switchTab({
            url: "/pages/views/home/<USER>"
          })
        }, 1000)
      } else {
        showNotify(res.message || "登录失败", "error")
      }
    }
  } catch (error) {
    console.error("登录过程出错", error)
    showNotify("登录失败，请重试", "error")
  } finally {
    loading.value = false
  }
}

// 显示通知
const showNotify = (message: string, type = "default") => {
  // 使用uni原生提示方法替代$up
  uni.showToast({
    title: message,
    icon: type === "success" ? "success" : "none",
    duration: 2000
  })
}

// 同意协议逻辑
const toggleAgreement = () => {
  agreement.value = !agreement.value
}

// 跳转到服务协议
const goToServiceAgreement = () => {
  uni.navigateTo({
    url: "/pages/views/agreement/service"
  })
}

// 跳转到隐私政策
const goToPrivacyAgreement = () => {
  uni.navigateTo({
    url: "/pages/views/agreement/privacy"
  })
}

// 返回上一页
const backToPage = () => {
  // uni.navigateBack();
  uni.switchTab({
    url: "/pages/views/home/<USER>"
  })
}

// 组件卸载时清除定时器
onUnmounted(() => {
  if (countdownTimer.value) {
    clearInterval(countdownTimer.value)
  }
})

// 组件加载时初始化
onMounted(() => {
  // 获取状态栏高度
  try {
    const sysInfo = uni.getSystemInfoSync()
    statusBarHeight.value = sysInfo.statusBarHeight || 0
  } catch (e) {
    console.error("获取系统信息失败", e)
  }
})
</script>

<style lang="scss" scoped>
@import "@/uni.scss";

.login-container {
  min-height: 90vh;
  display: flex;
  flex-direction: column;
  background-color: #ffffff;
  position: relative;
  overflow-x: hidden;
}

/* 状态栏适配 */
.status-bar {
  height: var(--status-bar-height);
  width: 100%;
  background-color: #ffffff;
}

/* 在不支持CSS变量的环境中使用JavaScript获取的高度 */
.status-bar-height {
  width: 100%;
}

.login-header {
  height: 100rpx;
  display: flex;
  align-items: center;
  padding: 0 30rpx;
  line-height: 100rpx;
}

.brand-container {
  margin-bottom: 0;
  padding: 20rpx 40rpx 30rpx;
  text-align: center;
}

.brand-logo {
  width: 240rpx;
  margin-bottom: 20rpx;
  position: relative;
  display: inline-block;
}

.brand-logo-image {
  width: 100%;
  height: 120rpx;
  object-fit: contain;
}

.brand-logo::after {
  content: "";
  position: absolute;
  bottom: -8rpx;
  left: 50%;
  transform: translateX(-50%);
  width: 80rpx;
  height: 4rpx;
  background: linear-gradient(45deg, $uni-primary, $uni-neon);
  border-radius: 2rpx;
}

.brand-name {
  font-size: 56rpx;
  font-weight: bold;
  color: #333333;
  letter-spacing: 2rpx;
  background: linear-gradient(45deg, $uni-primary, $uni-neon);
  background-clip: text;
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  text-shadow: 0 2rpx 4rpx rgba(0, 0, 0, 0.1);
}

.brand-desc {
  margin-top: 12rpx;
}

.brand-slogan {
  font-size: 26rpx;
  color: #666666;
  letter-spacing: 1rpx;
}

.login-main {
  flex: 1;
  display: flex;
  flex-direction: column;
  align-items: center;
  margin-top: 40rpx;
}

.login-content {
  width: 100%;
  padding: 0 40rpx;
}

.form-container {
  margin-bottom: 40rpx;
  padding: 0 20rpx;
}

.form-card {
  background-color: #ffffff;
  border-radius: $uni-border-radius-lg;
  padding: 30rpx;
  box-shadow: $uni-shadow-base;
  border: 1px solid rgba(240, 240, 240, 0.8);
  position: relative;
  overflow: hidden;
}

.form-card::before {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 6rpx;
  background: linear-gradient(to right, $uni-primary, $uni-neon);
}

.form-header {
  margin-bottom: 30rpx;
  position: relative;
}

.form-title {
  font-size: 36rpx;
  font-weight: bold;
  color: #333333;
  margin-bottom: 12rpx;
  letter-spacing: 1rpx;
  position: relative;
  display: inline-block;
}

.form-subtitle {
  font-size: 24rpx;
  color: #999999;
  white-space: pre-line;
  line-height: 1.4;
  text-align: left;
}

.login-form {
  margin-bottom: 30rpx;
}

.input-label {
  font-size: 26rpx;
  color: #666666;
  font-weight: 500;
}

.form-item {
  height: 100rpx;
  margin-bottom: 5rpx;
  background-color: #ffffff;
  border-radius: $uni-border-radius-lg;
  border: 1px solid #dcdfe6;
  box-shadow: none;
  overflow: hidden;
  transition: all 0.3s;
}

.form-item:hover {
  border-color: #c0c4cc;
}

/* 使用通用方法覆盖所有可能的焦点状态样式 */
:deep(.form-item-focus),
:deep(.form-item:focus),
:deep(.form-item:focus-within),
:deep(.is-focused .form-item) {
  border-color: $uni-primary !important;
  border-width: 1px !important;
  box-shadow: 0 0 0 2px rgba($uni-primary, 0.2) !important;
}

.code-button-wrapper {
  height: 30rpx;
  display: flex;
  align-items: center;
}

.switch-login-type {
  display: flex;
  justify-content: flex-end;
  margin-top: 15rpx;
  margin-bottom: 35rpx;
  padding-right: 10rpx;
  transition: all 0.3s ease;
}

.switch-login-text {
  display: flex;
  flex-direction: row;
  align-items: center;
  font-size: 26rpx;
  color: #666666;
  background-color: #ffffff;
  padding: 10rpx 16rpx;
  border-radius: $uni-border-radius-sm;
  border: 1px solid #f0f0f0;
  transition: all 0.3s ease;
}

.switch-login-text text {
  margin-right: 8rpx;
}

.switch-login-type:active .switch-login-text {
  opacity: 0.7;
  transform: translateY(2rpx);
  background-color: #f0f0f0;
}

.login-button {
  width: 100%;
  height: 80rpx;
  margin: 0 auto;
  font-size: 32rpx;
  font-weight: 500;
  letter-spacing: 4rpx;
  box-shadow: 0 10rpx 20rpx rgba($uni-primary, 0.25);
  transition: all 0.3s ease;
  border-radius: $uni-border-radius-lg;
  position: relative;
  overflow: hidden;
  background: linear-gradient(45deg, $uni-primary, $uni-neon);
}

.login-button::after {
  content: "";
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  background: linear-gradient(
    90deg,
    rgba(255, 255, 255, 0) 0%,
    rgba(255, 255, 255, 0.3) 50%,
    rgba(255, 255, 255, 0) 100%
  );
  animation: buttonShine 3s infinite;
}

@keyframes buttonShine {
  0% {
    left: -100%;
  }
  20%,
  100% {
    left: 100%;
  }
}

.login-button:active {
  transform: translateY(4rpx);
  box-shadow: 0 6rpx 10rpx rgba($uni-primary, 0.2);
}

.agreement-container {
  padding: 20rpx;
  margin-top: 20rpx;
  margin-bottom: 30rpx;
  background-color: #ffffff;
  border-radius: $uni-border-radius-base;
  display: flex;
  justify-content: center;
}

.agreement-inner {
  display: flex;
  align-items: center;
  justify-content: center;
}

:deep(.up-checkbox__icon-wrap) {
  margin-right: 12rpx;
  transition: all 0.3s ease;
}

:deep(.up-checkbox__icon) {
  transition: all 0.3s ease;
  color: $uni-primary !important;
}

.agreement-text-container {
  display: flex;
  flex-direction: row;
  align-items: center;
  margin-left: 10rpx;
  flex-wrap: wrap;
  justify-content: center;
}

.agreement-text {
  font-size: 26rpx;
  color: #666666;
  line-height: 1.5;
}

.agreement-link {
  font-size: 26rpx;
  color: $uni-primary;
  font-weight: 500;
  transition: all 0.3s ease;
}

.agreement-link:active {
  opacity: 0.7;
}

/* 确保验证码输入框内容垂直居中 */
:deep(input) {
  height: 80rpx;
  line-height: 80rpx;
  vertical-align: middle;
}

/* 输入框聚焦时样式 */
:deep(.form-item) {
  transition: all 0.2s ease;
}

:deep(.up-input-wrapper) {
  &.focus {
    .form-item {
      border-color: $uni-primary;
      box-shadow: 0 0 0 3px rgba($uni-primary, 0.15);
    }
  }
}

:deep(.up-form-item) {
  &.is-focused {
    .form-item {
      border-color: $uni-primary;
      box-shadow: 0 0 0 3px rgba($uni-primary, 0.15);
    }
  }
}
</style>
