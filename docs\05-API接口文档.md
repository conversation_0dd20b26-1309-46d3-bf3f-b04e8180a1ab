# 中古虾(SOKO) API接口文档

## 🌐 API架构概览

### 接口组织结构
中古虾项目采用**模块化API设计**，按业务领域组织接口：

```mermaid
graph TD
    A[API接口层] --> B[认证授权模块]
    A --> C[用户管理模块]
    A --> D[商品管理模块]
    A --> E[订单管理模块]
    A --> F[回收业务模块]
    A --> G[支付模块]
    A --> H[系统配置模块]
    
    B --> B1[登录注册]
    B --> B2[权限验证]
    B --> B3[Token管理]
    
    C --> C1[用户信息]
    C --> C2[会员体系]
    C --> C3[地址管理]
    
    D --> D1[商品列表]
    D --> D2[商品详情]
    D --> D3[分类管理]
    
    E --> E1[订单创建]
    E --> E2[订单查询]
    E --> E3[订单状态]
    
    F --> F1[回收订单]
    F --> F2[评估报价]
    F --> F3[物流跟踪]
```

### 技术特性
- **统一响应格式**: 标准化的API响应结构
- **请求拦截器**: 自动添加认证头和公共参数
- **响应拦截器**: 统一错误处理和状态码处理
- **类型安全**: TypeScript类型定义确保接口安全
- **加密传输**: 敏感数据RSA加密传输

## 🔧 HTTP客户端配置

### 基础配置
```javascript
// src/common/config.js
export default {
  // 根据环境选择不同的API基础地址
  baseUrl: process.env.NODE_ENV === "development" 
    ? "http://localhost:8080" 
    : "https://api.soko.com/api",
  
  // API超时时间设置(毫秒)
  timeout: 60000,
  
  // 是否允许跨域携带凭证
  withCredentials: true
}
```

### HTTP实例封装
```javascript
// src/api/api.js
const getHttpInstance = () => {
  if (!uni.$u || !uni.$u.http) {
    console.error('uView Plus HTTP 实例尚未初始化！')
    throw new Error('uView Plus HTTP instance is not initialized.')
  }
  return uni.$u.http
}
```

### 请求拦截器
```javascript
// src/util/request/interceptors.js
http.interceptors.request.use(
  (config) => {
    // 初始化请求数据
    config.data = config.data || {}
    config.header['isAdmin'] = false
    
    // 添加认证Token
    let token = uni.getStorageSync('token')
    if (token) {
      config.header['App-Authorization'] = 'FBearer ' + token
    }
    
    return config
  },
  (config) => Promise.reject(config)
)
```

### 响应拦截器
```javascript
http.interceptors.response.use(
  (response) => {
    const { data } = response
    const custom = response.config?.custom

    // 错误状态码处理
    if (data.code !== 200) {
      const errorHandlers = {
        401: () => {
          uni.navigateTo({ url: '/pages/views/login/login' })
          toast('未授权，请重新登录')
        },
        701: () => {
          uni.navigateTo({ url: '/pages/views/login/login' })
          toast('登录已过期，请重新登录')
        },
        403: () => toast('禁止访问'),
        404: () => toast('请求的资源不存在')
      }
      
      const handler = errorHandlers[data.code]
      if (handler) handler()
    }

    // 处理Token和公钥
    if (response.header['app-authorization']) {
      uni.setStorageSync('token', response.header['app-authorization'])
    }
    
    if (response.header['publickey']) {
      uni.setStorageSync('publickey', response.header['publickey'])
      // 24小时后清除公钥缓存
      setTimeout(() => {
        uni.removeStorageSync('publickey')
      }, 24 * 60 * 60 * 1000)
    }

    return data
  },
  (response) => {
    // 网络错误处理
    const errMsg = response.errMsg || ''
    const errorMessages = {
      'Failed to connect': '网络连接失败，请检查API服务是否正常运行',
      'timeout': '请求超时，请稍后重试'
    }
    
    const message = response?.data?.message || 
                   errorMessages[errMsg] || 
                   '请求失败，请稍后重试'
    toast(message)
    
    return {
      code: response?.statusCode || -1,
      message: message
    }
  }
)
```

## 🔐 认证授权模块

### 1. 登录接口

#### 密码登录
```javascript
// POST /login
export const loginWithPassword = async (params) => {
  const { username, password } = params
  
  try {
    // 获取公钥用于加密
    const publicKey = await publicKeyManager.getPublicKey()
    
    // 使用JSEncrypt加密密码
    const encrypt = new JSEncrypt()
    encrypt.setPublicKey(publicKey)
    const encryptedPassword = encrypt.encrypt(password)
    
    // 调用登录接口
    return login({
      username,
      password: encryptedPassword
    })
  } catch (error) {
    return {
      data: {
        success: false,
        message: error.message || "登录失败"
      }
    }
  }
}
```

**请求参数**:
```typescript
interface LoginRequest {
  username: string    // 用户名或手机号
  password: string    // RSA加密后的密码
}
```

**响应数据**:
```typescript
interface LoginResponse {
  code: number
  message: string
  data: {
    token: string
    userInfo: {
      id: string
      username: string
      nickname: string
      avatar: string
      memberLevel: number
    }
  }
}
```

#### 验证码登录
```javascript
// POST /sms/login
export const loginWithCode = (params) => {
  const http = getHttpInstance()
  return http.post("/sms/login", {
    mobile: params.username,
    code: params.code
  })
}
```

#### 发送验证码
```javascript
// POST /third/sms
export const sendVerificationCode = (params) => {
  const http = getHttpInstance()
  return http.post("/third/sms", params)
}
```

### 2. 用户管理接口

#### 获取用户信息
```javascript
// GET /user/info
export const getUserInfo = () => {
  const http = getHttpInstance()
  return http.get('/user/info')
}
```

#### 用户注册
```javascript
// POST /user
export const POST_REGISTER_USER = (params) => {
  const http = getHttpInstance()
  return http.post("/user", params)
}
```

#### 检查用户是否存在
```javascript
// GET /user/check/{username}
export const checkUser = (params) => {
  const http = getHttpInstance()
  return http.get(`/user/check/${params.username}`)
}
```

## 🛍️ 商品管理模块

### 1. 商品列表接口

#### 获取新品列表
```javascript
// GET /product/list/new
export const getNewProductList = (limit) => {
  const http = getHttpInstance()
  return http.get("/product/list/new", { params: limit })
}
```

#### 商品搜索
```javascript
// POST /product/search
export const listProduct = (params) => {
  const http = getHttpInstance()
  return http.post("/product/search", params)
}
```

**请求参数**:
```typescript
interface ProductSearchRequest {
  keyword?: string        // 搜索关键词
  categoryId?: string     // 分类ID
  brandId?: string        // 品牌ID
  minPrice?: number       // 最低价格
  maxPrice?: number       // 最高价格
  sortBy?: string         // 排序字段
  sortOrder?: 'asc' | 'desc'  // 排序方向
  page: number           // 页码
  size: number           // 每页数量
}
```

#### 按ACG分类获取商品
```javascript
// POST /product/list/acg
export const listProductByAcg = (acg) => {
  const http = getHttpInstance()
  return http.post("/product/list/acg", acg)
}
```

### 2. 商品详情接口

#### 获取商品详情
```javascript
// GET /product/detail/{id}
export const getProductDetail = (params) => {
  const http = getHttpInstance()
  return http.get("/product/detail", { params })
}
```

**响应数据**:
```typescript
interface ProductDetail {
  id: string
  name: string
  category: string
  brand: string
  acg: string
  type: string
  size: string
  description: string
  files: Array<{
    id: string
    url: string
    thumbnailUrl: string
  }>
  skus: Array<{
    id: string
    name: string
    price: number
    stock: number
    status: string
  }>
  minPrice: number
  newable: boolean
  domesticFreight: number
  internationalFreight: number
  totalSales: number
  salesType: string
}
```

### 3. 分类管理接口

#### 获取ACG分类树
```javascript
// GET /category/tree?code=ACG
export const listAcg = () => {
  const http = getHttpInstance()
  return http.get("/category/tree?code=ACG")
}
```

## 🛒 订单管理模块

### 1. 订单创建

#### 创建订单
```javascript
// POST /app/order
export const createOrder = (params) => {
  const http = getHttpInstance()
  return http.post("/app/order", params)
}
```

**请求参数**:
```typescript
interface CreateOrderRequest {
  items: Array<{
    skuId: string
    quantity: number
    price: number
  }>
  addressId: string
  couponId?: string
  remark?: string
  paymentMethod: string
}
```

### 2. 订单查询

#### 获取订单列表
```javascript
// GET /app/order/page
export const getOrderList = async (params) => {
  const http = getHttpInstance()
  return http.get("/app/order/page", { params })
}
```

#### 获取订单详情
```javascript
// GET /app/order/{id}
export const getOrderDetail = async (id) => {
  const http = getHttpInstance()
  try {
    const res = await http.get(`/app/order/${id}`)
    return [null, res.data]
  } catch (err) {
    return [err, null]
  }
}
```

### 3. 订单操作

#### 确认收货
```javascript
// POST /app/order/{orderId}/receive
export const confirmReceiveOrder = async (orderId, version) => {
  const http = getHttpInstance()
  try {
    const res = await http.post(`/app/order/${orderId}/receive`, {
      version: version
    })
    return [null, res]
  } catch (err) {
    return [err, null]
  }
}
```

## ♻️ 回收业务模块

### 1. 数据类型定义

```typescript
// src/api/recycle.ts
export interface RecycleOrder {
  id: string
  userId: string
  userPhone?: string
  brandName: string
  model: string
  categoryName: string
  productDesc?: string
  conditionDescription: string
  estimatedPrice: number
  finalPrice?: number
  orderStatus: string
  orderStatusDesc: string
  reviewedPrice?: number
  shippingInfo?: string
  contactPerson?: string
  createTime: number
  updateTime: number
  mainImage?: string
  files?: Array<{
    id: string
    url: string
    thumbnailUrl: string
  }>
}

export interface CreateOrderRequest {
  productName: string
  productDesc: string
  productModel?: string
  productCategory: string
  contactPerson: string
  contactPhone: string
  expectedPrice: number
  condition: string
  imageFiles: string[]
}
```

### 2. 回收订单接口

#### 创建回收订单
```javascript
// POST /api/recycle/orders
export const createOrder = (data: CreateOrderRequest): Promise<ApiResponse<string>> => {
  const http = getHttpInstance()
  return http.post('/api/recycle/orders', data)
}
```

#### 查询用户订单列表
```javascript
// GET /api/recycle/orders
export const getUserOrders = (params: OrderQueryParams): Promise<ApiResponse<PageResult<RecycleOrder>>> => {
  const http = getHttpInstance()
  return http.get('/api/recycle/orders', { params })
}
```

#### 获取订单详情
```javascript
// GET /api/recycle/orders/{orderId}
export const getOrderDetail = (orderId: string): Promise<ApiResponse<RecycleOrder>> => {
  const http = getHttpInstance()
  return http.get(`/api/recycle/orders/${orderId}`)
}
```

#### 取消订单
```javascript
// POST /api/recycle/orders/{orderId}/cancel
export const cancelOrder = (orderId: string): Promise<ApiResponse<void>> => {
  const http = getHttpInstance()
  return http.post(`/api/recycle/orders/${orderId}/cancel`)
}
```

#### 确认寄送
```javascript
// POST /api/recycle/orders/confirm-shipment
export const confirmShipment = (data: ShippingInfoRequest): Promise<ApiResponse<void>> => {
  const http = getHttpInstance()
  return http.post('/api/recycle/orders/confirm-shipment', data)
}
```

#### 申请退回
```javascript
// POST /api/recycle/orders/{orderId}/return
export const requestReturn = (orderId: string): Promise<ApiResponse<void>> => {
  const http = getHttpInstance()
  return http.post(`/api/recycle/orders/${orderId}/return`)
}
```

### 3. 文件上传接口

#### 图片上传
```javascript
// POST /api/file/upload
export const uploadImage = (file: File): Promise<ApiResponse<string>> => {
  const http = getHttpInstance()
  const formData = new FormData()
  formData.append('file', file)
  
  return http.post('/api/file/upload', formData, {
    header: {
      'Content-Type': 'multipart/form-data'
    }
  })
}
```

## 💳 支付模块

### 支付相关接口

#### 创建支付订单
```javascript
// POST /pay/order
export const createPayOrder = (params) => {
  const http = getHttpInstance()
  return http.post("/pay/order", params)
}
```

#### 查询支付状态
```javascript
// GET /pay/status/{orderId}
export const getPaymentStatus = (orderId) => {
  const http = getHttpInstance()
  return http.get(`/pay/status/${orderId}`)
}
```

## 🎫 优惠券模块

### 优惠券接口

#### 获取用户优惠券
```javascript
// GET /coupon-user/list
export const getUserCoupons = (params) => {
  const http = getHttpInstance()
  return http.get("/coupon-user/list", { params })
}
```

#### 获取可领取优惠券
```javascript
// GET /coupons/page/user
export const getCouponsPage = (limit) => {
  const http = getHttpInstance()
  return http.get("/coupons/page/user", { params: limit })
}
```

## 🔧 系统配置模块

### 系统参数接口

#### 获取系统参数
```javascript
// GET /system/list
export const getSystemParam = () => {
  const http = getHttpInstance()
  return http.get('/system/list')
}
```

#### 获取轮播图
```javascript
// GET /banner/list
export const getBanners = () => {
  const http = getHttpInstance()
  return http.get("/banner/list")
}
```

#### 获取热门关键词
```javascript
// GET /product/hot-keyword
export const GET_PRODUCT_HOTKEYWORD = () => {
  const http = getHttpInstance()
  return http.get("/product/hot-keyword")
}
```

## 📱 消息通知模块

### 消息接口

#### 获取用户消息列表
```javascript
// GET /message/userMessage
export const GET_NOTICES_LIST = (params) => {
  const http = getHttpInstance()
  return http.get("/message/userMessage", { params })
}
```

#### 标记消息已读
```javascript
// POST /message/readMessage
export const POST_READ_MESSAGE = (params) => {
  const http = getHttpInstance()
  return http.post('/message/readMessage', params)
}
```

## 📊 统一响应格式

### 标准响应结构
```typescript
interface ApiResponse<T = any> {
  success: boolean    // 请求是否成功
  data: T            // 响应数据
  message: string    // 响应消息
  code: number       // 状态码
}

interface PageResult<T> {
  records: T[]       // 数据列表
  total: number      // 总记录数
  size: number       // 每页大小
  current: number    // 当前页码
  pages: number      // 总页数
}
```

### 常见状态码
- **200**: 请求成功
- **401**: 未授权，需要登录
- **403**: 禁止访问
- **404**: 资源不存在
- **500**: 服务器内部错误
- **701**: 登录已过期

---

*本文档最后更新时间: 2025年6月*
