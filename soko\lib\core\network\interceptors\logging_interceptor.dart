import 'dart:convert';

import 'package:dio/dio.dart';
import 'package:flutter/foundation.dart';

/// 日志拦截器
class LoggingInterceptor extends Interceptor {
  @override
  void onRequest(RequestOptions options, RequestInterceptorHandler handler) {
    if (kDebugMode) {
      print('🚀 REQUEST[${options.method}] => PATH: ${options.path}');
      print('🚀 Headers: ${options.headers}');
      if (options.queryParameters.isNotEmpty) {
        print('🚀 Query Parameters: ${options.queryParameters}');
      }
      if (options.data != null) {
        print('🚀 Body: ${_formatData(options.data)}');
      }
      print('🚀 ────────────────────────────────────────────────────────────────');
    }
    super.onRequest(options, handler);
  }

  @override
  void onResponse(Response response, ResponseInterceptorHandler handler) {
    if (kDebugMode) {
      print('✅ RESPONSE[${response.statusCode}] => PATH: ${response.requestOptions.path}');
      print('✅ Headers: ${response.headers}');
      print('✅ Data: ${_formatData(response.data)}');
      print('✅ ────────────────────────────────────────────────────────────────');
    }
    super.onResponse(response, handler);
  }

  @override
  void onError(DioException err, ErrorInterceptorHandler handler) {
    if (kDebugMode) {
      print('❌ ERROR[${err.response?.statusCode}] => PATH: ${err.requestOptions.path}');
      print('❌ Error Type: ${err.type}');
      print('❌ Error Message: ${err.message}');
      if (err.response != null) {
        print('❌ Error Data: ${_formatData(err.response!.data)}');
      }
      print('❌ ────────────────────────────────────────────────────────────────');
    }
    super.onError(err, handler);
  }

  /// 格式化数据用于日志输出
  String _formatData(dynamic data) {
    try {
      if (data == null) return 'null';
      
      if (data is String) {
        // 尝试解析JSON字符串
        try {
          final jsonData = jsonDecode(data);
          return _prettyPrintJson(jsonData);
        } catch (e) {
          return data;
        }
      }
      
      if (data is Map || data is List) {
        return _prettyPrintJson(data);
      }
      
      return data.toString();
    } catch (e) {
      return 'Error formatting data: $e';
    }
  }

  /// 美化JSON输出
  String _prettyPrintJson(dynamic json) {
    try {
      const encoder = JsonEncoder.withIndent('  ');
      return encoder.convert(json);
    } catch (e) {
      return json.toString();
    }
  }
}
