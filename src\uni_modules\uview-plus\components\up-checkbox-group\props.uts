import { defineMixin } from '../../libs/vue'
import defProps from './checkboxGroup'
let crtProp = defProps['checkboxGroup'] as UTSJSONObject
export const propsCheckboxGroup = defineMixin({
    props: {
        // 标识符
        name: {
            type: String,
            default: crtProp['name']
        },
		// 绑定的值
		modelValue: {
		    type: Array,
		    default: crtProp['value']
		},
        // 形状，circle-圆形，square-方形
        shape: {
            type: String,
            default: crtProp['shape']
        },
        // 是否禁用全部checkbox
        disabled: {
            type: Boolean,
            default: crtProp['disabled']
        },

        // 选中状态下的颜色，如设置此值，将会覆盖parent的activeColor值
        activeColor: {
            type: String,
            default: crtProp['activeColor']
        },
        // 未选中的颜色
        inactiveColor: {
            type: String,
            default: crtProp['inactiveColor']
        },

        // 整个组件的尺寸，默认px
        size: {
            type: [String, Number],
            default: crtProp['size']
        },
        // 布局方式，row-横向，column-纵向
        placement: {
            type: String,
            default: crtProp['placement']
        },
        // label的字体大小，px单位
        labelSize: {
            type: [String, Number],
            default: crtProp['labelSize']
        },
        // label的字体颜色
        labelColor: {
            type: [String],
            default: crtProp['labelColor']
        },
        // 是否禁止点击文本操作
        labelDisabled: {
            type: Boolean,
            default: crtProp['labelDisabled']
        },
        // 图标颜色
        iconColor: {
            type: String,
            default: crtProp['iconColor']
        },
        // 图标的大小，单位px
        iconSize: {
            type: [String, Number],
            default: crtProp['iconSize']
        },
        // 勾选图标的对齐方式，left-左边，right-右边
        iconPlacement: {
            type: String,
            default: crtProp['iconPlacement']
        },
        // 竖向配列时，是否显示下划线
        borderBottom: {
            type: Boolean,
            default: crtProp['borderBottom']
        }
    }
})
