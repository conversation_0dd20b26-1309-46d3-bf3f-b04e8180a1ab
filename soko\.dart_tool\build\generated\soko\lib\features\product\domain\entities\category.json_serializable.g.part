// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

ProductCategory _$ProductCategoryFromJson(Map<String, dynamic> json) =>
    ProductCategory(
      id: json['id'] as String,
      name: json['name'] as String,
      code: json['code'] as String,
      description: json['description'] as String?,
      icon: json['icon'] as String?,
      image: json['image'] as String?,
      parentId: json['parentId'] as String?,
      level: (json['level'] as num).toInt(),
      sort: (json['sort'] as num).toInt(),
      isActive: json['isActive'] as bool,
      children: (json['children'] as List<dynamic>?)
          ?.map((e) => ProductCategory.fromJson(e as Map<String, dynamic>))
          .toList(),
      productCount: (json['productCount'] as num?)?.toInt(),
      createTime: (json['createTime'] as num).toInt(),
      updateTime: (json['updateTime'] as num).toInt(),
    );

Map<String, dynamic> _$ProductCategoryToJson(ProductCategory instance) =>
    <String, dynamic>{
      'id': instance.id,
      'name': instance.name,
      'code': instance.code,
      'description': instance.description,
      'icon': instance.icon,
      'image': instance.image,
      'parentId': instance.parentId,
      'level': instance.level,
      'sort': instance.sort,
      'isActive': instance.isActive,
      'children': instance.children,
      'productCount': instance.productCount,
      'createTime': instance.createTime,
      'updateTime': instance.updateTime,
    };
