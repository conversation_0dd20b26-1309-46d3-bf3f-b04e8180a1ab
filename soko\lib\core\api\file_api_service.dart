import 'dart:io';

import 'package:dio/dio.dart';

import 'package:soko/core/models/file_models.dart';
import 'package:soko/core/network/api_response.dart';
import 'package:soko/core/api/base_api_service.dart';

/// 文件API服务
class FileApiService extends BaseApiService {
  static const String _uploadPath = '/file/upload';
  static const String _uploadTokenPath = '/file/upload-token';
  static const String _fileInfoPath = '/file/info';
  static const String _deleteFilePath = '/file/delete';
  static const String _batchUploadPath = '/file/batch-upload';

  /// 获取上传Token
  Future<ApiResponse<FileUploadResponse>> getUploadToken({
    required FileUploadRequest request,
  }) async {
    return post<FileUploadResponse>(
      _uploadTokenPath,
      data: request.toJson(),
      fromJson: (data) => FileUploadResponse.fromJson(data as Map<String, dynamic>),
    );
  }

  /// 上传文件
  Future<ApiResponse<FileInfo>> uploadFile({
    required File file,
    required String fileType,
    String? category,
    ProgressCallback? onSendProgress,
    CancelToken? cancelToken,
  }) async {
    final fileName = file.path.split('/').last;
    final fileSize = await file.length();
    
    // 创建FormData
    final formData = FormData.fromMap({
      'file': await MultipartFile.fromFile(
        file.path,
        filename: fileName,
      ),
      'fileType': fileType,
      if (category != null) 'category': category,
    });

    return upload<FileInfo>(
      _uploadPath,
      formData,
      onSendProgress: onSendProgress,
      cancelToken: cancelToken,
      fromJson: (data) => FileInfo.fromJson(data as Map<String, dynamic>),
    );
  }

  /// 批量上传文件
  Future<ApiResponse<List<FileInfo>>> batchUploadFiles({
    required List<File> files,
    required String fileType,
    String? category,
    ProgressCallback? onSendProgress,
    CancelToken? cancelToken,
  }) async {
    final formData = FormData();
    
    // 添加文件
    for (var i = 0; i < files.length; i++) {
      final file = files[i];
      final fileName = file.path.split('/').last;
      formData.files.add(MapEntry(
        'files',
        await MultipartFile.fromFile(
          file.path,
          filename: fileName,
        ),
      ),);
    }
    
    // 添加其他参数
    formData.fields.add(MapEntry('fileType', fileType));
    if (category != null) {
      formData.fields.add(MapEntry('category', category));
    }

    return upload<List<FileInfo>>(
      _batchUploadPath,
      formData,
      onSendProgress: onSendProgress,
      cancelToken: cancelToken,
      fromJson: (data) => (data as List<dynamic>)
          .map((item) => FileInfo.fromJson(item as Map<String, dynamic>))
          .toList(),
    );
  }

  /// 上传图片（便捷方法）
  Future<ApiResponse<FileInfo>> uploadImage({
    required File imageFile,
    String? category,
    ProgressCallback? onSendProgress,
    CancelToken? cancelToken,
  }) async {
    return uploadFile(
      file: imageFile,
      fileType: 'image',
      category: category,
      onSendProgress: onSendProgress,
      cancelToken: cancelToken,
    );
  }

  /// 批量上传图片（便捷方法）
  Future<ApiResponse<List<FileInfo>>> batchUploadImages({
    required List<File> imageFiles,
    String? category,
    ProgressCallback? onSendProgress,
    CancelToken? cancelToken,
  }) async {
    return batchUploadFiles(
      files: imageFiles,
      fileType: 'image',
      category: category,
      onSendProgress: onSendProgress,
      cancelToken: cancelToken,
    );
  }

  /// 获取文件信息
  Future<ApiResponse<FileInfo>> getFileInfo({
    required String fileId,
  }) async {
    return get<FileInfo>(
      '$_fileInfoPath/$fileId',
      fromJson: (data) => FileInfo.fromJson(data as Map<String, dynamic>),
    );
  }

  /// 删除文件
  Future<ApiResponse<void>> deleteFile({
    required String fileId,
  }) async {
    return delete<void>('$_deleteFilePath/$fileId');
  }

  /// 批量删除文件
  Future<ApiResponse<void>> batchDeleteFiles({
    required List<String> fileIds,
  }) async {
    return delete<void>(
      _deleteFilePath,
      data: {'fileIds': fileIds},
    );
  }

  /// 通过URL上传文件
  Future<ApiResponse<FileInfo>> uploadFromUrl({
    required String url,
    required String fileType,
    String? category,
    String? fileName,
  }) async {
    return post<FileInfo>(
      '/file/upload-from-url',
      data: {
        'url': url,
        'fileType': fileType,
        if (category != null) 'category': category,
        if (fileName != null) 'fileName': fileName,
      },
      fromJson: (data) => FileInfo.fromJson(data as Map<String, dynamic>),
    );
  }

  /// 获取文件上传配置
  Future<ApiResponse<UploadConfig>> getUploadConfig() async {
    return get<UploadConfig>(
      '/file/upload-config',
      fromJson: (data) => UploadConfig.fromJson(data as Map<String, dynamic>),
    );
  }

  /// 压缩图片
  Future<ApiResponse<FileInfo>> compressImage({
    required String fileId,
    int? quality,
    int? maxWidth,
    int? maxHeight,
  }) async {
    return post<FileInfo>(
      '/file/compress',
      data: {
        'fileId': fileId,
        if (quality != null) 'quality': quality,
        if (maxWidth != null) 'maxWidth': maxWidth,
        if (maxHeight != null) 'maxHeight': maxHeight,
      },
      fromJson: (data) => FileInfo.fromJson(data as Map<String, dynamic>),
    );
  }

  /// 生成缩略图
  Future<ApiResponse<FileInfo>> generateThumbnail({
    required String fileId,
    int width = 200,
    int height = 200,
  }) async {
    return post<FileInfo>(
      '/file/thumbnail',
      data: {
        'fileId': fileId,
        'width': width,
        'height': height,
      },
      fromJson: (data) => FileInfo.fromJson(data as Map<String, dynamic>),
    );
  }
}

/// 上传配置
class UploadConfig {

  const UploadConfig({
    required this.maxFileSize,
    required this.allowedTypes,
    required this.allowedExtensions,
    required this.maxFilesPerUpload,
    required this.uploadUrl,
    this.uploadHeaders,
  });

  factory UploadConfig.fromJson(Map<String, dynamic> json) {
    return UploadConfig(
      maxFileSize: json['maxFileSize'] as int,
      allowedTypes: (json['allowedTypes'] as List<dynamic>).cast<String>(),
      allowedExtensions: (json['allowedExtensions'] as List<dynamic>).cast<String>(),
      maxFilesPerUpload: json['maxFilesPerUpload'] as int,
      uploadUrl: json['uploadUrl'] as String,
      uploadHeaders: json['uploadHeaders'] as Map<String, dynamic>?,
    );
  }
  final int maxFileSize;
  final List<String> allowedTypes;
  final List<String> allowedExtensions;
  final int maxFilesPerUpload;
  final String uploadUrl;
  final Map<String, dynamic>? uploadHeaders;

  Map<String, dynamic> toJson() {
    return {
      'maxFileSize': maxFileSize,
      'allowedTypes': allowedTypes,
      'allowedExtensions': allowedExtensions,
      'maxFilesPerUpload': maxFilesPerUpload,
      'uploadUrl': uploadUrl,
      'uploadHeaders': uploadHeaders,
    };
  }

  /// 检查文件类型是否允许
  bool isTypeAllowed(String fileType) {
    return allowedTypes.contains(fileType.toLowerCase());
  }

  /// 检查文件扩展名是否允许
  bool isExtensionAllowed(String extension) {
    return allowedExtensions.contains(extension.toLowerCase());
  }

  /// 检查文件大小是否允许
  bool isSizeAllowed(int fileSize) {
    return fileSize <= maxFileSize;
  }
}
