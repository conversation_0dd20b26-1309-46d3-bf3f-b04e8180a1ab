/*
 * <AUTHOR> jry
 * @Description  : uview-plus component's props mixin file
 * @version      : 4.0
 * @Date         : 2024-04-22 16:44:21
 * @LastAuthor   : jry
 * @lastTime     : 2024-08-28 12:27:20
 */
import { defineMixin } from '../../libs/vue'
import defProps from './row'
let crtProp = defProps['row'] as UTSJSONObject

export const propsRow = defineMixin({
    props: {
        // 给col添加间距，左右边距各占一半
        gutter: {
            type: [String, Number],
            default: crtProp['gutter']
        },
        // 水平排列方式，可选值为`start`(或`flex-start`)、`end`(或`flex-end`)、`center`、`around`(或`space-around`)、`between`(或`space-between`)
        justify: {
            type: String,
            default: crtProp['justify']
        },
        // 垂直对齐方式，可选值为top、center、bottom
        align: {
            type: String,
            default: crtProp['align']
        }
    }
})
