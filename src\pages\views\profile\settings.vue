<template>
  <div class="settings custom-nav-page">
    <!-- 添加自定义导航栏 -->
    <NavBar title="账户设置" bgColor="#FFFFFF" :showBack="true"></NavBar>

    <div class="settings-content">
      <!-- 头像设置 -->
      <view class="settings-section">
        <view class="settings-item" @click="changeAvatar">
          <view class="settings-label">头像</view>
          <view class="settings-value">
            <image
              class="avatar"
              :src="userInfo.avatar || '/static/logo-write.png'"
              mode="aspectFill"
            ></image>
            <van-icon name="arrow" size="16" color="#666666" />
          </view>
        </view>
      </view>

      <!-- 基本信息设置 -->
      <view class="settings-section">
        <view
          class="settings-item"
          @click="navigateTo('/pages/views/profile/edit-nickname')"
        >
          <view class="settings-label">昵称</view>
          <view class="settings-value">
            <text>{{ userInfo.nickname }}</text>
            <van-icon name="arrow" size="16" color="#666666" />
          </view>
        </view>

        <view class="settings-item-divider"></view>

        <view
          class="settings-item"
          @click="navigateTo('/pages/views/profile/edit-phone')"
        >
          <view class="settings-label">手机号</view>
          <view class="settings-value">
            <text>{{ formatPhone(userInfo.phone) }}</text>
            <van-icon name="arrow" size="16" color="#666666" />
          </view>
        </view>
      </view>

      <!-- 安全设置 -->
      <view class="settings-section">
        <view
          class="settings-item"
          @click="navigateTo('/pages/views/profile/edit-password')"
        >
          <view class="settings-label">修改密码</view>
          <view class="settings-value">
            <van-icon name="arrow" size="16" color="#666666" />
          </view>
        </view>
      </view>

      <!-- 应用设置 -->
      <view class="settings-section">
        <!-- <view class="settings-item">
          <view class="settings-label">消息推送</view>
          <view class="settings-value">
            <up-switch
              v-model="userInfo.pushEnabled"
              :style="switchBgStyle"
              @change="togglePushNotification"
              class="gradient-switch"
            ></up-switch>
          </view>
        </view> -->

        <!-- <view class="settings-item-divider"></view> -->

        <!-- <view class="settings-item">
          <view class="settings-label">深色模式</view>
          <view class="settings-value">
            <up-switch
              v-model="userInfo.darkMode"
              :style="switchBgStyle"
              @change="toggleDarkMode"
              class="gradient-switch"
            ></up-switch>
          </view>
        </view> -->
      </view>

      <!-- 退出按钮 -->
      <view class="settings-logout" @click="logout"> 退出登录 </view>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, computed } from "vue";
import NavBar from "@/components/NavBar.vue";
// 修复FaIcon导入
import "@/components/falcon/FaIcon.vue";
// @ts-ignore - 忽略模块声明文件缺失的错误
import { uploadFile, updateUser } from "@/api/api";
import { useStore } from "vuex";
import store from "@/store";

// 开关渐变背景样式
const switchBgStyle = computed(() => {
  return {
    "--switch-checked-bg": "linear-gradient(to right, #10B981, #34D399)",
    "--switch-node-bg": "#ffffff",
  };
});

// 用户信息接口
interface UserInfo {
  username: string;
  nickname: string;
  phone: string;
  avatar: string;
  pushEnabled: boolean;
  darkMode: boolean;
  [key: string]: any; // 允许添加其他可能的属性
}

const useMyStore = useStore();
// 默认用户信息
const defaultUserInfo: UserInfo = useMyStore.state.$userInfo;

// 用户信息状态
const userInfo = ref<UserInfo>({ ...defaultUserInfo });

// 获取本地存储中的用户信息
const getUserInfoFromStorage = (): UserInfo | null => {
  try {
    const userInfoStr = uni.getStorageSync("userInfo");
    return userInfoStr ? JSON.parse(userInfoStr) : null;
  } catch (e) {
    console.error("获取缓存的用户信息失败", e);
    return null;
  }
};

// 保存用户信息到本地存储
const saveUserInfoToStorage = (info: UserInfo): void => {
  try {
    uni.setStorageSync("userInfo", JSON.stringify(info));
  } catch (e) {
    console.error("保存用户信息到本地失败", e);
  }
};

// 初始化时从本地存储获取用户信息
onMounted(() => {
  const storeUserInfo = getUserInfoFromStorage();
  if (storeUserInfo) {
    userInfo.value = {
      ...defaultUserInfo,
      ...storeUserInfo,
    };
  }
});

// 格式化手机号，中间四位显示为星号
const formatPhone = (phone: string) => {
  if (!phone) return "";
  return phone.replace(/(\d{3})\d{4}(\d{4})/, "$1****$2");
};

// 页面跳转
const navigateTo = (url: string) => {
  uni.navigateTo({ url });
};

// 修改头像
const changeAvatar = () => {
  uni.chooseImage({
    count: 1,
    sizeType: ["compressed"],
    sourceType: ["album", "camera"],
    success: async (res) => {
      const tempFilePaths = res.tempFilePaths;

      uni.showLoading({
        title: "上传中...",
      });

      try {
        // 调用上传文件API
        const result = await uploadFile(
          tempFilePaths[0],
          (progress: number) => {
            console.log("上传进度:", progress);
          }
        );

        if (result && result.code === 200) {
          // 上传成功，获取文件路径
          const fileUrl = result.data.url;

          // 调用更新头像API
          const data = {
            id: userInfo.value.id,
            avatar: fileUrl,
            version: userInfo.value.version,
          };
          const updateResult = await updateUser(data);

          if (updateResult.code === 200) {
            // 更新成功，更新本地用户信息
            userInfo.value.avatar = fileUrl;
            saveUserInfoToStorage(userInfo.value);

            uni.showToast({
              title: "头像更新成功",
              icon: "success",
            });
          } else {
            uni.showToast({
              title: updateResult.message || "头像信息更新失败",
              icon: "none",
            });
          }
        } else {
          uni.showToast({
            title: result?.message || "上传失败",
            icon: "none",
          });
        }
      } catch (error: unknown) {
        console.error("上传或更新失败:", error);
        uni.showToast({
          title: error instanceof Error ? error.message : "上传失败",
          icon: "none",
        });
      } finally {
        uni.hideLoading();
      }
    },
  });
};

// 切换消息推送状态
const togglePushNotification = (value: boolean) => {
  userInfo.value.pushEnabled = value;
  // 更新本地存储中的用户信息
  saveUserInfoToStorage(userInfo.value);

  uni.showToast({
    title: value ? "已开启消息推送" : "已关闭消息推送",
    icon: "none",
  });
};

// 切换深色模式
const toggleDarkMode = (value: boolean) => {
  userInfo.value.darkMode = value;
  // 更新本地存储中的用户信息
  saveUserInfoToStorage(userInfo.value);

  // 这里可以添加深色模式的切换逻辑
  uni.showToast({
    title: value ? "已切换到深色模式" : "已切换到浅色模式",
    icon: "none",
  });
};

// 退出登录
const logout = () => {
  uni.showModal({
    title: "提示",
    content: "确定要退出登录吗？",
    success: (res) => {
      if (res.confirm) {
        // 清除本地存储的用户信息
        store.dispatch("logout");
        uni.showToast({
          title: "已退出登录",
          icon: "success",
        });

        // 退出后返回首页
        setTimeout(() => {
          uni.reLaunch({
            url: "/pages/views/home/<USER>",
          });
        }, 500);
      }
    },
  });
};
</script>

<style scoped lang="scss">
.settings {
  min-height: 100vh;
  background-color: #f5f5f5;
  padding-bottom: 30px;
}

.settings-content {
  padding: 16px; // 标准间距
}

.settings-section {
  background-color: #fff;
  border-radius: 12px; // 使用规范中的 xl 圆角
  margin-bottom: 16px; // 标准间距
  overflow: hidden;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);
}

.settings-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16px; // 标准间距
  position: relative;
}

.settings-item-divider {
  height: 1px;
  background-color: #f0f0f0;
  margin-left: 16px; // 标准间距
}

.settings-label {
  font-size: 16px; // 使用规范中的 base 字体大小
  color: #333333; // 使用规范中的主要文本颜色
  font-weight: 400; // 使用规范中的 Normal 字重
}

.settings-value {
  display: flex;
  align-items: center;
}

.settings-value text {
  font-size: 16px; // 使用规范中的 base 字体大小
  color: #666666; // 使用规范中的次要文本颜色
  margin-right: 8px; // 常规间距
}

.avatar {
  width: 40px;
  height: 40px;
  border-radius: 50%; // 完全圆形
  margin-right: 8px; // 常规间距
  object-fit: cover;
}

.settings-logout {
  background-color: #fff;
  color: #ef4444; // 使用规范中的奥特曼红(Primary)颜色
  text-align: center;
  padding: 16px 0; // 标准间距
  font-size: 16px; // 使用规范中的 base 字体大小
  border-radius: 12px; // 使用规范中的 xl 圆角
  margin-top: 24px; // 大间距
  font-weight: 500; // 使用规范中的 Medium 字重
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);
}

:root {
  --gradient-secondary: linear-gradient(to right, #10b981, #34d399);
}

/* 修复 SCSS 语法错误并简化 CSS 结构 */
:deep(.up-switch--checked) {
  background: linear-gradient(to right, #10b981, #34d399) !important;
}

:deep(.up-switch__node) {
  background-color: #ffffff !important;
}
</style>
