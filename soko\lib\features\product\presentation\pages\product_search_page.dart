import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:go_router/go_router.dart';

import 'package:soko/core/theme/app_colors.dart';
import 'package:soko/core/theme/app_text_styles.dart';
import 'package:soko/shared/presentation/widgets/loading_widget.dart';
import 'package:soko/features/product/presentation/providers/product_search_provider.dart';
import 'package:soko/features/product/presentation/providers/product_list_provider.dart';
import 'package:soko/features/product/presentation/widgets/product_card.dart';

/// 商品搜索页面
class ProductSearchPage extends ConsumerStatefulWidget {

  const ProductSearchPage({
    super.key,
    this.initialKeyword,
  });
  final String? initialKeyword;

  @override
  ConsumerState<ProductSearchPage> createState() => _ProductSearchPageState();
}

class _ProductSearchPageState extends ConsumerState<ProductSearchPage> {
  final TextEditingController _searchController = TextEditingController();
  final FocusNode _searchFocusNode = FocusNode();
  bool _showSearchResults = false;

  @override
  void initState() {
    super.initState();
    
    if (widget.initialKeyword != null) {
      _searchController.text = widget.initialKeyword!;
      _performSearch(widget.initialKeyword!);
    }

    _searchFocusNode.addListener(() {
      if (_searchFocusNode.hasFocus && _searchController.text.isEmpty) {
        setState(() {
          _showSearchResults = false;
        });
      }
    });
  }

  @override
  void dispose() {
    _searchController.dispose();
    _searchFocusNode.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppColors.background,
      appBar: _buildAppBar(),
      body: _showSearchResults ? _buildSearchResults() : _buildSearchHome(),
    );
  }

  /// 构建AppBar
  PreferredSizeWidget _buildAppBar() {
    return AppBar(
      backgroundColor: Colors.white,
      elevation: 1,
      leading: IconButton(
        onPressed: () => context.pop(),
        icon: const Icon(
          Icons.arrow_back,
          color: AppColors.textPrimary,
        ),
      ),
      title: Container(
        height: 36.h,
        decoration: BoxDecoration(
          color: AppColors.background,
          borderRadius: BorderRadius.circular(18.r),
        ),
        child: TextField(
          controller: _searchController,
          focusNode: _searchFocusNode,
          decoration: InputDecoration(
            hintText: '搜索商品',
            hintStyle: AppTextStyles.bodyMedium.copyWith(
              color: AppColors.textTertiary,
            ),
            prefixIcon: Icon(
              Icons.search,
              color: AppColors.textTertiary,
              size: 20.sp,
            ),
            border: InputBorder.none,
            contentPadding: EdgeInsets.symmetric(vertical: 8.h),
          ),
          onChanged: (value) {
            if (value.isNotEmpty) {
              ref.read(searchSuggestionProvider.notifier).getSuggestions(value);
            } else {
              ref.read(searchSuggestionProvider.notifier).clearSuggestions();
              setState(() {
                _showSearchResults = false;
              });
            }
          },
          onSubmitted: (value) {
            if (value.trim().isNotEmpty) {
              _performSearch(value.trim());
            }
          },
        ),
      ),
      actions: [
        TextButton(
          onPressed: () {
            final keyword = _searchController.text.trim();
            if (keyword.isNotEmpty) {
              _performSearch(keyword);
            }
          },
          child: Text(
            '搜索',
            style: AppTextStyles.bodyMedium.copyWith(
              color: AppColors.primary,
              fontWeight: FontWeight.w600,
            ),
          ),
        ),
      ],
    );
  }

  /// 构建搜索首页
  Widget _buildSearchHome() {
    final suggestionState = ref.watch(searchSuggestionProvider);
    final historyState = ref.watch(searchHistoryProvider);
    final hotSearchState = ref.watch(hotSearchProvider);

    return SingleChildScrollView(
      padding: EdgeInsets.all(16.w),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // 搜索建议
          if (suggestionState.suggestions.isNotEmpty) ...[
            _buildSuggestions(suggestionState.suggestions),
            SizedBox(height: 24.h),
          ],
          
          // 搜索历史
          if (historyState.history.isNotEmpty) ...[
            _buildSearchHistory(historyState.history),
            SizedBox(height: 24.h),
          ],
          
          // 热门搜索
          _buildHotSearch(hotSearchState),
        ],
      ),
    );
  }

  /// 构建搜索建议
  Widget _buildSuggestions(List<String> suggestions) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          '搜索建议',
          style: AppTextStyles.bodyLarge.copyWith(
            fontWeight: FontWeight.w600,
          ),
        ),
        SizedBox(height: 12.h),
        ...suggestions.map((suggestion) => ListTile(
          contentPadding: EdgeInsets.zero,
          leading: Icon(
            Icons.search,
            color: AppColors.textTertiary,
            size: 20.sp,
          ),
          title: Text(
            suggestion,
            style: AppTextStyles.bodyMedium,
          ),
          onTap: () => _performSearch(suggestion),
        ),),
      ],
    );
  }

  /// 构建搜索历史
  Widget _buildSearchHistory(List<String> history) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          children: [
            Text(
              '搜索历史',
              style: AppTextStyles.bodyLarge.copyWith(
                fontWeight: FontWeight.w600,
              ),
            ),
            const Spacer(),
            IconButton(
              onPressed: () {
                ref.read(searchHistoryProvider.notifier).clearHistory();
              },
              icon: Icon(
                Icons.delete_outline,
                color: AppColors.textTertiary,
                size: 20.sp,
              ),
            ),
          ],
        ),
        SizedBox(height: 8.h),
        Wrap(
          spacing: 8.w,
          runSpacing: 8.h,
          children: history.map((keyword) => GestureDetector(
            onTap: () => _performSearch(keyword),
            child: Container(
              padding: EdgeInsets.symmetric(horizontal: 12.w, vertical: 6.h),
              decoration: BoxDecoration(
                color: Colors.white,
                borderRadius: BorderRadius.circular(16.r),
                border: Border.all(color: AppColors.borderLight),
              ),
              child: Row(
                mainAxisSize: MainAxisSize.min,
                children: [
                  Text(
                    keyword,
                    style: AppTextStyles.bodySmall.copyWith(
                      color: AppColors.textSecondary,
                    ),
                  ),
                  SizedBox(width: 4.w),
                  GestureDetector(
                    onTap: () {
                      ref.read(searchHistoryProvider.notifier).removeHistory(keyword);
                    },
                    child: Icon(
                      Icons.close,
                      size: 14.sp,
                      color: AppColors.textTertiary,
                    ),
                  ),
                ],
              ),
            ),
          ),).toList(),
        ),
      ],
    );
  }

  /// 构建热门搜索
  Widget _buildHotSearch(HotSearchState hotSearchState) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          '热门搜索',
          style: AppTextStyles.bodyLarge.copyWith(
            fontWeight: FontWeight.w600,
          ),
        ),
        SizedBox(height: 12.h),
        if (hotSearchState.isLoading)
          const LoadingWidget()
        else if (hotSearchState.error != null)
          Text(
            '加载失败',
            style: AppTextStyles.bodyMedium.copyWith(
              color: AppColors.textTertiary,
            ),
          )
        else
          Wrap(
            spacing: 8.w,
            runSpacing: 8.h,
            children: hotSearchState.hotKeywords.asMap().entries.map((entry) {
              final index = entry.key;
              final keyword = entry.value;
              final isTop3 = index < 3;
              
              return GestureDetector(
                onTap: () => _performSearch(keyword),
                child: Container(
                  padding: EdgeInsets.symmetric(horizontal: 12.w, vertical: 6.h),
                  decoration: BoxDecoration(
                    color: isTop3 ? AppColors.primary.withOpacity(0.1) : Colors.white,
                    borderRadius: BorderRadius.circular(16.r),
                    border: Border.all(
                      color: isTop3 ? AppColors.primary.withOpacity(0.3) : AppColors.borderLight,
                    ),
                  ),
                  child: Text(
                    keyword,
                    style: AppTextStyles.bodySmall.copyWith(
                      color: isTop3 ? AppColors.primary : AppColors.textSecondary,
                      fontWeight: isTop3 ? FontWeight.w600 : FontWeight.normal,
                    ),
                  ),
                ),
              );
            }).toList(),
          ),
      ],
    );
  }

  /// 构建搜索结果
  Widget _buildSearchResults() {
    return ProductListPage(
      keyword: _searchController.text.trim(),
    );
  }

  /// 执行搜索
  void _performSearch(String keyword) {
    _searchController.text = keyword;
    _searchFocusNode.unfocus();
    
    // 添加到搜索历史
    ref.read(searchHistoryProvider.notifier).addHistory(keyword);
    
    // 清除搜索建议
    ref.read(searchSuggestionProvider.notifier).clearSuggestions();
    
    setState(() {
      _showSearchResults = true;
    });
  }
}
