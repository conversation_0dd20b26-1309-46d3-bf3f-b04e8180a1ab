<template>
	<view
	    class="up-row"
		ref="up-row"
	    :style="[rowStyle]"
	    @tap="clickHandler"
	>
		<slot />
	</view>
</template>

<script>
	import { propsRow } from './props';
	import { mpMixin } from '../../libs/mixin/mpMixin';
	import { mixin } from '../../libs/mixin/mixin';
	import { addUnit, addStyle, deepMerge, sleep } from '../../libs/function/index';
	/**
	 * Row 栅格系统中的行
	 * @description 通过基础的 12 分栏，迅速简便地创建布局 
	 * @tutorial https://ijry.github.io/uview-plus/components/layout.html
	 * @property {String | Number}	gutter		栅格间隔，左右各为此值的一半，单位px  (默认 0 )
	 * @property {String}			justify		水平排列方式(微信小程序暂不支持) 可选值为`start`(或`flex-start`)、`end`(或`flex-end`)、`center`、`around`(或`space-around`)、`between`(或`space-between`)  (默认 'start' )
	 * @property {String}			align		垂直排列方式 (默认 'center' )
	 * @property {Object}			customStyle	定义需要用到的外部样式
	 * 
	 * @event {Function} click row被点击
	 * @example <up-row justify="space-between" customStyle="margin-bottom: 10px"></up-row>
	 */
	export default {
		name: "up-row",
		mixins: [mpMixin, mixin, propsRow],
		data() {
			return {
				
			}
		},
		computed: {
			uJustify(): string {
				if (this.justify == 'end' || this.justify == 'start') return 'flex-' + this.justify
				else if (this.justify == 'around' || this.justify == 'between') return 'space-' + this.justify
				else return this.justify
			},
			uAlignItem(): string {
				if (this.align == 'top') return 'flex-start'
				if (this.align == 'bottom') return 'flex-end'
				else return this.align
			},
			rowStyle(): any {
				const style = {
					alignItems: this.uAlignItem,
					justifyContent: this.uJustify
				}
				// 通过给up-row左右两边的负外边距，消除up-col在有gutter时，第一个和最后一个元素的左内边距和右内边距造成的影响
				if(this.gutter != '') {
					style['marginLeft'] = addUnit((0-parseInt(this.gutter.toString()))/2)
					style['marginRight'] = addUnit((0-parseInt(this.gutter.toString()))/2)
				}
				return deepMerge(style, addStyle(this.customStyle))
			}
		},
		emits: ["click"],
		methods: {
			clickHandler(e: any): void {
				this.$emit('click')
			},
			getComponentWidth(): Promise<number|null> {
				return new Promise(resolve => {
					// 延时一定时间，以确保节点渲染完成
					//sleep().then(() => {
						// uview-plus封装的获取节点的方法，详见文档
						this.upGetRect('.up-row').then((res: UTSJSONObject) => {
							// console.log(res)
							resolve(res.getNumber('width'))
						})
					//})
				})
			},
		}
	}
</script>

<style lang="scss" scoped>
	@import "../../libs/css/components.scss";
	
	.up-row {
		@include flex;
	}
</style>
