import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

import 'package:soko/core/theme/app_colors.dart';
import 'package:soko/core/theme/app_text_styles.dart';

/// 商品筛选栏
class ProductFilterBar extends StatelessWidget {

  const ProductFilterBar({
    super.key,
    required this.currentSortBy,
    required this.currentSortOrder,
    this.onFilterTap,
    this.onSortChanged,
    this.hasActiveFilters = false,
  });
  final String currentSortBy;
  final String currentSortOrder;
  final VoidCallback? onFilterTap;
  final Function(String sortBy, String sortOrder)? onSortChanged;
  final bool hasActiveFilters;

  @override
  Widget build(BuildContext context) {
    return Container(
      height: 48.h,
      padding: EdgeInsets.symmetric(horizontal: 16.w),
      decoration: const BoxDecoration(
        color: Colors.white,
        border: Border(
          bottom: BorderSide(
            color: AppColors.borderLight,
          ),
        ),
      ),
      child: Row(
        children: [
          // 筛选按钮
          _buildFilterButton(),
          SizedBox(width: 16.w),
          // 排序选项
          Expanded(
            child: Row(
              children: [
                _buildSortButton('综合', 'createTime', 'desc'),
                _buildSortButton('价格', 'minPrice', currentSortOrder),
                _buildSortButton('销量', 'totalSales', 'desc'),
                _buildSortButton('新品', 'createTime', 'desc'),
              ],
            ),
          ),
        ],
      ),
    );
  }

  /// 构建筛选按钮
  Widget _buildFilterButton() {
    return InkWell(
      onTap: onFilterTap,
      child: Container(
        padding: EdgeInsets.symmetric(horizontal: 12.w, vertical: 8.h),
        decoration: BoxDecoration(
          color: hasActiveFilters
              ? AppColors.primary.withOpacity(0.1)
              : Colors.transparent,
          borderRadius: BorderRadius.circular(6.r),
          border: Border.all(
            color: hasActiveFilters ? AppColors.primary : AppColors.border,
          ),
        ),
        child: Row(
          mainAxisSize: MainAxisSize.min,
          children: [
            Icon(
              Icons.filter_list,
              size: 16.sp,
              color: hasActiveFilters
                  ? AppColors.primary
                  : AppColors.textSecondary,
            ),
            SizedBox(width: 4.w),
            Text(
              '筛选',
              style: AppTextStyles.bodySmall.copyWith(
                color: hasActiveFilters
                    ? AppColors.primary
                    : AppColors.textSecondary,
                fontWeight:
                    hasActiveFilters ? FontWeight.w600 : FontWeight.normal,
              ),
            ),
            if (hasActiveFilters) ...[
              SizedBox(width: 2.w),
              Container(
                width: 6.w,
                height: 6.w,
                decoration: const BoxDecoration(
                  color: AppColors.primary,
                  shape: BoxShape.circle,
                ),
              ),
            ],
          ],
        ),
      ),
    );
  }

  /// 构建排序按钮
  Widget _buildSortButton(String label, String sortBy, String sortOrder) {
    final isActive = currentSortBy == sortBy;
    final isAscending = sortOrder == 'asc';

    return Expanded(
      child: InkWell(
        onTap: () {
          if (onSortChanged != null) {
            if (sortBy == 'minPrice') {
              // 价格排序：切换升序/降序
              final newOrder =
                  isActive && currentSortOrder == 'asc' ? 'desc' : 'asc';
              onSortChanged!(sortBy, newOrder);
            } else {
              // 其他排序：固定降序
              onSortChanged!(sortBy, sortOrder);
            }
          }
        },
        child: Container(
          height: double.infinity,
          alignment: Alignment.center,
          child: Row(
            mainAxisAlignment: MainAxisAlignment.center,
            mainAxisSize: MainAxisSize.min,
            children: [
              Text(
                label,
                style: AppTextStyles.bodySmall.copyWith(
                  color: isActive ? AppColors.primary : AppColors.textPrimary,
                  fontWeight: isActive ? FontWeight.w600 : FontWeight.normal,
                ),
              ),
              if (sortBy == 'minPrice') ...[
                SizedBox(width: 2.w),
                Column(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    Icon(
                      Icons.keyboard_arrow_up,
                      size: 12.sp,
                      color: isActive && !isAscending
                          ? AppColors.primary
                          : AppColors.textTertiary,
                    ),
                    Icon(
                      Icons.keyboard_arrow_down,
                      size: 12.sp,
                      color: isActive && isAscending
                          ? AppColors.primary
                          : AppColors.textTertiary,
                    ),
                  ],
                ),
              ],
            ],
          ),
        ),
      ),
    );
  }
}

/// 商品筛选弹窗
class ProductFilterDialog extends StatefulWidget {

  const ProductFilterDialog({
    super.key,
    this.selectedCategory,
    this.selectedBrand,
    this.selectedCondition,
    this.minPrice,
    this.maxPrice,
    this.onApply,
  });
  final String? selectedCategory;
  final String? selectedBrand;
  final String? selectedCondition;
  final double? minPrice;
  final double? maxPrice;
  final Function({
    String? category,
    String? brand,
    String? condition,
    double? minPrice,
    double? maxPrice,
  })? onApply;

  @override
  State<ProductFilterDialog> createState() => _ProductFilterDialogState();
}

class _ProductFilterDialogState extends State<ProductFilterDialog> {
  String? _selectedCategory;
  String? _selectedBrand;
  String? _selectedCondition;
  final TextEditingController _minPriceController = TextEditingController();
  final TextEditingController _maxPriceController = TextEditingController();

  @override
  void initState() {
    super.initState();
    _selectedCategory = widget.selectedCategory;
    _selectedBrand = widget.selectedBrand;
    _selectedCondition = widget.selectedCondition;
    if (widget.minPrice != null) {
      _minPriceController.text = widget.minPrice!.toString();
    }
    if (widget.maxPrice != null) {
      _maxPriceController.text = widget.maxPrice!.toString();
    }
  }

  @override
  void dispose() {
    _minPriceController.dispose();
    _maxPriceController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Dialog(
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(12.r),
      ),
      child: Container(
        width: double.infinity,
        constraints: BoxConstraints(maxHeight: 600.h),
        padding: EdgeInsets.all(20.w),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // 标题
            Row(
              children: [
                Text(
                  '筛选条件',
                  style: AppTextStyles.headlineSmall.copyWith(
                    fontWeight: FontWeight.bold,
                  ),
                ),
                const Spacer(),
                IconButton(
                  onPressed: () => Navigator.of(context).pop(),
                  icon: Icon(Icons.close, size: 20.sp),
                  padding: EdgeInsets.zero,
                  constraints: BoxConstraints(
                    minWidth: 32.w,
                    minHeight: 32.w,
                  ),
                ),
              ],
            ),
            SizedBox(height: 20.h),
            // 筛选内容
            Expanded(
              child: SingleChildScrollView(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    _buildPriceRangeSection(),
                    SizedBox(height: 24.h),
                    _buildConditionSection(),
                  ],
                ),
              ),
            ),
            SizedBox(height: 20.h),
            // 底部按钮
            Row(
              children: [
                Expanded(
                  child: OutlinedButton(
                    onPressed: _resetFilters,
                    child: const Text('重置'),
                  ),
                ),
                SizedBox(width: 12.w),
                Expanded(
                  child: ElevatedButton(
                    onPressed: _applyFilters,
                    child: const Text('确定'),
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  /// 构建价格区间选择
  Widget _buildPriceRangeSection() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          '价格区间',
          style: AppTextStyles.bodyLarge.copyWith(
            fontWeight: FontWeight.w600,
          ),
        ),
        SizedBox(height: 12.h),
        Row(
          children: [
            Expanded(
              child: TextField(
                controller: _minPriceController,
                keyboardType: TextInputType.number,
                decoration: InputDecoration(
                  hintText: '最低价',
                  prefixText: '¥',
                  border: OutlineInputBorder(
                    borderRadius: BorderRadius.circular(8.r),
                  ),
                  contentPadding: EdgeInsets.symmetric(
                    horizontal: 12.w,
                    vertical: 8.h,
                  ),
                ),
              ),
            ),
            Padding(
              padding: EdgeInsets.symmetric(horizontal: 8.w),
              child: Text('至', style: AppTextStyles.bodyMedium),
            ),
            Expanded(
              child: TextField(
                controller: _maxPriceController,
                keyboardType: TextInputType.number,
                decoration: InputDecoration(
                  hintText: '最高价',
                  prefixText: '¥',
                  border: OutlineInputBorder(
                    borderRadius: BorderRadius.circular(8.r),
                  ),
                  contentPadding: EdgeInsets.symmetric(
                    horizontal: 12.w,
                    vertical: 8.h,
                  ),
                ),
              ),
            ),
          ],
        ),
      ],
    );
  }

  /// 构建商品状态选择
  Widget _buildConditionSection() {
    const conditions = [
      {'value': null, 'label': '全部'},
      {'value': 'NEW', 'label': '全新'},
      {'value': 'LIKE_NEW', 'label': '几乎全新'},
      {'value': 'GOOD', 'label': '良好'},
      {'value': 'FAIR', 'label': '一般'},
    ];

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          '商品状态',
          style: AppTextStyles.bodyLarge.copyWith(
            fontWeight: FontWeight.w600,
          ),
        ),
        SizedBox(height: 12.h),
        Wrap(
          spacing: 8.w,
          runSpacing: 8.h,
          children: conditions.map((condition) {
            final isSelected = _selectedCondition == condition['value'];
            return InkWell(
              onTap: () {
                setState(() {
                  _selectedCondition = condition['value'];
                });
              },
              child: Container(
                padding: EdgeInsets.symmetric(horizontal: 16.w, vertical: 8.h),
                decoration: BoxDecoration(
                  color: isSelected ? AppColors.primary : Colors.transparent,
                  borderRadius: BorderRadius.circular(20.r),
                  border: Border.all(
                    color: isSelected ? AppColors.primary : AppColors.grey300,
                  ),
                ),
                child: Text(
                  condition['label']! as String,
                  style: AppTextStyles.bodySmall.copyWith(
                    color: isSelected ? Colors.white : AppColors.grey700,
                    fontWeight:
                        isSelected ? FontWeight.w600 : FontWeight.normal,
                  ),
                ),
              ),
            );
          }).toList(),
        ),
      ],
    );
  }

  /// 重置筛选条件
  void _resetFilters() {
    setState(() {
      _selectedCategory = null;
      _selectedBrand = null;
      _selectedCondition = null;
      _minPriceController.clear();
      _maxPriceController.clear();
    });
  }

  /// 应用筛选条件
  void _applyFilters() {
    double? minPrice;
    double? maxPrice;

    if (_minPriceController.text.isNotEmpty) {
      minPrice = double.tryParse(_minPriceController.text);
    }
    if (_maxPriceController.text.isNotEmpty) {
      maxPrice = double.tryParse(_maxPriceController.text);
    }

    if (widget.onApply != null) {
      widget.onApply!(
        category: _selectedCategory,
        brand: _selectedBrand,
        condition: _selectedCondition,
        minPrice: minPrice,
        maxPrice: maxPrice,
      );
    }

    Navigator.of(context).pop();
  }
}
