// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'payment_request.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

_$PaymentRequestImpl _$$PaymentRequestImplFromJson(Map<String, dynamic> json) =>
    _$PaymentRequestImpl(
      orderId: json['orderId'] as String,
      amount: (json['amount'] as num).toInt(),
      paymentMethod: $enumDecode(_$PaymentMethodEnumMap, json['paymentMethod']),
      subject: json['subject'] as String,
      body: json['body'] as String?,
      notifyUrl: json['notifyUrl'] as String?,
      returnUrl: json['returnUrl'] as String?,
      timeoutMinutes: (json['timeoutMinutes'] as num?)?.toInt() ?? 30,
      isSandbox: json['isSandbox'] as bool? ?? true,
      extraParams: json['extraParams'] as Map<String, dynamic>?,
    );

Map<String, dynamic> _$$PaymentRequestImplToJson(
        _$PaymentRequestImpl instance) =>
    <String, dynamic>{
      'orderId': instance.orderId,
      'amount': instance.amount,
      'paymentMethod': _$PaymentMethodEnumMap[instance.paymentMethod]!,
      'subject': instance.subject,
      'body': instance.body,
      'notifyUrl': instance.notifyUrl,
      'returnUrl': instance.returnUrl,
      'timeoutMinutes': instance.timeoutMinutes,
      'isSandbox': instance.isSandbox,
      'extraParams': instance.extraParams,
    };

const _$PaymentMethodEnumMap = {
  PaymentMethod.alipay: 'alipay',
  PaymentMethod.wechat: 'wechat',
  PaymentMethod.unionpay: 'unionpay',
  PaymentMethod.balance: 'balance',
};

_$PaymentResponseImpl _$$PaymentResponseImplFromJson(
        Map<String, dynamic> json) =>
    _$PaymentResponseImpl(
      paymentOrderNo: json['paymentOrderNo'] as String,
      thirdPartyOrderNo: json['thirdPartyOrderNo'] as String?,
      paymentMethod: $enumDecode(_$PaymentMethodEnumMap, json['paymentMethod']),
      status: $enumDecode(_$PaymentStatusEnumMap, json['status']),
      amount: (json['amount'] as num).toInt(),
      payTime: json['payTime'] == null
          ? null
          : DateTime.parse(json['payTime'] as String),
      paymentParams: json['paymentParams'] as Map<String, dynamic>?,
      errorMessage: json['errorMessage'] as String?,
      extraInfo: json['extraInfo'] as Map<String, dynamic>?,
    );

Map<String, dynamic> _$$PaymentResponseImplToJson(
        _$PaymentResponseImpl instance) =>
    <String, dynamic>{
      'paymentOrderNo': instance.paymentOrderNo,
      'thirdPartyOrderNo': instance.thirdPartyOrderNo,
      'paymentMethod': _$PaymentMethodEnumMap[instance.paymentMethod]!,
      'status': _$PaymentStatusEnumMap[instance.status]!,
      'amount': instance.amount,
      'payTime': instance.payTime?.toIso8601String(),
      'paymentParams': instance.paymentParams,
      'errorMessage': instance.errorMessage,
      'extraInfo': instance.extraInfo,
    };

const _$PaymentStatusEnumMap = {
  PaymentStatus.pending: 'pending',
  PaymentStatus.processing: 'processing',
  PaymentStatus.success: 'success',
  PaymentStatus.failed: 'failed',
  PaymentStatus.cancelled: 'cancelled',
  PaymentStatus.timeout: 'timeout',
};
