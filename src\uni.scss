/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */

/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */

/* uni.scss */
// @import 'uview-plus/theme.scss';
@import '@/uni_modules/uview-plus/theme.scss';

/* 特摄模玩潮流主题色彩系统 */

/* 主色调系统 */
$uni-primary: #EF4444; // 奥特曼红
$uni-secondary: #10B981; // 假面骑士绿  
$uni-accent: #3B82F6; // 超级战队蓝

/* 特色色调系统 */
$uni-heroic: #F59E0B; // 英雄金
$uni-villain: #8B5CF6; // 反派紫
$uni-neon: #EC4899; // 霓虹粉
$uni-cyber: #06B6D4; // 赛博蓝
$uni-energy: #16A34A; // 能量绿
$uni-mecha: #6B7280; // 机械灰

/* 行为相关颜色 */
$uni-color-primary: $uni-primary;
$uni-color-success: #52C41A; // 成功绿
$uni-color-warning: #FAAD14; // 警告黄
$uni-color-error: #FF4D4F; // 错误红

/* 文字基本颜色 */
$uni-text-color: #333; // 基本色
$uni-text-color-inverse: #fff; // 反色
$uni-text-color-grey: #888; // 辅助灰色，稍微明亮点
$uni-text-color-placeholder: #999;
$uni-text-color-disable: #bbb;

/* 背景颜色 */
$uni-bg-color: #fff;
$uni-bg-color-grey: #f9f9f9; // 稍微亮一点
$uni-bg-color-hover: #f2f2f2; // 点击状态颜色
$uni-bg-color-mask: rgba(0, 0, 0, 0.4); // 遮罩颜色

/* 边框颜色 */
$uni-border-color: #e8e8e8; // 更轻一点的边框

/* 尺寸变量 */

/* 文字尺寸 */
$uni-font-size-sm: 12px;
$uni-font-size-base: 14px;
$uni-font-size-lg: 16px;

/* 图片尺寸 */
$uni-img-size-sm: 20px;
$uni-img-size-base: 26px;
$uni-img-size-lg: 40px;

/* Border Radius - 更大的圆角 */
$uni-border-radius-sm: 4px;
$uni-border-radius-base: 8px;
$uni-border-radius-lg: 16px;
$uni-border-radius-circle: 50%;

/* 水平间距 */
$uni-spacing-row-sm: 5px;
$uni-spacing-row-base: 10px;
$uni-spacing-row-lg: 15px;

/* 垂直间距 */
$uni-spacing-col-sm: 4px;
$uni-spacing-col-base: 8px;
$uni-spacing-col-lg: 12px;

/* 透明度 */
$uni-opacity-disabled: 0.3; // 组件禁用态的透明度

/* 阴影 */
$uni-shadow-sm: 0 2px 8px rgba(0, 0, 0, 0.05);
$uni-shadow-base: 0 4px 12px rgba(0, 0, 0, 0.08);
$uni-shadow-lg: 0 8px 20px rgba(0, 0, 0, 0.12);

/* 文章场景相关 */
$uni-color-title: #2c405a; // 文章标题颜色
$uni-font-size-title: 20px;
$uni-color-subtitle: #555; // 二级标题颜色
$uni-font-size-subtitle: 18px;
$uni-color-paragraph: #3f536e; // 文章段落颜色
$uni-font-size-paragraph: 15px;