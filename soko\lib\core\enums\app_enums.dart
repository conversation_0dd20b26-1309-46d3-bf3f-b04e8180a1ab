/// 应用枚举定义
library;

/// 用户性别
enum Gender {
  male('male', '男'),
  female('female', '女'),
  unknown('unknown', '未知');

  const Gender(this.value, this.label);
  final String value;
  final String label;
}

/// 用户状态
enum UserStatus {
  active('active', '正常'),
  inactive('inactive', '未激活'),
  banned('banned', '已封禁'),
  deleted('deleted', '已删除');

  const UserStatus(this.value, this.label);
  final String value;
  final String label;
}

/// 会员等级
enum MemberLevel {
  normal('normal', '普通会员'),
  vip('vip', 'VIP会员'),
  svip('svip', 'SVIP会员');

  const MemberLevel(this.value, this.label);
  final String value;
  final String label;
}

/// 订单状态
enum OrderStatus {
  pending('pending', '待付款'),
  paid('paid', '已付款'),
  shipped('shipped', '已发货'),
  delivered('delivered', '已送达'),
  completed('completed', '已完成'),
  cancelled('cancelled', '已取消'),
  refunded('refunded', '已退款');

  const OrderStatus(this.value, this.label);
  final String value;
  final String label;
}

/// 订单类型
enum OrderType {
  purchase('purchase', '购买订单'),
  recycle('recycle', '回收订单');

  const OrderType(this.value, this.label);
  final String value;
  final String label;
}

/// 支付方式
enum PaymentMethod {
  alipay('alipay', '支付宝'),
  wechat('wechat', '微信支付'),
  unionpay('unionpay', '银联支付'),
  balance('balance', '余额支付');

  const PaymentMethod(this.value, this.label);
  final String value;
  final String label;
}

/// 支付状态
enum PaymentStatus {
  pending('pending', '待支付'),
  processing('processing', '支付中'),
  success('success', '支付成功'),
  failed('failed', '支付失败'),
  cancelled('cancelled', '已取消');

  const PaymentStatus(this.value, this.label);
  final String value;
  final String label;
}

/// 商品状态
enum ProductStatus {
  active('active', '上架'),
  inactive('inactive', '下架'),
  soldOut('sold_out', '售罄'),
  deleted('deleted', '已删除');

  const ProductStatus(this.value, this.label);
  final String value;
  final String label;
}

/// 商品分类
enum ProductCategory {
  ultraman('ultraman', '奥特曼'),
  kamenRider('kamen_rider', '假面骑士'),
  superSentai('super_sentai', '超级战队'),
  gundam('gundam', '高达模型'),
  figure('figure', '手办'),
  other('other', '其他');

  const ProductCategory(this.value, this.label);
  final String value;
  final String label;
}

/// 商品条件
enum ProductCondition {
  brandNew('brand_new', '全新'),
  likeNew('like_new', '几乎全新'),
  excellent('excellent', '优秀'),
  good('good', '良好'),
  fair('fair', '一般'),
  poor('poor', '较差');

  const ProductCondition(this.value, this.label);
  final String value;
  final String label;
}

/// 回收订单状态
enum RecycleOrderStatus {
  created('created', '已创建'),
  confirmed('confirmed', '已确认'),
  picked('picked', '已取件'),
  evaluated('evaluated', '已评估'),
  completed('completed', '已完成'),
  cancelled('cancelled', '已取消');

  const RecycleOrderStatus(this.value, this.label);
  final String value;
  final String label;
}

/// 物流状态
enum ShippingStatus {
  pending('pending', '待发货'),
  shipped('shipped', '已发货'),
  inTransit('in_transit', '运输中'),
  delivered('delivered', '已送达'),
  failed('failed', '配送失败');

  const ShippingStatus(this.value, this.label);
  final String value;
  final String label;
}

/// 地址类型
enum AddressType {
  home('home', '家'),
  office('office', '公司'),
  other('other', '其他');

  const AddressType(this.value, this.label);
  final String value;
  final String label;
}

/// 优惠券类型
enum CouponType {
  discount('discount', '折扣券'),
  cashback('cashback', '满减券'),
  freeShipping('free_shipping', '包邮券');

  const CouponType(this.value, this.label);
  final String value;
  final String label;
}

/// 优惠券状态
enum CouponStatus {
  available('available', '可用'),
  used('used', '已使用'),
  expired('expired', '已过期');

  const CouponStatus(this.value, this.label);
  final String value;
  final String label;
}

/// 消息类型
enum MessageType {
  system('system', '系统消息'),
  order('order', '订单消息'),
  promotion('promotion', '促销消息'),
  announcement('announcement', '公告消息');

  const MessageType(this.value, this.label);
  final String value;
  final String label;
}

/// 消息状态
enum MessageStatus {
  unread('unread', '未读'),
  read('read', '已读'),
  deleted('deleted', '已删除');

  const MessageStatus(this.value, this.label);
  final String value;
  final String label;
}

/// 文件类型
enum FileType {
  image('image', '图片'),
  video('video', '视频'),
  audio('audio', '音频'),
  document('document', '文档'),
  other('other', '其他');

  const FileType(this.value, this.label);
  final String value;
  final String label;
}

/// 上传状态
enum UploadStatus {
  pending('pending', '待上传'),
  uploading('uploading', '上传中'),
  success('success', '上传成功'),
  failed('failed', '上传失败'),
  cancelled('cancelled', '已取消');

  const UploadStatus(this.value, this.label);
  final String value;
  final String label;
}

/// 网络状态
enum NetworkStatus {
  connected('connected', '已连接'),
  disconnected('disconnected', '已断开'),
  connecting('connecting', '连接中');

  const NetworkStatus(this.value, this.label);
  final String value;
  final String label;
}

/// 主题模式
enum ThemeMode {
  light('light', '浅色模式'),
  dark('dark', '深色模式'),
  system('system', '跟随系统');

  const ThemeMode(this.value, this.label);
  final String value;
  final String label;
}

/// 语言
enum AppLanguage {
  chinese('zh_CN', '简体中文'),
  english('en_US', 'English');

  const AppLanguage(this.value, this.label);
  final String value;
  final String label;
}

/// 排序方式
enum SortType {
  default_('default', '默认排序'),
  priceAsc('price_asc', '价格从低到高'),
  priceDesc('price_desc', '价格从高到低'),
  timeAsc('time_asc', '时间从早到晚'),
  timeDesc('time_desc', '时间从晚到早'),
  salesDesc('sales_desc', '销量从高到低'),
  ratingDesc('rating_desc', '评分从高到低');

  const SortType(this.value, this.label);
  final String value;
  final String label;
}

/// 筛选类型
enum FilterType {
  category('category', '分类'),
  price('price', '价格'),
  condition('condition', '成色'),
  brand('brand', '品牌'),
  location('location', '地区');

  const FilterType(this.value, this.label);
  final String value;
  final String label;
}

/// 评价等级
enum RatingLevel {
  excellent(5, '非常满意'),
  good(4, '满意'),
  average(3, '一般'),
  poor(2, '不满意'),
  terrible(1, '非常不满意');

  const RatingLevel(this.value, this.label);
  final int value;
  final String label;
}
