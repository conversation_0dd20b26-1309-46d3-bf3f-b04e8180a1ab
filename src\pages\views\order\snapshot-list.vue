<template>
  <view class="flex flex-col min-h-screen bg-slate-50 custom-nav-page">
    <!-- 自定义导航栏 -->
    <NavBar title="交易快照" bgColor="#FFFFFF" :showBack="true" />

    <!-- 主内容区域 -->
    <view class="flex-1 px-4 pt-0 pb-3 space-y-3 -mt-8">
      <!-- 说明文字 -->
      <view class="bg-white rounded-lg shadow-sm p-4">
        <view class="flex items-start">
          <view class="mr-2 mt-0.5">
            <van-icon name="info-o" size="16" color="#6B7280" />
          </view>
          <text class="text-sm text-text-secondary leading-relaxed">
            交易快照包含订单创建时的商品描述和下单信息，买卖双方和平台在发生交易争议时，该页面作为判定依据。
          </text>
        </view>
      </view>

      <!-- 订单包含的宝贝 -->
      <view class="bg-white rounded-lg shadow-sm">
        <view class="p-4 border-b border-slate-100 flex items-center">
          <view class="w-1 h-4 bg-primary rounded-sm mr-2"></view>
          <text class="text-base font-medium text-text-primary">订单包含的宝贝</text>
        </view>

        <!-- 加载状态 -->
        <view v-if="isLoading" class="p-4 space-y-3">
          <view v-for="i in 2" :key="i" class="flex gap-3 p-3 bg-slate-50 rounded-lg animate-pulse">
            <view class="h-20 w-20 rounded-lg bg-slate-200 flex-shrink-0"></view>
            <view class="flex-1 space-y-2">
              <view class="h-4 w-3/4 bg-slate-200 rounded-md"></view>
              <view class="h-4 w-1/2 bg-slate-200 rounded-md"></view>
              <view class="h-4 w-1/4 bg-slate-200 rounded-md"></view>
              <view class="flex justify-end mt-2">
                <view class="h-8 w-20 bg-slate-200 rounded-full"></view>
              </view>
            </view>
          </view>
        </view>

        <!-- 商品列表 -->
        <view v-else-if="orderItems && orderItems.length > 0" class="divide-y divide-slate-100">
          <view
            v-for="item in orderItems"
            :key="item.id"
            class="p-4" 
          >
            <view class="flex gap-3 mb-3"> 
              <!-- 商品图片 -->
              <view class="relative h-20 w-20 bg-slate-50 rounded-md overflow-hidden flex-shrink-0 border border-slate-100 shadow-sm">
                <image
                  :src="item.image || '/static/logo-back.png'"
                  :alt="item.name"
                  class="w-full h-full object-contain"
                  mode="aspectFit"
                />
              </view>

              <!-- 右侧文本信息 -->
              <view class="flex-1 min-w-0 flex flex-col justify-center">
                <!-- 商品名称 -->
                <text class="text-sm font-medium text-text-primary line-clamp-2 block mb-1">{{ item.name || '未知商品' }}</text>

                <!-- 副标题（规格） -->
                <view v-if="item.options" class="mb-1.5">
                  <text v-if="!isValidJSON(item.options)" class="text-xs text-text-secondary line-clamp-1">{{ item.options }}</text>
                  <view v-else class="flex flex-wrap">
                    <text class="text-xs text-text-secondary line-clamp-1">
                      <text v-for="(value, key, index) in parseSpecification(item.options)" :key="index">
                        {{ index > 0 ? ' | ' : '' }}{{ key }}: {{ value }}
                      </text>
                    </text>
                  </view>
                </view>
                <text v-else class="text-xs text-text-secondary block mb-1.5">&nbsp;</text>

                <!-- 价格和数量同一行 -->
                <view class="flex justify-between items-baseline">
                  <text class="text-lg font-semibold text-red-500">¥{{ item.price ? formatPrice(item.price) : '0.00' }}</text>
                  <text class="text-xs text-text-secondary">x {{ item.quantity || 1 }}</text>
                </view>
              </view>
            </view>

            <!-- 底部右侧的交易快照按钮 -->
            <view class="flex justify-end">
              <view
                @click="viewItemSnapshot(item)"
                class="border border-orange-400 text-orange-500 bg-orange-50 rounded-full px-4 py-1.5 text-xs font-medium hover:bg-orange-100 active:bg-orange-200 transition-all duration-200 cursor-pointer"
                style="line-height: normal;"
              >
                交易快照
              </view>
            </view>
          </view>
        </view>
        
        <!-- 无商品信息 -->
        <view v-else class="py-10 px-6 text-center">
          <van-icon name="warning-o" size="32" color="#9CA3AF" />
          <text class="text-sm text-text-secondary block mt-2">未找到订单商品信息</text>
          <view class="mt-4">
            <view class="inline-block bg-slate-100 text-text-secondary rounded-full px-4 py-1.5 text-xs" @click="fetchOrderItems">
              重新加载
            </view>
          </view>
        </view>
      </view>
    </view>
  </view>
</template>

<script lang="ts" setup>
import { ref, onMounted } from 'vue';
import NavBar from '@/components/NavBar.vue';
import { getOrderSnapshots } from '@/api/api'; // 导入获取订单快照列表的API

// 后端接口返回的数据类型
interface SnapshotApiData {
  id: string | number;
  orderId?: string;
  orderNo?: string;
  productId?: string;
  productSkuId?: string;
  productName?: string;
  specification?: string;
  description?: string;
  imageUrl?: string;
  price?: number | string;
  originalPrice?: number | string;
  quantity?: number;
  totalPrice?: number | string;
  realPrice?: number | string;
  salesType?: string;
}

// 页面使用的前端数据类型
interface SnapshotOrderItem {
  id: string | number;
  name?: string;
  image?: string;
  options?: string;
  description?: string;
  price?: number | string;
  originalPrice?: number | string;
  quantity?: number;
  totalPrice?: number | string;
  realPrice?: number | string;
  salesType?: string;
  snapshotId?: string | number;
}

const orderId = ref('');
const isLoading = ref(true);
const orderItems = ref<SnapshotOrderItem[]>([]);

onMounted(() => {
  const pages = getCurrentPages();
  const currentPage = pages[pages.length - 1];
  // @ts-ignore
  if (currentPage && currentPage.$page && currentPage.$page.options) {
    // @ts-ignore
    orderId.value = currentPage.$page.options.orderId || '';
  }

  if (orderId.value) {
    fetchOrderItems();
  } else {
    isLoading.value = false;
    uni.showToast({
      title: '订单ID缺失',
      icon: 'none',
    });
  }
});

// 检查字符串是否为有效的JSON
function isValidJSON(str: string): boolean {
  try {
    JSON.parse(str);
    return true;
  } catch (e) {
    return false;
  }
}

// 解析规格JSON字符串
function parseSpecification(jsonStr: string): Record<string, string> {
  try {
    const parsed = JSON.parse(jsonStr);
    
    // 如果是对象格式 {key1: value1, key2: value2}
    if (parsed && typeof parsed === 'object' && !Array.isArray(parsed)) {
      return parsed;
    }
    
    // 如果是数组格式 [{key: 'key1', value: 'value1'}, {key: 'key2', value: 'value2'}]
    if (Array.isArray(parsed)) {
      const result: Record<string, string> = {};
      parsed.forEach(item => {
        if (item.key && item.value) {
          result[item.key] = item.value;
        }
      });
      return result;
    }
    
    // 兜底返回空对象
    return {};
  } catch (e) {
    console.error('解析规格JSON失败:', e);
    return {};
  }
}

async function fetchOrderItems() {
  isLoading.value = true;
  try {
    const response = await getOrderSnapshots(orderId.value);
    
    if (response && response.data) {
      const snapshots = response.data as SnapshotApiData[];
      
      if (Array.isArray(snapshots) && snapshots.length > 0) {
        // 转换API返回的数据为组件所需格式，映射字段名
        orderItems.value = snapshots.map(item => ({
          id: item.id || item.productSkuId || '',
          name: item.productName,
          image: item.imageUrl || '/static/logo-back.png',
          options: item.specification, // 规格信息
          description: item.description,
          price: item.price,
          originalPrice: item.originalPrice,
          quantity: item.quantity || 1,
          totalPrice: item.totalPrice,
          realPrice: item.realPrice,
          salesType: item.salesType,
          snapshotId: item.id, // 使用快照ID
        }));
      } else {
        orderItems.value = [];
        uni.showToast({ title: '该订单暂无快照信息', icon: 'none' });
      }
    } else {
      orderItems.value = [];
      uni.showToast({ title: '未能加载快照信息', icon: 'none' });
    }
  } catch (error) {
    uni.showToast({
      title: '加载快照信息失败',
      icon: 'none',
    });
  } finally {
    isLoading.value = false;
  }
}

// 格式化价格 (可以从detail.vue抽离到公共utils)
function formatPrice(price: string | number | undefined): string {
  if (price === undefined || price === null) {
    return '0.00';
  }
  if (typeof price === 'string') {
    price = parseFloat(price) || 0;
  }
  return price.toFixed(2);
}


// 点击单个商品交易快照按钮
function viewItemSnapshot(item: SnapshotOrderItem) {
  if (item.snapshotId) {
    // 为了简化跳转，直接将必要的信息作为URL参数传递
    const params = {
      id: item.id,
      name: item.name || '未知商品',
      image: item.image,
      options: item.options,
      description: item.description || '',
      price: item.price,
      quantity: item.quantity || 1,
      salesType: item.salesType || '',
      originalPrice: item.originalPrice,
      snapshotId: item.snapshotId
    };
    
    // 将对象转换为JSON字符串并进行编码
    const itemData = encodeURIComponent(JSON.stringify(params));
    
    // 跳转到商品快照详情页
    uni.navigateTo({
      url: `/pages/views/order/item-snapshot-detail?orderId=${orderId.value}&itemData=${itemData}`
    });
  } else {
    uni.showToast({ title: '该商品暂无快照信息', icon: 'none' });
  }
}
</script>

<style scoped>
/* 页面特定样式 */
.custom-nav-page {
  padding-top: var(--status-bar-height); /* 适配状态栏 */
}

/* 添加过渡动画 */
.animate-pulse {
  animation: pulse 1.5s cubic-bezier(0.4, 0, 0.6, 1) infinite;
}

@keyframes pulse {
  0%, 100% {
    opacity: 1;
  }
  50% {
    opacity: 0.7;
  }
}

/* 文本截断样式 */
.line-clamp-2 {
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
  text-overflow: ellipsis;
}

.line-clamp-1 {
  display: -webkit-box;
  -webkit-line-clamp: 1;
  -webkit-box-orient: vertical;
  overflow: hidden;
  text-overflow: ellipsis;
}
</style>