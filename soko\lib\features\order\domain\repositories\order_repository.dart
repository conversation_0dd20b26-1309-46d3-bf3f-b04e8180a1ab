import 'package:dartz/dartz.dart' hide Order;

import 'package:soko/core/error/failures.dart';
import 'package:soko/core/models/paginated_data.dart';
import 'package:soko/core/models/query_params.dart';
import 'package:soko/features/order/domain/entities/order.dart';
import 'package:soko/features/order/domain/entities/order_create_request.dart';

/// 订单仓库接口
abstract class OrderRepository {
  /// 获取订单列表
  Future<Either<Failure, PaginatedData<Order>>> getOrders(QueryParams params);

  /// 获取订单详情
  Future<Either<Failure, Order>> getOrderById(String orderId);

  /// 创建订单
  Future<Either<Failure, Order>> createOrder(Map<String, dynamic> orderData);

  /// 创建订单（使用请求对象）
  Future<Either<Failure, Order>> createOrderFromRequest(
      OrderCreateRequest request,);

  /// 更新订单状态
  Future<Either<Failure, Order>> updateOrderStatus(
      String orderId, String status,);

  /// 取消订单
  Future<Either<Failure, bool>> cancelOrder(String orderId);

  /// 确认收货
  Future<Either<Failure, bool>> confirmReceived(String orderId);

  /// 申请退款
  Future<Either<Failure, bool>> requestRefund(String orderId, String reason);

  /// 删除订单
  Future<Either<Failure, bool>> deleteOrder(String orderId);
}
