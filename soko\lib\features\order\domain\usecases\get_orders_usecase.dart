import 'package:dartz/dartz.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';

import 'package:soko/core/error/failures.dart';
import 'package:soko/core/models/paginated_data.dart';
import 'package:soko/core/models/query_params.dart';
import 'package:soko/core/usecases/usecase.dart';
import 'package:soko/features/order/data/repositories/order_repository_impl.dart';
import 'package:soko/features/order/domain/entities/order.dart';
import 'package:soko/features/order/domain/repositories/order_repository.dart';

/// 获取订单列表用例
class GetOrdersUseCase implements UseCase<PaginatedData<Order>, QueryParams> {

  GetOrdersUseCase(this.repository);
  final OrderRepository repository;

  @override
  Future<Either<Failure, PaginatedData<Order>>> call(QueryParams params) async {
    return repository.getOrders(params);
  }
}

/// GetOrdersUseCase 提供者
final getOrdersUseCaseProvider = Provider<GetOrdersUseCase>((ref) {
  final repository = ref.read(orderRepositoryProvider);
  return GetOrdersUseCase(repository);
});
