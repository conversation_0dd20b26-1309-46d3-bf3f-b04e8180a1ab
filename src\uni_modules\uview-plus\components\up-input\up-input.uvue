<template>
    <view class="up-input" :class="inputClass" :style="[wrapperStyle]">
        <view class="up-input__content">
            <view
                class="up-input__content__prefix-icon"
                v-if="prefixIcon != '' || $slots['prefix'] != null"
            >
                <slot name="prefix">
                    <up-icon
                        :name="prefixIcon"
                        size="18"
                        :customStyle="prefixIconStyle"
                    ></up-icon>
                </slot>
            </view>
            <view class="up-input__content__field-wrapper" @tap="clickHandler">
				<!-- 根据uni-app的input组件文档，H5和APP中只要声明了password参数(无论true还是false)，type均失效，此时
					为了防止type=number时，又存在password属性，type无效，此时需要设置password为undefined
				 -->
            	<input
                    ref="input-native"
            	    class="up-input__content__field-wrapper__field"
            	    :style="[inputStyle]"
            	    :type="type"
            	    :focus="focus"
            	    :cursor="cursor"
            	    :value="innerValue"
            	    :auto-blur="autoBlur"
            	    :disabled="disabled || readonly"
            	    :maxlength="maxlength"
            	    :placeholder="placeholder"
            	    :placeholder-style="placeholderStyle"
            	    :placeholder-class="placeholderClass"
            	    :confirm-type="confirmType"
            	    :confirm-hold="confirmHold"
            	    :hold-keyboard="holdKeyboard"
            	    :cursor-spacing="cursorSpacing"
            	    :adjust-position="adjustPosition"
            	    :selection-end="selectionEnd"
            	    :selection-start="selectionStart"
            	    :password="password || type === 'password' || false"
                    :ignoreCompositionEvent="ignoreCompositionEvent"
            	    @input="onInput"
            	    @blur="onBlur"
            	    @focus="onFocus"
            	    @confirm="onConfirm"
            	    @keyboardheightchange="onkeyboardheightchange"
            	/>
            </view>
            <view
                class="up-input__content__clear"
                v-if="isShowClear"
                @click="onClear"
            >
                <up-icon
                    name="close"
                    size="11"
                    color="#ffffff"
                    customStyle="line-height: 12px"
                ></up-icon>
            </view>
            <view
                class="up-input__content__subfix-icon"
                v-if="suffixIcon != '' || $slots['suffix'] != null"
            >
                <slot name="suffix">
                    <up-icon
                        :name="suffixIcon"
                        size="18"
                        :customStyle="suffixIconStyle"
                    ></up-icon>
                </slot>
            </view>
        </view>
    </view>
</template>

<script>
import { propsInput } from "./props";
import { mpMixin } from '../../libs/mixin/mpMixin';
import { mixin } from '../../libs/mixin/mixin';
// import { debounce } from '../../libs/function/debounce';
import { addStyle, addUnit, deepMerge, formValidate, sleep, os } from '../../libs/function/index';
/**
 * Input 输入框
 * @description  此组件为一个输入框，默认没有边框和样式，是专门为配合表单组件up-form而设计的，利用它可以快速实现表单验证，输入内容，下拉选择等功能。
 * @tutorial https://uview-plus.jiangruyi.com/components/input.html
 * @property {String | Number}	value					输入的值
 * @property {String}			type					输入框类型，见上方说明 （ 默认 'text' ）
 * @property {Boolean}			fixed					如果 textarea 是在一个 position:fixed 的区域，需要显示指定属性 fixed 为 true，兼容性：微信小程序、百度小程序、字节跳动小程序、QQ小程序 （ 默认 false ）
 * @property {Boolean}			disabled				是否禁用输入框 （ 默认 false ）
 * @property {String}			disabledColor			禁用状态时的背景色（ 默认 '#f5f7fa' ）
 * @property {Boolean}			clearable				是否显示清除控件 （ 默认 false ）
 * @property {Boolean}			password				是否密码类型 （ 默认 false ）
 * @property {Number}       	maxlength				最大输入长度，设置为 -1 的时候不限制最大长度 （ 默认 -1 ）
 * @property {String}			placeholder				输入框为空时的占位符
 * @property {String}			placeholderClass		指定placeholder的样式类，注意页面或组件的style中写了scoped时，需要在类名前写/deep/ （ 默认 'input-placeholder' ）
 * @property {String | Object}	placeholderStyle		指定placeholder的样式，字符串/对象形式，如"color: red;"
 * @property {Boolean}			showWordLimit			是否显示输入字数统计，只在 type ="text"或type ="textarea"时有效 （ 默认 false ）
 * @property {String}			confirmType				设置右下角按钮的文字，兼容性详见uni-app文档 （ 默认 'done' ）
 * @property {Boolean}			confirmHold				点击键盘右下角按钮时是否保持键盘不收起，H5无效 （ 默认 false ）
 * @property {Boolean}			holdKeyboard			focus时，点击页面的时候不收起键盘，微信小程序有效 （ 默认 false ）
 * @property {Boolean}			focus					自动获取焦点，在 H5 平台能否聚焦以及软键盘是否跟随弹出，取决于当前浏览器本身的实现。nvue 页面不支持，需使用组件的 focus()、blur() 方法控制焦点 （ 默认 false ）
 * @property {Boolean}			autoBlur				键盘收起时，是否自动失去焦点，目前仅App3.0.0+有效 （ 默认 false ）
 * @property {Boolean}			disableDefaultPadding	是否去掉 iOS 下的默认内边距，仅微信小程序，且type=textarea时有效 （ 默认 false ）
 * @property {String ｜ Number}	cursor					指定focus时光标的位置（ 默认 140 ）
 * @property {String ｜ Number}	cursorSpacing			输入框聚焦时底部与键盘的距离 （ 默认 30 ）
 * @property {String ｜ Number}	selectionStart			光标起始位置，自动聚集时有效，需与selection-end搭配使用 （ 默认 -1 ）
 * @property {String ｜ Number}	selectionEnd			光标结束位置，自动聚集时有效，需与selection-start搭配使用 （ 默认 -1 ）
 * @property {Boolean}			adjustPosition			键盘弹起时，是否自动上推页面 （ 默认 true ）
 * @property {String}			inputAlign				输入框内容对齐方式（ 默认 'left' ）
 * @property {String | Number}	fontSize				输入框字体的大小 （ 默认 '15px' ）
 * @property {String}			color					输入框字体颜色	（ 默认 '#303133' ）
 * @property {Function}			formatter			    内容式化函数
 * @property {String}			prefixIcon				输入框前置图标
 * @property {String | Object}	prefixIconStyle			前置图标样式，对象或字符串
 * @property {String}			suffixIcon				输入框后置图标
 * @property {String | Object}	suffixIconStyle			后置图标样式，对象或字符串
 * @property {String}			border					边框类型，surround-四周边框，bottom-底部边框，none-无边框 （ 默认 'surround' ）
 * @property {Boolean}			readonly				是否只读，与disabled不同之处在于disabled会置灰组件，而readonly则不会 （ 默认 false ）
 * @property {String}			shape					输入框形状，circle-圆形，square-方形 （ 默认 'square' ）
 * @property {Object}			customStyle				定义需要用到的外部样式
 * @property {Boolean}			ignoreCompositionEvent	是否忽略组件内对文本合成系统事件的处理。
 * @example <up-input v-model="value" :password="true" suffix-icon="lock-fill" />
 */
const defaultFormat = function(value: string): string {
	return value
}
export default {
    name: "up-input",
    mixins: [mpMixin, mixin, propsInput],
    data() {
        return {
            // 清除操作
            clearInput: false,
            // 输入框的值
            innerValue: "",
            // 是否处于获得焦点状态
            focused: false,
            // value是否第一次变化，在watch中，由于加入immediate属性，会在第一次触发，此时不应该认为value发生了变化
            firstChange: true,
            // value绑定值的变化是由内部还是外部引起的
            changeFromInner: false,
			// 过滤处理方法
			innerFormatter: defaultFormat as Function
        };
    },
    created() {
        // 格式化过滤方法
        // if (this.formatter != null) {
        //     this.innerFormatter = this.formatter;
        // }
    },
    watch: {
        modelValue: {
            immediate: true,
            handler(newVal: string, oldVal: string) {
                // console.log(newVal, oldVal)
                if (this.changeFromInner || this.innerValue === newVal) {
                    this.changeFromInner = false; // 重要否则会出现双向绑定失效问题https://github.com/ijry/uview-plus/issues/419
                    return;
                }
                this.innerValue = newVal;
                // 在H5中，外部value变化后，修改input中的值，不会触发@input事件，此时手动调用值变化方法
                if (
                    this.firstChange === false &&
					this.changeFromInner === false
                ) {
                    this.valueChange(this.innerValue, true);
                } else {
					// 尝试调用up-form的验证方法
                    if(!this.firstChange) formValidate(this, "change");
				}
                this.firstChange = false;
                // 重置changeFromInner的值为false，标识下一次引起默认为外部引起的
                this.changeFromInner = false;
            }
        }
    },
    computed: {
        // 是否显示清除控件
        isShowClear(): boolean {
            const { clearable, readonly, focused, innerValue } = this;
            return !!clearable && !readonly && !!focused && innerValue !== "";
        },
        // 组件的类名
        inputClass(): string {
            let classes: string[] = [],
                { border, disabled, shape } = this;
            if (border === "surround") {
				(classes = classes.concat(["up-border", "up-input--radius"]));
			}
            classes.push(`up-input--${shape}`);
            if (border === "bottom") {
                (classes = classes.concat([
                    "up-border-bottom",
                    "up-input--no-radius",
                ]));
			}
            return classes.join(" ");
        },
        // 组件的样式
        wrapperStyle(): any {
            const style = {};
            // 禁用状态下，被背景色加上对应的样式
            if (this.disabled) {
                style['backgroundColor'] = this.disabledColor;
            }
            // 无边框时，去除内边距
            if (this.border === "none") {
                style['padding'] = "0";
            } else {
                style['paddingTop'] = "6px";
                style['paddingBottom'] = "6px";
                style['paddingLeft'] = "9px";
                style['paddingRight'] = "9px";
            }
            return deepMerge(style, addStyle(this.customStyle));
        },
        // 输入框的样式
        inputStyle(): any {
            const style = {
                color: this.color,
                fontSize: addUnit(this.fontSize),
				textAlign: this.inputAlign
            };
            return style;
        },
    },
    // #ifdef VUE3
    emits: ['update:modelValue', 'focus', 'blur', 'change', 'confirm', 'clear', 'keyboardheightchange'],
    // #endif
    methods: {
		// 在微信小程序中，不支持将函数当做props参数，故只能通过ref形式调用
		setFormatter(e: Function): void {
			this.innerFormatter = e
		},
        // 当键盘输入时，触发input事件
        onInput(e: UniInputEvent): void {
            let { value = "" } = e.detail;
            // 为了避免props的单向数据流特性，需要先将innerValue值设置为当前值，再在$nextTick中重新赋予设置后的值才有效
            console.log('onInput', value, this.innerValue)
            this.innerValue = value;
            this.$nextTick(() => {
             //    let formatValue: string = this.innerFormatter(value);
            	// this.innerValue = formatValue;
				// this.valueChange(formatValue);
                this.valueChange(value.toString(), false);
            })
        },
        // 输入框失去焦点时触发
        onBlur(event: UniInputBlurEvent): void {
            this.$emit("blur", event.detail.value);
            // H5端的blur会先于点击清除控件的点击click事件触发，导致focused
            // 瞬间为false，从而隐藏了清除控件而无法被点击到
            // sleep(150).then(() => {
                this.focused = false;
            // });
            // 尝试调用up-form的验证方法
            formValidate(this, "blur");
        },
        // 输入框聚焦时触发
        onFocus(event: UniInputFocusEvent): void {
            this.focused = true;
            this.$emit("focus");
        },
        doFocus(): void {
			(this.$refs['input-native'] as UniInputElement).focus();
        },
        doBlur(): void {
			(this.$refs['input-native'] as UniInputElement).blur();
        },
        // 点击完成按钮时触发
        onConfirm(event: UniInputConfirmEvent): void {
            this.$emit("confirm", this.innerValue);
        },
        // 键盘高度发生变化的时候触发此事件
        // 兼容性：微信小程序2.7.0+、App 3.1.0+
		onkeyboardheightchange(event: UniInputKeyboardHeightChangeEventDetail) {
            this.$emit("keyboardheightchange", event);
        },
        // 内容发生变化，进行处理
        valueChange(value: string, isOut: boolean): void {
            if(this.clearInput) {
                this.innerValue = '';
                this.clearInput = false;
            }
            this.$nextTick(() => {
                if (!isOut || this.clearInput) {
                    // 标识value值的变化是由内部引起的
                    this.changeFromInner = true;
                    this.$emit("change", value);
                    this.$emit("update:modelValue", value);
                }

                // 尝试调用up-form的验证方法
                formValidate(this, "change");
            });
        },
        // 点击清除控件
        onClear(): void {
            this.clearInput = true;
            this.innerValue = "";
            this.$nextTick(() => {
                this.valueChange("", false);
                this.$emit("clear");
            });
        },
        /**
         * 在安卓nvue上，事件无法冒泡
         * 在某些时间，我们希望监听up-from-item的点击事件，此时会导致点击up-form-item内的up-input后
         * 无法触发up-form-item的点击事件，这里通过手动调用up-form-item的方法进行触发
         */
        clickHandler(): void {
            // if (os() === "android") {
            //     const formItem = $parent.call(this, "up-form-item");
            //     if (formItem) {
            //         formItem.clickHandler();
            //     }
            // }
        },
    },
};
</script>

<style lang="scss" scoped>
@import "../../libs/css/components.scss";

.up-input {
    @include flex(row);
    align-items: center;
    justify-content: space-between;
    flex: 1;

    &--radius,
    &--square {
        border-radius: 4px;
    }

    &--no-radius {
        border-radius: 0;
    }

    &--circle {
        border-radius: 100px;
    }

    &__content {
        flex: 1;
        @include flex(row);
        align-items: center;
        justify-content: space-between;

        &__field-wrapper {
            position: relative;
            @include flex(row);
            margin: 0;
            flex: 1;
			
			&__field {
				line-height: 26px;
				text-align: left;
				color: $up-main-color;
				height: 24px;
				font-size: 15px;
				flex: 1;
			}
        }

        &__clear {
            width: 20px;
            height: 20px;
            border-radius: 100px;
            background-color: #c6c7cb;
            @include flex(row);
            align-items: center;
            justify-content: center;
            transform: scale(0.82);
            margin-left: 4px;
        }

        &__subfix-icon {
            margin-left: 4px;
        }

        &__prefix-icon {
            margin-right: 4px;
        }
    }
}
</style>
