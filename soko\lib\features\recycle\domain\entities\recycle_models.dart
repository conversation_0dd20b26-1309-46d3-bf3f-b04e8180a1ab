import 'package:json_annotation/json_annotation.dart';

import 'package:soko/core/models/query_params.dart';

part 'recycle_models.g.dart';

/// 回收分类项
@JsonSerializable()
class CategoryItem {
  const CategoryItem({
    required this.id,
    required this.name,
    this.description,
    this.icon,
    required this.sort,
    required this.isActive,
  });

  factory CategoryItem.fromJson(Map<String, dynamic> json) =>
      _$CategoryItemFromJson(json);
  @Json<PERSON>ey(name: 'id')
  final String id;

  @JsonKey(name: 'name')
  final String name;

  @Json<PERSON>ey(name: 'description')
  final String? description;

  @JsonKey(name: 'icon')
  final String? icon;

  @JsonKey(name: 'sort')
  final int sort;

  @Json<PERSON>ey(name: 'isActive')
  final bool isActive;

  Map<String, dynamic> toJson() => _$CategoryItemToJson(this);
}

/// 成色选项
@JsonSerializable()
class ConditionOption {
  const ConditionOption({
    required this.id,
    required this.name,
    this.description,
    required this.priceMultiplier,
    required this.sort,
  });

  factory ConditionOption.fromJson(Map<String, dynamic> json) =>
      _$ConditionOptionFromJson(json);
  @JsonKey(name: 'id')
  final String id;

  @JsonKey(name: 'name')
  final String name;

  @JsonKey(name: 'description')
  final String? description;

  @JsonKey(name: 'priceMultiplier')
  final double priceMultiplier;

  @JsonKey(name: 'sort')
  final int sort;

  Map<String, dynamic> toJson() => _$ConditionOptionToJson(this);
}

/// 创建回收订单请求
@JsonSerializable()
class CreateRecycleOrderRequest {
  const CreateRecycleOrderRequest({
    required this.productName,
    this.productDesc,
    this.productModel,
    this.productCategory,
    this.brandId,
    this.modelId,
    this.conditionId,
    this.condition,
    this.expectedPrice,
    this.contactName,
    this.contactPhone,
    this.pickupAddress,
    this.imageFiles,
    this.remark,
  });

  factory CreateRecycleOrderRequest.fromJson(Map<String, dynamic> json) =>
      _$CreateRecycleOrderRequestFromJson(json);

  @JsonKey(name: 'productName')
  final String productName;

  @JsonKey(name: 'productDesc')
  final String? productDesc;

  @JsonKey(name: 'productModel')
  final String? productModel;

  @JsonKey(name: 'productCategory')
  final String? productCategory;

  @JsonKey(name: 'brandId')
  final String? brandId;

  @JsonKey(name: 'modelId')
  final String? modelId;

  @JsonKey(name: 'conditionId')
  final String? conditionId;

  @JsonKey(name: 'condition')
  final String? condition;

  @JsonKey(name: 'expectedPrice')
  final double? expectedPrice;

  @JsonKey(name: 'contactName')
  final String? contactName;

  @JsonKey(name: 'contactPhone')
  final String? contactPhone;

  @JsonKey(name: 'pickupAddress')
  final String? pickupAddress;

  @JsonKey(name: 'imageFiles')
  final List<String>? imageFiles;

  @JsonKey(name: 'remark')
  final String? remark;

  Map<String, dynamic> toJson() => _$CreateRecycleOrderRequestToJson(this);
}

/// 品牌信息
@JsonSerializable()
class BrandInfo {
  const BrandInfo({
    required this.id,
    required this.name,
    this.logo,
    required this.categoryId,
    required this.isPopular,
    required this.sort,
  });

  factory BrandInfo.fromJson(Map<String, dynamic> json) =>
      _$BrandInfoFromJson(json);
  @JsonKey(name: 'id')
  final String id;

  @JsonKey(name: 'name')
  final String name;

  @JsonKey(name: 'logo')
  final String? logo;

  @JsonKey(name: 'categoryId')
  final String categoryId;

  @JsonKey(name: 'isPopular')
  final bool isPopular;

  @JsonKey(name: 'sort')
  final int sort;

  Map<String, dynamic> toJson() => _$BrandInfoToJson(this);
}

/// 产品型号信息
@JsonSerializable()
class ProductModel {
  const ProductModel({
    required this.id,
    required this.name,
    required this.brandId,
    required this.categoryId,
    required this.basePrice,
    this.specifications,
    required this.isPopular,
    required this.sort,
  });

  factory ProductModel.fromJson(Map<String, dynamic> json) =>
      _$ProductModelFromJson(json);
  @JsonKey(name: 'id')
  final String id;

  @JsonKey(name: 'name')
  final String name;

  @JsonKey(name: 'brandId')
  final String brandId;

  @JsonKey(name: 'categoryId')
  final String categoryId;

  @JsonKey(name: 'basePrice')
  final double basePrice;

  @JsonKey(name: 'specifications')
  final Map<String, dynamic>? specifications;

  @JsonKey(name: 'isPopular')
  final bool isPopular;

  @JsonKey(name: 'sort')
  final int sort;

  Map<String, dynamic> toJson() => _$ProductModelToJson(this);
}

/// 回收订单创建表单数据
class RecycleOrderFormData {
  const RecycleOrderFormData({
    this.categoryId,
    this.brandId,
    this.modelId,
    this.customProductName,
    this.productDescription,
    this.conditionId,
    this.imageFiles = const [],
    this.contactName,
    this.contactPhone,
    this.pickupAddress,
    this.expectedPrice,
  });
  final String? categoryId;
  final String? brandId;
  final String? modelId;
  final String? customProductName;
  final String? productDescription;
  final String? conditionId;
  final List<String> imageFiles;
  final String? contactName;
  final String? contactPhone;
  final String? pickupAddress;
  final double? expectedPrice;

  /// 验证表单数据是否完整
  bool get isValid {
    return (categoryId != null || customProductName?.isNotEmpty == true) &&
        conditionId != null &&
        imageFiles.isNotEmpty &&
        contactName?.isNotEmpty == true &&
        contactPhone?.isNotEmpty == true &&
        pickupAddress?.isNotEmpty == true;
  }

  /// 转换为创建订单请求
  CreateRecycleOrderRequest toCreateRequest() {
    return CreateRecycleOrderRequest(
      productName: customProductName ?? '',
      productDesc: productDescription ?? '',
      productModel: modelId,
      productCategory: categoryId ?? '',
      contactName: contactName ?? '',
      contactPhone: contactPhone ?? '',
      expectedPrice: expectedPrice ?? 0.0,
      condition: conditionId ?? '',
      imageFiles: imageFiles,
    );
  }

  RecycleOrderFormData copyWith({
    String? categoryId,
    String? brandId,
    String? modelId,
    String? customProductName,
    String? productDescription,
    String? conditionId,
    List<String>? imageFiles,
    String? contactName,
    String? contactPhone,
    String? pickupAddress,
    double? expectedPrice,
  }) {
    return RecycleOrderFormData(
      categoryId: categoryId ?? this.categoryId,
      brandId: brandId ?? this.brandId,
      modelId: modelId ?? this.modelId,
      customProductName: customProductName ?? this.customProductName,
      productDescription: productDescription ?? this.productDescription,
      conditionId: conditionId ?? this.conditionId,
      imageFiles: imageFiles ?? this.imageFiles,
      contactName: contactName ?? this.contactName,
      contactPhone: contactPhone ?? this.contactPhone,
      pickupAddress: pickupAddress ?? this.pickupAddress,
      expectedPrice: expectedPrice ?? this.expectedPrice,
    );
  }
}

/// 回收订单文件
@JsonSerializable()
class RecycleOrderFile {
  const RecycleOrderFile({
    required this.id,
    required this.url,
    required this.fileName,
    required this.fileSize,
    required this.fileType,
    required this.isMain,
    this.description,
    required this.uploadTime,
  });

  factory RecycleOrderFile.fromJson(Map<String, dynamic> json) =>
      _$RecycleOrderFileFromJson(json);

  @JsonKey(name: 'id')
  final String id;

  @JsonKey(name: 'url')
  final String url;

  @JsonKey(name: 'fileName')
  final String fileName;

  @JsonKey(name: 'fileSize')
  final int fileSize;

  @JsonKey(name: 'fileType')
  final String fileType;

  @JsonKey(name: 'isMain')
  final bool isMain;

  @JsonKey(name: 'description')
  final String? description;

  @JsonKey(name: 'uploadTime')
  final int uploadTime;

  Map<String, dynamic> toJson() => _$RecycleOrderFileToJson(this);

  /// 是否为图片文件
  bool get isImage {
    final imageTypes = ['jpg', 'jpeg', 'png', 'gif', 'webp', 'bmp'];
    final extension = fileType.toLowerCase();
    return imageTypes.contains(extension);
  }

  /// 格式化文件大小
  String get formattedSize {
    if (fileSize < 1024) {
      return '${fileSize}B';
    } else if (fileSize < 1024 * 1024) {
      return '${(fileSize / 1024).toStringAsFixed(1)}KB';
    } else {
      return '${(fileSize / (1024 * 1024)).toStringAsFixed(1)}MB';
    }
  }
}

/// 订单评价
@JsonSerializable()
class OrderReview {
  const OrderReview({
    required this.id,
    required this.orderId,
    required this.userId,
    required this.rating,
    this.comment,
    this.images,
    required this.createTime,
  });

  factory OrderReview.fromJson(Map<String, dynamic> json) =>
      _$OrderReviewFromJson(json);

  @JsonKey(name: 'id')
  final String id;

  @JsonKey(name: 'orderId')
  final String orderId;

  @JsonKey(name: 'userId')
  final String userId;

  @JsonKey(name: 'rating')
  final int rating;

  @JsonKey(name: 'comment')
  final String? comment;

  @JsonKey(name: 'images')
  final List<String>? images;

  @JsonKey(name: 'createTime')
  final int createTime;

  Map<String, dynamic> toJson() => _$OrderReviewToJson(this);
}
