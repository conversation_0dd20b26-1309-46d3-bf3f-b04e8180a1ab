import 'package:json_annotation/json_annotation.dart';

import 'package:soko/core/models/query_params.dart';

part 'recycle_models.g.dart';

/// 回收分类项
@JsonSerializable()
class CategoryItem {

  const CategoryItem({
    required this.id,
    required this.name,
    this.description,
    this.icon,
    required this.sort,
    required this.isActive,
  });

  factory CategoryItem.fromJson(Map<String, dynamic> json) =>
      _$CategoryItemFromJson(json);
  @Json<PERSON>ey(name: 'id')
  final String id;

  @JsonKey(name: 'name')
  final String name;

  @Json<PERSON>ey(name: 'description')
  final String? description;

  @JsonKey(name: 'icon')
  final String? icon;

  @JsonKey(name: 'sort')
  final int sort;

  @Json<PERSON>ey(name: 'isActive')
  final bool isActive;

  Map<String, dynamic> toJson() => _$CategoryItemToJson(this);
}

/// 成色选项
@JsonSerializable()
class ConditionOption {

  const ConditionOption({
    required this.id,
    required this.name,
    this.description,
    required this.priceMultiplier,
    required this.sort,
  });

  factory ConditionOption.fromJson(Map<String, dynamic> json) =>
      _$ConditionOptionFromJson(json);
  @JsonKey(name: 'id')
  final String id;

  @JsonKey(name: 'name')
  final String name;

  @JsonKey(name: 'description')
  final String? description;

  @JsonKey(name: 'priceMultiplier')
  final double priceMultiplier;

  @JsonKey(name: 'sort')
  final int sort;

  Map<String, dynamic> toJson() => _$ConditionOptionToJson(this);
}

/// 回收订单查询参数
class RecycleOrderQueryParams extends QueryParams {

  const RecycleOrderQueryParams({
    super.page = 1,
    super.size = 20,
    this.orderStatus,
    this.productName,
    this.startDate,
    this.endDate,
  });
  final String? orderStatus;
  final String? productName;
  final String? startDate;
  final String? endDate;

  @override
  Map<String, dynamic> toJson() {
    final json = super.toJson();
    if (orderStatus != null) json['orderStatus'] = orderStatus;
    if (productName != null) json['productName'] = productName;
    if (startDate != null) json['startDate'] = startDate;
    if (endDate != null) json['endDate'] = endDate;
    return json;
  }

  RecycleOrderQueryParams copyWith({
    int? page,
    int? size,
    String? orderStatus,
    String? productName,
    String? startDate,
    String? endDate,
  }) {
    return RecycleOrderQueryParams(
      page: page ?? this.page,
      size: size ?? this.size,
      orderStatus: orderStatus ?? this.orderStatus,
      productName: productName ?? this.productName,
      startDate: startDate ?? this.startDate,
      endDate: endDate ?? this.endDate,
    );
  }
}

/// 品牌信息
@JsonSerializable()
class BrandInfo {

  const BrandInfo({
    required this.id,
    required this.name,
    this.logo,
    required this.categoryId,
    required this.isPopular,
    required this.sort,
  });

  factory BrandInfo.fromJson(Map<String, dynamic> json) =>
      _$BrandInfoFromJson(json);
  @JsonKey(name: 'id')
  final String id;

  @JsonKey(name: 'name')
  final String name;

  @JsonKey(name: 'logo')
  final String? logo;

  @JsonKey(name: 'categoryId')
  final String categoryId;

  @JsonKey(name: 'isPopular')
  final bool isPopular;

  @JsonKey(name: 'sort')
  final int sort;

  Map<String, dynamic> toJson() => _$BrandInfoToJson(this);
}

/// 产品型号信息
@JsonSerializable()
class ProductModel {

  const ProductModel({
    required this.id,
    required this.name,
    required this.brandId,
    required this.categoryId,
    required this.basePrice,
    this.specifications,
    required this.isPopular,
    required this.sort,
  });

  factory ProductModel.fromJson(Map<String, dynamic> json) =>
      _$ProductModelFromJson(json);
  @JsonKey(name: 'id')
  final String id;

  @JsonKey(name: 'name')
  final String name;

  @JsonKey(name: 'brandId')
  final String brandId;

  @JsonKey(name: 'categoryId')
  final String categoryId;

  @JsonKey(name: 'basePrice')
  final double basePrice;

  @JsonKey(name: 'specifications')
  final Map<String, dynamic>? specifications;

  @JsonKey(name: 'isPopular')
  final bool isPopular;

  @JsonKey(name: 'sort')
  final int sort;

  Map<String, dynamic> toJson() => _$ProductModelToJson(this);
}

/// 回收订单创建表单数据
class RecycleOrderFormData {

  const RecycleOrderFormData({
    this.categoryId,
    this.brandId,
    this.modelId,
    this.customProductName,
    this.productDescription,
    this.conditionId,
    this.imageFiles = const [],
    this.contactName,
    this.contactPhone,
    this.pickupAddress,
    this.expectedPrice,
  });
  final String? categoryId;
  final String? brandId;
  final String? modelId;
  final String? customProductName;
  final String? productDescription;
  final String? conditionId;
  final List<String> imageFiles;
  final String? contactName;
  final String? contactPhone;
  final String? pickupAddress;
  final double? expectedPrice;

  /// 验证表单数据是否完整
  bool get isValid {
    return (categoryId != null || customProductName?.isNotEmpty == true) &&
        conditionId != null &&
        imageFiles.isNotEmpty &&
        contactName?.isNotEmpty == true &&
        contactPhone?.isNotEmpty == true &&
        pickupAddress?.isNotEmpty == true;
  }

  /// 转换为创建订单请求
  CreateRecycleOrderRequest toCreateRequest() {
    return CreateRecycleOrderRequest(
      productName: customProductName ?? '',
      productDesc: productDescription ?? '',
      productModel: modelId,
      productCategory: categoryId ?? '',
      contactPerson: contactName ?? '',
      contactPhone: contactPhone ?? '',
      expectedPrice: expectedPrice ?? 0.0,
      condition: conditionId ?? '',
      imageFiles: imageFiles,
    );
  }

  RecycleOrderFormData copyWith({
    String? categoryId,
    String? brandId,
    String? modelId,
    String? customProductName,
    String? productDescription,
    String? conditionId,
    List<String>? imageFiles,
    String? contactName,
    String? contactPhone,
    String? pickupAddress,
    double? expectedPrice,
  }) {
    return RecycleOrderFormData(
      categoryId: categoryId ?? this.categoryId,
      brandId: brandId ?? this.brandId,
      modelId: modelId ?? this.modelId,
      customProductName: customProductName ?? this.customProductName,
      productDescription: productDescription ?? this.productDescription,
      conditionId: conditionId ?? this.conditionId,
      imageFiles: imageFiles ?? this.imageFiles,
      contactName: contactName ?? this.contactName,
      contactPhone: contactPhone ?? this.contactPhone,
      pickupAddress: pickupAddress ?? this.pickupAddress,
      expectedPrice: expectedPrice ?? this.expectedPrice,
    );
  }
}
