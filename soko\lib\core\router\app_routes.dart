/// 应用路由常量
class AppRoutes {
  // 私有构造函数
  AppRoutes._();
  
  // 基础页面
  static const String splash = '/splash';
  static const String login = '/login';
  static const String register = '/register';
  
  // 主要页面（底部导航）
  static const String home = '/';
  static const String cart = '/cart';
  static const String recycle = '/recycle';
  static const String profile = '/profile';
  
  // 商品相关
  static const String productList = '/products';
  static const String productDetail = '/product/:id';
  static const String productSearch = '/search';
  
  // 购物车和订单
  static const String orderConfirm = '/order/confirm';
  static const String orderList = '/orders';
  static const String orderDetail = '/order/:id';
  static const String orderResult = '/order/result';
  
  // 回收业务
  static const String recycleHome = '/recycle';
  static const String createRecycleOrder = '/recycle/create';
  static const String recycleOrderList = '/recycle/orders';
  static const String recycleOrderDetail = '/recycle/order/:id';
  static const String recycleShipping = '/recycle/shipping';
  
  // 用户相关
  static const String userInfo = '/profile/info';
  static const String userSettings = '/profile/settings';
  static const String userAddress = '/profile/address';
  static const String userAddressEdit = '/profile/address/edit';
  static const String userCoupons = '/profile/coupons';
  static const String userMember = '/profile/member';
  
  // 其他页面
  static const String notification = '/notification';
  static const String announcement = '/announcement';
  static const String about = '/about';
  static const String feedback = '/feedback';
  static const String webview = '/webview';
  
  // 工具方法：构建带参数的路由
  static String productDetailPath(String productId) {
    return '/product/$productId';
  }
  
  static String orderDetailPath(String orderId) {
    return '/order/$orderId';
  }
  
  static String recycleOrderDetailPath(String orderId) {
    return '/recycle/order/$orderId';
  }
  
  static String productListWithCategory(String categoryId) {
    return '$productList?categoryId=$categoryId';
  }
  
  static String productListWithKeyword(String keyword) {
    return '$productList?keyword=$keyword';
  }
  
  static String orderListWithType(String type) {
    return '$orderList?type=$type';
  }
  
  static String webviewWithUrl(String url, {String? title}) {
    final uri = Uri(
      path: webview,
      queryParameters: {
        'url': url,
        if (title != null) 'title': title,
      },
    );
    return uri.toString();
  }
}
