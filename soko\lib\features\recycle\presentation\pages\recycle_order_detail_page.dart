import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:go_router/go_router.dart';
import 'package:cached_network_image/cached_network_image.dart';

import 'package:soko/shared/presentation/widgets/custom_app_bar.dart';
import 'package:soko/shared/presentation/widgets/loading_widget.dart';
import 'package:soko/shared/presentation/widgets/error_retry_widget.dart';
import 'package:soko/shared/presentation/widgets/status_badge.dart';
import 'package:soko/features/recycle/presentation/providers/recycle_order_detail_provider.dart';
import 'package:soko/features/recycle/presentation/widgets/order_status_timeline.dart';
import 'package:soko/features/recycle/presentation/widgets/order_info_section.dart';
import 'package:soko/features/recycle/presentation/widgets/order_images_section.dart';
import 'package:soko/features/recycle/presentation/widgets/order_price_section.dart';
import 'package:soko/features/recycle/presentation/widgets/order_action_buttons.dart';
import 'package:soko/features/recycle/domain/entities/recycle_order.dart';
import 'package:soko/features/recycle/domain/entities/recycle_models.dart';
import 'package:soko/core/enums/app_enums.dart';
import 'package:soko/core/router/app_routes.dart';

/// 回收订单详情页面
class RecycleOrderDetailPage extends ConsumerStatefulWidget {
  const RecycleOrderDetailPage({
    super.key,
    required this.orderId,
  });

  final String orderId;

  @override
  ConsumerState<RecycleOrderDetailPage> createState() =>
      _RecycleOrderDetailPageState();
}

class _RecycleOrderDetailPageState
    extends ConsumerState<RecycleOrderDetailPage> {
  @override
  void initState() {
    super.initState();
    // 页面初始化时加载订单详情
    WidgetsBinding.instance.addPostFrameCallback((_) {
      ref
          .read(recycleOrderDetailProvider(widget.orderId).notifier)
          .loadOrderDetail();
    });
  }

  @override
  Widget build(BuildContext context) {
    final orderDetailState =
        ref.watch(recycleOrderDetailProvider(widget.orderId));

    return Scaffold(
      appBar: const CustomAppBar(
        title: '订单详情',
        showBackButton: true,
      ),
      body: orderDetailState.when(
        initial: () => const LoadingWidget(),
        loading: () => const LoadingWidget(),
        success: (order) => _buildOrderDetail(order),
        error: (error, stackTrace) => ErrorRetryWidget(
          message: error,
          onRetry: () => ref
              .read(recycleOrderDetailProvider(widget.orderId).notifier)
              .loadOrderDetail(),
        ),
      ),

      // 底部操作按钮
      bottomNavigationBar: orderDetailState.maybeWhen(
        success: (order) => OrderActionButtons(
          order: order,
          onCancel: order.canCancel ? () => _showCancelDialog() : null,
          onConfirmPrice: order.canConfirmPrice ? () => _confirmPrice() : null,
          onConfirmShipment:
              order.canConfirmShipment ? () => _navigateToShipping() : null,
          onViewLogistics:
              order.canViewLogistics ? () => _navigateToLogistics() : null,
          onReorder: order.canReorder ? () => _reorder() : null,
        ),
        orElse: () => null,
      ),
    );
  }

  /// 构建订单详情内容
  Widget _buildOrderDetail(RecycleOrder order) {
    return RefreshIndicator(
      onRefresh: () => ref
          .read(recycleOrderDetailProvider(widget.orderId).notifier)
          .refreshOrderDetail(),
      child: SingleChildScrollView(
        padding: EdgeInsets.all(16.w),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // 订单状态时间线
            OrderStatusTimeline(order: order),
            SizedBox(height: 20.h),

            // 订单基本信息
            OrderInfoSection(order: order),
            SizedBox(height: 20.h),

            // 商品图片（暂时注释，需要解决类型冲突）
            // if (order.files != null && order.files!.isNotEmpty)
            //   OrderImagesSection(files: order.files!),
            // if (order.files != null && order.files!.isNotEmpty)
            //   SizedBox(height: 20.h),

            // 价格信息
            OrderPriceSection(order: order),
            SizedBox(height: 20.h),

            // 联系信息
            if (order.contactPerson != null || order.userPhone != null)
              _buildContactSection(order),
            if (order.contactPerson != null || order.userPhone != null)
              SizedBox(height: 20.h),

            // 备注信息（暂时移除，因为RecycleOrder中没有remark字段）
            // if (order.remark != null && order.remark!.isNotEmpty)
            //   _buildRemarkSection(order.remark!),
            // if (order.remark != null && order.remark!.isNotEmpty)
            //   SizedBox(height: 20.h),

            // 订单时间信息
            _buildTimeSection(order),

            // 底部安全距离
            SizedBox(height: 100.h),
          ],
        ),
      ),
    );
  }

  /// 构建联系信息部分
  Widget _buildContactSection(RecycleOrder order) {
    return Container(
      padding: EdgeInsets.all(16.w),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(12.r),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.05),
            blurRadius: 8,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(
                Icons.contact_phone,
                size: 20.w,
                color: Theme.of(context).primaryColor,
              ),
              SizedBox(width: 8.w),
              Text(
                '联系信息',
                style: TextStyle(
                  fontSize: 16.sp,
                  fontWeight: FontWeight.w600,
                ),
              ),
            ],
          ),
          SizedBox(height: 12.h),
          if (order.contactPerson != null) ...[
            _buildInfoRow('联系人', order.contactPerson!),
            SizedBox(height: 8.h),
          ],
          if (order.userPhone != null) _buildInfoRow('联系电话', order.userPhone!),
        ],
      ),
    );
  }

  /// 构建时间信息部分
  Widget _buildTimeSection(RecycleOrder order) {
    return Container(
      padding: EdgeInsets.all(16.w),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(12.r),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.05),
            blurRadius: 8,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(
                Icons.schedule,
                size: 20.w,
                color: Theme.of(context).primaryColor,
              ),
              SizedBox(width: 8.w),
              Text(
                '时间信息',
                style: TextStyle(
                  fontSize: 16.sp,
                  fontWeight: FontWeight.w600,
                ),
              ),
            ],
          ),
          SizedBox(height: 12.h),
          _buildInfoRow('创建时间', _formatDateTime(order.createTime)),
          SizedBox(height: 8.h),
          _buildInfoRow('更新时间', _formatDateTime(order.updateTime)),
        ],
      ),
    );
  }

  /// 构建信息行
  Widget _buildInfoRow(String label, String value) {
    return Row(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        SizedBox(
          width: 80.w,
          child: Text(
            label,
            style: TextStyle(
              fontSize: 14.sp,
              color: Colors.grey[600],
            ),
          ),
        ),
        Expanded(
          child: Text(
            value,
            style: TextStyle(
              fontSize: 14.sp,
              color: Colors.grey[800],
            ),
          ),
        ),
      ],
    );
  }

  /// 格式化日期时间
  String _formatDateTime(int timestamp) {
    final dateTime = DateTime.fromMillisecondsSinceEpoch(timestamp);
    return '${dateTime.year}-${dateTime.month.toString().padLeft(2, '0')}-${dateTime.day.toString().padLeft(2, '0')} '
        '${dateTime.hour.toString().padLeft(2, '0')}:${dateTime.minute.toString().padLeft(2, '0')}';
  }

  /// 显示取消订单对话框
  void _showCancelDialog() {
    showDialog<void>(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('取消订单'),
        content: const Text('确定要取消这个回收订单吗？取消后无法恢复。'),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('取消'),
          ),
          TextButton(
            onPressed: () {
              Navigator.of(context).pop();
              ref
                  .read(recycleOrderDetailProvider(widget.orderId).notifier)
                  .cancelOrder();
            },
            child: const Text('确定'),
          ),
        ],
      ),
    );
  }

  /// 确认价格
  void _confirmPrice() {
    ref
        .read(recycleOrderDetailProvider(widget.orderId).notifier)
        .confirmPrice();
  }

  /// 导航到寄送页面
  void _navigateToShipping() {
    // TODO: 导航到寄送页面
  }

  /// 导航到物流页面
  void _navigateToLogistics() {
    context.push(AppRoutes.logisticsTrackingPath(widget.orderId));
  }

  /// 重新下单
  void _reorder() {
    // TODO: 重新下单
  }
}
