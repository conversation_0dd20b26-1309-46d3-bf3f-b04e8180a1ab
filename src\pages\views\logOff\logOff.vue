<template>
  <div
    class="min-h-screen flex flex-col items-center justify-center bg-gray-100 p-8"
  >
    <div class="bg-white p-12 rounded-2xl shadow-lg w-full max-w-md space-y-8">
      <h2 class="text-3xl font-extrabold text-center text-red-700">
        确定要注销您的账号吗？
      </h2>
      <p class="text-gray-800 text-center text-lg">
        此操作不可逆，注销后您的所有数据将被永久删除。
      </p>
      <div class="space-y-4">
        <input
          v-model="password"
          type="password"
          class="w-full p-3 border border-gray-300 rounded-lg focus:outline-none focus:border-red-500 focus:ring-red-500 focus:ring-1"
          placeholder="请输入密码"
        />
        <input
          v-model="confirmPassword"
          type="password"
          class="w-full p-3 border border-gray-300 rounded-lg focus:outline-none focus:border-red-500 focus:ring-red-500 focus:ring-1"
          placeholder="请再次输入密码"
        />
      </div>
      <div class="flex justify-center space-x-6">
        <button
          class="px-8 py-3 bg-gray-200 text-gray-800 rounded-lg font-medium hover:bg-gray-300 transition-colors"
          @click="cancelLogOff"
        >
          取消
        </button>
        <button
          class="px-8 py-3 bg-red-600 text-white rounded-lg font-medium hover:bg-red-700 transition-colors"
          @click="confirmLogOff"
          :disabled="password !== confirmPassword"
        >
          确认注销
        </button>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref } from "vue"
import { Toast } from "vant"
import { DELETE_USER_DEACTIVATE } from "@/api/api"
import store from "@/store"
import publicKeyManager from "@/util/common/publicKey"
import JSEncrypt from "jsencrypt"
// 定义密码和确认密码的响应式变量
const password = ref("")
const confirmPassword = ref("")

// 取消注销操作
const cancelLogOff = () => {
  uni.navigateBack()
}

// 确认注销操作
const confirmLogOff = async () => {
  if (password.value !== confirmPassword.value) {
    Toast("两次输入的密码不一致，请重新输入。")
    return
  }
  try {
    const res = await uni.showModal({
      title: "确认注销",
      content: "您确定要注销账号吗？此操作不可逆。",
      confirmText: "确认",
      cancelText: "取消"
    })
    if (res.confirm) {
      // 获取公钥用于加密
      const publicKey = await publicKeyManager.getPublicKey()

      // 使用JSEncrypt加密密码
      const encrypt = new JSEncrypt()
      encrypt.setPublicKey(publicKey)
      const encryptedPassword = encrypt.encrypt(confirmPassword.value)

      const result = await DELETE_USER_DEACTIVATE({
        password: encryptedPassword
      })
      if (result.code === 200) {
        Toast("注销成功！")
        uni.navigateTo({ url: "/pages/views/login/login" })
        uni.closeSocket()
        store.commit("SET_WEBSOCKET_CONNECTED", false)
        store.commit("CLEAR_USER_INFO")
      }
    }
  } catch (error) {
    console.error("注销操作出错:", error)
  }
}
</script>
