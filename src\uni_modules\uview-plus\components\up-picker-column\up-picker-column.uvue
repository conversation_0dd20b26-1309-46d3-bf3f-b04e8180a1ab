<template>
	<picker-view-column>
		<view class="up-picker-column">
		</view>
	</picker-view-column>
</template>

<script>
	import { propsPickerColumn } from './props';
	import { mpMixin } from '../../libs/mixin/mpMixin';
	import { mixin } from '../../libs/mixin/mixin';
	/**
	 * PickerColumn 
	 * @description 
	 * @tutorial url
	 * @property {String}
	 * @event {Function}
	 * @example
	 */
	export default {
		name: 'up-picker-column',
		mixins: [mpMixin, mixin, propsPickerColumn],
	}
</script>

<style lang="scss" scoped>
	@import "../../libs/css/components.scss";
</style>
