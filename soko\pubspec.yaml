name: soko
description: 中古虾(SOKO) - 特摄模玩主题电商平台 Flutter版本

# The following line prevents the package from being accidentally published to
# pub.dev using `flutter pub publish`. This is preferred for private packages.
publish_to: 'none' # Remove this line if you wish to publish to pub.dev

version: 1.0.0+1

environment:
  sdk: '>=3.0.0 <4.0.0'
  flutter: '>=3.10.0'

dependencies:
  flutter:
    sdk: flutter

  # UI组件库
  cupertino_icons: ^1.0.6

  # 状态管理
  provider: ^6.1.1
  riverpod: ^2.4.9
  flutter_riverpod: ^2.4.9

  # 网络请求
  dio: ^5.4.0
  retrofit: ^4.0.3
  json_annotation: ^4.8.1

  # 函数式编程
  dartz: ^0.10.1

  # 路由管理
  go_router: ^12.1.3

  # 本地存储
  shared_preferences: ^2.2.2
  hive: ^2.2.3
  hive_flutter: ^1.1.0

  # 图片处理
  cached_network_image: ^3.3.0
  image_picker: ^1.0.4

  # 工具类
  intl: ^0.19.0
  crypto: ^3.0.3
  uuid: ^4.2.1

  # 状态管理增强
  freezed_annotation: ^2.4.1

  # UI增强
  flutter_screenutil: ^5.9.0
  flutter_svg: ^2.0.9
  lottie: ^2.7.0

  # 权限管理
  permission_handler: ^11.1.0

  # 设备信息
  device_info_plus: ^9.1.1
  package_info_plus: ^4.2.0

dev_dependencies:
  flutter_test:
    sdk: flutter

  # 代码生成
  build_runner: ^2.4.7
  retrofit_generator: ^8.0.4
  json_serializable: ^6.7.1
  hive_generator: ^2.0.1
  freezed: ^2.4.6

  # 代码质量
  flutter_lints: ^3.0.1
  very_good_analysis: ^5.1.0

flutter:
  uses-material-design: true

  # 资源文件
  assets:
    - assets/images/
    - assets/icons/
    - assets/animations/
    - assets/fonts/

  # 字体配置
  fonts:
    - family: PingFang
      fonts:
        - asset: assets/fonts/PingFang-Regular.ttf
        - asset: assets/fonts/PingFang-Medium.ttf
          weight: 500
        - asset: assets/fonts/PingFang-Bold.ttf
          weight: 700
