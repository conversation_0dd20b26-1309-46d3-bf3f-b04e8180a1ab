import 'package:flutter_riverpod/flutter_riverpod.dart';

import 'package:soko/features/recycle/domain/entities/recycle_models.dart';
import 'package:soko/features/recycle/domain/repositories/recycle_repository.dart';
import 'package:soko/features/recycle/data/repositories/recycle_repository_impl.dart';

/// 价格评估状态管理
class PriceEvaluationNotifier
    extends StateNotifier<AsyncValue<PriceEvaluationResult?>> {
  PriceEvaluationNotifier(this._recycleRepository)
      : super(const AsyncValue.data(null));

  final RecycleRepository _recycleRepository;
  PriceEvaluationRequest? _lastRequest;

  /// 执行价格评估
  Future<void> evaluate(PriceEvaluationRequest request) async {
    _lastRequest = request;
    state = const AsyncValue.loading();

    try {
      final result = await _recycleRepository.evaluatePrice(request);
      state = AsyncValue.data(result);
    } catch (error, stackTrace) {
      state = AsyncValue.error(error, stackTrace);
    }
  }

  /// 刷新评估结果
  Future<void> refresh() async {
    if (_lastRequest != null) {
      await evaluate(_lastRequest!);
    }
  }

  /// 重试评估
  Future<void> retry() async {
    if (_lastRequest != null) {
      await evaluate(_lastRequest!);
    }
  }

  /// 清除评估结果
  void clear() {
    state = const AsyncValue.data(null);
    _lastRequest = null;
  }

  /// 获取历史评估记录
  Future<List<PriceEvaluationResult>> getHistory() async {
    try {
      return await _recycleRepository.getEvaluationHistory();
    } catch (error) {
      return [];
    }
  }
}

/// 价格评估Provider
final priceEvaluationProvider = StateNotifierProvider<PriceEvaluationNotifier,
    AsyncValue<PriceEvaluationResult?>>((ref) {
  final recycleRepository = ref.watch(recycleRepositoryProvider);
  return PriceEvaluationNotifier(recycleRepository);
});

/// 评估历史Provider
final evaluationHistoryProvider =
    FutureProvider<List<PriceEvaluationResult>>((ref) async {
  final notifier = ref.read(priceEvaluationProvider.notifier);
  return notifier.getHistory();
});

/// 快速评估Provider - 用于简单的价格预估
final quickEvaluationProvider =
    FutureProvider.family<double, Map<String, String>>((ref, params) async {
  final recycleRepository = ref.watch(recycleRepositoryProvider);
  return recycleRepository.getQuickEstimate(
    brandId: params['brandId']!,
    modelId: params['modelId']!,
    conditionId: params['conditionId']!,
  );
});

/// 价格趋势Provider - 获取特定产品的价格趋势
final priceTrendProvider =
    FutureProvider.family<List<PriceTrendData>, String>((ref, productId) async {
  final recycleRepository = ref.watch(recycleRepositoryProvider);
  return recycleRepository.getPriceTrend(productId);
});
