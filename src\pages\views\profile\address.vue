<template>
  <view class="page min-h-screen bg-gray-50 pb-safe">
    <NavBar title="收货地址" :showBack="true"></NavBar>

    <div class="address-content px-4 py-3">
      <van-pull-refresh v-model="refreshLoading" @refresh="onRefresh">
        <!-- 空状态 -->
        <up-empty
          v-if="addresses.length === 0"
          mode="address"
          icon="map-marked"
          text="暂无收货地址"
          marginTop="120"
        >
        </up-empty>

        <!-- 地址列表 -->
        <view v-else class="address-list space-y-4 mb-6">
          <scroll-view scroll-y style="height: calc(100vh - 200px)">
            <view
              v-for="(item, index) in addresses"
              :key="index"
              class="address-item card-style"
            >
              <!-- 移除右上角角标 -->
              <view class="address-info">
                <view class="address-header">
                  <text
                    class="name text-base font-semibold text-text-primary mr-3"
                    >{{ item.name }}</text
                  >
                  <text class="phone text-sm text-text-secondary">{{
                    item.phone
                  }}</text>
                </view>
                <view
                  class="address-detail text-sm text-text-primary leading-normal mb-1"
                >
                  {{ item.province }}{{ item.city }}{{ item.district
                  }}{{ item.detail }}
                </view>
                <view class="tag-line mt-1 -ml-1">
                  <!-- 默认地址标签放在最左侧 -->
                  <up-tag
                    v-if="item.isDefault === true || item.iDefault === true"
                    text="默认地址"
                    type="error"
                    plain
                    size="mini"
                  />
                  <!-- 其他标签放在右侧，有左边距 -->
                  <up-tag
                    v-if="item.tagName"
                    :text="item.tagName"
                    plain
                    size="mini"
                    :color="secondaryColor"
                    class="ml-2"
                  />
                </view>
              </view>
              <view class="address-actions pl-4">
                <up-button
                  icon="编辑"
                  size="mini"
                  :custom-style="editButtonStyle"
                  @click="editAddress(item)"
                ></up-button>
              </view>
            </view>
          </scroll-view>
        </view>
      </van-pull-refresh>
      <!-- 底部添加按钮 -->
      <view
        class="add-address fixed bottom-6 left-0 right-0 flex justify-center z-10"
      >
        <up-button
          text="新增收货地址"
          shape="circle"
          :custom-style="addButtonStyle"
          @click="addNewAddress"
        ></up-button>
      </view>
    </div>
  </view>
</template>

<script setup lang="ts">
import { ref, onMounted } from "vue"
import { useStore } from "vuex"
import NavBar from "@/components/NavBar.vue"
// @ts-ignore
import { getAddressList } from "@/api/api"
import { onShow } from "@dcloudio/uni-app"

const store = useStore()
const secondaryColor = "#10B981" // 使用规范中的骑士绿 secondary 颜色

// 是否已初始化加载过数据（避免首次加载调用两次接口）
const isInitialized = ref(false)
const loading = ref(false)
const refreshLoading = ref(false)

// 地址数据
interface AddressItem {
  id: string | number
  name: string
  phone: string
  province: string
  city: string
  district: string
  detail: string
  iDefault: boolean
  tagName?: string
  isDefault?: boolean
}

const addresses = ref<AddressItem[]>([])

// 按钮样式
const addButtonStyle = {
  height: "44px",
  fontSize: "16px",
  width: "90%",
  borderRadius: "22px", // 全圆角按钮
  background: "linear-gradient(to right, #10B981, #34D399)", // 从次要色到浅绿色的渐变
  fontWeight: "500",
  boxShadow: "0 4px 12px rgba(16, 185, 129, 0.4)",
  border: "none",
  color: "#ffffff" // 白色文字
}

const editButtonStyle = {
  minWidth: "32px",
  height: "32px", // 将高度从36px减小到32px
  borderColor: "transparent",
  color: "#ffffff",
  background: "linear-gradient(135deg, #10B981, #34D399)", // 与添加按钮协调的渐变
  boxShadow: "0 2px 8px rgba(16, 185, 129, 0.3)",
  display: "flex",
  justifyContent: "center",
  alignItems: "center",
  borderRadius: "8px" // 使用较小的圆角
}

// 调试函数，确保地址列表正确加载和处理
const logAddresses = (addressList: AddressItem[]) => {
  console.log("地址列表数据:", JSON.stringify(addressList))
  // 检查默认地址标志
  const hasDefault = addressList.some(
    (addr: AddressItem) => addr.iDefault === true || addr.isDefault === true
  )
  console.log("是否有默认地址:", hasDefault)
  return addressList
}

// 获取地址列表
const fetchAddresses = async () => {
  loading.value = true
  try {
    const res = await getAddressList()
    // 确保至少有一个默认地址，用于测试
    const addressData = res.data || []

    // 如果有地址但没有默认地址，强制将第一个设为默认
    if (addressData.length > 0) {
      const hasDefault = addressData.some(
        (addr: AddressItem) => addr.iDefault === true || addr.isDefault === true
      )
      if (!hasDefault) {
        addressData[0].iDefault = true
        console.log("已强制设置第一个地址为默认")
      }
    }

    // 使用处理后的数据
    addresses.value = logAddresses(addressData)
  } catch (error) {
    console.error("获取地址列表失败", error)
    uni.showToast({
      title: "获取地址列表失败",
      icon: "none"
    })
  } finally {
    loading.value = false
  }
}

// 添加新地址
const addNewAddress = () => {
  uni.navigateTo({
    url: "/pages/views/profile/address-edit"
  })
}

// 编辑地址
const editAddress = (address: AddressItem) => {
  try {
    // 手动构建URL参数字符串，避免使用可能不兼容的URLSearchParams
    let url = `/pages/views/profile/address-edit?id=${address.id}`

    // 添加地址总数
    url += `&addressCount=${addresses.value.length}`

    // 判断是否为唯一地址
    if (addresses.value.length === 1) {
      url += `&isOnly=true`
    }

    uni.navigateTo({
      url,
      fail: (err) => {
        console.error("跳转编辑页面失败", err)
        uni.showToast({
          title: "无法打开编辑页面",
          icon: "none"
        })
      }
    })
  } catch (error) {
    console.error("编辑地址失败", error)
    uni.showToast({
      title: "操作失败，请重试",
      icon: "none"
    })
  }
}

// 加载地址列表
const loadAddressList = () => {
  isInitialized.value = true
  fetchAddresses()
}

// 页面加载和显示时获取地址列表
onMounted(() => {
  loadAddressList()
})

// 监听页面显示，从编辑页返回时刷新列表
onShow(() => {
  // 只有初始化后的再次显示才重新加载（避免首次加载重复调用）
  if (isInitialized.value) {
    loadAddressList()
  }
})

const onRefresh = () => {
  loadAddressList()
  refreshLoading.value = false
}
</script>

<style scoped lang="scss">
.card-style {
  background-color: #fff;
  border-radius: 12px; // rounded-xl
  padding: 16px; // p-4
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.06);
  position: relative;
  transition: all 0.3s ease; // animate-normal

  &:active {
    transform: translateY(2px);
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
  }
}

// 移除默认地址左边框样式
// .default-address {
//   border-left: 3px solid #EF4444;
// }

// 优化默认角标样式
// .default-badge {
//   position: absolute;
//   top: 0;
//   right: 0;
//   background-color: #EF4444;
//   color: white;
//   font-size: 12px;
//   padding: 4px 10px;
//   border-top-right-radius: 12px;
//   border-bottom-left-radius: 8px;
//   font-weight: 500;
//   z-index: 10;
//   box-shadow: 0 2px 4px rgba(239, 68, 68, 0.3);
// }

.address-item {
  display: flex;
  align-items: flex-start;
  margin-top: 24rpx;
}

.address-info {
  flex: 1;
}

.address-header {
  display: flex;
  align-items: center;
  margin-bottom: 12px;
}

.name {
  font-size: 16px;
  font-weight: 600;
  color: #333;
  margin-right: 12px;
}

.phone {
  font-size: 14px;
  color: #666;
}

.address-detail {
  font-size: 14px;
  color: #333;
  line-height: 1.6;
  margin-bottom: 4px;
}

.tag-line {
  margin-top: 4px;
  margin-left: -4px; /* 负左边距，让标签更靠左 */
}

.address-actions {
  display: flex;
  align-items: center;
  padding-left: 15px;
}

.add-address {
  position: fixed;
  bottom: 30px;
  left: 0;
  right: 0;
  display: flex;
  justify-content: center;
  z-index: 10;
}

:deep(.up-empty__image) {
  width: 120px !important;
  height: 120px !important;
  margin-bottom: 16px;
}

:deep(.up-empty__text) {
  color: #666666;
  font-size: 16px !important;
}

:deep(.up-tag) {
  margin-left: 8px;
}

:deep(.up-tag--error) {
  background-color: #ef4444 !important;
}
</style>
