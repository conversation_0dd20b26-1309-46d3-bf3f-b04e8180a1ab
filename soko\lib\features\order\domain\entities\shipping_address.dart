import 'package:freezed_annotation/freezed_annotation.dart';

part 'shipping_address.freezed.dart';
part 'shipping_address.g.dart';

/// 收货地址
@freezed
class ShippingAddress with _$ShippingAddress {
  const factory ShippingAddress({
    /// 地址ID
    required String id,
    
    /// 收货人姓名
    required String receiverName,
    
    /// 收货人电话
    required String receiverPhone,
    
    /// 省份
    required String province,
    
    /// 城市
    required String city,
    
    /// 区县
    required String district,
    
    /// 详细地址
    required String detailAddress,
    
    /// 创建时间
    required DateTime createdAt, /// 更新时间
    required DateTime updatedAt, /// 邮政编码（可选）
    String? postalCode,
    
    /// 地址标签（如：家、公司等）
    String? label,
    
    /// 是否为默认地址
    @Default(false) bool isDefault,
  }) = _ShippingAddress;

  factory ShippingAddress.fromJson(Map<String, dynamic> json) =>
      _$ShippingAddressFromJson(json);
}

/// ShippingAddress 扩展方法
extension ShippingAddressExtension on ShippingAddress {
  /// 获取完整地址
  String get fullAddress {
    return '$province$city$district$detailAddress';
  }

  /// 获取地区信息
  String get regionInfo {
    return '$province $city $district';
  }

  /// 获取显示用的收货人信息
  String get receiverInfo {
    return '$receiverName $receiverPhone';
  }

  /// 验证地址是否完整
  bool get isValid {
    return receiverName.isNotEmpty &&
           receiverPhone.isNotEmpty &&
           province.isNotEmpty &&
           city.isNotEmpty &&
           district.isNotEmpty &&
           detailAddress.isNotEmpty;
  }

  /// 获取地址摘要（用于列表显示）
  String get summary {
    final region = '$city$district';
    final detail = detailAddress.length > 20 
        ? '${detailAddress.substring(0, 20)}...' 
        : detailAddress;
    return '$region $detail';
  }
}
