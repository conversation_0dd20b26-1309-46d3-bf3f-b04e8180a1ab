import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

import 'package:soko/shared/presentation/widgets/custom_card.dart';
import 'package:soko/features/order/domain/entities/shipping_address.dart';

/// 订单创建地址选择组件
class OrderCreateAddressSection extends StatelessWidget {

  const OrderCreateAddressSection({
    super.key,
    this.selectedAddress,
    required this.onAddressSelected,
  });
  final ShippingAddress? selectedAddress;
  final ValueChanged<ShippingAddress> onAddressSelected;

  @override
  Widget build(BuildContext context) {
    return CustomCard(
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // 标题
          Row(
            children: [
              Icon(
                Icons.location_on,
                color: Colors.blue,
                size: 20.w,
              ),
              SizedBox(width: 8.w),
              Text(
                '收货地址',
                style: TextStyle(
                  fontSize: 16.sp,
                  fontWeight: FontWeight.w600,
                ),
              ),
            ],
          ),
          <PERSON><PERSON><PERSON><PERSON>(height: 16.h),

          // 地址内容
          if (selectedAddress != null)
            _buildSelectedAddress(selectedAddress!)
          else
            _buildNoAddress(),
        ],
      ),
    );
  }

  /// 构建已选择的地址
  Widget _buildSelectedAddress(ShippingAddress address) {
    return InkWell(
      onTap: _showAddressSelector,
      child: Container(
        width: double.infinity,
        padding: EdgeInsets.all(12.w),
        decoration: BoxDecoration(
          color: Colors.blue.withValues(alpha: 0.05),
          borderRadius: BorderRadius.circular(8.r),
          border: Border.all(color: Colors.blue.withValues(alpha: 0.2)),
        ),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // 收货人信息
            Row(
              children: [
                Text(
                  address.receiverName,
                  style: TextStyle(
                    fontSize: 16.sp,
                    fontWeight: FontWeight.w500,
                    color: Colors.black87,
                  ),
                ),
                SizedBox(width: 16.w),
                Text(
                  address.receiverPhone,
                  style: TextStyle(
                    fontSize: 14.sp,
                    color: Colors.grey[600],
                  ),
                ),
                const Spacer(),
                if (address.isDefault)
                  Container(
                    padding: EdgeInsets.symmetric(horizontal: 6.w, vertical: 2.h),
                    decoration: BoxDecoration(
                      color: Colors.red,
                      borderRadius: BorderRadius.circular(4.r),
                    ),
                    child: Text(
                      '默认',
                      style: TextStyle(
                        fontSize: 10.sp,
                        color: Colors.white,
                      ),
                    ),
                  ),
              ],
            ),
            SizedBox(height: 8.h),

            // 详细地址
            Text(
              address.fullAddress,
              style: TextStyle(
                fontSize: 14.sp,
                color: Colors.black87,
                height: 1.4,
              ),
            ),
            SizedBox(height: 8.h),

            // 更换地址提示
            Row(
              children: [
                const Spacer(),
                Text(
                  '点击更换地址',
                  style: TextStyle(
                    fontSize: 12.sp,
                    color: Colors.blue,
                  ),
                ),
                Icon(
                  Icons.chevron_right,
                  color: Colors.blue,
                  size: 16.w,
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  /// 构建无地址状态
  Widget _buildNoAddress() {
    return InkWell(
      onTap: _showAddressSelector,
      child: Container(
        width: double.infinity,
        padding: EdgeInsets.all(20.w),
        decoration: BoxDecoration(
          color: Colors.grey.withValues(alpha: 0.05),
          borderRadius: BorderRadius.circular(8.r),
          border: Border.all(color: Colors.grey.withValues(alpha: 0.2)),
        ),
        child: Column(
          children: [
            Icon(
              Icons.add_location_alt_outlined,
              color: Colors.grey[400],
              size: 48.w,
            ),
            SizedBox(height: 8.h),
            Text(
              '请选择收货地址',
              style: TextStyle(
                fontSize: 14.sp,
                color: Colors.grey[600],
              ),
            ),
            SizedBox(height: 4.h),
            Text(
              '点击添加收货地址',
              style: TextStyle(
                fontSize: 12.sp,
                color: Colors.blue,
              ),
            ),
          ],
        ),
      ),
    );
  }

  /// 显示地址选择器
  void _showAddressSelector() {
    // TODO(address): 实现地址选择功能
    // 这里应该跳转到地址选择页面或显示地址选择弹窗
    
    // 临时模拟选择地址
    final mockAddress = ShippingAddress(
      id: 'addr_001',
      receiverName: '张三',
      receiverPhone: '13800138000',
      province: '广东省',
      city: '深圳市',
      district: '南山区',
      detailAddress: '科技园南区深南大道10000号腾讯大厦',
      label: '公司',
      isDefault: true,
      createdAt: DateTime.now(),
      updatedAt: DateTime.now(),
    );
    
    onAddressSelected(mockAddress);
  }
}
