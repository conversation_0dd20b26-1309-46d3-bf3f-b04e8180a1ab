import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:go_router/go_router.dart';

import 'package:soko/shared/presentation/widgets/custom_app_bar.dart';
import 'package:soko/shared/presentation/widgets/loading_widget.dart';
import 'package:soko/features/recycle/presentation/providers/recycle_home_provider.dart';
import 'package:soko/core/router/app_routes.dart';
import 'package:soko/features/recycle/presentation/widgets/recycle_stats_card.dart';
import 'package:soko/features/recycle/presentation/widgets/recycle_process_section.dart';
import 'package:soko/features/recycle/presentation/widgets/recycle_categories_section.dart';
import 'package:soko/features/recycle/presentation/widgets/recycle_recent_orders_section.dart';
import 'package:soko/features/recycle/presentation/widgets/recycle_quick_actions.dart';

/// 回收首页
class RecycleHomePage extends ConsumerStatefulWidget {
  const RecycleHomePage({super.key});

  @override
  ConsumerState<RecycleHomePage> createState() => _RecycleHomePageState();
}

class _RecycleHomePageState extends ConsumerState<RecycleHomePage> {
  @override
  void initState() {
    super.initState();
    // 页面初始化时加载数据
    WidgetsBinding.instance.addPostFrameCallback((_) {
      ref.read(recycleHomeProvider.notifier).initializeHomeData();
    });
  }

  @override
  Widget build(BuildContext context) {
    final homeState = ref.watch(recycleHomeProvider);

    return Scaffold(
      appBar: const CustomAppBar(
        title: '回收',
        showBackButton: false,
      ),
      body: RefreshIndicator(
        onRefresh: () =>
            ref.read(recycleHomeProvider.notifier).refreshHomeData(),
        child: SingleChildScrollView(
          physics: const AlwaysScrollableScrollPhysics(),
          padding: EdgeInsets.all(16.w),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // 快速操作区域
              RecycleQuickActions(
                onCreateOrder: _navigateToCreateOrder,
                onViewOrders: _navigateToOrderList,
              ),
              SizedBox(height: 20.h),

              // 统计信息卡片
              homeState.statsState.when(
                idle: () => const SizedBox.shrink(),
                loading: () => const LoadingWidget(),
                success: (stats) => RecycleStatsCard(stats: stats),
                error: (error) => _buildErrorCard('统计信息加载失败', error),
              ),
              SizedBox(height: 20.h),

              // 回收分类
              homeState.categoriesState.when(
                idle: () => const SizedBox.shrink(),
                loading: () => const LoadingWidget(),
                success: (categories) => RecycleCategoriesSection(
                  categories: categories,
                  onCategoryTap: (category) =>
                      _navigateToCreateOrder(categoryId: category.id),
                ),
                error: (error) => _buildErrorCard('分类信息加载失败', error),
              ),
              SizedBox(height: 20.h),

              // 回收流程说明
              homeState.processState.when(
                idle: () => const SizedBox.shrink(),
                loading: () => const LoadingWidget(),
                success: (process) =>
                    RecycleProcessSection(processSteps: process),
                error: (error) => _buildErrorCard('流程说明加载失败', error),
              ),
              SizedBox(height: 20.h),

              // 最近订单
              homeState.recentOrdersState.when(
                idle: () => const SizedBox.shrink(),
                loading: () => const LoadingWidget(),
                success: (orders) => RecycleRecentOrdersSection(
                  orders: orders,
                  onOrderTap: (order) => _navigateToOrderDetail(order.id),
                  onViewAll: _navigateToOrderList,
                ),
                error: (error) => _buildErrorCard('最近订单加载失败', error),
              ),
            ],
          ),
        ),
      ),
    );
  }

  /// 构建错误卡片
  Widget _buildErrorCard(String title, String error) {
    return Container(
      width: double.infinity,
      padding: EdgeInsets.all(16.w),
      decoration: BoxDecoration(
        color: Colors.red.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(8.r),
        border: Border.all(color: Colors.red.withValues(alpha: 0.3)),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            title,
            style: TextStyle(
              fontSize: 14.sp,
              fontWeight: FontWeight.w500,
              color: Colors.red[700],
            ),
          ),
          SizedBox(height: 4.h),
          Text(
            error,
            style: TextStyle(
              fontSize: 12.sp,
              color: Colors.red[600],
            ),
          ),
        ],
      ),
    );
  }

  /// 导航到创建订单页面
  void _navigateToCreateOrder({String? categoryId}) {
    // TODO(navigation): 实现导航到创建订单页面
    // context.push('/recycle/create', extra: {'categoryId': categoryId});
  }

  /// 导航到订单列表页面
  void _navigateToOrderList() {
    context.push(AppRoutes.recycleOrderList);
  }

  /// 导航到订单详情页面
  void _navigateToOrderDetail(String orderId) {
    // TODO(navigation): 实现导航到订单详情页面
    // context.push('/recycle/order/$orderId');
  }
}
