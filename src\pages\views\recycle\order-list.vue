<template>
  <view class="min-h-screen bg-gray-50">
    <!-- 自定义导航栏 -->
    <StatusBarPlaceholder />
    <NavBar title="我的回收" />

    <!-- 状态筛选 -->
    <view class="bg-white border-b border-gray-200">
      <scroll-view scroll-x class="whitespace-nowrap">
        <view class="flex px-4 py-3">
          <view
            class="px-4 py-2 rounded-full text-sm font-medium mr-3 transition-colors"
            :class="currentStatus === '' ? 'bg-primary-500 text-white' : 'bg-gray-100 text-gray-600'"
            @click="filterByStatus('')"
          >
            全部
          </view>
          <view
            class="px-4 py-2 rounded-full text-sm font-medium mr-3 transition-colors"
            :class="currentStatus === 'PENDING_APPROVAL' ? 'bg-primary-500 text-white' : 'bg-gray-100 text-gray-600'"
            @click="filterByStatus('PENDING_APPROVAL')"
          >
            待审核
          </view>
          <view
            class="px-4 py-2 rounded-full text-sm font-medium mr-3 transition-colors"
            :class="currentStatus === 'PRICE_QUOTED' ? 'bg-primary-500 text-white' : 'bg-gray-100 text-gray-600'"
            @click="filterByStatus('PRICE_QUOTED')"
          >
            已报价
          </view>
          <view
            class="px-4 py-2 rounded-full text-sm font-medium mr-3 transition-colors"
            :class="currentStatus === 'SHIPPING_CONFIRMED' ? 'bg-primary-500 text-white' : 'bg-gray-100 text-gray-600'"
            @click="filterByStatus('SHIPPING_CONFIRMED')"
          >
            已寄送
          </view>
          <view
            class="px-4 py-2 rounded-full text-sm font-medium mr-3 transition-colors"
            :class="currentStatus === 'COMPLETED' ? 'bg-primary-500 text-white' : 'bg-gray-100 text-gray-600'"
            @click="filterByStatus('COMPLETED')"
          >
            已完成
          </view>
        </view>
      </scroll-view>
    </view>

    <!-- 订单列表 -->
    <view class="px-4 py-4">
      <view v-if="loading && orderList.length === 0" class="text-center py-8">
        <text class="text-gray-500">加载中...</text>
      </view>

      <view v-else-if="orderList.length === 0" class="text-center py-16">
        <image src="/static/empty-order.png" class="w-24 h-24 mx-auto mb-4 opacity-50"/>
        <text class="text-gray-500 mb-4 block">暂无回收订单</text>
        <button
          class="bg-primary-500 text-white px-6 py-2 rounded-lg text-sm font-medium active:bg-primary-600"
          @click="goToCreateOrder"
        >
          立即回收
        </button>
      </view>

      <view v-else class="space-y-3">
        <view
          class="bg-white rounded-lg p-4 shadow-sm active:bg-gray-50"
          v-for="order in orderList"
          :key="order.id"
          @click="goToOrderDetail(order.id)"
        >
          <!-- 订单头部 -->
          <view class="flex justify-between items-start mb-3">
            <view class="flex-1">
              <text class="text-base font-medium text-gray-800 block">
                {{ order.brandName }} {{ order.model }}
              </text>
              <text class="text-sm text-gray-500">
                {{ formatTime(order.createTime) }}
              </text>
            </view>
            <view
              class="px-2 py-1 rounded-full text-xs font-medium"
              :class="getStatusClass(order.orderStatus)"
            >
              {{ order.orderStatusDesc }}
            </view>
          </view>

          <!-- 订单内容 -->
          <view class="flex items-center space-x-3">
            <image
              :src="order.mainImage || '/static/default-product.png'"
              class="w-16 h-16 rounded-lg object-cover"
              mode="aspectFill"
            />
            <view class="flex-1 min-w-0">
              <text class="text-sm text-gray-600 line-clamp-2 block">
                {{ order.productDesc || '暂无描述' }}
              </text>
              <view class="flex justify-between items-center mt-2">
                <text class="text-sm text-gray-500">
                  期望价格: ¥{{ order.estimatedPrice }}
                </text>
                <text
                  v-if="order.finalPrice"
                  class="text-lg font-semibold text-green-600"
                >
                  ¥{{ order.finalPrice }}
                </text>
              </view>
            </view>
          </view>

          <!-- 操作按钮 -->
          <view v-if="getOrderActions(order.orderStatus).length > 0" class="flex justify-end space-x-2 mt-3 pt-3 border-t border-gray-100">
            <button
              v-for="action in getOrderActions(order.orderStatus)"
              :key="action.key"
              class="px-4 py-2 rounded-lg text-sm font-medium transition-colors"
              :class="getActionClass(action.type)"
              @click.stop="handleOrderAction(order.id, action.key)"
            >
              {{ action.label }}
            </button>
          </view>
        </view>
      </view>

      <!-- 加载更多 -->
      <view v-if="hasMore && orderList.length > 0" class="text-center py-4">
        <button
          class="text-primary-500 text-sm"
          :disabled="loading"
          @click="loadMore"
        >
          {{ loading ? '加载中...' : '加载更多' }}
        </button>
      </view>
    </view>
  </view>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue'
import StatusBarPlaceholder from '@/components/StatusBarPlaceholder.vue'
import NavBar from '@/components/NavBar.vue'
import { recycleApi, recycleUtils } from '@/api/recycle'
import type { RecycleOrder, OrderQueryParams } from '@/api/recycle'

// 类型定义
interface RecycleOrder {
  id: string
  brandName: string
  model: string
  productDesc: string
  orderStatus: string
  orderStatusDesc: string
  estimatedPrice: number
  finalPrice?: number
  mainImage?: string
  createTime: number
}

interface OrderAction {
  key: string
  label: string
  type: 'primary' | 'danger' | 'warning' | 'default'
}

// 响应式数据
const orderList = ref<RecycleOrder[]>([])
const loading = ref(false)
const hasMore = ref(true)
const currentPage = ref(1)
const currentStatus = ref('')

// 方法
const filterByStatus = async (status: string) => {
  currentStatus.value = status
  currentPage.value = 1
  hasMore.value = true
  await loadOrderList(true)
}

const loadOrderList = async (refresh = false) => {
  if (loading.value) return

  loading.value = true

  try {
    const params: OrderQueryParams = {
      page: currentPage.value,
      size: 10,
      orderStatus: currentStatus.value
    }

    const res = await recycleApi.getUserOrders(params)
    const orders = res.data.records || []

    if (refresh) {
      orderList.value = orders
    } else {
      orderList.value = [...orderList.value, ...orders]
    }

    hasMore.value = orders.length === 10
    currentPage.value++

  } catch (error: any) {
    uni.showToast({
      title: error.message || '加载失败',
      icon: 'none'
    })
  } finally {
    loading.value = false
  }
}

const loadMore = () => {
  if (hasMore.value && !loading.value) {
    loadOrderList()
  }
}

const goToCreateOrder = () => {
  uni.navigateTo({
    url: '/pages/views/recycle/create-order'
  })
}

const goToOrderDetail = (orderId: string) => {
  uni.navigateTo({
    url: `/pages/views/recycle/order-detail?id=${orderId}`
  })
}

const getStatusClass = (status: string): string => {
  const statusMap: Record<string, string> = {
    'PENDING_APPROVAL': 'bg-yellow-100 text-yellow-800',
    'PRICE_QUOTED': 'bg-blue-100 text-blue-800',
    'SHIPPING_CONFIRMED': 'bg-purple-100 text-purple-800',
    'RECEIVED': 'bg-indigo-100 text-indigo-800',
    'COMPLETED': 'bg-green-100 text-green-800',
    'CANCELLED': 'bg-gray-100 text-gray-800',
    'RETURNED': 'bg-red-100 text-red-800'
  }
  return statusMap[status] || 'bg-gray-100 text-gray-800'
}

const getOrderActions = (status: string): OrderAction[] => {
  const actionsMap: Record<string, OrderAction[]> = {
    'PENDING_APPROVAL': [
      { key: 'cancel', label: '取消订单', type: 'danger' }
    ],
    'PRICE_QUOTED': [
      { key: 'cancel', label: '取消订单', type: 'danger' },
      { key: 'confirm_shipment', label: '确认寄送', type: 'primary' }
    ],
    'RECEIVED': [
      { key: 'request_return', label: '申请退回', type: 'warning' }
    ]
  }
  return actionsMap[status] || []
}

const getActionClass = (type: string): string => {
  const classMap: Record<string, string> = {
    'primary': 'bg-primary-500 text-white active:bg-primary-600',
    'danger': 'bg-red-500 text-white active:bg-red-600',
    'warning': 'bg-yellow-500 text-white active:bg-yellow-600',
    'default': 'bg-gray-200 text-gray-800 active:bg-gray-300'
  }
  return classMap[type] || classMap.default
}

const handleOrderAction = async (orderId: string, action: string) => {
  switch (action) {
    case 'cancel':
      await cancelOrder(orderId)
      break
    case 'confirm_shipment':
      goToShippingInfo(orderId)
      break
    case 'request_return':
      await requestReturn(orderId)
      break
  }
}

const cancelOrder = async (orderId: string) => {
  try {
    const res = await uni.showModal({
      title: '确认取消',
      content: '确定要取消这个回收订单吗？'
    })

    if (res.confirm) {
      // TODO: 调用取消订单API
      uni.showToast({
        title: '订单已取消',
        icon: 'success'
      })
      await loadOrderList(true)
    }
  } catch (error) {
    uni.showToast({
      title: '取消失败',
      icon: 'none'
    })
  }
}

const goToShippingInfo = (orderId: string) => {
  uni.navigateTo({
    url: `/pages/views/recycle/shipping-info?orderId=${orderId}`
  })
}

const requestReturn = async (orderId: string) => {
  try {
    const res = await uni.showModal({
      title: '申请退回',
      content: '确定要申请退回商品吗？'
    })

    if (res.confirm) {
      // TODO: 调用申请退回API
      uni.showToast({
        title: '申请成功',
        icon: 'success'
      })
      await loadOrderList(true)
    }
  } catch (error) {
    uni.showToast({
      title: '申请失败',
      icon: 'none'
    })
  }
}

const formatTime = (timestamp: number): string => {
  const date = new Date(timestamp)
  const now = new Date()
  const diff = now.getTime() - date.getTime()

  if (diff < 60 * 1000) return '刚刚'
  if (diff < 60 * 60 * 1000) return `${Math.floor(diff / (60 * 1000))}分钟前`
  if (diff < 24 * 60 * 60 * 1000) return `${Math.floor(diff / (60 * 60 * 1000))}小时前`
  if (diff < 7 * 24 * 60 * 60 * 1000) return `${Math.floor(diff / (24 * 60 * 60 * 1000))}天前`

  return date.toLocaleDateString('zh-CN', {
    year: 'numeric',
    month: '2-digit',
    day: '2-digit'
  })
}

// 生命周期
onMounted(() => {
  loadOrderList(true)
})
</script>

<style lang="scss" scoped>
.line-clamp-2 {
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}
</style>
