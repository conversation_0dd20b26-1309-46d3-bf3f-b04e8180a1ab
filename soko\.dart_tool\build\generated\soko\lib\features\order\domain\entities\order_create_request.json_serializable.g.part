// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

_$OrderCreateRequestImpl _$$OrderCreateRequestImplFromJson(
        Map<String, dynamic> json) =>
    _$OrderCreateRequestImpl(
      items: (json['items'] as List<dynamic>)
          .map((e) => OrderCreateItem.fromJson(e as Map<String, dynamic>))
          .toList(),
      addressId: json['addressId'] as String,
      paymentMethod: $enumDecode(_$PaymentMethodEnumMap, json['paymentMethod']),
      couponId: json['couponId'] as String?,
      note: json['note'] as String?,
      deliveryMethod: $enumDecodeNullable(
              _$DeliveryMethodEnumMap, json['deliveryMethod']) ??
          DeliveryMethod.standard,
      invoiceType:
          $enumDecodeNullable(_$InvoiceTypeEnumMap, json['invoiceType']),
      invoiceTitle: json['invoiceTitle'] as String?,
    );

Map<String, dynamic> _$$OrderCreateRequestImplToJson(
        _$OrderCreateRequestImpl instance) =>
    <String, dynamic>{
      'items': instance.items,
      'addressId': instance.addressId,
      'paymentMethod': _$PaymentMethodEnumMap[instance.paymentMethod]!,
      'couponId': instance.couponId,
      'note': instance.note,
      'deliveryMethod': _$DeliveryMethodEnumMap[instance.deliveryMethod]!,
      'invoiceType': _$InvoiceTypeEnumMap[instance.invoiceType],
      'invoiceTitle': instance.invoiceTitle,
    };

const _$PaymentMethodEnumMap = {
  PaymentMethod.alipay: 'alipay',
  PaymentMethod.wechat: 'wechat',
  PaymentMethod.unionpay: 'unionpay',
  PaymentMethod.balance: 'balance',
};

const _$DeliveryMethodEnumMap = {
  DeliveryMethod.standard: 'standard',
  DeliveryMethod.express: 'express',
  DeliveryMethod.pickup: 'pickup',
};

const _$InvoiceTypeEnumMap = {
  InvoiceType.none: 'none',
  InvoiceType.personal: 'personal',
  InvoiceType.company: 'company',
};

_$OrderCreateItemImpl _$$OrderCreateItemImplFromJson(
        Map<String, dynamic> json) =>
    _$OrderCreateItemImpl(
      productId: json['productId'] as String,
      skuId: json['skuId'] as String,
      quantity: (json['quantity'] as num).toInt(),
      price: (json['price'] as num).toDouble(),
      productName: json['productName'] as String,
      skuName: json['skuName'] as String?,
      productImage: json['productImage'] as String?,
    );

Map<String, dynamic> _$$OrderCreateItemImplToJson(
        _$OrderCreateItemImpl instance) =>
    <String, dynamic>{
      'productId': instance.productId,
      'skuId': instance.skuId,
      'quantity': instance.quantity,
      'price': instance.price,
      'productName': instance.productName,
      'skuName': instance.skuName,
      'productImage': instance.productImage,
    };
