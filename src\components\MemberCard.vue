<template>
  <view
    :class="[
      'transition-all duration-300',
      'rounded-3xl p-6 mb-2',
      'shadow-lg',
      color === 'gradient-blue'
        ? (active
            ? 'border-2 border-blue-700 scale-105 bg-gradient-to-r from-indigo-500 to-blue-500 text-white'
            : 'border border-blue-200 bg-gradient-to-r from-indigo-400 to-blue-400 text-white/90 opacity-90')
        : (active
            ? 'border-2 border-black scale-105 bg-white'
            : 'border border-gray-200 bg-white/90 opacity-90'),
      'hover:scale-105 hover:shadow-xl'
    ]"
  >
    <view class="flex items-center mb-3">
      <view class="flex-1">
        <view :class="[
          'text-xl font-bold tracking-wide',
          (color === 'gradient-blue' || color === 'orange')
            ? 'text-transparent bg-clip-text bg-gradient-to-r from-indigo-500 to-blue-500'
            : 'text-gray-900'
        ]">{{ title }}</view>
        <view :class="[
          'text-xs mt-1',
          (color === 'gradient-blue' || color === 'orange')
            ? 'text-transparent bg-clip-text bg-gradient-to-r from-indigo-400 to-blue-400'
            : 'text-gray-400'
        ]">{{ subtitle }}</view>
      </view>
      <view :class="[
        'text-2xl font-bold',
        (color === 'gradient-blue' || color === 'orange')
          ? 'text-transparent bg-clip-text bg-gradient-to-r from-indigo-500 to-blue-500'
          : 'text-gray-900'
      ]">{{ price }}</view>
    </view>
    <view class="mb-3">
      <span :class="[
        'inline-block px-3 py-1 rounded-full text-xs font-semibold bg-blue-100 text-blue-700'
      ]">{{ discount }}</span>
    </view>
    <ul class="mb-5 space-y-2">
      <li v-for="item in benefits" :key="item" :class="['flex items-center text-sm', color === 'gradient-blue' ? 'text-white' : 'text-gray-700']">
        <view class="w-6 h-6 flex items-center justify-center rounded-full mr-2 bg-green-100">
          <span class="text-green-500 text-lg">✔</span>
        </view>
        <span>{{ item }}</span>
      </li>
    </ul>
    <button
      :disabled="buttonText === '当前方案' || disabled"
      :class="[
        'w-full py-2 font-medium transition-all duration-200',
        (buttonText === '当前方案' || buttonText === '立即开通')
          ? 'text-sm rounded-2xl'
          : 'text-base rounded-xl',
        (buttonText === '当前方案' || disabled)
          ? 'bg-gray-100 text-gray-400 cursor-not-allowed border-none'
          : color === 'gradient-blue'
            ? 'bg-white/20 text-white border border-white/30 shadow-md'
            : (buttonText === '立即开通')
              ? 'bg-black text-white'
              : 'bg-gradient-to-r from-primary to-primary-700 text-white shadow-md hover:from-primary-700 hover:to-primary'
      ]"
      @click="handleClick"
    >{{ buttonText }}</button>
  </view>
</template>

<script setup lang="ts">
import { defineProps, defineEmits } from 'vue'

const props = defineProps({
  title: String,
  subtitle: String,
  price: String,
  discount: String,
  benefits: Array as () => string[],
  buttonText: String,
  color: String,
  active: Boolean,
  disabled: Boolean
})
const emit = defineEmits(['open'])

function handleClick() {
  if (props.buttonText === '当前方案') return
  if (!props.disabled) emit('open')
}
</script>

<style scoped>
button:disabled {
  border: none !important;
  outline: none !important;
  box-shadow: none !important;
  background-clip: padding-box !important;
}
</style> 