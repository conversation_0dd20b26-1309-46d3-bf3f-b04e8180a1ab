import 'package:dio/dio.dart';

import 'package:soko/core/network/api_response.dart';
import 'package:soko/core/network/dio_client.dart';

/// 基础API服务类
abstract class BaseApiService {

  BaseApiService() {
    _dioClient = DioClient.instance;
  }
  late final DioClient _dioClient;

  /// 获取Dio实例
  Dio get dio => _dioClient.dio;

  /// GET请求
  Future<ApiResponse<T>> get<T>(
    String path, {
    Map<String, dynamic>? queryParameters,
    Options? options,
    CancelToken? cancelToken,
    T Function(dynamic)? fromJson,
  }) async {
    try {
      final response = await _dioClient.get<Map<String, dynamic>>(
        path,
        queryParameters: queryParameters,
        options: options,
        cancelToken: cancelToken,
      );

      return _handleResponse<T>(response, from<PERSON>son);
    } catch (e) {
      rethrow;
    }
  }

  /// POST请求
  Future<ApiResponse<T>> post<T>(
    String path, {
    dynamic data,
    Map<String, dynamic>? queryParameters,
    Options? options,
    CancelToken? cancelToken,
    T Function(dynamic)? fromJson,
  }) async {
    try {
      final response = await _dioClient.post<Map<String, dynamic>>(
        path,
        data: data,
        queryParameters: queryParameters,
        options: options,
        cancelToken: cancelToken,
      );

      return _handleResponse<T>(response, fromJson);
    } catch (e) {
      rethrow;
    }
  }

  /// PUT请求
  Future<ApiResponse<T>> put<T>(
    String path, {
    dynamic data,
    Map<String, dynamic>? queryParameters,
    Options? options,
    CancelToken? cancelToken,
    T Function(dynamic)? fromJson,
  }) async {
    try {
      final response = await _dioClient.put<Map<String, dynamic>>(
        path,
        data: data,
        queryParameters: queryParameters,
        options: options,
        cancelToken: cancelToken,
      );

      return _handleResponse<T>(response, fromJson);
    } catch (e) {
      rethrow;
    }
  }

  /// DELETE请求
  Future<ApiResponse<T>> delete<T>(
    String path, {
    dynamic data,
    Map<String, dynamic>? queryParameters,
    Options? options,
    CancelToken? cancelToken,
    T Function(dynamic)? fromJson,
  }) async {
    try {
      final response = await _dioClient.delete<Map<String, dynamic>>(
        path,
        data: data,
        queryParameters: queryParameters,
        options: options,
        cancelToken: cancelToken,
      );

      return _handleResponse<T>(response, fromJson);
    } catch (e) {
      rethrow;
    }
  }

  /// 上传文件
  Future<ApiResponse<T>> upload<T>(
    String path,
    FormData formData, {
    Map<String, dynamic>? queryParameters,
    Options? options,
    CancelToken? cancelToken,
    ProgressCallback? onSendProgress,
    T Function(dynamic)? fromJson,
  }) async {
    try {
      final response = await _dioClient.upload<Map<String, dynamic>>(
        path,
        formData,
        queryParameters: queryParameters,
        options: options,
        cancelToken: cancelToken,
        onSendProgress: onSendProgress,
      );

      return _handleResponse<T>(response, fromJson);
    } catch (e) {
      rethrow;
    }
  }

  /// 下载文件
  Future<Response> download(
    String urlPath,
    String savePath, {
    ProgressCallback? onReceiveProgress,
    Map<String, dynamic>? queryParameters,
    CancelToken? cancelToken,
    bool deleteOnError = true,
    String lengthHeader = Headers.contentLengthHeader,
    Options? options,
  }) async {
    try {
      return await _dioClient.download(
        urlPath,
        savePath,
        onReceiveProgress: onReceiveProgress,
        queryParameters: queryParameters,
        cancelToken: cancelToken,
        deleteOnError: deleteOnError,
        lengthHeader: lengthHeader,
        options: options,
      );
    } catch (e) {
      rethrow;
    }
  }

  /// 处理响应数据
  ApiResponse<T> _handleResponse<T>(
    Response<Map<String, dynamic>> response,
    T Function(dynamic)? fromJson,
  ) {
    final data = response.data;
    if (data == null) {
      throw Exception('Response data is null');
    }

    // 检查响应格式
    if (data.containsKey('code') && data.containsKey('message')) {
      // 标准API响应格式
      final code = data['code'] as int;
      final message = data['message'] as String;
      final responseData = data['data'];

      T? parsedData;
      if (responseData != null && fromJson != null) {
        parsedData = fromJson(responseData);
      } else {
        parsedData = responseData as T?;
      }

      return ApiResponse<T>(
        code: code,
        message: message,
        data: parsedData,
        timestamp: data['timestamp'] as int?,
      );
    } else {
      // 直接返回数据的格式
      T? parsedData;
      if (fromJson != null) {
        parsedData = fromJson(data);
      } else {
        parsedData = data as T?;
      }

      return ApiResponse<T>(
        code: 200,
        message: 'Success',
        data: parsedData,
      );
    }
  }

  /// 处理分页响应
  PageResponse<T> handlePageResponse<T>(
    Map<String, dynamic> data,
    T Function(Map<String, dynamic>) fromJson,
  ) {
    final records = data['records'] as List<dynamic>? ?? [];
    final total = data['total'] as int? ?? 0;
    final size = data['size'] as int? ?? 0;
    final current = data['current'] as int? ?? 1;
    final pages = data['pages'] as int? ?? 0;

    final list = records
        .cast<Map<String, dynamic>>()
        .map((item) => fromJson(item))
        .toList();

    return PageResponse<T>(
      list: list,
      total: total,
      page: current,
      pageSize: size,
      totalPages: pages,
      hasNext: current < pages,
      hasPrev: current > 1,
    );
  }

  /// 构建查询参数
  Map<String, dynamic> buildQueryParams(Map<String, dynamic> params) {
    final result = <String, dynamic>{};
    
    for (final entry in params.entries) {
      if (entry.value != null) {
        result[entry.key] = entry.value;
      }
    }
    
    return result;
  }
}
