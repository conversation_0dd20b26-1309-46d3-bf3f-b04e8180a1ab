import 'package:flutter_riverpod/flutter_riverpod.dart';

import 'package:soko/features/product/domain/entities/product.dart';
import 'package:soko/features/product/data/datasources/product_api_service.dart';

/// 商品比较状态
class ProductCompareState {
  const ProductCompareState({
    required this.products,
    this.isLoading = false,
    this.error,
  });

  final List<Product> products;
  final bool isLoading;
  final String? error;

  ProductCompareState copyWith({
    List<Product>? products,
    bool? isLoading,
    String? error,
  }) {
    return ProductCompareState(
      products: products ?? this.products,
      isLoading: isLoading ?? this.isLoading,
      error: error,
    );
  }
}

/// 商品比较状态管理
class ProductCompareNotifier extends StateNotifier<AsyncValue<ProductCompareState>> {
  ProductCompareNotifier(this._apiService) 
      : super(const AsyncValue.data(ProductCompareState(products: [])));

  final ProductApiService _apiService;
  static const int maxCompareCount = 4;

  /// 添加商品到比较列表
  Future<void> addProduct(String productId) async {
    final currentState = state.value;
    if (currentState == null) return;

    // 检查是否已存在
    if (currentState.products.any((p) => p.id == productId)) {
      return;
    }

    // 检查数量限制
    if (currentState.products.length >= maxCompareCount) {
      state = AsyncValue.error('最多只能比较$maxCompareCount个商品', StackTrace.current);
      return;
    }

    try {
      state = AsyncValue.data(currentState.copyWith(isLoading: true));
      
      final product = await _apiService.getProductDetail(productId);
      final updatedProducts = [...currentState.products, product];
      
      state = AsyncValue.data(ProductCompareState(products: updatedProducts));
    } catch (error, stackTrace) {
      state = AsyncValue.error(error, stackTrace);
    }
  }

  /// 从比较列表中移除商品
  void removeProduct(String productId) {
    final currentState = state.value;
    if (currentState == null) return;

    final updatedProducts = currentState.products
        .where((p) => p.id != productId)
        .toList();
    
    state = AsyncValue.data(ProductCompareState(products: updatedProducts));
  }

  /// 清空所有商品
  void clearAll() {
    state = const AsyncValue.data(ProductCompareState(products: []));
  }

  /// 刷新商品数据
  Future<void> refresh() async {
    final currentState = state.value;
    if (currentState == null || currentState.products.isEmpty) return;

    try {
      state = AsyncValue.data(currentState.copyWith(isLoading: true));
      
      final productIds = currentState.products.map((p) => p.id).toList();
      final updatedProducts = <Product>[];
      
      for (final productId in productIds) {
        try {
          final product = await _apiService.getProductDetail(productId);
          updatedProducts.add(product);
        } catch (e) {
          // 如果某个商品获取失败，跳过它
          continue;
        }
      }
      
      state = AsyncValue.data(ProductCompareState(products: updatedProducts));
    } catch (error, stackTrace) {
      state = AsyncValue.error(error, stackTrace);
    }
  }

  /// 获取比较结果
  Map<String, List<String>> getComparisonData() {
    final currentState = state.value;
    if (currentState == null || currentState.products.isEmpty) {
      return {};
    }

    final products = currentState.products;
    final comparisonData = <String, List<String>>{};

    // 基本信息
    comparisonData['商品名称'] = products.map((p) => p.name).toList();
    comparisonData['品牌'] = products.map((p) => p.brand).toList();
    comparisonData['价格'] = products.map((p) => '¥${p.price.toStringAsFixed(0)}').toList();
    comparisonData['状况'] = products.map((p) => p.condition).toList();
    comparisonData['位置'] = products.map((p) => p.location).toList();

    // 规格参数（如果有的话）
    final allSpecKeys = <String>{};
    for (final product in products) {
      if (product.specifications != null) {
        allSpecKeys.addAll(product.specifications!.keys);
      }
    }

    for (final specKey in allSpecKeys) {
      comparisonData[specKey] = products.map((p) {
        return p.specifications?[specKey] ?? '-';
      }).toList();
    }

    return comparisonData;
  }

  /// 获取价格比较结果
  Map<String, dynamic> getPriceComparison() {
    final currentState = state.value;
    if (currentState == null || currentState.products.isEmpty) {
      return {};
    }

    final prices = currentState.products.map((p) => p.price).toList();
    prices.sort();

    return {
      'lowest': prices.first,
      'highest': prices.last,
      'average': prices.reduce((a, b) => a + b) / prices.length,
      'difference': prices.last - prices.first,
    };
  }
}

/// 商品比较Provider
final productCompareProvider = StateNotifierProvider<ProductCompareNotifier, AsyncValue<ProductCompareState>>((ref) {
  final apiService = ref.watch(productApiServiceProvider);
  return ProductCompareNotifier(apiService);
});

/// 比较数据Provider
final comparisonDataProvider = Provider<Map<String, List<String>>>((ref) {
  final compareNotifier = ref.read(productCompareProvider.notifier);
  return compareNotifier.getComparisonData();
});

/// 价格比较Provider
final priceComparisonProvider = Provider<Map<String, dynamic>>((ref) {
  final compareNotifier = ref.read(productCompareProvider.notifier);
  return compareNotifier.getPriceComparison();
});
