import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';

import 'package:soko/core/utils/validator_utils.dart';
import 'package:soko/shared/presentation/widgets/custom_button.dart';
import 'package:soko/features/auth/presentation/providers/auth_provider.dart';

/// 短信验证码按钮组件
class SmsCodeButton extends ConsumerWidget {

  const SmsCodeButton({
    super.key,
    required this.phone,
    required this.type,
    this.onSuccess,
    this.onError,
  });
  final String phone;
  final String type;
  final VoidCallback? onSuccess;
  final VoidCallback? onError;

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final smsState = ref.watch(smsCodeProvider);
    final countdown = ref.watch(smsCountdownProvider);
    final isLoading = smsState.isLoading;
    final canSend = countdown == 0 && !isLoading;

    return OutlineButton(
      text: _getButtonText(countdown),
      size: ButtonSize.small,
      isLoading: isLoading,
      isDisabled: !canSend,
      onPressed: canSend ? () => _sendSmsCode(context, ref) : null,
    );
  }

  String _getButtonText(int countdown) {
    if (countdown > 0) {
      return '${countdown}s';
    }
    return '获取验证码';
  }

  void _sendSmsCode(BuildContext context, WidgetRef ref) {
    // 验证手机号
    if (phone.isEmpty || !ValidatorUtils.isValidPhone(phone)) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(content: Text('请输入正确的手机号')),
      );
      return;
    }

    // 发送验证码
    ref.read(smsCodeProvider.notifier).sendSmsCode(
      phone: phone,
      type: type,
    );

    // 监听发送结果
    ref.listen(smsCodeProvider, (previous, next) {
      next.when(
        idle: () {},
        loading: () {},
        success: (_) {
          // 发送成功，开始倒计时
          ref.read(smsCountdownProvider.notifier).startCountdown();
          onSuccess?.call();
          
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(content: Text('验证码已发送')),
          );
        },
        error: (message) {
          // 发送失败
          onError?.call();
          
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(content: Text(message)),
          );
        },
      );
    });
  }
}
