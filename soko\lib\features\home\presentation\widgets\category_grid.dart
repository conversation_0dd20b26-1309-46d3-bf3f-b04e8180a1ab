import 'package:flutter/material.dart';

import 'package:soko/shared/presentation/widgets/custom_image.dart';
import 'package:soko/features/product/data/datasources/product_api_service.dart';

/// 分类网格组件
class CategoryGrid extends StatelessWidget {

  const CategoryGrid({
    super.key,
    required this.categories,
    this.maxCount = 8,
    this.itemHeight = 80,
    this.padding,
    this.onCategoryTap,
  });
  final List<CategoryNode> categories;
  final int maxCount;
  final double itemHeight;
  final EdgeInsetsGeometry? padding;
  final Function(CategoryNode category)? onCategoryTap;

  @override
  Widget build(BuildContext context) {
    if (categories.isEmpty) {
      return const SizedBox.shrink();
    }

    // 限制显示数量，最后一个显示"更多"
    final displayCategories = categories.take(maxCount - 1).toList();
    final hasMore = categories.length > maxCount - 1;

    return Container(
      color: Colors.white,
      padding: padding ?? const EdgeInsets.symmetric(vertical: 20),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          const Padding(
            padding: EdgeInsets.symmetric(horizontal: 16),
            child: Text(
              'ACG分类',
              style: TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.w600,
              ),
            ),
          ),
          const SizedBox(height: 16),
          SizedBox(
            height: itemHeight,
            child: ListView.builder(
              scrollDirection: Axis.horizontal,
              padding: const EdgeInsets.symmetric(horizontal: 16),
              itemCount: displayCategories.length + (hasMore ? 1 : 0),
              itemBuilder: (context, index) {
                if (index == displayCategories.length && hasMore) {
                  // 更多按钮
                  return _buildMoreItem();
                }
                
                final category = displayCategories[index];
                return _buildCategoryItem(category);
              },
            ),
          ),
        ],
      ),
    );
  }

  /// 构建分类项
  Widget _buildCategoryItem(CategoryNode category) {
    return GestureDetector(
      onTap: () => onCategoryTap?.call(category),
      child: Container(
        width: 60,
        margin: const EdgeInsets.only(right: 16),
        child: Column(
          children: [
            Container(
              width: 48,
              height: 48,
              decoration: BoxDecoration(
                color: Colors.blue.withOpacity(0.1),
                borderRadius: BorderRadius.circular(24),
              ),
              child: category.icon != null
                  ? CustomImage(
                      imageUrl: category.icon,
                      width: 24,
                      height: 24,
                      fit: BoxFit.contain,
                    )
                  : const Icon(
                      Icons.category,
                      color: Colors.blue,
                      size: 24,
                    ),
            ),
            const SizedBox(height: 8),
            Text(
              category.name,
              style: const TextStyle(
                fontSize: 12,
                fontWeight: FontWeight.w500,
              ),
              textAlign: TextAlign.center,
              maxLines: 1,
              overflow: TextOverflow.ellipsis,
            ),
          ],
        ),
      ),
    );
  }

  /// 构建更多按钮
  Widget _buildMoreItem() {
    return GestureDetector(
      onTap: () {
        // 跳转到分类页面
      },
      child: Container(
        width: 60,
        margin: const EdgeInsets.only(right: 16),
        child: Column(
          children: [
            Container(
              width: 48,
              height: 48,
              decoration: BoxDecoration(
                color: Colors.grey.withOpacity(0.1),
                borderRadius: BorderRadius.circular(24),
              ),
              child: const Icon(
                Icons.more_horiz,
                color: Colors.grey,
                size: 24,
              ),
            ),
            const SizedBox(height: 8),
            const Text(
              '更多',
              style: TextStyle(
                fontSize: 12,
                fontWeight: FontWeight.w500,
                color: Colors.grey,
              ),
              textAlign: TextAlign.center,
            ),
          ],
        ),
      ),
    );
  }
}

/// 网格布局的分类组件
class CategoryGridView extends StatelessWidget {

  const CategoryGridView({
    super.key,
    required this.categories,
    this.crossAxisCount = 4,
    this.childAspectRatio = 1.0,
    this.padding,
    this.onCategoryTap,
  });
  final List<CategoryNode> categories;
  final int crossAxisCount;
  final double childAspectRatio;
  final EdgeInsetsGeometry? padding;
  final Function(CategoryNode category)? onCategoryTap;

  @override
  Widget build(BuildContext context) {
    if (categories.isEmpty) {
      return const SizedBox.shrink();
    }

    return Container(
      color: Colors.white,
      padding: padding ?? const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          const Text(
            'ACG分类',
            style: TextStyle(
              fontSize: 18,
              fontWeight: FontWeight.w600,
            ),
          ),
          const SizedBox(height: 16),
          GridView.builder(
            shrinkWrap: true,
            physics: const NeverScrollableScrollPhysics(),
            gridDelegate: SliverGridDelegateWithFixedCrossAxisCount(
              crossAxisCount: crossAxisCount,
              crossAxisSpacing: 12,
              mainAxisSpacing: 12,
              childAspectRatio: childAspectRatio,
            ),
            itemCount: categories.length,
            itemBuilder: (context, index) {
              final category = categories[index];
              return _buildGridCategoryItem(category);
            },
          ),
        ],
      ),
    );
  }

  /// 构建网格分类项
  Widget _buildGridCategoryItem(CategoryNode category) {
    return GestureDetector(
      onTap: () => onCategoryTap?.call(category),
      child: Container(
        decoration: BoxDecoration(
          color: Colors.grey[50],
          borderRadius: BorderRadius.circular(8),
          border: Border.all(
            color: Colors.grey[200]!,
          ),
        ),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Container(
              width: 40,
              height: 40,
              decoration: BoxDecoration(
                color: Colors.blue.withOpacity(0.1),
                borderRadius: BorderRadius.circular(20),
              ),
              child: category.icon != null
                  ? CustomImage(
                      imageUrl: category.icon,
                      width: 20,
                      height: 20,
                      fit: BoxFit.contain,
                    )
                  : const Icon(
                      Icons.category,
                      color: Colors.blue,
                      size: 20,
                    ),
            ),
            const SizedBox(height: 8),
            Text(
              category.name,
              style: const TextStyle(
                fontSize: 12,
                fontWeight: FontWeight.w500,
              ),
              textAlign: TextAlign.center,
              maxLines: 1,
              overflow: TextOverflow.ellipsis,
            ),
          ],
        ),
      ),
    );
  }
}
