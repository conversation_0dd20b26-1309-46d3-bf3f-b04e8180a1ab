import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

import 'package:soko/shared/presentation/widgets/custom_card.dart';
import 'package:soko/features/order/domain/entities/order.dart';

/// 订单物流跟踪卡片
class OrderDetailTrackingCard extends StatelessWidget {

  const OrderDetailTrackingCard({
    super.key,
    required this.order,
  });
  final Order order;

  @override
  Widget build(BuildContext context) {
    return CustomCard(
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // 标题
          Row(
            children: [
              Icon(
                Icons.local_shipping,
                color: Colors.green,
                size: 20.w,
              ),
              SizedBox(width: 8.w),
              Text(
                '物流信息',
                style: TextStyle(
                  fontSize: 16.sp,
                  fontWeight: FontWeight.w600,
                ),
              ),
            ],
          ),
          SizedBox(height: 16.h),

          // 物流公司和单号
          if (order.trackingCompany?.isNotEmpty == true ||
              order.trackingNo?.isNotEmpty == true) ...[
            Row(
              children: [
                if (order.trackingCompany?.isNotEmpty == true) ...[
                  Text(
                    '物流公司：',
                    style: TextStyle(
                      fontSize: 14.sp,
                      color: Colors.grey[600],
                    ),
                  ),
                  Text(
                    order.trackingCompany!,
                    style: TextStyle(
                      fontSize: 14.sp,
                      color: Colors.black87,
                      fontWeight: FontWeight.w500,
                    ),
                  ),
                ],
              ],
            ),
            SizedBox(height: 8.h),

            if (order.trackingNo?.isNotEmpty == true) ...[
              Row(
                children: [
                  Text(
                    '快递单号：',
                    style: TextStyle(
                      fontSize: 14.sp,
                      color: Colors.grey[600],
                    ),
                  ),
                  Expanded(
                    child: Text(
                      order.trackingNo!,
                      style: TextStyle(
                        fontSize: 14.sp,
                        color: Colors.black87,
                        fontWeight: FontWeight.w500,
                      ),
                    ),
                  ),
                  // 复制按钮
                  InkWell(
                    onTap: () => _copyTrackingNo(context),
                    child: Padding(
                      padding: EdgeInsets.all(4.w),
                      child: Icon(
                        Icons.copy,
                        size: 16.w,
                        color: Colors.blue,
                      ),
                    ),
                  ),
                ],
              ),
              SizedBox(height: 16.h),
            ],

            // 查看物流详情按钮
            SizedBox(
              width: double.infinity,
              child: OutlinedButton.icon(
                onPressed: () => _viewTrackingDetails(context),
                icon: Icon(Icons.track_changes, size: 16.w),
                label: const Text('查看物流详情'),
                style: OutlinedButton.styleFrom(
                  padding: EdgeInsets.symmetric(vertical: 12.h),
                ),
              ),
            ),
          ] else ...[
            // 暂无物流信息
            Container(
              width: double.infinity,
              padding: EdgeInsets.symmetric(vertical: 24.h),
              child: Column(
                children: [
                  Icon(
                    Icons.local_shipping_outlined,
                    size: 48.w,
                    color: Colors.grey[400],
                  ),
                  SizedBox(height: 8.h),
                  Text(
                    '暂无物流信息',
                    style: TextStyle(
                      fontSize: 14.sp,
                      color: Colors.grey[600],
                    ),
                  ),
                ],
              ),
            ),
          ],
        ],
      ),
    );
  }

  /// 复制快递单号
  void _copyTrackingNo(BuildContext context) {
    if (order.trackingNo?.isNotEmpty == true) {
      // TODO: 实现复制到剪贴板功能
      // Clipboard.setData(ClipboardData(text: order.trackingNo!));
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(content: Text('快递单号已复制到剪贴板')),
      );
    }
  }

  /// 查看物流详情
  void _viewTrackingDetails(BuildContext context) {
    // TODO: 实现查看物流详情功能
    // 可以跳转到物流详情页面或显示物流详情弹窗
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('物流详情'),
        content: const Text('物流详情功能正在开发中...'),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('确定'),
          ),
        ],
      ),
    );
  }
}
