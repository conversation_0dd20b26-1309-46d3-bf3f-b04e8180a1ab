import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';

import 'package:soko/shared/presentation/widgets/custom_app_bar.dart';

/// 个人中心页面
class ProfilePage extends ConsumerWidget {
  const ProfilePage({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    return const Scaffold(
      appBar: CustomAppBar(
        title: '我的',
        showBackButton: false,
      ),
      body: Center(
        child: Text('个人中心页面 - 待实现'),
      ),
    );
  }
}
