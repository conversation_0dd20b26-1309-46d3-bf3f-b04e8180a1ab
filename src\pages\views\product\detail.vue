<template>
  <view
    class="flex flex-col items-center w-full min-h-screen bg-slate-50 px-0 pb-safe box-border"
  >
    <NavBar title="商品详情" bgColor="#FFFFFF" :showBack="false">
      <!-- 自定义左侧内容 -->
      <template #left>
        <view class="rounded-full p-2" @tap="goBack">
          <i class="fas fa-arrow-left text-text-primary"></i>
        </view>
      </template>
      <template #right>
        <view
          class="w-10 h-10 rounded-full flex items-center justify-center cursor-pointer"
          @click="shareProduct"
        >
          <text class="fas fa-share-alt text-gray-600"></text>
        </view>
      </template>
    </NavBar>

    <!-- 主要内容区域 - 可滚动 -->
    <scroll-view
      scroll-y
      class="flex-1 flex flex-col pb-20"
      :scroll-top="scrollTop"
      id="detailScrollView"
    >
      <!-- 商品图片区域 -->
      <view class="relative w-full bg-white">
        <swiper
          class="w-full"
          style="height: 600rpx"
          :indicator-dots="true"
          :current="currentImage"
          indicator-active-color="#ff5000"
          :autoplay="false"
          @change="handleSwiperChange"
        >
          <swiper-item v-for="(image, index) in productImages" :key="index">
            <CacheImgs :src="image" mode="aspectFit" class="w-full h-full" />
          </swiper-item>
        </swiper>
      </view>

      <!-- 缩略图选择器 - 独立于商品图片区域 -->
      <view class="bg-white pb-4 pt-3">
        <view class="flex px-5 gap-3">
          <view
            v-for="(image, index) in productImages"
            :key="index"
            class="relative w-16 h-16 cursor-pointer rounded-full overflow-hidden"
            @tap="swiperTap(index)"
          >
            <view
              :class="[
                'absolute inset-0 rounded-full',
                currentImage === index ? 'bg-primary' : 'bg-gray-400'
              ]"
            ></view>
            <view class="absolute inset-px rounded-full overflow-hidden">
              <CacheImgs :src="image" mode="aspectFill" class="w-full h-full" />
            </view>
          </view>
        </view>
      </view>

      <!-- 价格及基本信息区域 -->
      <view class="bg-white px-5 py-4 mt-2">
        <view class="flex items-baseline">
          <text class="text-xl font-bold text-primary tracking-wide">
            <text class="text-sm">¥</text>
            {{ formatPrice(currentSku ? currentSku.price : 0) }}
          </text>
          <view
            v-if="currentSku && currentSku.originalPrice > currentSku.price"
            class="flex items-baseline"
          >
            <text class="mx-2 text-gray-400">|</text>
            <text class="text-xs text-gray-500">优惠前</text>
            <text class="ml-1 text-xs text-gray-400 line-through">
              <text class="text-2xs">¥</text>
              {{ formatPrice(currentSku.originalPrice) }}
            </text>
            <text
              class="ml-2 bg-green-500 text-white rounded-full px-2 text-xs tracking-wide"
            >
              {{
                calculateDiscount(currentSku.price, currentSku.originalPrice)
              }}
            </text>
          </view>
        </view>

        <text class="text-lg font-bold mt-2 tracking-wide">{{
          product.name
        }}</text>

        <!-- 商品信息标签 -->
        <view class="flex flex-wrap gap-2 mt-2">
          <view
            :class="[
              'rounded-full px-2 py-0.5 flex items-center border',
              isPreSale
                ? 'bg-amber-50 border-amber-300 text-amber-600'
                : 'bg-green-50 border-green-300 text-green-600'
            ]"
          >
            <text
              :class="[
                'fas mr-1 text-2xs',
                isPreSale ? 'fa-clock' : 'fa-check-circle'
              ]"
            ></text>
            <text class="text-xs font-medium">{{
              isPreSale ? "预定" : "现货"
            }}</text>
          </view>
          <view
            class="bg-gray-100 rounded-full px-2 py-0.5 flex items-center border border-gray-200"
            v-if="product.brand"
          >
            <text class="text-xs text-gray-600">{{ product.brand }}</text>
          </view>
          <view
            class="bg-gray-100 rounded-full px-2 py-0.5 flex items-center border border-gray-200"
            v-if="product.acg"
          >
            <text class="text-xs text-gray-600">{{ product.acg }}</text>
          </view>
          <view
            class="bg-gray-100 rounded-full px-2 py-0.5 flex items-center border border-gray-200"
            v-if="product.size"
          >
            <text class="text-xs text-gray-600">{{ product.size }}</text>
          </view>
          <view
            class="bg-gray-100 rounded-full px-2 py-0.5 flex items-center border border-gray-200"
            v-if="product.type"
          >
            <text class="text-xs text-gray-600">{{ product.type }}</text>
          </view>
        </view>

        <view class="flex items-center justify-between mt-3">
          <view class="flex items-center">
            <text class="text-xs text-gray-500">已售 {{ totalSales }}</text>
          </view>
        </view>
      </view>

      <!-- 商品详情标签页 -->
      <view class="bg-white mt-2 pb-4" id="detailSection">
        <view class="flex border-b">
          <view
            v-for="(tab, index) in tabs"
            :key="index"
            :class="[
              'flex-1 h-12 flex items-center justify-center relative',
              currentTab === index
                ? 'text-primary font-medium'
                : 'text-gray-500'
            ]"
            @click="handleTabChange(index)"
          >
            <text class="text-sm">{{ tab.name }}</text>
            <view
              v-if="currentTab === index"
              class="absolute bottom-0 left-0 right-0 h-0.5 bg-primary"
            ></view>
          </view>
        </view>

        <!-- 商品详情内容 -->
        <view v-if="currentTab === 0" class="px-5 pt-4">
          <view class="rich-text">
            <p
              v-if="product.description"
              class="rich-text-content"
              v-html="parseRichText(product.description)"
            ></p>

            <view
              v-else
              class="mt-4 aspect-video w-full bg-slate-100 rounded-lg overflow-hidden relative"
            >
              <view class="absolute inset-0 flex items-center justify-center">
                <text class="text-gray-500">暂无商品详情</text>
              </view>
            </view>
          </view>
        </view>

        <!-- 规格参数内容 -->
        <view v-if="currentTab === 1" class="px-5 pt-4">
          <view
            class="rounded-xl overflow-hidden border-2 border-gray-500 shadow"
          >
            <view
              v-for="(spec, index) in specs"
              :key="index"
              :class="[
                'flex',
                index % 2 === 0 ? 'bg-slate-50' : 'bg-white',
                index !== specs.length - 1 ? 'border-b border-gray-200' : ''
              ]"
            >
              <view class="py-3 px-4 w-1/3 text-sm">{{ spec.name }}</view>
              <view class="py-3 px-4 w-2/3 text-sm">{{ spec.value }}</view>
            </view>
          </view>
        </view>

        <!-- 服务保障内容 -->
        <view v-if="currentTab === 2" class="px-5 pt-4">
          <view class="space-y-3">
            <view class="flex items-start bg-slate-50 p-4 rounded-xl">
              <view class="text-primary mr-3 mt-0.5">
                <text class="fas fa-undo text-lg"></text>
              </view>
              <view>
                <text class="font-medium">99天无理由退货</text>
                <text class="text-sm text-gray-500 mt-1 block"
                  >确认收货后99天内，如您对商品不满意，可享受无理由退货服务（商品需保持原始状态，保证关节硬件无损）。</text
                >
              </view>
            </view>

            <view class="flex items-start bg-slate-50 p-4 rounded-xl">
              <view class="text-primary mr-3 mt-0.5">
                <text class="fas fa-shield-alt text-lg"></text>
              </view>
              <view>
                <text class="font-medium">品质保证</text>
                <text class="text-sm text-gray-500 mt-1 block"
                  >所有商品均为正品。我们的专业团队会对每件商品进行严格检查，确保品质无忧。</text
                >
              </view>
            </view>

            <view class="flex items-start bg-slate-50 p-4 rounded-xl">
              <view class="text-primary mr-3 mt-0.5">
                <text class="fas fa-box text-lg"></text>
              </view>
              <view>
                <text class="font-medium">精美包装</text>
                <text class="text-sm text-gray-500 mt-1 block"
                  >采用防震气泡材料和定制包装盒，确保商品在运输过程中完好无损，为您的收藏品提供最佳保护。</text
                >
              </view>
            </view>
          </view>
        </view>
      </view>
    </scroll-view>

    <!-- 底部购买栏 - 固定在底部 -->
    <view
      class="fixed bottom-0 left-0 right-0 bg-white p-3 pr-4 flex items-center justify-between z-10"
      style="border-top: 1px solid #d1d5db"
    >
      <view class="w-16 h-12 rounded-lg flex items-center justify-center">
        <image src="/static/logo-pink.png" mode="aspectFit" class="w-14 h-14" />
      </view>
      <view class="flex gap-4 ml-2">
        <!-- 缺货状态 -->
        <template v-if="isOutOfStock">
          <view class="w-64 flex items-center justify-center">
            <text class="text-destructive font-medium">商品已缺货</text>
          </view>
        </template>
        <template v-else>
          <!-- 非预售商品显示加入购物车按钮 -->
          <button
            v-if="!isPreSale"
            class="w-28 rounded-full h-9 flex items-center justify-center cart-btn"
            @click="openSpecsPopup('cart')"
          >
            <text class="cart-btn-text">加入购物车</text>
          </button>
          <!-- 预售商品显示预售标签 -->
          <view v-else class="w-28 flex flex-col items-center justify-center">
            <text class="text-xs text-primary font-medium">预定商品</text>
            <text class="text-xs text-gray-500">{{ preSaleInfo }}</text>
          </view>
          <!-- 还原后的立即购买按钮 -->
          <button
            class="w-28 rounded-full bg-gradient-to-r from-primary to-pink-400 text-white h-9 flex items-center justify-center"
            @click="openSpecsPopup('buy')"
          >
            <text class="fas fa-bolt mr-2 text-xs"></text>
            <text style="font-size: 12px; line-height: 1; font-weight: 500">{{
              isPreSale ? "预定购买" : "立即购买"
            }}</text>
          </button>
        </template>
      </view>
    </view>

    <!-- 规格选择弹出层 -->
    <up-popup
      :show="specsPopupVisible"
      mode="bottom"
      @close="closeSpecsPopup"
      round="16"
      safe-area-inset-bottom
    >
      <view class="specs-popup">
        <!-- 弹窗头部 -->
        <view class="p-3 border-b flex items-center justify-between relative">
          <view class="w-7"></view>
          <view class="flex items-center">
            <text class="fas fa-undo text-primary text-xs mr-1.5"></text>
            <text class="text-sm text-gray-700 font-medium"
              >99天无理由退货</text
            >
          </view>
          <view
            class="h-7 w-7 rounded-full bg-gray-100 flex items-center justify-center close-btn active:scale-95 transition-transform"
            @click="closeSpecsPopup"
          >
            <text class="fas fa-times text-gray-500 text-xs"></text>
          </view>
        </view>

        <!-- 分割线 -->
        <view class="border-b-2 border-gray-400 my-2 mx-4"></view>

        <!-- 商品信息预览 -->
        <view class="py-3 px-4 flex">
          <view
            class="relative w-20 h-20 rounded-lg overflow-hidden border bg-slate-50"
          >
            <image
              :src="mainImage"
              mode="aspectFill"
              class="w-full h-full"
            ></image>
          </view>
          <view class="ml-3 flex-1 flex flex-col justify-start">
            <view class="flex items-baseline">
              <view v-if="isAllSpecsSelected">
                <text class="text-xl font-bold text-primary"
                  ><text class="text-sm">¥</text
                  >{{ formatPrice(getCurrentPrice()) }}</text
                >
                <view
                  v-if="
                    currentSku && currentSku.originalPrice > currentSku.price
                  "
                  class="flex items-baseline"
                >
                  <text class="mx-2 text-gray-400">|</text>
                  <text class="text-xs text-gray-500">优惠前</text>
                  <text class="ml-1 text-xs text-gray-400 line-through"
                    ><text class="text-2xs">¥</text
                    >{{ formatPrice(currentSku.originalPrice) }}</text
                  >
                </view>
              </view>
              <view v-else>
                <text class="text-xl font-bold text-primary"
                  ><text class="text-sm">¥</text
                  >{{ formatPrice(getMinPrice) }}</text
                >
                <text class="text-xs text-gray-500 ml-1">起</text>
              </view>
            </view>

            <!-- 数量选择器 -->
            <view class="flex items-center mt-auto">
              <view class="flex items-center">
                <button
                  class="h-6 w-6 rounded-md border border-gray-200 flex items-center justify-center"
                  @click="decrementQuantity"
                  :disabled="quantity <= 1"
                  :style="quantity <= 1 ? 'opacity:0.5;' : ''"
                >
                  <text class="fas fa-minus" style="font-size: 10px"></text>
                </button>
                <view
                  class="mx-1 w-10 h-6 border border-gray-200 rounded-md flex items-center justify-center"
                >
                  <input
                    type="number"
                    v-model.number="quantity"
                    class="w-full h-full text-center"
                    style="font-size: 12px"
                    :max="getInputMax"
                    :min="1"
                    @input="onQuantityInput"
                  />
                </view>
                <button
                  class="h-6 w-6 rounded-md border border-gray-200 flex items-center justify-center"
                  @click="incrementQuantity"
                  :disabled="getInputMin"
                  :style="getInputMin ? 'opacity:0.5;' : ''"
                >
                  <text class="fas fa-plus" style="font-size: 10px"></text>
                </button>
              </view>
            </view>
          </view>
        </view>

        <!-- 规格内容区 -->
        <view class="specs-content px-4 pt-1 pb-3">
          <view class="pb-2 mb-2" v-if="selectedSpecs.length > 0">
            <view
              class="py-1.5 px-3 bg-slate-50 rounded-lg flex items-center justify-between"
            >
              <text class="text-xs text-gray-400">已选</text>
              <text class="text-xs text-primary">{{
                getSelectedSpecText()
              }}</text>
            </view>
          </view>

          <scroll-view scroll-y class="max-h-[400rpx] pb-1">
            <!-- 规格选择 -->
            <view v-if="productSpecs.length > 0">
              <view
                v-for="(specGroup, groupIndex) in productSpecs"
                :key="groupIndex"
                class="mb-4"
              >
                <view class="flex justify-between items-center mb-2">
                  <view class="flex items-center">
                    <text class="text-sm text-gray-700 font-medium">{{
                      specGroup.name
                    }}</text>
                    <text class="text-2xs text-gray-400 ml-1.5"
                      >({{ specGroup.values.length }})</text
                    >
                  </view>
                  <text
                    class="text-2xs text-gray-400"
                    v-if="currentSku && selectedSkuId && groupIndex === 0"
                  >
                    库存: {{ skuStockMap[currentSku.id] ?? 0 }}
                  </text>
                </view>
                <view class="flex flex-wrap gap-2.5">
                  <view
                    v-for="(specValue, valueIndex) in specGroup.values"
                    :key="valueIndex"
                    :class="[
                      'px-3 py-1 rounded-md relative spec-item flex items-center justify-center',
                      isSpecSelected(groupIndex, valueIndex)
                        ? 'border-primary bg-primary/10 text-primary font-medium'
                        : 'bg-gray-50 border-transparent text-gray-700',
                      !isSpecAvailable(groupIndex, valueIndex)
                        ? 'opacity-50 cursor-not-allowed'
                        : 'cursor-pointer hover:bg-gray-100'
                    ]"
                    @click="
                      isSpecAvailable(groupIndex, valueIndex)
                        ? selectSpec(groupIndex, valueIndex, specValue)
                        : null
                    "
                  >
                    <text class="text-xs">{{ specValue }}</text>
                    <view
                      v-if="isSpecSelected(groupIndex, valueIndex)"
                      class="spec-selected-badge"
                    >
                      <van-icon name="success" color="red" />
                    </view>
                  </view>
                </view>
              </view>
            </view>
          </scroll-view>
        </view>

        <!-- 底部确认按钮 -->
        <view class="p-4 border-t">
          <button
            class="w-full rounded-full h-10 flex items-center justify-center"
            :class="[
              { 'opacity-50': !selectedSkuId },
              selectedAction === 'cart'
                ? 'bg-gradient-to-r from-blue-500 to-blue-400'
                : 'bg-gradient-to-r from-primary to-pink-400'
            ]"
            @click="confirmSelection"
            :disabled="!selectedSkuId"
          >
            <text
              class="text-white font-medium"
              style="font-size: 14px; line-height: 1"
              >{{ selectedAction === "cart" ? "加入购物车" : "立即购买" }}</text
            >
          </button>
        </view>
      </view>
    </up-popup>
  </view>
</template>

<script>
import CacheImgs from "@/components/CacheImgs.vue"
import {
  getProductDetail,
  addToCart,
  getBatchSkuStock,
  getSkuStock
} from "@/api/api"
import { onShow } from "@dcloudio/uni-app"
import UniShare from "@/uni_modules/uni-share/js_sdk/uni-share.js"
import NavBar from "@/components/NavBar.vue"
import store from "@/store"
const uniShare = new UniShare()

export default {
  components: { NavBar, CacheImgs },
  data() {
    return {
      productId: null,
      currentImage: 0,
      quantity: 1,
      isLiked: false,
      currentTab: 0,
      specsPopupVisible: false,
      selectedAction: null, // 'cart' 或 'buy'
      selectedSkuId: null,
      // 添加新的规格选择状态
      selectedSpecs: [],
      scrollTop: 0,
      tabs: [{ name: "商品详情" }, { name: "规格参数" }, { name: "服务保障" }],
      // 商品数据
      product: {
        id: "",
        name: "",
        category: "",
        brand: "",
        acg: "",
        type: "",
        size: "",
        description: "",
        files: [],
        skus: [],
        minPrice: 0,
        newable: false,
        domesticFreight: 0,
        internationalFreight: 0,
        totalSales: 0,
        salesType: ""
      },
      specs: [{ name: "产地", value: "日本" }],
      loading: true,
      skuStockMap: {} // 新增：skuId -> 库存
    }
  },

  computed: {
    getIsMerChant() {
      return store.state.$userInfo.identity === "MERCHANT"
    },

    getInputMin() {
      return (
        this.quantity >=
        Math.min(
          this.currentSku ? this.skuStockMap[this.currentSku.id] ?? 0 : 0,
          this.getIsMerChant ? this.skuStockMap[this.currentSku.id] : 2
        )
      )
    },

    getInputMax() {
      console.log(this.skuStockMap[this.currentSku.id], "this.getIsMerChant ")
      return Math.min(
        this.currentSku ? this.skuStockMap[this.currentSku.id] ?? 1 : 1,
        this.getIsMerChant ? this.skuStockMap[this.currentSku.id] : 2
      )
    },

    isOutOfStock() {
      // 检查所有SKU库存，如果都为0则为缺货
      if (!this.product.skus || this.product.skus.length === 0) {
        return true // 无SKU视为缺货
      }
      return this.product.skus.every((sku) => {
        const stock = this.skuStockMap[sku.id]
        return !stock || stock <= 0
      })
    },
    isPreSale() {
      return this.product.salesType === "PRESALE"
    },
    preSaleInfo() {
      return "下单后30左右发货"
    },
    productImages() {
      return this.product.files.map((file) => file.thumbnailUrl || file.url)
    },
    mainImage() {
      // 查找主图
      const mainFile = this.product.files.find((file) => file.main)
      // 如果没有主图，返回第一张图片，优先使用缩略图
      return mainFile
        ? mainFile.thumbnailUrl || mainFile.url
        : this.product.files[0].thumbnailUrl || this.product.files[0].url
    },
    specifications() {
      return this.product.skus && this.product.skus.length > 0
        ? this.product.skus.filter((sku) => sku.status === "U")
        : []
    },
    currentSku() {
      if (
        !this.selectedSkuId &&
        this.product.skus &&
        this.product.skus.length > 0
      ) {
        // 如果有已选规格组合，根据规格组合查找SKU
        if (this.selectedSpecs.length > 0) {
          const matchedSku = this.findSkuBySpecs()
          if (matchedSku) {
            this.selectedSkuId = matchedSku.id
          } else {
            // 如果没有匹配的SKU，选择第一个有效SKU
            const validSku = this.product.skus.find((sku) => sku.status === "U")
            if (validSku) {
              this.selectedSkuId = validSku.id
            }
          }
        } else {
          // 如果没有选中规格，默认选择第一个规格
          const validSku = this.product.skus.find((sku) => sku.status === "U")
          if (validSku) {
            this.selectedSkuId = validSku.id
            this.initSelectedSpecs(validSku)
          }
        }
      }

      return this.product.skus.find(
        (spec) => spec.id === this.selectedSkuId && spec.status === "U"
      )
    },
    totalSales() {
      return this.product.skus
        ? this.product.skus.reduce((total, sku) => total + (sku.sales || 0), 0)
        : 0
    },
    productSpecs() {
      // 从所有SKU中提取规格分类和值
      if (!this.product.skus || this.product.skus.length === 0) {
        return []
      }

      // 规格分组
      const specGroups = {}

      // 遍历所有有效SKU提取规格信息
      this.product.skus.forEach((sku) => {
        if (!sku.specification || sku.status !== "U") return

        try {
          // 检查是否是JSON格式
          if (
            sku.specification.trim().startsWith("{") &&
            sku.specification.trim().endsWith("}")
          ) {
            // 尝试解析JSON
            const specObj = JSON.parse(sku.specification)

            // 遍历JSON对象的每个属性
            Object.entries(specObj).forEach(([groupName, value]) => {
              if (!specGroups[groupName]) {
                specGroups[groupName] = new Set()
              }

              specGroups[groupName].add(String(value))
            })
          }
        } catch (e) {
          console.error("规格解析错误", e)
          // 处理单规格情况
          const groupName = "规格"

          if (!specGroups[groupName]) {
            specGroups[groupName] = new Set()
          }

          specGroups[groupName].add(sku.specification.trim())
        }
      })

      // 转换成数组格式，并按字母顺序排序规格值
      return Object.keys(specGroups).map((groupName) => ({
        name: groupName,
        values: Array.from(specGroups[groupName]).sort()
      }))
    },
    isAllSpecsSelected() {
      return this.selectedSpecs.length === this.productSpecs.length
    },
    getMinPrice() {
      // 优先使用product中的minPrice字段
      if (
        this.product.minPrice !== undefined &&
        this.product.minPrice !== null
      ) {
        return this.product.minPrice
      }
      if (!this.product.skus || this.product.skus.length === 0) {
        return 0
      }
      // 获取所有有效SKU中的最低价格（有库存）
      const validSkus = this.product.skus.filter(
        (sku) => sku.status === "U" && this.skuStockMap[sku.id] > 0
      )
      if (validSkus.length === 0) {
        return 0
      }
      return Math.min(...validSkus.map((sku) => sku.price))
    }
  },
  onLoad(options) {
    if (options.id) {
      this.productId = options.id
      this.fetchProductDetails(options.id)
    }

    // 页面加载时重置滚动位置
    this.resetScrollPosition()
  },
  onShow() {
    // 每次显示页面时重置滚动位置
    this.resetScrollPosition()
  },
  onReady() {
    // 页面渲染完成后再次确保滚动位置重置
    setTimeout(() => {
      this.resetScrollPosition()
    }, 100)
  },
  onPageScroll(e) {
    // 记录当前滚动位置，用于监控滚动状态
    this.currentScrollTop = e.scrollTop
  },
  methods: {
    checkPurchaseLimit() {
      if (this.getIsMerChant) {
        return false
      }
      const limit = 2
      if (this.quantity > limit) {
        this.quantity = limit
        uni.showToast({
          title: `每个 SKU 单次限购 2 件`,
          icon: "none"
        })
        return true
      }
      return false
    },

    // 返回上一页
    goBack() {
      // 获取当前页面栈
      const pages = getCurrentPages()
      // 如果页面栈长度大于1，说明有上一页，直接返回
      if (pages.length > 1) {
        uni.navigateBack()
      } else {
        // 否则跳转到首页
        uni.switchTab({
          url: "/pages/views/shop/shop"
        })
      }
    },

    shareProduct() {
      uniShare.show(
        {
          content: {
            type: 0,
            href: "https://uniapp.dcloud.io/",
            title: "标题",
            summary: "描述",
            imageUrl:
              "https://img-cdn-aliyun.dcloud.net.cn/stream/icon/__UNI__HelloUniApp.png"
          },
          menus: [
            {
              img: "/static/app-plus/sharemenu/wechatfriend.png",
              text: "微信好友",
              share: {
                provider: "weixin",
                scene: "WXSceneSession"
              }
            },
            {
              img: "/static/app-plus/sharemenu/qq.png",
              text: "QQ",
              share: {
                provider: "qq"
              }
            },
            {
              img: "/static/app-plus/sharemenu/copyurl.jpg",
              text: "复制",
              share: "copyurl"
            },
            {
              img: "/static/app-plus/sharemenu/more.jpg",
              text: "更多",
              share: "shareSystem"
            }
          ],
          cancelText: "取消分享"
        },
        (e) => {
          console.log("Share status:", uniShare.isShow)
          console.log("Share result:", e)
        }
      )
    },

    resetScrollPosition() {
      // 重置scroll-view的滚动位置
      this.scrollTop = 0

      // 使用uni.pageScrollTo强制页面滚动到顶部
      uni.pageScrollTo({
        scrollTop: 0,
        duration: 0 // 设置为0以立即滚动而非动画过渡
      })

      // 双重保险：延时后再次强制滚动到顶部
      setTimeout(() => {
        this.scrollTop = 0
        uni.pageScrollTo({
          scrollTop: 0,
          duration: 0
        })
      }, 50)

      // 三重保险：再次延时确保在DOM更新后滚动生效
      setTimeout(() => {
        this.scrollTop = 0
        uni.pageScrollTo({
          scrollTop: 0,
          duration: 0
        })
      }, 150)
    },
    async fetchProductDetails(id) {
      this.loading = true
      getProductDetail({ id, status: "U" })
        .then(async (res) => {
          if (res.data) {
            this.product = res.data
            // 默认选择第一个有效的SKU
            if (this.product.skus && this.product.skus.length > 0) {
              const validSku = this.product.skus.find(
                (sku) => sku.status === "U"
              )
              if (validSku) {
                this.selectedSkuId = validSku.id
              }
              // 批量查询所有SKU库存
              await this.fetchAllSkuStock()
            }
            // 构建规格参数数据
            this.specs = [
              { name: "分类", value: this.product.category || "暂无" },
              { name: "品牌", value: this.product.brand || "暂无" },
              { name: "作品", value: this.product.acg || "暂无" },
              { name: "品类", value: this.product.type || "暂无" },
              { name: "尺寸", value: this.product.size || "暂无" }
            ]
          } else {
            uni.showToast({
              title: "获取商品信息失败",
              icon: "none"
            })
          }
        })
        .catch((err) => {
          console.error("请求失败", err)
          uni.showToast({
            title: "网络请求失败",
            icon: "none"
          })
        })
        .finally(() => {
          this.loading = false
        })
    },
    async fetchAllSkuStock() {
      if (!this.product.skus || this.product.skus.length === 0) return
      const skuIds = this.product.skus.map((sku) => sku.id)
      try {
        const res = await getBatchSkuStock(skuIds)
        if (res && res.data) {
          this.skuStockMap = res.data
        } else {
          this.skuStockMap = {}
        }
      } catch (e) {
        this.skuStockMap = {}
      }
    },
    formatPrice(price) {
      return price ? price.toLocaleString() : "0"
    },
    calculateDiscount(price, originalPrice) {
      if (!price || !originalPrice || originalPrice <= 0) return ""
      const discount = Math.round((price / originalPrice) * 10)
      return `${discount}折`
    },
    handleSwiperChange(e) {
      this.currentImage = e.detail.current
    },
    incrementQuantity() {
      const maxStock = this.selectedSkuId
        ? this.skuStockMap[this.selectedSkuId] ?? 0
        : 0
      const maxLimit = Math.min(
        maxStock,
        this.getIsMerChant ? this.skuStockMap[this.currentSku.id] : 2
      )
      if (this.quantity < maxLimit) {
        this.quantity += 1
      }
    },
    decrementQuantity() {
      if (this.quantity > 1) {
        this.quantity -= 1
      }
    },
    onQuantityInput(e) {
      // 只允许输入1或2，且不超过库存
      let val = Number(e.detail.value)
      const maxStock = this.selectedSkuId
        ? this.skuStockMap[this.selectedSkuId] ?? 0
        : 0
      const maxLimit = Math.min(
        maxStock,
        this.getIsMerChant ? this.skuStockMap[this.currentSku.id] : 2
      )
      if (isNaN(val) || val < 1) val = 1
      if (val > maxLimit) val = maxLimit
      this.quantity = val
    },
    scrollToDetail() {
      const query = uni.createSelectorQuery().in(this)
      query
        .select("#detailSection")
        .boundingClientRect((data) => {
          uni.pageScrollTo({
            selector: "#detailSection",
            duration: 300
          })
        })
        .exec()
    },
    openSpecsPopup(action) {
      // 预售商品不允许加入购物车
      if (action === "cart" && this.isPreSale) {
        uni.showToast({
          title: "预定商品不可加入购物车",
          icon: "none"
        })
        return
      }

      this.selectedAction = action
      this.specsPopupVisible = true
    },
    closeSpecsPopup() {
      this.specsPopupVisible = false
    },
    selectSku(skuId) {
      this.selectedSkuId = skuId
      const sku = this.product.skus.find((s) => s.id === skuId)
      if (sku) {
        this.initSelectedSpecs(sku)
      }
    },
    getCurrentPrice() {
      if (this.currentSku) {
        return this.currentSku.price
      }
      return 0
    },
    confirmSelection() {
      // 检查是否已选择所有规格类型
      if (this.selectedSpecs.length < this.productSpecs.length) {
        uni.showToast({
          title: `请选择${this.getMissingSpecNames()}`,
          icon: "none"
        })
        return
      }

      if (!this.selectedSkuId) {
        uni.showToast({
          title: "请选择规格",
          icon: "none"
        })
        return
      }

      // 检查是否有匹配的SKU
      const matchedSku = this.findSkuBySpecs()
      if (!matchedSku) {
        uni.showToast({
          title: "所选规格组合不可用",
          icon: "none"
        })
        return
      }

      // 检查库存
      const stock = this.selectedSkuId
        ? this.skuStockMap[this.selectedSkuId] ?? 0
        : 0
      if (stock <= 0) {
        uni.showToast({
          title: "所选规格库存不足",
          icon: "none"
        })
        return
      }

      // 限购校验
      if (!this.getIsMerChant && this.checkPurchaseLimit()) {
        return
      }

      const data = {
        productId: this.product.id,
        skuId: this.selectedSkuId,
        quantity: this.quantity,
        price: this.getCurrentPrice(),
        isPreSale: this.isPreSale
      }

      // 预售商品不允许加入购物车
      if (this.selectedAction === "cart" && this.isPreSale) {
        uni.showToast({
          title: "预定商品不可加入购物车",
          icon: "none"
        })
        return
      }

      if (this.selectedAction === "cart") {
        this.addToCart(data)
      } else {
        this.buyNow(data)
      }

      this.closeSpecsPopup()
    },
    addToCart(data) {
      // 添加商品到购物车的逻辑
      uni.showLoading({
        title: "添加中..."
      })

      // 获取thumbnailUrl作为图片URL
      let imageUrl = this.mainImage
      if (this.product.files.length > 0) {
        const mainFile = this.product.files.find((file) => file.main)
        // 优先使用主图的缩略图，如果没有则使用第一张图片的缩略图，最后才用原图
        imageUrl = mainFile
          ? mainFile.thumbnailUrl || mainFile.url
          : this.product.files[0].thumbnailUrl || this.product.files[0].url
      }

      const cartData = {
        productId: data.productId,
        productSkuId: data.skuId,
        productName: this.product.name,
        productPrice: data.price,
        image: imageUrl,
        quantity: data.quantity
      }

      addToCart(cartData)
        .then((res) => {
          uni.hideLoading()
          if (res.code === 200) {
            uni.showToast({
              title: "已添加到购物车",
              icon: "success"
            })
          } else {
            uni.showToast({
              title: res.data?.message || "添加失败，请稍后重试",
              icon: "none"
            })
          }
        })
        .catch((err) => {
          uni.hideLoading()
          let errorMsg = "网络错误，请稍后重试"
          if (err.data && err.data.message) {
            errorMsg = err.data.message
          }
          uni.showToast({
            title: errorMsg,
            icon: "none"
          })
        })
    },
    buyNow(data) {
      // 立即购买逻辑
      console.log("立即购买", data)

      // 获取商品图片
      let imageUrl = this.mainImage
      if (this.product.files.length > 0) {
        const mainFile = this.product.files.find((file) => file.main)
        // 优先使用主图的缩略图，如果没有则使用第一张图片的缩略图，最后才用原图
        imageUrl = mainFile
          ? mainFile.thumbnailUrl || mainFile.url
          : this.product.files[0].thumbnailUrl || this.product.files[0].url
      }

      // 检查当前SKU信息
      const currentSku =
        this.currentSku ||
        this.product.skus.find((sku) => sku.id === this.selectedSkuId)
      if (!currentSku) {
        uni.showToast({
          title: "请选择有效的商品规格",
          icon: "none"
        })
        return
      }

      // 检查库存
      const stock = this.selectedSkuId
        ? this.skuStockMap[this.selectedSkuId] ?? 0
        : 0
      if (stock < this.quantity) {
        uni.showToast({
          title: "商品库存不足",
          icon: "none"
        })
        return
      }

      // 限购校验
      if (!this.getIsMerChant && this.checkPurchaseLimit()) {
        return
      }

      // 构建立即购买的订单项数组（符合订单确认页面的数据格式）
      const orderItem = {
        id: this.product.id,
        productId: this.product.id,
        productSkuId: this.selectedSkuId,
        price: currentSku.price,
        quantity: this.quantity,
        productName: this.product.name,
        productPrice: currentSku.price.toString(),
        image: imageUrl,
        salesType: this.isPreSale ? "PRESALE" : "STOCK",
        skus: this.product.skus, // 提供所有SKU以便在确认页面格式化规格信息
        specification: this.getSelectedSpecText()
      }

      // 使用eventChannel方式传递数据，这是订单确认页面期望的方式
      uni.navigateTo({
        url: "/pages/views/order/confirm",
        success: (res) => {
          // 通过eventChannel向被打开页面传送数据
          res.eventChannel.emit("selectedItems", [orderItem])
        }
      })
    },
    handleTabChange(index) {
      this.currentTab = index
    },
    parseRichText(text) {
      if (!text) return ""

      try {
        // 尝试解析JSON字符串
        let content = text

        // 检查是否是字符串形式的JSON
        if (
          typeof text === "string" &&
          (text.startsWith("{") || text.startsWith('"{'))
        ) {
          // 如果是带引号的JSON字符串，先去掉外层引号
          if (text.startsWith('"')) {
            text = JSON.parse(text)
          }

          const deltaObj = typeof text === "string" ? JSON.parse(text) : text

          // 检查是否是Quill Delta格式
          if (deltaObj && deltaObj.ops) {
            // 简单处理：提取所有insert的文本内容
            content = deltaObj.ops
              .map((op) => {
                if (typeof op.insert === "string") {
                  return op.insert
                } else if (op.insert && op.insert.image) {
                  return `<img src="${op.insert.image}" style="max-width:100%;height:auto;display:block;margin:10px 0;" />`
                }
                return ""
              })
              .join("")

            // 将换行符转换为<br>标签
            content = content.replace(/\n/g, "<br>")
            return content
          }
        }

        // 如果不是Delta格式或解析失败，则按普通HTML处理
        // 处理图片样式，使其自适应屏幕宽度
        let processedText = content.replace(
          /<img/gi,
          '<img style="max-width:100%;height:auto;display:block;margin:10px 0;"'
        )

        // 处理表格样式
        processedText = processedText.replace(
          /<table/gi,
          '<table style="width:100%;border-collapse:collapse;margin:10px 0;"'
        )

        // 处理段落样式
        processedText = processedText.replace(
          /<p/gi,
          '<p style="margin:8px 0;line-height:1.6;"'
        )

        return processedText
      } catch (error) {
        console.error("富文本解析错误:", error)
        // 解析失败时直接返回原文本
        return text
      }
    },
    swiperTap(index) {
      // 直接设置currentImage即可，swiper会通过绑定自动更新
      this.currentImage = index
    },
    initSelectedSpecs(sku) {
      if (!sku || !sku.specification) return

      this.selectedSpecs = []

      // 处理JSON格式
      if (
        sku.specification.trim().startsWith("{") &&
        sku.specification.trim().endsWith("}")
      ) {
        try {
          const specObj = JSON.parse(sku.specification)

          // 将JSON对象转换为规格数组
          Object.entries(specObj).forEach(([groupName, value]) => {
            this.selectedSpecs.push({
              groupName,
              value: String(value)
            })
          })
          return
        } catch (e) {
          console.error("规格解析错误", e)
          // 处理异常情况，会继续执行下面的逻辑
        }
      }

      // 处理不带冒号的单规格情况
      if (!sku.specification.includes(":")) {
        this.selectedSpecs = [
          {
            groupName: "规格",
            value: sku.specification.trim()
          }
        ]
        return
      }

      // 处理多规格情况
      try {
        // 解析规格字符串（例如"颜色:红色;尺码:XL"）
        const specPairs = sku.specification.split(";")

        specPairs.forEach((pair) => {
          const [groupName, value] = pair.split(":").map((part) => part.trim())
          if (groupName && value) {
            this.selectedSpecs.push({
              groupName,
              value
            })
          }
        })
      } catch (e) {
        console.error("规格解析错误", e)
        // 处理异常，确保至少有一个规格被选中
        this.selectedSpecs = [
          {
            groupName: "规格",
            value: sku.specification
          }
        ]
      }
    },
    selectSpec(groupIndex, valueIndex, specValue) {
      const groupName = this.productSpecs[groupIndex].name
      const value = this.productSpecs[groupIndex].values[valueIndex]

      // 检查该规格是否可选
      if (!this.isSpecAvailable(groupIndex, valueIndex)) {
        return // 直接返回，不显示提示
      }

      // 更新已选规格
      const existingIndex = this.selectedSpecs.findIndex(
        (spec) => spec.groupName === groupName
      )

      // 如果已选择了该规格值，则取消选择
      if (
        existingIndex !== -1 &&
        this.selectedSpecs[existingIndex].value === value
      ) {
        // 从已选规格中移除该规格
        this.selectedSpecs.splice(existingIndex, 1)
        // 重置SKU ID
        this.selectedSkuId = null
        return
      }

      if (existingIndex !== -1) {
        // 如果已经选择了该组的规格，更新值
        this.selectedSpecs[existingIndex].value = specValue
      } else {
        // 否则添加新的规格选择
        this.selectedSpecs.push({
          groupName,
          value: specValue
        })
      }

      // 根据规格组合查找匹配的SKU
      const matchedSku = this.findSkuBySpecs()
      if (matchedSku) {
        this.selectedSkuId = matchedSku.id
        // 库存检查
        if (this.quantity > matchedSku.stock) {
          this.quantity = Math.max(1, skuStockMap[matchedSku.id] ?? 0)
        }
      } else {
        // 如果没有找到匹配的SKU，可能是因为组合不存在
        // 尝试调整其他已选规格，保留当前点击的规格

        // 清除除当前规格组以外的所有已选规格
        this.selectedSpecs = [
          {
            groupName,
            value: specValue
          }
        ]

        // 重新尝试查找匹配的SKU
        const newMatchedSku = this.findSkuBySpecs()
        if (newMatchedSku) {
          this.selectedSkuId = newMatchedSku.id
          // 库存检查
          if (this.quantity > newMatchedSku.stock) {
            this.quantity = Math.max(1, skuStockMap[newMatchedSku.id] ?? 0)
          }
        }
      }
    },
    findSkuBySpecs() {
      if (!this.product.skus || this.selectedSpecs.length === 0) {
        return null
      }

      // 处理单规格情况
      if (
        this.selectedSpecs.length === 1 &&
        this.selectedSpecs[0].groupName === "规格"
      ) {
        return this.product.skus.find((sku) => {
          if (sku.status !== "U") return false

          // 处理不带冒号的单规格
          if (
            !sku.specification.includes(":") &&
            !sku.specification.startsWith("{")
          ) {
            return sku.specification.trim() === this.selectedSpecs[0].value
          }

          // 处理带冒号的单规格
          if (
            sku.specification.includes(":") &&
            !sku.specification.startsWith("{")
          ) {
            return sku.specification.includes(
              `规格:${this.selectedSpecs[0].value}`
            )
          }

          // 处理JSON格式
          if (
            sku.specification.trim().startsWith("{") &&
            sku.specification.trim().endsWith("}")
          ) {
            try {
              const specObj = JSON.parse(sku.specification)
              return String(specObj["规格"]) === this.selectedSpecs[0].value
            } catch (e) {
              console.error("规格解析错误", e)
              return false
            }
          }

          return false
        })
      }

      // 处理多规格情况或JSON格式
      return this.product.skus.find((sku) => {
        if (sku.status !== "U") return false

        // 处理JSON格式
        if (
          sku.specification.trim().startsWith("{") &&
          sku.specification.trim().endsWith("}")
        ) {
          try {
            const specObj = JSON.parse(sku.specification)

            // 检查每个已选规格是否匹配
            return this.selectedSpecs.every(
              (spec) => String(specObj[spec.groupName]) === spec.value
            )
          } catch (e) {
            console.error("规格解析错误", e)
            return false
          }
        }

        // 跳过不带冒号的规格（单规格情况已在上面处理）
        if (!sku.specification.includes(":")) return false

        const skuSpecs = sku.specification.split(";").sort().join(";")

        const specMatchString = this.selectedSpecs
          .map((spec) => `${spec.groupName}:${spec.value}`)
          .sort()
          .join(";")

        return skuSpecs === specMatchString
      })
    },
    isSpecSelected(groupIndex, valueIndex) {
      const groupName = this.productSpecs[groupIndex].name
      const value = this.productSpecs[groupIndex].values[valueIndex]

      return this.selectedSpecs.some(
        (spec) => spec.groupName === groupName && spec.value === value
      )
    },
    isSpecAvailable(groupIndex, valueIndex) {
      const groupName = this.productSpecs[groupIndex].name
      const value = this.productSpecs[groupIndex].values[valueIndex]
      const otherSpecs = this.selectedSpecs.filter(
        (spec) => spec.groupName !== groupName
      )
      if (otherSpecs.length === 0) {
        return this.product.skus.some((sku) => {
          if (sku.status !== "U" || (this.skuStockMap[sku.id] ?? 0) <= 0) {
            return false
          }
          // 检查是否为JSON格式
          if (
            sku.specification.trim().startsWith("{") &&
            sku.specification.trim().endsWith("}")
          ) {
            try {
              const specObj = JSON.parse(sku.specification)
              return String(specObj[groupName]) === value
            } catch (e) {
              return false
            }
          }

          // 单规格情况
          if (groupName === "规格" && !sku.specification.includes(":")) {
            return sku.specification.trim() === value
          }

          // 多规格情况
          return sku.specification.includes(`${groupName}:${value}`)
        })
      }

      return this.product.skus.some((sku) => {
        if (sku.status !== "U" || (this.skuStockMap[sku.id] ?? 0) <= 0) {
          return false
        }
        // 检查规格是否为JSON格式
        if (
          sku.specification.trim().startsWith("{") &&
          sku.specification.trim().endsWith("}")
        ) {
          try {
            const specObj = JSON.parse(sku.specification)

            // 检查当前规格
            if (String(specObj[groupName]) !== value) {
              return false
            }

            // 检查其他规格
            return otherSpecs.every(
              (spec) => String(specObj[spec.groupName]) === spec.value
            )
          } catch (e) {
            console.error("规格解析错误", e)
            return false
          }
        }

        // 单规格情况特殊处理
        if (groupName === "规格" && !sku.specification.includes(":")) {
          return sku.specification.trim() === value
        }

        // 多规格情况，检查是否包含当前规格
        const hasCurrentSpec = sku.specification.includes(
          `${groupName}:${value}`
        )
        if (!hasCurrentSpec) {
          return false
        }

        // 检查是否包含所有其他已选规格
        return otherSpecs.every((spec) => {
          // 单规格情况特殊处理
          if (spec.groupName === "规格" && !sku.specification.includes(":")) {
            return sku.specification.trim() === spec.value
          }
          return sku.specification.includes(`${spec.groupName}:${spec.value}`)
        })
      })
    },
    getSelectedSpecText() {
      if (this.selectedSpecs.length === 0) {
        return "请选择规格"
      }

      return this.selectedSpecs
        .map((spec) => `${spec.groupName}: ${spec.value}`)
        .join("，")
    },
    getMissingSpecNames() {
      const selectedSpecNames = this.selectedSpecs.map((spec) => spec.groupName)
      const allSpecNames = this.productSpecs.map((spec) => spec.name)
      return allSpecNames
        .filter((name) => !selectedSpecNames.includes(name))
        .join("、")
    }
  }
}
</script>

<style scoped>
.cart-btn {
  border: 1.5px solid var(--tw-prose-primary, #ef4444); /* 主题主色，兼容tailwind变量 */
  background: #fff;
  color: #ef4444;
  transition: border-color 0.2s, box-shadow 0.2s;
  box-sizing: border-box;
  box-shadow: 0 1px 4px rgba(239, 68, 68, 0.06);
}
.cart-btn:active {
  border-color: #d32f2f;
  background: #fef2f2;
}
.cart-btn:disabled,
.cart-btn[disabled] {
  border-color: #e5e7eb;
  color: #a3a3a3;
  background: #f3f4f6;
  opacity: 0.6;
  cursor: not-allowed;
}
.cart-btn-text {
  font-size: 14px;
  font-weight: 500;
  letter-spacing: 0.5px;
}
</style>
