// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'order.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

Order _$OrderFromJson(Map<String, dynamic> json) => Order(
      id: json['id'] as String,
      orderNo: json['orderNo'] as String,
      userId: json['userId'] as String,
      totalAmount: (json['totalAmount'] as num).toDouble(),
      payAmount: (json['payAmount'] as num).toDouble(),
      domesticFreight: (json['domesticFreight'] as num?)?.toDouble(),
      internationalFreight: (json['internationalFreight'] as num?)?.toDouble(),
      discountAmount: (json['discountAmount'] as num?)?.toDouble(),
      payType: json['payType'] as String?,
      orderStatus: json['orderStatus'] as String,
      receiverName: json['receiverName'] as String,
      receiverPhone: json['receiverPhone'] as String,
      receiverProvince: json['receiverProvince'] as String,
      receiverCity: json['receiverCity'] as String,
      receiverDistrict: json['receiverDistrict'] as String,
      receiverAddress: json['receiverAddress'] as String,
      note: json['note'] as String?,
      trackingNo: json['trackingNo'] as String?,
      trackingCompany: json['trackingCompany'] as String?,
      salesType: json['salesType'] as String?,
      orderTime: (json['orderTime'] as num).toInt(),
      payTime: (json['payTime'] as num?)?.toInt(),
      shipTime: (json['shipTime'] as num?)?.toInt(),
      receiveTime: (json['receiveTime'] as num?)?.toInt(),
      createTime: (json['createTime'] as num).toInt(),
      updateTime: (json['updateTime'] as num).toInt(),
      orderItems: (json['orderItems'] as List<dynamic>?)
          ?.map((e) => OrderItem.fromJson(e as Map<String, dynamic>))
          .toList(),
      orderRecords: (json['orderRecords'] as List<dynamic>?)
          ?.map((e) => OrderRecord.fromJson(e as Map<String, dynamic>))
          .toList(),
    );

Map<String, dynamic> _$OrderToJson(Order instance) => <String, dynamic>{
      'id': instance.id,
      'orderNo': instance.orderNo,
      'userId': instance.userId,
      'totalAmount': instance.totalAmount,
      'payAmount': instance.payAmount,
      'domesticFreight': instance.domesticFreight,
      'internationalFreight': instance.internationalFreight,
      'discountAmount': instance.discountAmount,
      'payType': instance.payType,
      'orderStatus': instance.orderStatus,
      'receiverName': instance.receiverName,
      'receiverPhone': instance.receiverPhone,
      'receiverProvince': instance.receiverProvince,
      'receiverCity': instance.receiverCity,
      'receiverDistrict': instance.receiverDistrict,
      'receiverAddress': instance.receiverAddress,
      'note': instance.note,
      'trackingNo': instance.trackingNo,
      'trackingCompany': instance.trackingCompany,
      'salesType': instance.salesType,
      'orderTime': instance.orderTime,
      'payTime': instance.payTime,
      'shipTime': instance.shipTime,
      'receiveTime': instance.receiveTime,
      'createTime': instance.createTime,
      'updateTime': instance.updateTime,
      'orderItems': instance.orderItems,
      'orderRecords': instance.orderRecords,
    };

OrderItem _$OrderItemFromJson(Map<String, dynamic> json) => OrderItem(
      id: json['id'] as String,
      orderId: json['orderId'] as String,
      productId: json['productId'] as String,
      productName: json['productName'] as String,
      productImage: json['productImage'] as String?,
      skuId: json['skuId'] as String?,
      skuName: json['skuName'] as String?,
      price: (json['price'] as num).toDouble(),
      quantity: (json['quantity'] as num).toInt(),
      totalAmount: (json['totalAmount'] as num).toDouble(),
    );

Map<String, dynamic> _$OrderItemToJson(OrderItem instance) => <String, dynamic>{
      'id': instance.id,
      'orderId': instance.orderId,
      'productId': instance.productId,
      'productName': instance.productName,
      'productImage': instance.productImage,
      'skuId': instance.skuId,
      'skuName': instance.skuName,
      'price': instance.price,
      'quantity': instance.quantity,
      'totalAmount': instance.totalAmount,
    };

OrderRecord _$OrderRecordFromJson(Map<String, dynamic> json) => OrderRecord(
      id: json['id'] as String,
      orderId: json['orderId'] as String,
      status: json['status'] as String,
      description: json['description'] as String,
      operateTime: (json['operateTime'] as num).toInt(),
      operatorId: json['operatorId'] as String?,
      operatorName: json['operatorName'] as String?,
    );

Map<String, dynamic> _$OrderRecordToJson(OrderRecord instance) =>
    <String, dynamic>{
      'id': instance.id,
      'orderId': instance.orderId,
      'status': instance.status,
      'description': instance.description,
      'operateTime': instance.operateTime,
      'operatorId': instance.operatorId,
      'operatorName': instance.operatorName,
    };
