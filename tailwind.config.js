/** @type {import('tailwindcss').Config} */
module.exports = {
  darkMode: ['class'],
  content: ['./src/**/*.{vue,js,ts,jsx,tsx}'],
  important: true, // 确保Tailwind样式优先级高于其他样式
  corePlugins: {
    preflight: false, // 禁用preflight以避免与uni-app的默认样式冲突
  },
  prefix: '',
  theme: {
    container: {
      center: true,
      padding: {
        DEFAULT: '1rem',
        md: '1.5rem',
      },
    },
    extend: {
      // 移动应用特定的屏幕尺寸
      screens: {
        xs: '360px',
        sm: '390px',
        md: '430px',
        lg: '768px',
        xl: '1024px',
      },
      colors: {
        border: 'hsl(var(--border))',
        input: 'hsl(var(--input))',
        ring: 'hsl(var(--ring))',
        background: 'hsl(var(--background))',
        foreground: 'hsl(var(--foreground))',
        // 设计规范中定义的文本颜色
        text: {
          primary: '#333333', // 主要文本
          secondary: '#666666', // 次要文本
          disabled: '#A3A3A3', // 禁用文本
        },
        primary: {
          DEFAULT: '#EF4444', // 明亮的奥特曼红
          foreground: '#FFFFFF',
          50: '#FEF2F2',
          100: '#FEE2E2',
          200: '#FECACA',
          300: '#FCA5A5',
          400: '#F87171',
          500: '#EF4444',
          600: '#DC2626',
          700: '#B91C1C',
          800: '#991B1B',
          900: '#7F1D1D',
          950: '#450A0A',
        },
        secondary: {
          DEFAULT: '#10B981', // 明亮的假面骑士绿
          foreground: '#FFFFFF',
          50: '#ECFDF5',
          100: '#D1FAE5',
          200: '#A7F3D0',
          300: '#6EE7B7',
          400: '#34D399',
          500: '#10B981',
          600: '#059669',
          700: '#047857',
          800: '#065F46',
          900: '#064E3B',
          950: '#022C22',
        },
        accent: {
          DEFAULT: '#3B82F6', // 明亮的超级战队蓝
          foreground: '#FFFFFF',
          50: '#EFF6FF',
          100: '#DBEAFE',
          200: '#BFDBFE',
          300: '#93C5FD',
          400: '#60A5FA',
          500: '#3B82F6',
          600: '#2563EB',
          700: '#1D4ED8',
          800: '#1E40AF',
          900: '#1E3A8A',
          950: '#172554',
        },
        heroic: {
          DEFAULT: '#F59E0B', // 明亮的英雄金色
          foreground: '#FFFFFF',
          50: '#FFFBEB',
          100: '#FEF3C7',
          200: '#FDE68A',
          300: '#FCD34D',
          400: '#FBBF24',
          500: '#F59E0B',
          600: '#D97706',
          700: '#B45309',
          800: '#92400E',
          900: '#78350F',
          950: '#451A03',
        },
        villain: {
          DEFAULT: '#8B5CF6', // 明亮的反派紫色
          foreground: '#FFFFFF',
          50: '#F5F3FF',
          100: '#EDE9FE',
          200: '#DDD6FE',
          300: '#C4B5FD',
          400: '#A78BFA',
          500: '#8B5CF6',
          600: '#7C3AED',
          700: '#6D28D9',
          800: '#5B21B6',
          900: '#4C1D95',
          950: '#2E1065',
        },
        mecha: {
          DEFAULT: '#6B7280', // 明亮的机械灰色
          foreground: '#FFFFFF',
          50: '#F9FAFB',
          100: '#F3F4F6',
          200: '#E5E7EB',
          300: '#D1D5DB',
          400: '#9CA3AF',
          500: '#6B7280',
          600: '#4B5563',
          700: '#374151',
          800: '#1F2937',
          900: '#111827',
          950: '#030712',
        },
        neon: {
          DEFAULT: '#EC4899', // 霓虹粉色
          foreground: '#FFFFFF',
          50: '#FDF2F8',
          100: '#FCE7F3',
          200: '#FBCFE8',
          300: '#F9A8D4',
          400: '#F472B6',
          500: '#EC4899',
          600: '#DB2777',
          700: '#BE185D',
          800: '#9D174D',
          900: '#831843',
          950: '#500724',
        },
        cyber: {
          DEFAULT: '#06B6D4', // 赛博蓝色
          foreground: '#FFFFFF',
          50: '#ECFEFF',
          100: '#CFFAFE',
          200: '#A5F3FC',
          300: '#67E8F9',
          400: '#22D3EE',
          500: '#06B6D4',
          600: '#0891B2',
          700: '#0E7490',
          800: '#155E75',
          900: '#164E63',
          950: '#083344',
        },
        energy: {
          DEFAULT: '#16A34A', // 能量绿色
          foreground: '#FFFFFF',
          50: '#F0FDF4',
          100: '#DCFCE7',
          200: '#BBF7D0',
          300: '#86EFAC',
          400: '#4ADE80',
          500: '#22C55E',
          600: '#16A34A',
          700: '#15803D',
          800: '#166534',
          900: '#14532D',
          950: '#052E16',
        },
        destructive: {
          DEFAULT: '#FF4D4F', // 更加年轻的错误红色
          foreground: '#FFFFFF',
        },
        warning: {
          DEFAULT: '#FAAD14', // 更加年轻的警告黄色
          foreground: '#000000',
        },
        success: {
          DEFAULT: '#52C41A', // 更加年轻的成功绿色
          foreground: '#FFFFFF',
        },
        info: {
          DEFAULT: '#06B6D4', // 信息提示色（使用赛博蓝）
          foreground: '#FFFFFF',
        },
        muted: {
          DEFAULT: 'hsl(var(--muted))',
          foreground: 'hsl(var(--muted-foreground))',
        },
        popover: {
          DEFAULT: 'hsl(var(--popover))',
          foreground: 'hsl(var(--popover-foreground))',
        },
        card: {
          DEFAULT: 'hsl(var(--card))',
          foreground: 'hsl(var(--card-foreground))',
        },
        // 保留原配置中的颜色
        'red-brand': '#DB0038',
        'yellow-brand': '#F7C200',
        'blue-tab': '#519dd9',
        'gray-light': '#f5f5f5',
        price: '#9c4400',
      },
      // 移动应用特定的圆角设置
      borderRadius: {
        lg: 'var(--radius)',
        md: 'calc(var(--radius) - 2px)',
        sm: 'calc(var(--radius) - 4px)',
        xl: '1rem',
        '2xl': '1.25rem',
        '3xl': '1.5rem',
        '4xl': '2rem',
        full: '9999px',
      },
      // 移动应用特定的阴影设置
      boxShadow: {
        app: '0 2px 8px rgba(0, 0, 0, 0.1)',
        'app-lg': '0 4px 12px rgba(0, 0, 0, 0.1)',
        'bottom-bar': '0 -2px 10px rgba(0, 0, 0, 0.05)',
        'top-bar': '0 2px 10px rgba(0, 0, 0, 0.05)',
        button: '0 2px 4px rgba(0, 0, 0, 0.1)',
        card: '0 2px 8px rgba(0, 0, 0, 0.08)',
      },
      // 移动应用特定的动画
      keyframes: {
        'accordion-down': {
          from: { height: '0' },
          to: { height: 'var(--radix-accordion-content-height)' },
        },
        'accordion-up': {
          from: { height: 'var(--radix-accordion-content-height)' },
          to: { height: '0' },
        },
        pulse: {
          '0%, 100%': { opacity: '1' },
          '50%': { opacity: '0.5' },
        },
        glow: {
          '0%, 100%': { boxShadow: '0 0 #0000' },
          '50%': { boxShadow: '0 0 20px rgba(239, 68, 68, 0.7)' },
        },
        // 移动应用特定的动画
        slideUp: {
          '0%': { transform: 'translateY(100%)' },
          '100%': { transform: 'translateY(0)' },
        },
        slideDown: {
          '0%': { transform: 'translateY(-100%)' },
          '100%': { transform: 'translateY(0)' },
        },
        fadeIn: {
          '0%': { opacity: '0' },
          '100%': { opacity: '1' },
        },
        fadeOut: {
          '0%': { opacity: '1' },
          '100%': { opacity: '0' },
        },
        shimmer: {
          '0%': { backgroundPosition: '-200% 0' },
          '100%': { backgroundPosition: '200% 0' },
        },
        bounce: {
          '0%, 100%': { transform: 'translateY(0)' },
          '50%': { transform: 'translateY(-4px)' },
        },
      },
      animation: {
        'accordion-down': 'accordion-down 0.2s ease-out',
        'accordion-up': 'accordion-up 0.2s ease-out',
        'pulse-slow': 'pulse 3s cubic-bezier(0.4, 0, 0.6, 1) infinite',
        'glow-red': 'glow 2s infinite',
        // 移动应用特定的动画
        'slide-up': 'slideUp 0.3s ease-out',
        'slide-down': 'slideDown 0.3s ease-out',
        'fade-in': 'fadeIn 0.2s ease-out',
        'fade-out': 'fadeOut 0.2s ease-out',
        shimmer: 'shimmer 2s infinite linear',
        bounce: 'bounce 0.5s ease-in-out',
        // 设计规范中定义的动画时长
        fast: 'var(--animation-duration, 0.15s)',
        normal: 'var(--animation-duration, 0.3s)',
        slow: 'var(--animation-duration, 0.5s)',
      },
      // 移动应用特定的间距
      spacing: {
        safe: 'env(safe-area-inset-bottom)',
        'safe-top': 'env(safe-area-inset-top)',
        'safe-left': 'env(safe-area-inset-left)',
        'safe-right': 'env(safe-area-inset-right)',
      },
      // 移动应用特定的字体大小
      fontSize: {
        '2xs': ['0.625rem', { lineHeight: '0.75rem' }], // 10px
        xs: ['0.75rem', { lineHeight: '1rem' }], // 12px
        sm: ['0.875rem', { lineHeight: '1.25rem' }], // 14px
        base: ['1rem', { lineHeight: '1.5rem' }], // 16px
        lg: ['1.125rem', { lineHeight: '1.75rem' }], // 18px
        xl: ['1.25rem', { lineHeight: '1.75rem' }], // 20px
        '2xl': ['1.5rem', { lineHeight: '2rem' }], // 24px
        '3xl': ['1.875rem', { lineHeight: '2.25rem' }], // 30px
        '4xl': ['2.25rem', { lineHeight: '2.5rem' }], // 36px
      },
      // 移动应用特定的高度
      height: {
        screen: ['100vh', '100dvh'],
        'screen-small': ['100vh', '100svh'],
        'screen-large': ['100vh', '100lvh'],
        'status-bar': '24px',
        'nav-bar': '44px',
        'tab-bar': '49px',
        'bottom-bar': '64px',
      },
      // 移动应用特定的宽度
      width: {
        screen: ['100vw', '100dvw'],
      },
      // 移动应用特定的Z-index
      zIndex: {
        'bottom-bar': 40,
        modal: 50,
        toast: 60,
        'status-bar': 70,
      },
      // 保留原配置中的字体
      fontFamily: {
        harmony: ['HarmonyOS Sans SC', 'sans-serif'],
        inter: ['Inter', 'sans-serif'],
        albert: ['Albert Sans', 'sans-serif'],
      },
    },
  },
  // 启用动画插件
  plugins: [require('tailwindcss-animate')],
}
