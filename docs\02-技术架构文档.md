# 中古虾(SOKO) 技术架构文档

## 🏗️ 整体架构概览

### 架构模式
中古虾采用**现代化前端架构**，基于Vue 3 + UniApp的跨平台解决方案：

```mermaid
graph TD
    A[用户界面层] --> B[业务逻辑层]
    B --> C[数据管理层]
    C --> D[API接口层]
    D --> E[后端服务]
    
    A1[Vue 3 Components] --> A
    A2[uView Plus UI] --> A
    A3[Tailwind CSS] --> A
    
    B1[Composables] --> B
    B2[Business Logic] --> B
    B3[Route Guards] --> B
    
    C1[Vuex Store] --> C
    C2[Local Storage] --> C
    C3[Cache Management] --> C
    
    D1[HTTP Client] --> D
    D2[Request/Response Interceptors] --> D
    D3[Error Handling] --> D
```

### 技术栈架构
- **前端框架**: Vue 3.4.29 (Composition API)
- **跨平台框架**: UniApp (支持H5、小程序、App)
- **类型系统**: TypeScript 4.9.4
- **构建工具**: Vite 5.2.8
- **状态管理**: Vuex 4.1.0
- **UI组件库**: uView Plus + Tailwind CSS
- **样式预处理**: SCSS + PostCSS

## 📁 项目目录结构

### 完整目录树
```
soko-frontend/
├── docs/                          # 项目文档
├── src/                           # 源代码目录
│   ├── api/                       # API接口层
│   │   ├── api.js                # 主要API接口
│   │   ├── recycle.ts            # 回收业务API
│   │   └── recycling.ts          # 回收相关API
│   ├── common/                    # 公共资源
│   │   ├── locales/              # 国际化语言包
│   │   │   ├── zh.js            # 中文语言包
│   │   │   └── en.js            # 英文语言包
│   │   ├── area.ts               # 地区数据
│   │   ├── city.ts               # 城市数据
│   │   ├── province.ts           # 省份数据
│   │   ├── classify.data.js      # 分类数据
│   │   ├── config.js             # 配置文件
│   │   ├── design-system.md      # 设计系统文档
│   │   ├── design-utils.scss     # 设计工具样式
│   │   ├── mixin.js              # 混入
│   │   └── props.js              # 属性配置
│   ├── components/                # 公共组件
│   │   ├── falcon/               # Falcon组件系列
│   │   │   └── FaIcon.vue       # 图标组件
│   │   ├── recycle/              # 回收业务组件
│   │   │   ├── RecycleImageUploader.vue    # 图片上传组件
│   │   │   ├── RecycleOrderCard.vue        # 订单卡片组件
│   │   │   └── RecycleStatusBadge.vue      # 状态徽章组件
│   │   ├── CacheImgs.vue         # 缓存图片组件
│   │   ├── DesignSystem.vue      # 设计系统组件
│   │   ├── MemberCard.vue        # 会员卡组件
│   │   ├── MemberOpenPopup.vue   # 会员开通弹窗
│   │   ├── NavBar.vue            # 导航栏组件
│   │   ├── PaymentFailedPopup.vue # 支付失败弹窗
│   │   └── StatusBarPlaceholder.vue # 状态栏占位组件
│   ├── composables/               # 组合式函数
│   │   ├── useRecycle.ts         # 回收业务逻辑
│   │   └── useRecycleOrder.ts    # 回收订单逻辑
│   ├── constants/                 # 常量定义
│   │   └── recycle.ts            # 回收相关常量
│   ├── enum/                      # 枚举类型
│   │   └── index.ts              # 枚举定义
│   ├── pages/                     # 页面文件
│   │   ├── views/                # 具体页面视图
│   │   │   ├── home/             # 首页
│   │   │   ├── shop/             # 商店
│   │   │   ├── cart/             # 购物车
│   │   │   ├── profile/          # 个人中心
│   │   │   ├── recycle/          # 回收模块
│   │   │   ├── order/            # 订单管理
│   │   │   ├── product/          # 商品相关
│   │   │   ├── login/            # 登录
│   │   │   ├── member/           # 会员中心
│   │   │   └── ...               # 其他业务页面
│   │   └── pages.json            # 页面配置文件
│   ├── static/                    # 静态资源
│   │   ├── app-plus/             # App平台资源
│   │   ├── common/               # 公共资源
│   │   ├── css/                  # 样式文件
│   │   ├── tabbar/               # 底部导航图标
│   │   └── *.png                 # 应用图标
│   ├── store/                     # 状态管理
│   │   └── index.js              # Vuex store配置
│   ├── styles/                    # 样式文件
│   ├── tools/                     # 工具函数
│   │   ├── getLocalUserInfo.ts   # 用户信息获取
│   │   └── index.ts              # 工具函数入口
│   ├── types/                     # TypeScript类型定义
│   │   └── recycle.ts            # 回收业务类型
│   ├── uni_modules/               # UniApp模块
│   │   ├── uview-plus/           # uView Plus组件库
│   │   ├── uni-share/            # 分享模块
│   │   └── wa-clipboard/         # 剪贴板模块
│   ├── util/                      # 工具类
│   │   ├── common/               # 公共工具
│   │   └── request/              # 请求封装
│   ├── App.vue                    # 应用入口组件
│   ├── main.ts                    # 应用入口文件
│   ├── manifest.json              # 应用配置清单
│   ├── pages.json                 # 页面路由配置
│   ├── shime-uni.d.ts            # UniApp类型声明
│   ├── tree.md                    # 目录结构说明
│   ├── uni.scss                   # 全局样式变量
│   └── env.d.ts                   # 环境变量类型声明
├── dist/                          # 构建输出目录
├── node_modules/                  # 依赖包
├── unpackage/                     # UniApp打包输出
├── package.json                   # 项目配置文件
├── package-lock.json              # 依赖锁定文件
├── vite.config.ts                 # Vite配置文件
├── tailwind.config.js             # Tailwind CSS配置
├── postcss.config.js              # PostCSS配置
├── tsconfig.json                  # TypeScript配置
├── workspace.yaml                 # 工作空间配置
└── README.md                      # 项目说明文档
```

### 目录职责说明

#### 核心业务层
- **`src/pages/views/`**: 页面视图层，按业务模块组织
- **`src/api/`**: API接口层，封装后端服务调用
- **`src/store/`**: 全局状态管理，使用Vuex模式
- **`src/composables/`**: 业务逻辑复用，Vue 3 Composition API

#### 基础设施层
- **`src/components/`**: 可复用UI组件库
- **`src/common/`**: 公共资源和配置
- **`src/util/`**: 工具函数和辅助类
- **`src/types/`**: TypeScript类型定义

#### 资源配置层
- **`src/static/`**: 静态资源文件
- **`src/styles/`**: 全局样式文件
- **`src/uni_modules/`**: UniApp生态模块

## ⚙️ 核心配置文件

### 1. Vite配置 (vite.config.ts)
```typescript
export default defineConfig({
  resolve: {
    alias: {
      "@": path.resolve(__dirname, "src"), // 路径别名
    },
  },
  plugins: [
    uni(),                    // UniApp插件
    visualizer(),            // 打包分析
  ],
  server: {
    port: 8078,              // 开发服务器端口
    hmr: true,               // 热更新
  },
  css: {
    preprocessorOptions: {
      scss: {
        additionalData: '@import "@/uni.scss";', // 全局SCSS变量
      },
    },
    postcss: {
      plugins: [
        require("tailwindcss/nesting"),  // Tailwind嵌套支持
        require("tailwindcss"),          // Tailwind CSS
        require("autoprefixer"),         // 自动前缀
      ],
    },
  },
});
```

### 2. 应用配置 (manifest.json)
```json
{
  "name": "SOKO",
  "appid": "__UNI__34B8741",
  "description": "中古模玩交易平台",
  "versionName": "1.0.0",
  "vueVersion": "3",
  "locale": "auto",
  "fallbackLocale": "zh-Hans"
}
```

### 3. 页面路由配置 (pages.json)
- **主包页面**: 首页作为主入口
- **分包策略**: 按业务模块分包，优化加载性能
- **预加载规则**: 核心页面预加载提升用户体验
- **全局样式**: 统一的导航栏和状态栏配置

### 4. 环境配置 (src/common/config.js)
```javascript
export default {
  baseUrl: process.env.NODE_ENV === "development" 
    ? "http://localhost:8080" 
    : "https://api.soko.com/api",
  timeout: 60000,
  withCredentials: true
}
```

## 🔧 构建与开发工具链

### 构建流程
```mermaid
graph LR
    A[源代码] --> B[TypeScript编译]
    B --> C[Vue SFC编译]
    C --> D[SCSS预处理]
    D --> E[Tailwind CSS处理]
    E --> F[PostCSS后处理]
    F --> G[Vite打包]
    G --> H[UniApp编译]
    H --> I[多端输出]
```

### 开发工具配置
- **代码检查**: ESLint + TypeScript严格模式
- **代码格式化**: Prettier自动格式化
- **热更新**: Vite HMR快速开发反馈
- **路径别名**: `@`指向`src`目录
- **类型检查**: Vue TSC类型检查

### 依赖管理策略
- **核心依赖**: Vue 3 + UniApp + TypeScript
- **UI组件**: uView Plus + Tailwind CSS
- **工具库**: Day.js、MD5、JSEncrypt等轻量级库
- **开发依赖**: Vite、ESLint、TypeScript等构建工具

## 🌐 路由架构设计

### 路由配置策略
项目采用UniApp的页面路由系统，结合分包策略优化性能：

#### 主包配置
```json
{
  "pages": [
    {
      "path": "pages/views/home/<USER>",
      "style": {
        "navigationBarTitleText": "首页",
        "navigationStyle": "custom"
      }
    }
  ]
}
```

#### 分包配置
```json
{
  "subPackages": [
    {
      "root": "pages/views",
      "pages": [
        // 用户相关页面
        { "path": "profile/profile" },
        { "path": "profile/settings" },
        // 商品相关页面
        { "path": "product/detail" },
        { "path": "product/products" },
        // 回收业务页面
        { "path": "recycle/home" },
        { "path": "recycle/create-order" },
        // 订单管理页面
        { "path": "order/list" },
        { "path": "order/detail" }
      ]
    }
  ]
}
```

#### 预加载策略
```json
{
  "preloadRule": {
    "pages/views/home/<USER>": {
      "network": "all",
      "packages": ["pages/views"]
    }
  }
}
```

### 路由守卫机制
```typescript
// src/util/common/routeGuard.ts
export const initRouteIntercept = () => {
  // 页面跳转拦截
  uni.addInterceptor('navigateTo', {
    invoke(args) {
      // 权限检查逻辑
      const needAuth = checkAuthRequired(args.url);
      if (needAuth && !isLoggedIn()) {
        uni.navigateTo({ url: '/pages/views/login/login' });
        return false;
      }
    }
  });
};
```

## 📊 状态管理架构

### Vuex Store结构
```javascript
// src/store/index.js
const store = createStore({
  state: {
    // 用户状态
    $userInfo: getInitialUserInfo(),
    $isLoggedIn: false,
    $token: uni.getStorageSync("token"),

    // 业务状态
    $certificationStatus: null,
    $systemConfig: [],

    // 通信状态
    $websocketConnected: false,
    $warningBadge: 0,
    $warningMessage: {
      message: "",
      showMessage: false,
      id: ""
    }
  },

  mutations: {
    // 状态变更方法
    SET_USER_INFO(state, userInfo) {
      state.$userInfo = userInfo;
    },
    SET_LOGIN_STATUS(state, status) {
      state.$isLoggedIn = status;
    }
  },

  actions: {
    // 异步操作
    async login({ commit }, credentials) {
      const response = await loginAPI(credentials);
      commit('SET_USER_INFO', response.data);
      commit('SET_LOGIN_STATUS', true);
    }
  }
});
```

### 状态持久化策略
- **Token管理**: 使用uni.getStorageSync持久化登录状态
- **用户信息**: 本地存储用户基本信息，减少API调用
- **配置缓存**: 系统配置信息本地缓存，提升加载速度

## 🔌 API接口层设计

### HTTP客户端封装
```javascript
// src/api/api.js
const getHttpInstance = () => {
  if (!uni.$u || !uni.$u.http) {
    throw new Error("uView Plus HTTP instance is not initialized.");
  }
  return uni.$u.http;
};

// 登录API示例
export const loginWithPassword = async (params) => {
  const { username, password } = params;

  try {
    // 获取公钥加密密码
    const publicKey = await publicKeyManager.getPublicKey();
    const encrypt = new JSEncrypt();
    encrypt.setPublicKey(publicKey);
    const encryptedPassword = encrypt.encrypt(password);

    // 调用登录接口
    return login({
      username,
      password: encryptedPassword
    });
  } catch (error) {
    console.error("登录失败", error);
    return {
      data: {
        success: false,
        message: error.message || "登录失败"
      }
    };
  }
};
```

### API分层设计
- **基础层**: HTTP客户端封装，统一错误处理
- **业务层**: 按模块组织API接口（用户、商品、订单、回收）
- **安全层**: 数据加密、token管理、权限验证
- **缓存层**: 接口缓存、离线支持

## 🎨 组件架构设计

### 组件分层策略
```
components/
├── falcon/              # 基础UI组件
│   └── FaIcon.vue      # 图标组件
├── recycle/            # 业务组件
│   ├── RecycleImageUploader.vue
│   ├── RecycleOrderCard.vue
│   └── RecycleStatusBadge.vue
└── common/             # 通用组件
    ├── NavBar.vue
    ├── MemberCard.vue
    └── StatusBarPlaceholder.vue
```

### 组件设计原则
1. **单一职责**: 每个组件只负责一个功能
2. **高内聚低耦合**: 组件内部逻辑紧密，组件间依赖最小
3. **可复用性**: 通过props和slots提供灵活配置
4. **类型安全**: 使用TypeScript定义组件接口

### Composables复用逻辑
```typescript
// src/composables/useRecycle.ts
export const useRecycle = () => {
  const recycleOrders = ref([]);
  const loading = ref(false);

  const fetchRecycleOrders = async () => {
    loading.value = true;
    try {
      const response = await getRecycleOrdersAPI();
      recycleOrders.value = response.data;
    } finally {
      loading.value = false;
    }
  };

  return {
    recycleOrders,
    loading,
    fetchRecycleOrders
  };
};
```

## 🔒 安全架构设计

### 数据安全策略
- **密码加密**: RSA公钥加密用户密码
- **Token管理**: JWT token自动刷新机制
- **数据传输**: HTTPS加密传输
- **本地存储**: 敏感数据加密存储

### 权限控制机制
- **路由守卫**: 页面级权限控制
- **组件权限**: 组件级显示控制
- **API权限**: 接口调用权限验证
- **数据权限**: 用户数据访问控制

## 📱 跨平台适配策略

### 平台兼容性处理
```javascript
// 条件编译示例
// #ifdef MP-WEIXIN
// 微信小程序特定代码
// #endif

// #ifdef H5
// H5平台特定代码
// #endif

// #ifdef APP-PLUS
// App平台特定代码
// #endif
```

### 样式适配方案
- **rpx单位**: 自动适配不同屏幕尺寸
- **安全区域**: 处理刘海屏和底部安全区域
- **主题适配**: 支持深色模式和主题切换
- **字体适配**: 响应式字体大小调整

---

*本文档最后更新时间: 2025年6月*
