import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

/// 回收订单筛选工具栏
class RecycleOrderFilterBar extends StatefulWidget {
  const RecycleOrderFilterBar({
    super.key,
    this.onFilterChanged,
  });

  final Function(Map<String, dynamic>)? onFilterChanged;

  @override
  State<RecycleOrderFilterBar> createState() => _RecycleOrderFilterBarState();
}

class _RecycleOrderFilterBarState extends State<RecycleOrderFilterBar> {
  String? _selectedCategory;
  String _searchKeyword = '';
  DateTime? _startDate;
  DateTime? _endDate;

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: EdgeInsets.symmetric(horizontal: 16.w, vertical: 8.h),
      decoration: BoxDecoration(
        color: Colors.white,
        border: Border(
          bottom: BorderSide(
            color: Colors.grey[200]!,
            width: 1,
          ),
        ),
      ),
      child: Row(
        children: [
          // 搜索框
          Expanded(
            child: Container(
              height: 36.h,
              decoration: BoxDecoration(
                color: Colors.grey[100],
                borderRadius: BorderRadius.circular(18.r),
              ),
              child: TextField(
                onChanged: (value) {
                  _searchKeyword = value;
                  _applyFilters();
                },
                decoration: InputDecoration(
                  hintText: '搜索订单号、品牌、型号',
                  hintStyle: TextStyle(
                    fontSize: 12.sp,
                    color: Colors.grey[500],
                  ),
                  prefixIcon: Icon(
                    Icons.search,
                    size: 18.w,
                    color: Colors.grey[500],
                  ),
                  border: InputBorder.none,
                  contentPadding: EdgeInsets.symmetric(
                    horizontal: 12.w,
                    vertical: 8.h,
                  ),
                ),
                style: TextStyle(fontSize: 12.sp),
              ),
            ),
          ),
          SizedBox(width: 8.w),
          
          // 分类筛选按钮
          _buildFilterButton(
            icon: Icons.category,
            label: _selectedCategory ?? '分类',
            onTap: _showCategoryFilter,
          ),
          SizedBox(width: 8.w),
          
          // 时间筛选按钮
          _buildFilterButton(
            icon: Icons.date_range,
            label: _getDateRangeLabel(),
            onTap: _showDateRangeFilter,
          ),
        ],
      ),
    );
  }

  /// 构建筛选按钮
  Widget _buildFilterButton({
    required IconData icon,
    required String label,
    required VoidCallback onTap,
  }) {
    final hasFilter = (icon == Icons.category && _selectedCategory != null) ||
                     (icon == Icons.date_range && (_startDate != null || _endDate != null));

    return GestureDetector(
      onTap: onTap,
      child: Container(
        padding: EdgeInsets.symmetric(horizontal: 8.w, vertical: 6.h),
        decoration: BoxDecoration(
          color: hasFilter ? Theme.of(context).primaryColor.withValues(alpha: 0.1) : Colors.grey[100],
          borderRadius: BorderRadius.circular(16.r),
          border: hasFilter 
              ? Border.all(color: Theme.of(context).primaryColor.withValues(alpha: 0.3))
              : null,
        ),
        child: Row(
          mainAxisSize: MainAxisSize.min,
          children: [
            Icon(
              icon,
              size: 14.w,
              color: hasFilter ? Theme.of(context).primaryColor : Colors.grey[600],
            ),
            SizedBox(width: 4.w),
            Text(
              label,
              style: TextStyle(
                fontSize: 11.sp,
                color: hasFilter ? Theme.of(context).primaryColor : Colors.grey[600],
                fontWeight: hasFilter ? FontWeight.w500 : FontWeight.normal,
              ),
            ),
          ],
        ),
      ),
    );
  }

  /// 显示分类筛选
  void _showCategoryFilter() {
    showModalBottomSheet(
      context: context,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.vertical(top: Radius.circular(16.r)),
      ),
      builder: (context) => Container(
        padding: EdgeInsets.all(16.w),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              '选择分类',
              style: TextStyle(
                fontSize: 16.sp,
                fontWeight: FontWeight.w600,
              ),
            ),
            SizedBox(height: 16.h),
            
            // 分类选项
            Wrap(
              spacing: 8.w,
              runSpacing: 8.h,
              children: [
                _buildCategoryChip('全部', null),
                _buildCategoryChip('手机', 'phone'),
                _buildCategoryChip('平板', 'tablet'),
                _buildCategoryChip('笔记本', 'laptop'),
                _buildCategoryChip('台式机', 'desktop'),
                _buildCategoryChip('相机', 'camera'),
                _buildCategoryChip('其他', 'other'),
              ],
            ),
            SizedBox(height: 16.h),
          ],
        ),
      ),
    );
  }

  /// 构建分类选择项
  Widget _buildCategoryChip(String label, String? value) {
    final isSelected = _selectedCategory == value;
    
    return GestureDetector(
      onTap: () {
        setState(() {
          _selectedCategory = value;
        });
        Navigator.of(context).pop();
        _applyFilters();
      },
      child: Container(
        padding: EdgeInsets.symmetric(horizontal: 12.w, vertical: 8.h),
        decoration: BoxDecoration(
          color: isSelected ? Theme.of(context).primaryColor : Colors.grey[100],
          borderRadius: BorderRadius.circular(16.r),
        ),
        child: Text(
          label,
          style: TextStyle(
            fontSize: 12.sp,
            color: isSelected ? Colors.white : Colors.grey[700],
            fontWeight: isSelected ? FontWeight.w500 : FontWeight.normal,
          ),
        ),
      ),
    );
  }

  /// 显示日期范围筛选
  void _showDateRangeFilter() {
    showDateRangePicker(
      context: context,
      firstDate: DateTime.now().subtract(const Duration(days: 365)),
      lastDate: DateTime.now(),
      initialDateRange: _startDate != null && _endDate != null
          ? DateTimeRange(start: _startDate!, end: _endDate!)
          : null,
      builder: (context, child) {
        return Theme(
          data: Theme.of(context).copyWith(
            colorScheme: Theme.of(context).colorScheme.copyWith(
              primary: Theme.of(context).primaryColor,
            ),
          ),
          child: child!,
        );
      },
    ).then((dateRange) {
      if (dateRange != null) {
        setState(() {
          _startDate = dateRange.start;
          _endDate = dateRange.end;
        });
        _applyFilters();
      }
    });
  }

  /// 获取日期范围标签
  String _getDateRangeLabel() {
    if (_startDate == null && _endDate == null) {
      return '时间';
    }
    
    if (_startDate != null && _endDate != null) {
      final start = '${_startDate!.month}/${_startDate!.day}';
      final end = '${_endDate!.month}/${_endDate!.day}';
      return '$start-$end';
    }
    
    return '时间';
  }

  /// 应用筛选条件
  void _applyFilters() {
    widget.onFilterChanged?.call({
      'category': _selectedCategory,
      'keyword': _searchKeyword.isEmpty ? null : _searchKeyword,
      'startDate': _startDate,
      'endDate': _endDate,
    });
  }
}
