import 'package:soko/core/utils/validator_utils.dart';

/// String扩展方法
extension StringExtension on String {
  /// 是否为空或空白字符
  bool get isEmptyOrWhitespace => trim().isEmpty;

  /// 是否不为空且不为空白字符
  bool get isNotEmptyAndNotWhitespace => !isEmptyOrWhitespace;

  /// 首字母大写
  String get capitalize {
    if (isEmpty) return this;
    return this[0].toUpperCase() + substring(1).toLowerCase();
  }

  /// 每个单词首字母大写
  String get capitalizeWords {
    if (isEmpty) return this;
    return split(' ').map((word) => word.capitalize).join(' ');
  }

  /// 移除所有空白字符
  String get removeWhitespace => replaceAll(RegExp(r'\s+'), '');

  /// 移除HTML标签
  String get removeHtmlTags => replaceAll(RegExp('<[^>]*>'), '');

  /// 截断文本
  String truncate(int maxLength, {String suffix = '...'}) {
    if (length <= maxLength) return this;
    return '${substring(0, maxLength)}$suffix';
  }

  /// 反转字符串
  String get reverse => split('').reversed.join();

  /// 是否包含数字
  bool get hasNumbers => RegExp(r'\d').hasMatch(this);

  /// 是否包含字母
  bool get hasLetters => RegExp('[a-zA-Z]').hasMatch(this);

  /// 是否包含特殊字符
  bool get hasSpecialCharacters => RegExp(r'[!@#$%^&*(),.?":{}|<>]').hasMatch(this);

  /// 是否为纯数字
  bool get isNumeric => ValidatorUtils.isNumeric(this);

  /// 是否为有效邮箱
  bool get isValidEmail => ValidatorUtils.isValidEmail(this);

  /// 是否为有效手机号
  bool get isValidPhone => ValidatorUtils.isValidPhone(this);

  /// 是否为有效URL
  bool get isValidUrl => ValidatorUtils.isValidUrl(this);

  /// 转换为int（安全转换）
  int? get toIntOrNull => int.tryParse(this);

  /// 转换为double（安全转换）
  double? get toDoubleOrNull => double.tryParse(this);

  /// 转换为bool
  bool get toBool {
    final lower = toLowerCase();
    return lower == 'true' || lower == '1' || lower == 'yes';
  }

  /// 获取字符串的MD5哈希值
  String get md5Hash {
    // 这里需要导入crypto包
    // import 'dart:convert';
    // import 'package:crypto/crypto.dart';
    // return md5.convert(utf8.encode(this)).toString();
    return this; // 临时返回原字符串
  }

  /// 驼峰命名转下划线
  String get camelToSnake {
    return replaceAllMapped(
      RegExp('[A-Z]'),
      (match) => '_${match.group(0)!.toLowerCase()}',
    );
  }

  /// 下划线转驼峰命名
  String get snakeToCamel {
    return replaceAllMapped(
      RegExp('_([a-z])'),
      (match) => match.group(1)!.toUpperCase(),
    );
  }

  /// 格式化手机号（隐藏中间4位）
  String get formatPhone {
    if (length != 11) return this;
    return '${substring(0, 3)}****${substring(7)}';
  }

  /// 格式化身份证号（隐藏中间部分）
  String get formatIdCard {
    if (length < 8) return this;
    final start = substring(0, 4);
    final end = substring(length - 4);
    return '$start****$end';
  }

  /// 格式化银行卡号（每4位一组）
  String get formatBankCard {
    return replaceAllMapped(
      RegExp(r'(\d{4})(?=\d)'),
      (match) => '${match.group(1)} ',
    );
  }

  /// 提取数字
  String get extractNumbers => replaceAll(RegExp(r'[^\d]'), '');

  /// 提取字母
  String get extractLetters => replaceAll(RegExp('[^a-zA-Z]'), '');

  /// 计算字符串相似度（简单版本）
  double similarity(String other) {
    if (this == other) return 1;
    if (isEmpty || other.isEmpty) return 0;

    final longer = length > other.length ? this : other;
    final shorter = length > other.length ? other : this;

    if (longer.isEmpty) return 1;

    final editDistance = _levenshteinDistance(longer, shorter);
    return (longer.length - editDistance) / longer.length;
  }

  /// 计算编辑距离
  int _levenshteinDistance(String s1, String s2) {
    final len1 = s1.length;
    final len2 = s2.length;

    final matrix = List.generate(
      len1 + 1,
      (i) => List.filled(len2 + 1, 0),
    );

    for (var i = 0; i <= len1; i++) {
      matrix[i][0] = i;
    }

    for (var j = 0; j <= len2; j++) {
      matrix[0][j] = j;
    }

    for (var i = 1; i <= len1; i++) {
      for (var j = 1; j <= len2; j++) {
        final cost = s1[i - 1] == s2[j - 1] ? 0 : 1;
        matrix[i][j] = [
          matrix[i - 1][j] + 1, // 删除
          matrix[i][j - 1] + 1, // 插入
          matrix[i - 1][j - 1] + cost, // 替换
        ].reduce((a, b) => a < b ? a : b);
      }
    }

    return matrix[len1][len2];
  }
}

/// 可空String扩展方法
extension NullableStringExtension on String? {
  /// 是否为null或空
  bool get isNullOrEmpty => this == null || this!.isEmpty;

  /// 是否不为null且不为空
  bool get isNotNullAndNotEmpty => !isNullOrEmpty;

  /// 是否为null、空或空白字符
  bool get isNullOrWhitespace => this == null || this!.isEmptyOrWhitespace;

  /// 是否不为null、不为空且不为空白字符
  bool get isNotNullAndNotWhitespace => !isNullOrWhitespace;

  /// 安全获取字符串，null时返回默认值
  String orDefault([String defaultValue = '']) => this ?? defaultValue;

  /// 安全执行操作
  T? let<T>(T Function(String) operation) {
    return this != null ? operation(this!) : null;
  }
}
