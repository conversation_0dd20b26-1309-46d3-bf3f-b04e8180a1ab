<template>
	<view
	    class="up-radio cursor-pointer"
		@tap.stop="wrapperClickHandler"
	    :style="[radioStyle]"
	    :class="[`up-radio-label--${parentData['iconPlacement']}`,
			(parentData['borderBottom'] != '' && parentData['placement'] === 'column')
			? 'up-border-bottom' : '']"
	>
		<view
		    class="up-radio__icon-wrap cursor-pointer"
		    @tap.stop="iconClickHandler"
		    :class="iconClasses"
		    :style="iconWrapStyle"
		>
			<slot name="icon">
				<up-icon
				    class="up-radio__icon-wrap__icon"
				    name="checkbox-mark"
				    :size="elIconSize"
				    :color="elIconColor"
				/>
			</slot>
		</view>
		<text
			class="up-radio__text"
		    @tap.stop="labelClickHandler"
		    :style="{
				color: elDisabled ? elInactiveColor : elLabelColor,
				fontSize: elLabelSize,
				lineHeight: elLabelSize
			}"
		>{{label}}</text>
	</view>
</template>

<script>
	import { propsRadio } from './props';
	import { mpMixin } from '../../libs/mixin/mpMixin';
	import { mixin } from '../../libs/mixin/mixin';
	import { addUnit, addStyle, os, deepMerge, formValidate, error } from '../../libs/function/index';
	/**
	 * radio 单选框
	 * @<NAME_EMAIL>
	 * @description 单选框用于有一个选择，用户只能选择其中一个的场景。搭配up-radio-group使用
	 * @tutorial https://ijry.github.io/uview-plus/components/radio.html
	 * @property {String | Number}	name			radio的名称
	 * @property {String}			shape			形状，square为方形，circle为圆型
	 * @property {Boolean}			disabled		是否禁用
	 * @property {String | Boolean}	labelDisabled	是否禁止点击提示语选中单选框
	 * @property {String}			activeColor		选中时的颜色，如设置parent的active-color将失效
	 * @property {String}			inactiveColor	未选中的颜色
	 * @property {String | Number}	iconSize		图标大小，单位px
	 * @property {String | Number}	labelSize		label字体大小，单位px
	 * @property {String | Number}	label			label提示文字，因为nvue下，直接slot进来的文字，由于特殊的结构，无法修改样式
	 * @property {String | Number}	size			整体的大小
	 * @property {String}			iconColor		图标颜色
	 * @property {String}			labelColor		label的颜色
	 * @property {Object}			customStyle		组件的样式，对象形式
	 * 
	 * @event {Function} change 某个radio状态发生变化时触发(选中状态)
	 * @example <up-radio :labelDisabled="false">门掩黄昏，无计留春住</up-radio>
	 */
	export default {
		name: "up-radio",
		mixins: [mpMixin, mixin, propsRadio],
		data() {
			return {
				checked: false,
				// 当你看到这段代码的时候，
				// 父组件的默认值，因为头条小程序不支持在computed中使用this.parent.shape的形式
				// 故只能使用如此方法
				parentData: {
					iconSize: '12px',
					labelDisabled: false,
					labelColor: '',
					labelSize: '',
					disabled: false,
					shape: '',
					activeColor: '',
					inactiveColor: '',
					size: '18px',
					value: '',
					modelValue: '',
					iconColor: '',
					placement: 'row',
					borderBottom: false,
					iconPlacement: 'left'
				} as UTSJSONObject
			}
		},
		computed: {
			// 是否禁用，如果父组件up-raios-group禁用的话，将会忽略子组件的配置
			elDisabled(): boolean {
				let disabledStr = this.disabled.toString()
				if (disabledStr !== '') {
					if (disabledStr == 'true') {
						return true
					} else {
						return false
					}
				} else {
					return this.parentData['disabled'] as boolean
				}
			},
			// 是否禁用label点击
			elLabelDisabled(): boolean {
				let labelDisabledStr = this.labelDisabled.toString()
				if (labelDisabledStr !== '') {
					if (labelDisabledStr == 'true') {
						return true
					} else {
						return false
					}
				} else {
					return this.parentData['labelDisabled'] as boolean
				}
			},
			// 组件尺寸，对应size的值，默认值为21px
			elSize(): string {
				return this.size.toString() != ''
					? this.size.toString()
					: (this.parentData['size'].toString() != '' ? this.parentData['size'].toString() : '21px');
			},
			// 组件的勾选图标的尺寸，默认12px
			elIconSize(): any {
				return this.iconSize.toString() != ''
					? this.iconSize.toString()
					: (this.parentData['iconSize'].toString() != '' ? this.parentData['iconSize'].toString() : '12px');
			},
			// 组件选中激活时的颜色
			elActiveColor(): string {
				return this.activeColor != ''
					? this.activeColor
					: (this.parentData['activeColor'] as string != '' ? this.parentData['activeColor'] as string : '#2979ff');
			},
			// 组件选未中激活时的颜色
			elInactiveColor(): string {
				return this.inactiveColor != ''
					? this.inactiveColor
					: (this.parentData['inactiveColor'] as string !='' ? this.parentData['inactiveColor'] as string :
					'#c8c9cc');
			},
			// label的颜色
			elLabelColor(): string {
				return this.labelColor != ''
					? this.labelColor
					: (this.parentData['labelColor'] as string !='' ? this.parentData['labelColor'] as string : '#606266')
			},
			// 组件的形状
			elShape(): string {
				return this.shape !=''
					? this.shape
					: (this.parentData['shape'] as string != '' ? this.parentData['shape'] as string : 'circle');
			},
			// label大小
			elLabelSize(): string {
				return addUnit(
					this.labelSize.toString() != ''
						? this.labelSize.toString()
						: (this.parentData['labelSize'].toString() != '' ? this.parentData['labelSize'].toString() :
					'15'))
			},
			elIconColor(): string {
				const iconColor = this.iconColor != ''
					? this.iconColor
					: (this.parentData['iconColor'] as string != '' ? this.parentData['iconColor'] as string :
						'#ffffff');
				// 图标的颜色
				if (this.elDisabled) {
					// disabled状态下，已勾选的radio图标改为elInactiveColor
					return this.checked ? this.elInactiveColor : 'transparent'
				} else {
					return this.checked ? iconColor : 'transparent'
				}
			},
			iconClasses(): string {
				let classes: string[]= []
				// 组件的形状
				classes.push('up-radio__icon-wrap--' + this.elShape)
				if (this.elDisabled) {
					classes.push('up-radio__icon-wrap--disabled')
				}
				if (this.checked && this.elDisabled) {
					classes.push('up-radio__icon-wrap--disabled--checked')
				}
				return classes.join(' ')
			},
			iconWrapStyle(): any {
				// radio的整体样式
				const style = {}
				style['backgroundColor'] = this.checked && !this.elDisabled ? this.elActiveColor : '#ffffff'
				style['borderColor'] = this.checked && !this.elDisabled ? this.elActiveColor : this.elInactiveColor
				// style['color'] = '#ffffff'
				style['width'] = addUnit(this.elSize)
				style['height'] = addUnit(this.elSize)
				// 如果是图标在右边的话，移除它的右边距
				if (this.parentData['iconPlacement'] === 'right') {
					style['marginRight'] = 0
				}
				return style
			},
			radioStyle(): any {
				const style = {}
				if(this.parentData['borderBottom'] as boolean
					&& this.parentData['placement'] as string === 'row') {
					error('检测到您将borderBottom设置为true，需要同时将up-radio-group的placement设置为column才有效')
				}
				// 当父组件设置了显示下边框并且排列形式为纵向时，给内容和边框之间加上一定间隔
				if(this.parentData['borderBottom'] as boolean
					&& this.parentData['placement'] as string === 'column') {
					// ios像素密度高，需要多一点的距离
					style['paddingBottom'] = os() === 'ios' ? '12px' : '8px'
				}
				return deepMerge(style, addStyle(this.customStyle))
			}
		},
		mounted() {
			this.init()
		},
		emits: ["change"],
		methods: {
			init(): void {
				// 支付宝小程序不支持provide/inject，所以使用这个方法获取整个父组件，在created定义，避免循环引用
				this.updateParentData()
				if (this.$parent == null) {
					error('up-radio必须搭配up-radio-group组件使用')
				}
				// 设置初始化时，是否默认选中的状态
				this.checked = this.name === this.parentData['modelValue']
			},
			updateParentData(): void {
				this.getParentData('up-radio-group')
			},
			// 点击图标
			iconClickHandler(e: any): void {
				this.preventEvent(e)
				// 如果整体被禁用，不允许被点击
				if (!this.elDisabled) {
					this.setRadioCheckedStatus()
				}
			},
			// 横向两端排列时，点击组件即可触发选中事件
			wrapperClickHandler(e: any): void {
				if (this.parentData['iconPlacement'] === 'right') {
					this.iconClickHandler(e)
				}
			},
			// 点击label
			labelClickHandler(e: any): void {
				this.preventEvent(e)
				// 如果按钮整体被禁用或者label被禁用，则不允许点击文字修改状态
				if (!this.elLabelDisabled && !this.elDisabled) {
					this.setRadioCheckedStatus()
				}
			},
			emitEvent(): void {
				// up-radio的checked不为true时(意味着未选中)，才发出事件，避免多次点击触发事件
				if (!this.checked) {
					this.$emit('change', this.name)
					// 尝试调用up-form的验证方法，进行一定延迟，否则微信小程序更新可能会不及时
					this.$nextTick(() => {
						formValidate(this, 'change')
					})
				}
			},
			// 改变组件选中状态
			// 这里的改变的依据是，更改本组件的checked值为true，同时通过父组件遍历所有up-radio实例
			// 将本组件外的其他up-radio的checked都设置为false(都被取消选中状态)，因而只剩下一个为选中状态
			setRadioCheckedStatus(): void {
				this.emitEvent()
				// 将本组件标记为选中状态
				this.checked = true
				if (this.$parent != null) {
					this.$parent!.$callMethod('unCheckedOther', this)
				}
			}
		}
	}
</script>

<style lang="scss" scoped>
	@import "../../libs/css/components.scss";
	$up-radio-wrap-margin-right:6px !default;
	$up-radio-wrap-font-size:20px !default;
	$up-radio-wrap-border-width:1px !default;
	$up-radio-wrap-border-color: #c8c9cc !default;
	$up-radio-line-height:0 !default;
	$up-radio-circle-border-radius:50px !default;
	$up-radio-square-border-radius:3px !default;
	$up-radio-checked-color:#fff !default;
	$up-radio-checked-background-color:red !default;
	$up-radio-checked-border-color: #2979ff !default;
	$up-radio-disabled-background-color:#ebedf0 !default;
	$up-radio-disabled--checked-color:#c8c9cc !default;
	$up-radio-label-margin-left: 5px !default;
	$up-radio-label-margin-right:12px !default;
	$up-radio-label-color:$up-content-color !default;
	$up-radio-label-font-size:15px !default;
	$up-radio-label-disabled-color:#c8c9cc !default;
	
	.up-radio {
		@include flex(row);
		overflow: hidden;
		flex-direction: row;
		align-items: center;
		margin-bottom: 5px;
		margin-top: 5px;
		
		&-label--left {
			flex-direction: row
		}

		&-label--right {
			flex-direction: row-reverse;
			justify-content: space-between
		}

		&__icon-wrap {
			/* #ifndef APP-NVUE */
			box-sizing: border-box;
			// nvue下，border-color过渡有问题
			transition-property: border-color, background-color, color;
			transition-duration: 0.2s;
			/* #endif */
			// color: $up-content-color;
			@include flex;
			align-items: center;
			justify-content: center;
			// color: transparent;
			// text-align: center;
			margin-right: $up-radio-wrap-margin-right;
			// font-size: $up-radio-wrap-font-size;
			border-width: $up-radio-wrap-border-width;
			border-color: $up-radio-wrap-border-color;
			border-style: solid;

			/* #ifdef MP-TOUTIAO */
			// 头条小程序兼容性问题，需要设置行高为0，否则图标偏下
			&__icon {
				line-height: $up-radio-line-height;
			}

			/* #endif */

			&--circle {
				border-radius: $up-radio-circle-border-radius;
			}

			&--square {
				border-radius: $up-radio-square-border-radius;
			}

			&--checked {
				color: $up-radio-checked-color;
				background-color: $up-radio-checked-background-color;
				border-color: $up-radio-checked-border-color;
			}

			&--disabled {
				background-color: $up-radio-disabled-background-color !important;
			}

			&--disabled--checked {
				color: $up-radio-disabled--checked-color !important;
			}
		}

		&__label {
			margin-left: $up-radio-label-margin-left;
			margin-right: $up-radio-label-margin-right;
			color: $up-radio-label-color;
			font-size: $up-radio-label-font-size;

			&--disabled {
				color: $up-radio-label-disabled-color;
			}
		}
	}
</style>
