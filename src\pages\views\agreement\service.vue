<script setup lang="ts">
import NavBar from '@/components/NavBar.vue'
</script>

<template>
  <view class="page min-h-screen bg-white">
    <NavBar title="服务协议" :showBack="true"></NavBar>
    
    <view class="service-content">
      <view class="service-title">服务协议</view>
      <view class="service-section">
        <view class="service-section-title">1. 协议说明</view>
        <view class="service-text">
          欢迎使用SOKO中古模玩平台服务！本协议是您与SOKO平台之间关于使用SOKO服务所订立的协议。在使用SOKO服务前，请您务必仔细阅读并同意本协议。
        </view>
      </view>
      
      <view class="service-section">
        <view class="service-section-title">2. 服务内容</view>
        <view class="service-text">
          SOKO平台是一个专注于中古模玩产品的在线交易平台，为用户提供商品浏览、购买、支付、物流等服务。SOKO可能会根据平台的发展，适时调整服务内容和形式。
        </view>
      </view>
      
      <view class="service-section">
        <view class="service-section-title">3. 用户账户</view>
        <view class="service-text">
          您需要注册成为SOKO用户才能使用完整服务。您应当提供真实、准确、完整的个人资料，并保持资料的及时更新。您应妥善保管账户名和密码，避免他人未经授权使用您的账户。
        </view>
      </view>
      
      <view class="service-section">
        <view class="service-section-title">4. 交易规则</view>
        <view class="service-text">
          用户在平台进行交易时，应当遵守平台制定的交易规则。包括但不限于商品信息浏览、下单、支付、收货、评价等环节的规则。平台有权根据业务需要调整交易规则，并予以公示。
        </view>
      </view>
      
      <view class="service-section">
        <view class="service-section-title">5. 用户行为规范</view>
        <view class="service-text">
          用户在使用SOKO服务时，不得从事以下行为：发布虚假信息、侵犯他人知识产权、干扰平台正常运营、利用平台从事非法活动等。如有违反，平台有权采取警告、限制或终止服务等措施。
        </view>
      </view>
      
      <view class="service-section">
        <view class="service-section-title">6. 协议变更</view>
        <view class="service-text">
          SOKO有权在必要时修改本协议条款。您可以在平台上查阅最新版本的协议条款。本协议条款变更后，如果您继续使用SOKO服务，即视为您已接受修改后的协议。
        </view>
      </view>
      
      <view class="service-footer">
        最后更新日期：2023年12月1日
      </view>
    </view>
  </view>
</template>

<style scoped lang="scss">
.service-content {
  padding: 16px;
  background-color: #ffffff;
  min-height: 100%;
  
  .service-title {
    font-size: 20px;
    font-weight: bold;
    margin-bottom: 20px;
    color: #303133;
    text-align: center;
  }
  
  .service-section {
    margin-bottom: 20px;
    
    .service-section-title {
      font-size: 16px;
      font-weight: 600;
      margin-bottom: 8px;
      color: #303133;
    }
    
    .service-text {
      font-size: 14px;
      line-height: 1.6;
      color: #606266;
    }
  }
  
  .service-footer {
    font-size: 12px;
    color: #909399;
    text-align: center;
    margin-top: 30px;
    padding-bottom: 20px;
  }
}
</style>
