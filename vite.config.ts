import { defineConfig } from "vite";
import uni from "@dcloudio/vite-plugin-uni";
import { visualizer } from "rollup-plugin-visualizer";
import path from "path";
// import commonjs from '@rollup/plugin-commonjs';

// https://vitejs.dev/config/
export default defineConfig({
  resolve: {
    alias: {
      "@": path.resolve(__dirname, "src"), // 使用 path.resolve 动态生成路径
    },
  },
  plugins: [
    // commonjs(),
    uni(),
    visualizer(),
  ],
  server: {
    port: 8078,
    fs: {
      // Allow serving files from one level up to the project root
      allow: [".."],
    },
    hmr: true, // 开启热更新
  },
  css: {
    preprocessorOptions: {
      scss: {
        additionalData: '@import "@/uni.scss";',
      },
    },
    postcss: {
      plugins: [
        require("tailwindcss/nesting"),
        require("tailwindcss"),
        require("autoprefixer"),
      ],
    },
  },
});
