<template>
  <view class="page-container">
    <view class="text-2xl font-bold mb-6">特摄模玩设计系统示例</view>

    <!-- 排版示例 -->
    <view class="mb-8">
      <view class="text-xl font-semibold mb-4">排版系统</view>
      <view class="p-4 bg-white rounded-lg shadow-app">
        <view class="text-4xl font-bold text-primary mb-2">特大标题 (4xl)</view>
        <view class="text-3xl font-bold text-secondary mb-2">大标题 (3xl)</view>
        <view class="text-2xl font-semibold text-accent mb-2"
          >中标题 (2xl)</view
        >
        <view class="text-xl font-semibold text-villain mb-2">小标题 (xl)</view>
        <view class="text-lg font-medium mb-2">大号正文 (lg)</view>
        <view class="text-base mb-2">标准正文 (base)</view>
        <view class="text-sm mb-2">小号正文 (sm)</view>
        <view class="text-xs mb-2">小号文本 (xs)</view>
        <view class="text-2xs">极小文本 (2xs)</view>
      </view>
    </view>

    <!-- 颜色系统 -->
    <view class="mb-8">
      <view class="text-xl font-semibold mb-4">颜色系统</view>
      <view class="p-4 bg-white rounded-lg shadow-app">
        <view class="mb-4">
          <view class="text-lg font-medium mb-2">主题色</view>
          <view class="flex flex-wrap gap-2">
            <view
              class="w-24 h-24 bg-primary rounded-lg flex items-center justify-center text-white"
              >奥特曼红</view
            >
            <view
              class="w-24 h-24 bg-secondary rounded-lg flex items-center justify-center text-white"
              >骑士绿</view
            >
            <view
              class="w-24 h-24 bg-accent rounded-lg flex items-center justify-center text-white"
              >战队蓝</view
            >
          </view>
        </view>

        <view class="mb-4">
          <view class="text-lg font-medium mb-2">辅助色</view>
          <view class="flex flex-wrap gap-2">
            <view
              class="w-24 h-24 bg-heroic rounded-lg flex items-center justify-center text-white"
              >英雄金</view
            >
            <view
              class="w-24 h-24 bg-villain rounded-lg flex items-center justify-center text-white"
              >反派紫</view
            >
            <view
              class="w-24 h-24 bg-neon rounded-lg flex items-center justify-center text-white"
              >霓虹粉</view
            >
            <view
              class="w-24 h-24 bg-cyber rounded-lg flex items-center justify-center text-white"
              >赛博蓝</view
            >
            <view
              class="w-24 h-24 bg-energy rounded-lg flex items-center justify-center text-white"
              >能量绿</view
            >
            <view
              class="w-24 h-24 bg-mecha rounded-lg flex items-center justify-center text-white"
              >机械灰</view
            >
          </view>
        </view>

        <view class="mb-4">
          <view class="text-lg font-medium mb-2">渐变色</view>
          <view class="flex flex-wrap gap-2">
            <view
              class="w-40 h-16 bg-gradient-primary rounded-lg flex items-center justify-center text-white"
              >主要渐变</view
            >
            <view
              class="w-40 h-16 bg-gradient-secondary rounded-lg flex items-center justify-center text-white"
              >次要渐变</view
            >
            <view
              class="w-40 h-16 bg-gradient-accent rounded-lg flex items-center justify-center text-white"
              >强调渐变</view
            >
            <view
              class="w-40 h-16 bg-gradient-special rounded-lg flex items-center justify-center text-white"
              >特殊渐变</view
            >
          </view>
        </view>
      </view>
    </view>

    <!-- 间距系统 -->
    <view class="mb-8">
      <view class="text-xl font-semibold mb-4">间距系统</view>
      <view class="p-4 bg-white rounded-lg shadow-app">
        <view class="flex flex-col gap-4">
          <view class="p-1 bg-primary text-white rounded">p-1 (4px)</view>
          <view class="p-1.5 bg-primary text-white rounded">p-1.5 (6px)</view>
          <view class="p-2 bg-primary text-white rounded">p-2 (8px)</view>
          <view class="p-3 bg-primary text-white rounded">p-3 (12px)</view>
          <view class="p-4 bg-primary text-white rounded">p-4 (16px)</view>
          <view class="p-6 bg-primary text-white rounded">p-6 (24px)</view>
        </view>
      </view>
    </view>

    <!-- 按钮系统 -->
    <view class="mb-8">
      <view class="text-xl font-semibold mb-4">按钮系统</view>
      <view class="p-4 bg-white rounded-lg shadow-app">
        <view class="mb-4">
          <view class="text-lg font-medium mb-2">按钮变体</view>
          <view class="flex flex-wrap gap-2">
            <up-button type="primary" text="默认按钮"></up-button>
            <up-button type="success" text="次要按钮"></up-button>
            <up-button type="info" plain text="轮廓按钮"></up-button>
            <up-button type="warning" text="警告按钮"></up-button>
            <up-button type="error" text="危险按钮"></up-button>
            <up-button type="primary" link text="链接按钮"></up-button>
          </view>
        </view>

        <view class="mb-4">
          <view class="text-lg font-medium mb-2">按钮尺寸</view>
          <view class="flex flex-wrap gap-2 items-center">
            <up-button type="primary" size="mini" text="小型"></up-button>
            <up-button type="primary" text="默认"></up-button>
            <up-button type="primary" size="large" text="大型"></up-button>
            <up-button type="primary" icon="star" shape="circle"></up-button>
          </view>
        </view>

        <view class="mb-4">
          <view class="text-lg font-medium mb-2">自定义按钮</view>
          <view class="flex flex-wrap gap-2">
            <view
              class="bg-gradient-primary px-4 py-2 rounded-tokusatsu text-white font-medium shadow-hero"
            >
              特摄风格按钮
            </view>
            <view
              class="bg-gradient-secondary px-4 py-2 rounded-hero-mask text-white font-medium shadow-energy"
            >
              英雄面具按钮
            </view>
            <view
              class="bg-gradient-special px-4 py-2 rounded-villain text-white font-medium shadow-mecha"
            >
              反派风格按钮
            </view>
          </view>
        </view>
      </view>
    </view>

    <!-- 卡片和容器 -->
    <view class="mb-8">
      <view class="text-xl font-semibold mb-4">卡片和容器</view>
      <view class="grid grid-cols-1 md:grid-cols-2 gap-4">
        <!-- 标准卡片 -->
        <view class="p-4 bg-white rounded-lg shadow-app">
          <view class="text-lg font-semibold mb-2">标准卡片</view>
          <view class="text-sm text-gray-600 mb-3"
            >这是一个标准卡片的描述文本</view
          >
          <view class="mb-4">卡片主要内容区域</view>
          <view class="flex justify-end">
            <up-button type="primary" size="mini" text="操作按钮"></up-button>
          </view>
        </view>

        <!-- 特摄风格卡片 -->
        <view class="p-4 bg-white rounded-tokusatsu border-mecha">
          <view class="text-lg font-semibold text-mecha mb-2"
            >机械风格卡片</view
          >
          <view class="text-sm text-gray-600 mb-3">带有特摄元素的卡片设计</view>
          <view class="mb-4">机械风格边框和形状</view>
          <view class="flex justify-end">
            <up-button type="primary" size="mini" text="变身"></up-button>
          </view>
        </view>

        <!-- 英雄风格卡片 -->
        <view class="p-4 bg-white rounded-hero-mask border-hero shadow-hero">
          <view class="text-hero-title mb-2">英雄卡片</view>
          <view class="text-sm text-gray-600 mb-3">带有英雄元素的卡片设计</view>
          <view class="mb-4">具有特殊光效和形状</view>
          <view class="flex justify-end">
            <view
              class="bg-gradient-primary px-4 py-1.5 rounded-lg text-white text-sm font-medium"
            >
              必杀技
            </view>
          </view>
        </view>

        <!-- 渐变卡片 -->
        <view class="p-4 bg-gradient-accent rounded-lg text-white shadow-app">
          <view class="text-lg font-bold mb-2">渐变卡片</view>
          <view class="text-sm opacity-80 mb-3">带有渐变背景的卡片设计</view>
          <view class="mb-4">适合用于强调内容或营销区域</view>
          <view class="flex justify-end">
            <view
              class="bg-white px-4 py-1.5 rounded-lg text-accent text-sm font-medium"
            >
              了解更多
            </view>
          </view>
        </view>
      </view>
    </view>

    <!-- 徽章和标签 -->
    <view class="mb-8">
      <view class="text-xl font-semibold mb-4">徽章和标签</view>
      <view class="p-4 bg-white rounded-lg shadow-app">
        <view class="flex flex-wrap gap-3">
          <up-tag text="默认标签" type="primary"></up-tag>
          <up-tag text="次要标签" type="success"></up-tag>
          <up-tag text="警告标签" type="warning"></up-tag>
          <up-tag text="错误标签" type="error"></up-tag>
          <up-tag text="轮廓标签" plain type="primary"></up-tag>

          <view class="mt-4 w-full"></view>

          <!-- 自定义标签 -->
          <view class="px-2 py-1 bg-primary text-white text-xs rounded-full">
            圆形徽章
          </view>
          <view
            class="px-2 py-1 bg-gradient-secondary text-white text-xs rounded-tokusatsu"
          >
            特摄风格
          </view>
          <view class="px-2 py-1 bg-villain text-white text-xs rounded-villain">
            反派风格
          </view>
          <view
            class="px-2 py-1 bg-gradient-special text-white text-xs rounded-lg animate-energy-pulse"
          >
            能量脉冲
          </view>
        </view>
      </view>
    </view>

    <!-- 动画效果 -->
    <view class="mb-8">
      <view class="text-xl font-semibold mb-4">动画效果</view>
      <view class="p-4 bg-white rounded-lg shadow-app">
        <view class="flex flex-wrap gap-4">
          <view class="p-4 bg-primary text-white rounded-lg animate-transform">
            变身闪光
          </view>
          <view
            class="p-4 bg-energy text-white rounded-lg animate-energy-pulse"
          >
            能量脉冲
          </view>
          <view
            class="p-4 bg-villain text-white rounded-lg animate-villain-appear"
          >
            反派出场
          </view>
        </view>
      </view>
    </view>
  </view>
</template>

<script>
export default {
  name: 'DesignSystem',
}
</script>

<style lang="scss">
/* 引入设计系统工具类 */
@import '@/common/design-utils.scss';

/* 组件特定样式 */
.text-2xs {
  font-size: 10px;
}
</style>
