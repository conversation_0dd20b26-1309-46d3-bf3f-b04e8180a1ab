import 'package:flutter_riverpod/flutter_riverpod.dart';

import 'package:soko/features/recycle/domain/entities/recycle_order.dart';
import 'package:soko/features/recycle/domain/entities/recycle_models.dart';
import 'package:soko/core/models/paginated_data.dart';
import 'package:soko/core/enums/app_enums.dart';

/// 回收业务远程数据源接口
abstract class RecycleRemoteDataSource {
  Future<PaginatedData<RecycleOrder>> getRecycleOrders({
    int page = 1,
    int pageSize = 20,
    String? status,
    String? category,
    String? keyword,
    DateTime? startDate,
    DateTime? endDate,
  });

  Future<RecycleOrder> getRecycleOrderDetail(String orderId);
  Future<RecycleOrder> createRecycleOrder(CreateRecycleOrderRequest request);
  Future<void> cancelRecycleOrder(String orderId);
  Future<void> confirmShipment(String orderId, Map<String, dynamic> shippingInfo);
  Future<List<CategoryItem>> getRecycleCategories();
  Future<Map<String, dynamic>> getRecycleStats();
  Future<List<Map<String, dynamic>>> getRecycleProcess();
  Future<List<RecycleOrder>> getRecentOrders({int limit = 5});
}

/// 回收业务远程数据源实现（模拟数据）
class RecycleRemoteDataSourceImpl implements RecycleRemoteDataSource {
  @override
  Future<PaginatedData<RecycleOrder>> getRecycleOrders({
    int page = 1,
    int pageSize = 20,
    String? status,
    String? category,
    String? keyword,
    DateTime? startDate,
    DateTime? endDate,
  }) async {
    // 模拟网络延迟
    await Future.delayed(const Duration(milliseconds: 800));

    // 生成模拟数据
    final mockOrders = _generateMockOrders();
    
    // 应用筛选条件
    var filteredOrders = mockOrders.where((order) {
      if (status != null && order.orderStatus != status) return false;
      if (category != null && order.categoryName != category) return false;
      if (keyword != null && 
          !order.brandName.toLowerCase().contains(keyword.toLowerCase()) &&
          !order.model.toLowerCase().contains(keyword.toLowerCase()) &&
          !order.id.toLowerCase().contains(keyword.toLowerCase())) return false;
      return true;
    }).toList();

    // 分页处理
    final startIndex = (page - 1) * pageSize;
    final endIndex = (startIndex + pageSize).clamp(0, filteredOrders.length);
    final pageData = filteredOrders.sublist(
      startIndex.clamp(0, filteredOrders.length),
      endIndex,
    );

    return PaginatedData<RecycleOrder>(
      data: pageData,
      currentPage: page,
      pageSize: pageSize,
      totalPages: (filteredOrders.length / pageSize).ceil(),
      totalCount: filteredOrders.length,
      hasMore: endIndex < filteredOrders.length,
    );
  }

  @override
  Future<RecycleOrder> getRecycleOrderDetail(String orderId) async {
    await Future.delayed(const Duration(milliseconds: 500));
    
    final mockOrders = _generateMockOrders();
    final order = mockOrders.firstWhere(
      (order) => order.id == orderId,
      orElse: () => throw Exception('订单不存在'),
    );
    
    return order;
  }

  @override
  Future<RecycleOrder> createRecycleOrder(CreateRecycleOrderRequest request) async {
    await Future.delayed(const Duration(milliseconds: 1000));
    
    // 模拟创建订单
    return RecycleOrder(
      id: 'RO${DateTime.now().millisecondsSinceEpoch}',
      userId: 'user123',
      brandName: request.productName.split(' ').first,
      model: request.productModel ?? '未知型号',
      categoryName: request.productCategory,
      conditionDescription: request.condition,
      estimatedPrice: request.expectedPrice,
      orderStatus: 'PENDING_APPROVAL',
      orderStatusDesc: '待审核',
      createTime: DateTime.now().millisecondsSinceEpoch,
      updateTime: DateTime.now().millisecondsSinceEpoch,
    );
  }

  @override
  Future<void> cancelRecycleOrder(String orderId) async {
    await Future.delayed(const Duration(milliseconds: 500));
    // 模拟取消订单操作
  }

  @override
  Future<void> confirmShipment(String orderId, Map<String, dynamic> shippingInfo) async {
    await Future.delayed(const Duration(milliseconds: 500));
    // 模拟确认寄送操作
  }

  @override
  Future<List<CategoryItem>> getRecycleCategories() async {
    await Future.delayed(const Duration(milliseconds: 300));
    
    return [
      const CategoryItem(
        id: 'phone',
        name: '手机',
        description: '智能手机回收',
        icon: 'phone',
        sort: 1,
        isActive: true,
      ),
      const CategoryItem(
        id: 'tablet',
        name: '平板',
        description: '平板电脑回收',
        icon: 'tablet',
        sort: 2,
        isActive: true,
      ),
      const CategoryItem(
        id: 'laptop',
        name: '笔记本',
        description: '笔记本电脑回收',
        icon: 'laptop',
        sort: 3,
        isActive: true,
      ),
    ];
  }

  @override
  Future<Map<String, dynamic>> getRecycleStats() async {
    await Future.delayed(const Duration(milliseconds: 300));
    
    return {
      'totalOrders': 156,
      'completedOrders': 128,
      'totalAmount': 25680.50,
      'thisMonthOrders': 23,
    };
  }

  @override
  Future<List<Map<String, dynamic>>> getRecycleProcess() async {
    await Future.delayed(const Duration(milliseconds: 200));
    
    return [
      {'step': 1, 'title': '提交订单', 'description': '填写设备信息，上传照片'},
      {'step': 2, 'title': '价格评估', 'description': '专业评估师给出回收价格'},
      {'step': 3, 'title': '确认寄送', 'description': '同意价格后寄送设备'},
      {'step': 4, 'title': '检测确认', 'description': '收到设备后进行最终检测'},
      {'step': 5, 'title': '完成交易', 'description': '确认无误后转账到账'},
    ];
  }

  @override
  Future<List<RecycleOrder>> getRecentOrders({int limit = 5}) async {
    await Future.delayed(const Duration(milliseconds: 300));
    
    final mockOrders = _generateMockOrders();
    return mockOrders.take(limit).toList();
  }

  /// 生成模拟订单数据
  List<RecycleOrder> _generateMockOrders() {
    final now = DateTime.now();
    
    return [
      RecycleOrder(
        id: 'RO202412270001',
        userId: 'user123',
        brandName: 'iPhone',
        model: '14 Pro Max',
        categoryName: '手机',
        conditionDescription: '外观良好，功能正常',
        estimatedPrice: 4500.00,
        finalPrice: 4200.00,
        orderStatus: 'COMPLETED',
        orderStatusDesc: '已完成',
        createTime: now.subtract(const Duration(days: 2)).millisecondsSinceEpoch,
        updateTime: now.subtract(const Duration(hours: 6)).millisecondsSinceEpoch,
        mainImage: 'https://example.com/iphone14pro.jpg',
      ),
      RecycleOrder(
        id: 'RO202412270002',
        userId: 'user123',
        brandName: 'Samsung',
        model: 'Galaxy S23',
        categoryName: '手机',
        conditionDescription: '屏幕有轻微划痕',
        estimatedPrice: 3200.00,
        reviewedPrice: 2800.00,
        orderStatus: 'PRICE_QUOTED',
        orderStatusDesc: '已报价',
        createTime: now.subtract(const Duration(days: 1)).millisecondsSinceEpoch,
        updateTime: now.subtract(const Duration(hours: 2)).millisecondsSinceEpoch,
      ),
      RecycleOrder(
        id: 'RO202412270003',
        userId: 'user123',
        brandName: 'iPad',
        model: 'Air 5',
        categoryName: '平板',
        conditionDescription: '全新未拆封',
        estimatedPrice: 3800.00,
        orderStatus: 'PENDING_APPROVAL',
        orderStatusDesc: '待审核',
        createTime: now.subtract(const Duration(hours: 12)).millisecondsSinceEpoch,
        updateTime: now.subtract(const Duration(hours: 12)).millisecondsSinceEpoch,
      ),
      RecycleOrder(
        id: 'RO202412270004',
        userId: 'user123',
        brandName: 'MacBook',
        model: 'Pro 16"',
        categoryName: '笔记本',
        conditionDescription: '使用正常，有使用痕迹',
        estimatedPrice: 8500.00,
        orderStatus: 'SHIPPING_CONFIRMED',
        orderStatusDesc: '已寄送',
        createTime: now.subtract(const Duration(hours: 8)).millisecondsSinceEpoch,
        updateTime: now.subtract(const Duration(hours: 4)).millisecondsSinceEpoch,
      ),
      RecycleOrder(
        id: 'RO202412270005',
        userId: 'user123',
        brandName: 'Xiaomi',
        model: '13 Pro',
        categoryName: '手机',
        conditionDescription: '后盖有裂纹',
        estimatedPrice: 1800.00,
        orderStatus: 'CANCELLED',
        orderStatusDesc: '已取消',
        createTime: now.subtract(const Duration(days: 3)).millisecondsSinceEpoch,
        updateTime: now.subtract(const Duration(days: 2)).millisecondsSinceEpoch,
      ),
    ];
  }
}

/// 回收远程数据源Provider
final recycleRemoteDataSourceProvider = Provider<RecycleRemoteDataSource>((ref) {
  return RecycleRemoteDataSourceImpl();
});
