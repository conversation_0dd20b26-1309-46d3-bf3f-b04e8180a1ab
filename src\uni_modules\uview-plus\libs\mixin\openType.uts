import { defineMixin } from '../vue.uts'

export const openType = defineMixin({
    props: {
        openType: String
    },
    methods: {
        onGetUserInfo(event: UniCustomEvent<UTSJSONObject>) {
            this.$emit('getuserinfo', event.detail)
        },
        onContact(event: UniCustomEvent<UTSJSONObject>) {
            this.$emit('contact', event.detail)
        },
        onGetPhoneNumber(event: UniCustomEvent<UTSJSONObject>) {
            this.$emit('getphonenumber', event.detail)
        },
        onError(event: UniCustomEvent<UTSJSONObject>) {
            this.$emit('error', event.detail)
        },
        onLaunchApp(event: UniCustomEvent<UTSJSONObject>) {
            this.$emit('launchapp', event.detail)
        },
        onOpenSetting(event: UniCustomEvent<UTSJSONObject>) {
            this.$emit('opensetting', event.detail)
        }
    }
})
