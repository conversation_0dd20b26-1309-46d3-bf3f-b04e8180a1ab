import { ref } from "vue"
import { useStore } from "vuex"

interface WebSocketOptions {
  url: string
  token?: string
}

export function useWebSocket(options: WebSocketOptions) {
  const store = useStore()
  const { url, token } = options
  const messages = ref<string[]>([])
  const socketTask = ref<any>(null)
  const timeCount = ref<any>(null)
  const isConnecting = ref(false)

  const cleanup = () => {
    if (socketTask.value) {
      uni.closeSocket()
      socketTask.value = null
    }
    if (timeCount.value) {
      clearTimeout(timeCount.value)
      timeCount.value = null
    }
    isConnecting.value = false
  }

  const connect = () => {
    console.log("WebSocket连接中...")
    if (store.state.$websocketConnected || isConnecting.value) return
    if (!token) {
      console.warn("未检测到token，无法建立WebSocket连接")
      return
    }

    isConnecting.value = true
    cleanup()

    socketTask.value = uni.connectSocket({ url })

    uni.onSocketOpen((res) => {
      store.commit("SET_WEBSOCKET_CONNECTED", true)
      isConnecting.value = false
      console.log("WebSocket连接已打开！")
      const msg = JSON.stringify({ token, type: "auth" })
      uni.sendSocketMessage({ data: msg })
    })

    uni.onSocketMessage((event: MessageEvent) => {
      try {
        const res = JSON.parse(event.data)
        if (res.code === 200) {
          const resData = res.data
          if (resData.messageType === "MESSAGE") {
            console.log(resData)
            store.dispatch("addMessage", resData.message)
          } else if (resData.messageType === "COUNT") {
            store.commit("SET_UNREAD_COUNT", resData.message.count)
          }
        }
      } catch (error) {
        uni.$emit("showNoticeBar:error", error)
      }
    })

    uni.onSocketError((res) => {
      store.commit("SET_WEBSOCKET_CONNECTED", false)
      isConnecting.value = false
      cleanup()
      timeCount.value = setTimeout(() => {
        connect()
      }, 2000)
    })

    uni.onSocketClose(() => {
      store.commit("SET_WEBSOCKET_CONNECTED", false)
      isConnecting.value = false
      console.log("WebSocket连接已关闭")
    })
  }

  return {
    messages,
    connect
  }
}
