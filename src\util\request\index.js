// 引入配置
import config from "@/common/config";
// 引入拦截器配置
import { requestInterceptors, responseInterceptors } from "./interceptors.js";
// 引入luch-request
import { http } from "@/uni_modules/uview-plus";
//  初始化请求配置
const initRequest = (vm) => {
  http.setConfig((defaultConfig) => {
    /* defaultConfig 为默认全局配置 */
    defaultConfig.baseURL = config.baseUrl; /* 根域名 */
    defaultConfig.timeout = config.timeout; /* 超时时间 */
    defaultConfig.withCredentials =
      config.withCredentials; /* 跨域请求是否携带凭证 */

    // 自动检测API服务可用性
    // checkApiAvailability();

    return defaultConfig;
  });
  requestInterceptors();
  responseInterceptors();
};

// 检测API服务可用性
const checkApiAvailability = () => {
  // 仅在开发环境下进行检测
  if (process.env.NODE_ENV === "development") {
    http
      .get("/health-check", { custom: { toast: false, catch: true } })
      .then(() => {
        console.log("API服务连接正常");
      })
      .catch((err) => {
        console.warn("API服务可能不可用，请确保服务已启动:", config.baseUrl);
      });
  }
};

export { initRequest };
