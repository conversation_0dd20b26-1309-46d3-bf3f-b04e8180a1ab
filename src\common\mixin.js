import store from '@/store'

export default {
    data() {
        return {
			isWeixin: true
        }
    },
    // onTabItemTap事件在点击底部tabbar时触发
    onTabItemTap(item) {
        // 检查是否点击的是需要登录的tabbar项
        if (
            (item.pagePath === 'pages/views/cart/cart') && 
            !store.getters.isLoggedIn
        ) {
            // 如果需要登录但用户未登录，阻止默认跳转行为并提示登录
            uni.showToast({
                title: '请先登录',
                icon: 'none',
                duration: 1500
            })
            
            // 延迟跳转到登录页面
            setTimeout(() => {
                uni.navigateTo({
                    url: '/pages/views/login/login'
                })
            }, 1500)
            
            // 返回到上一个页面或首页
            const pages = getCurrentPages()
            if (pages.length > 1) {
                uni.navigateBack()
            } else {
                uni.switchTab({
                    url: '/pages/views/home/<USER>'
                })
            }
        }
    }
}
