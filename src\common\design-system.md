# SOKO 特摄模玩应用 UX 设计规范

本文档定义了 SOKO 特摄模玩应用的 UX 设计规范，确保整个项目设计的一致性和用户体验的连贯性。

## 1. 排版系统

### 字体

- **主要字体**: Inter
- **备用字体**: 系统默认无衬线字体
- **应用范围**: 所有界面文本，包括标题、正文和按钮等

### 字体大小

| 名称 | 大小 | 用途 |
|------|------|------|
| 2xs | 10px | 极小文本，用于辅助信息 |
| xs | 12px | 小号文本，用于次要信息 |
| sm | 14px | 小号正文，用于辅助说明 |
| base | 16px | 标准正文，用于主要内容 |
| lg | 18px | 大号正文，用于强调内容 |
| xl | 20px | 小标题，用于卡片标题 |
| 2xl | 24px | 中标题，用于页面标题 |
| 3xl | 30px | 大标题，用于主要标题 |
| 4xl | 36px | 特大标题，用于营销页面 |

### 字重

| 名称 | 权重 | 用途 |
|------|------|------|
| Normal | 400 | 用于正文文本 |
| Medium | 500 | 用于强调和副标题 |
| Semibold | 600 | 用于小标题 |
| Bold | 700 | 用于主标题和强调 |

### 行高

| 名称 | 倍数 | 用途 |
|------|------|------|
| leading-none | 1 | 用于特殊场景 |
| leading-tight | 1.25 | 用于标题 |
| leading-snug | 1.375 | 用于短文本 |
| leading-normal | 1.5 | 用于正文 |
| leading-relaxed | 1.625 | 用于长文本 |

### 文本颜色

| 名称 | 颜色代码 | Tailwind类 | 用途 |
|------|----------|------------|------|
| 主要文本 | #333333 | text-text-primary | 用于主要内容 |
| 次要文本 | #666666 | text-text-secondary | 用于辅助内容 |
| 强调文本 | 主题色 | text-primary | 用于强调内容 |
| 禁用文本 | #A3A3A3 | text-text-disabled | 用于禁用状态 |

## 2. 间距系统

### 基础间距单位

基础单位为 4px (0.25rem)，所有间距都是这个基础单位的倍数。

### 间距比例

| 倍数 | 大小 | 用途 |
|------|------|------|
| 1 | 4px | 极小间距，用于紧凑元素内部 |
| 1.5 | 6px | 小间距，用于相关元素之间 |
| 2 | 8px | 常规间距，用于组件内元素 |
| 3 | 12px | 中等间距，用于相关组件之间 |
| 4 | 16px | 标准间距，用于组件之间 |
| 6 | 24px | 大间距，用于区块之间 |

### 组件内部间距

| 组件 | 间距 | 用途 |
|------|------|------|
| 按钮内边距 | 小(px-3 py-1.5) 默认(px-4 py-2) 大(px-6 py-3) | 按钮内部间距 |
| 卡片内边距 | 标准卡片(p-4) 紧凑卡片(p-3) | 卡片内部间距 |

### 布局间距

| 类型 | 间距 | 用途 |
|------|------|------|
| 页面边距 | 标准页面水平边距(px-4) 紧凑页面(px-3) | 页面水平边距 |
| 组件间距 | 组件垂直间距(space-y-4) 紧凑布局(space-y-3) | 组件之间的垂直间距 |

## 3. 颜色系统

### 主题色

| 名称 | 颜色代码 | Tailwind类 | 用途 |
|------|----------|------------|------|
| 奥特曼红 (Primary) | #EF4444 | text-primary, bg-primary | 主要按钮、重要元素 |
| 骑士绿 (Secondary) | #10B981 | text-secondary, bg-secondary | 次要按钮、成功状态 |
| 战队蓝 (Accent) | #3B82F6 | text-accent, bg-accent | 强调元素、链接 |

### 辅助色

| 名称 | 颜色代码 | Tailwind类 | 用途 |
|------|----------|------------|------|
| 英雄金 (Heroic) | #F59E0B | text-heroic, bg-heroic | 特殊元素、促销信息 |
| 反派紫 (Villain) | #8B5CF6 | text-villain, bg-villain | 高级功能、特殊状态 |
| 霓虹粉 (Neon) | #EC4899 | text-neon, bg-neon | 突出显示、活动标签 |

### 功能色

| 名称 | 颜色代码 | Tailwind类 | 用途 |
|------|----------|------------|------|
| 错误 | #FF4D4F | text-destructive, bg-destructive | 错误信息、警告提示 |
| 警告 | #FAAD14 | text-warning, bg-warning | 警告状态、注意事项 |
| 成功 | #52C41A | text-success, bg-success | 成功状态、完成提示 |
| 信息 | #06B6D4 | text-info, bg-info | 信息提示、通知 |

### 中性色

按照浅色到深色排列的梯度：50-100-200-300-400-500-600-700-800-900。

每种主题色都支持这些梯度变化，例如：`text-primary-50`、`bg-primary-100` 等。

### 渐变色

| 名称 | 渐变设置 | Tailwind类 | 用途 |
|------|----------|------------|------|
| 主要渐变 | from-primary to-neon | bg-gradient-to-r from-primary to-neon | 主要按钮、突出元素 |
| 次要渐变 | from-secondary to-energy | bg-gradient-to-r from-secondary to-energy | 次要按钮、特殊卡片 |
| 强调渐变 | from-accent to-cyber | bg-gradient-to-r from-accent to-cyber | 活动标签、特殊元素 |
| 特殊渐变 | from-heroic to-villain | bg-gradient-to-r from-heroic to-villain | 限时活动、特殊促销 |

## 4. 组件系统

### 按钮

**按钮变体**:
- 默认按钮：主要操作
- 次要按钮：次要操作
- 轮廓按钮：不强调的操作
- 幽灵按钮：背景透明的按钮
- 危险按钮：删除等危险操作
- 链接按钮：文本链接形式的按钮

**按钮尺寸**:
- 小型按钮：紧凑界面
- 默认按钮：常规使用
- 大型按钮：重要操作
- 图标按钮：仅图标的按钮

**按钮状态**:
- 正常状态：可点击
- 禁用状态：不可点击
- 渐变状态：使用渐变色
- 圆形状态：完全圆形的按钮

### 表单元素

**输入框**:
- 标准输入框：常规输入
- 带标签输入框：带有标签的输入框
- 禁用输入框：不可输入的状态

**开关**:
- 开启/关闭状态：切换功能的开关

**单选框和复选框**:
- 单选框组：互斥选项
- 复选框：可多选的项目

### 卡片和容器

**标准卡片**:
- 带有标题、描述、内容和底部操作区域的卡片

**简单容器**:
- 用于分组相关内容的简单容器

**次要容器**:
- 用于显示次要或辅助信息的容器

### 徽章和标签

**徽章变体**:
- 默认徽章：常规提示
- 次要徽章：次要提示
- 轮廓徽章：轮廓样式
- 危险徽章：警告或错误提示

**徽章样式**:
- 圆角徽章：常规样式
- 圆形徽章：圆形样式
- 带图标徽章：带有图标的徽章

### 图标

- 使用 fontawesome 图标库
- 标准尺寸：16px(小)、20px(默认)、24px(大)

## 5. 布局系统

### 屏幕尺寸

| 名称 | 尺寸 | 用途 |
|------|------|------|
| xs | 360px | 小型手机 |
| sm | 390px | 标准手机 |
| md | 430px | 大型手机 |
| lg | 768px | 平板设备 |
| xl | 1024px | 桌面设备 |

### 网格系统

- **基础网格**: 4列布局，适用于移动设备
- **响应式网格**: 小屏2列，大屏4列

### 导航布局

- **标准页面导航**: 顶部导航栏 + 内容区域
- **底部标签导航**: 内容区域 + 底部标签栏

### 安全区域

使用安全区域类确保内容不被设备特性遮挡：
- `pb-safe`: 底部安全区域
- `pt-safe-top`: 顶部安全区域
- `pl-safe-left`: 左侧安全区域
- `pr-safe-right`: 右侧安全区域

### 交互规范

- **触摸目标**: 最小触摸目标 44px × 44px，确保良好的可点击性
- **动画时长**: 
  - 快速(150ms): `animate-fast`
  - 标准(300ms): `animate-normal`
  - 缓慢(500ms): `animate-slow`

## 6. 圆角系统

### 基础圆角尺寸

| 名称 | 大小 | 用途 |
|------|------|------|
| none | 0px | 无圆角，用于特殊场景 |
| sm | 2px | 小圆角，用于小型元素 |
| DEFAULT | 4px | 标准圆角，用于标准元素 |
| md | 6px | 中等圆角，用于中型元素 |
| lg | 8px | 大圆角，用于大型元素 |
| xl | 12px | 特大圆角，用于卡片和面板 |
| 2xl | 16px | 超大圆角，用于大型卡片 |
| 3xl | 24px | 极大圆角，用于特大型容器 |

### 特殊圆角

- **full (9999px)**: 完全圆角，用于按钮、徽章和头像
- **圆形元素**: 宽高相等的圆形，用于图标按钮和头像
- **顶部圆角 (rounded-t-xl)**: 仅顶部圆角，用于模态框和底部表单

### 组件圆角应用指南

**按钮圆角**:
- 默认按钮: rounded (4px)
- 中等圆角按钮: rounded-lg (8px)
- 大圆角按钮: rounded-xl (12px)
- 全圆角按钮: rounded-full (9999px)

推荐使用 rounded-lg 或 rounded-full 作为按钮的标准圆角。

**卡片圆角**:
- 标准卡片: rounded-lg (8px)
- 大圆角卡片: rounded-xl (12px)

推荐使用 rounded-xl 或 rounded-2xl 作为卡片的标准圆角。

**其他组件圆角**:
- 输入框和表单元素: rounded-lg
- 图片和媒体: rounded-lg 或 rounded-xl
- 模态框和弹出窗口: rounded-2xl
- 徽章和标签: rounded-full

### 圆角使用原则

- **一致性原则**: 在整个应用中保持圆角的一致性，同类组件使用相同的圆角值
- **层次感**: 主要内容区域使用较小的圆角，浮动元素和强调元素使用较大的圆角
- **品牌特性**: 特摄模玩应用的圆角设计应体现年轻、活力的品牌特性
- **可访问性**: 适当的圆角可以引导用户视线、区分功能区域、减轻视觉疲劳

## 7. 设计原则

1. **一致性**: 保持视觉和交互的一致性，减少用户的认知负担，提高使用效率。
2. **简洁性**: 简化界面和交互，去除不必要的元素，让用户专注于核心任务。
3. **反馈性**: 为用户操作提供及时、清晰的反馈，增强用户对系统的信任感。
4. **可访问性**: 确保不同能力的用户都能有效地使用产品，包括色彩对比和触摸目标大小。
5. **效率**: 优化用户流程，减少完成任务所需的步骤，提高用户效率。
6. **愉悦性**: 创造愉悦的用户体验，通过精心设计的动画和视觉效果增强情感连接。

## 8. 实施指南

在实施此设计规范时，请使用以下方法：

1. **使用预定义的 Tailwind 类**: 项目中已配置好所有颜色和尺寸，直接使用类如 `text-primary`、`bg-secondary` 等。

2. **使用 SCSS 变量**: 对于需要自定义样式的场景，可以使用 `uni.scss` 中定义的变量。

3. **组件化开发**: 尽可能使用和创建可重用组件，确保界面一致性。

4. **响应式设计**: 使用响应式工具类确保应用在不同设备上的表现良好。

5. **遵循特摄主题**: 在设计中融入特摄元素，增强品牌辨识度和用户体验。 