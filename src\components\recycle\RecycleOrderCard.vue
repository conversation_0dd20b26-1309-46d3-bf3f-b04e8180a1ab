<template>
  <view
    class="bg-white rounded-lg p-4 shadow-sm active:bg-gray-50 transition-colors"
    @click="$emit('click')"
  >
    <!-- 订单头部 -->
    <view class="flex justify-between items-start mb-3">
      <view class="flex-1">
        <text class="text-base font-medium text-gray-800 block">
          {{ order.brandName }} {{ order.model }}
        </text>
        <text class="text-sm text-gray-500">
          {{ formatTime(order.createTime) }}
        </text>
      </view>
      <view
        class="px-2 py-1 rounded-full text-xs font-medium"
        :class="getStatusClass(order.orderStatus)"
      >
        {{ order.orderStatusDesc }}
      </view>
    </view>

    <!-- 订单内容 -->
    <view class="flex items-center space-x-3">
      <image
        :src="order.mainImage || '/static/default-product.png'"
        class="w-16 h-16 rounded-lg object-cover flex-shrink-0"
        mode="aspectFill"
      />
      <view class="flex-1 min-w-0">
        <text class="text-sm text-gray-600 line-clamp-2 block">
          {{ order.productDesc || '暂无描述' }}
        </text>
        <view class="flex justify-between items-center mt-2">
          <text class="text-sm text-gray-500">
            期望价格: ¥{{ order.estimatedPrice }}
          </text>
          <text
            v-if="order.finalPrice"
            class="text-lg font-semibold text-green-600"
          >
            ¥{{ order.finalPrice }}
          </text>
        </view>
      </view>
    </view>

    <!-- 操作按钮 -->
    <view v-if="showActions && availableActions.length > 0" class="flex justify-end space-x-2 mt-3 pt-3 border-t border-gray-100">
      <button
        v-for="action in availableActions"
        :key="action.key"
        class="px-4 py-2 rounded-lg text-sm font-medium transition-colors"
        :class="getActionClass(action.type)"
        @click.stop="$emit('action', action.key)"
      >
        {{ action.label }}
      </button>
    </view>
  </view>
</template>

<script setup lang="ts">
import { computed } from 'vue'
import { recycleUtils } from '@/api/recycle'
import type { RecycleOrder } from '@/api/recycle'

// Props
interface Props {
  order: RecycleOrder
  showActions?: boolean
}

const props = withDefaults(defineProps<Props>(), {
  showActions: false
})

// Emits
defineEmits<{
  click: []
  action: [actionKey: string]
}>()

// 计算属性
const availableActions = computed(() => {
  const status = props.order.orderStatus
  const actionsMap: Record<string, Array<{ key: string; label: string; type: string }>> = {
    'PENDING_APPROVAL': [
      { key: 'cancel', label: '取消订单', type: 'danger' }
    ],
    'PRICE_QUOTED': [
      { key: 'cancel', label: '取消订单', type: 'danger' },
      { key: 'confirm_shipment', label: '确认寄送', type: 'primary' }
    ],
    'RECEIVED': [
      { key: 'request_return', label: '申请退回', type: 'warning' }
    ]
  }
  return actionsMap[status] || []
})

// 方法
const getStatusClass = (status: string): string => {
  return recycleUtils.getStatusClass(status)
}

const getActionClass = (type: string): string => {
  const classMap: Record<string, string> = {
    'primary': 'bg-primary-500 text-white active:bg-primary-600',
    'danger': 'bg-red-500 text-white active:bg-red-600',
    'warning': 'bg-yellow-500 text-white active:bg-yellow-600',
    'default': 'bg-gray-200 text-gray-800 active:bg-gray-300'
  }
  return classMap[type] || classMap.default
}

const formatTime = (timestamp: number): string => {
  return recycleUtils.formatTime(timestamp)
}
</script>

<style lang="scss" scoped>
.line-clamp-2 {
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}
</style>
