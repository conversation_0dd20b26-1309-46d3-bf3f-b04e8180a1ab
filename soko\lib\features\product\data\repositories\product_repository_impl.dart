import 'package:flutter_riverpod/flutter_riverpod.dart';

import 'package:soko/features/product/domain/entities/product_models.dart';
import 'package:soko/features/product/domain/repositories/product_repository.dart';
import 'package:soko/features/product/data/datasources/product_remote_datasource.dart';
import 'package:soko/core/models/paginated_data.dart';

/// 商品业务仓库实现
class ProductRepositoryImpl implements ProductRepository {
  const ProductRepositoryImpl(this._remoteDataSource);

  final ProductRemoteDataSource _remoteDataSource;

  @override
  Future<PaginatedData<Product>> searchProducts(ProductSearchRequest request) async {
    try {
      return await _remoteDataSource.searchProducts(request);
    } catch (error) {
      rethrow;
    }
  }

  @override
  Future<Product> getProductDetail(String productId) async {
    try {
      return await _remoteDataSource.getProductDetail(productId);
    } catch (error) {
      rethrow;
    }
  }

  @override
  Future<List<ProductCategory>> getCategories() async {
    try {
      return await _remoteDataSource.getCategories();
    } catch (error) {
      rethrow;
    }
  }

  @override
  Future<PaginatedData<Product>> getProductsByCategory(
    String categoryId, {
    int page = 1,
    int pageSize = 20,
    String? sortBy,
    String? sortOrder,
  }) async {
    try {
      return await _remoteDataSource.getProductsByCategory(
        categoryId,
        page,
        pageSize,
        sortBy,
        sortOrder,
      );
    } catch (error) {
      rethrow;
    }
  }

  @override
  Future<PaginatedData<Product>> getProductsByBrand(
    String brandId, {
    int page = 1,
    int pageSize = 20,
    String? sortBy,
    String? sortOrder,
  }) async {
    try {
      return await _remoteDataSource.getProductsByBrand(
        brandId,
        page,
        pageSize,
        sortBy,
        sortOrder,
      );
    } catch (error) {
      rethrow;
    }
  }

  @override
  Future<List<Product>> getRecommendedProducts({
    String? categoryId,
    String? userId,
    int limit = 10,
  }) async {
    try {
      return await _remoteDataSource.getRecommendedProducts(
        categoryId,
        userId,
        limit,
      );
    } catch (error) {
      rethrow;
    }
  }

  @override
  Future<List<Product>> getPopularProducts({
    String? categoryId,
    int limit = 10,
  }) async {
    try {
      return await _remoteDataSource.getPopularProducts(categoryId, limit);
    } catch (error) {
      rethrow;
    }
  }

  @override
  Future<List<Product>> getLatestProducts({
    String? categoryId,
    int limit = 10,
  }) async {
    try {
      return await _remoteDataSource.getLatestProducts(categoryId, limit);
    } catch (error) {
      rethrow;
    }
  }

  @override
  Future<List<Product>> getSimilarProducts(
    String productId, {
    int limit = 10,
  }) async {
    try {
      return await _remoteDataSource.getSimilarProducts(productId, limit);
    } catch (error) {
      rethrow;
    }
  }

  @override
  Future<void> addToFavorites(String productId) async {
    try {
      await _remoteDataSource.addToFavorites(productId);
    } catch (error) {
      rethrow;
    }
  }

  @override
  Future<void> removeFromFavorites(String productId) async {
    try {
      await _remoteDataSource.removeFromFavorites(productId);
    } catch (error) {
      rethrow;
    }
  }

  @override
  Future<PaginatedData<Product>> getFavoriteProducts({
    int page = 1,
    int pageSize = 20,
  }) async {
    try {
      return await _remoteDataSource.getFavoriteProducts(page, pageSize);
    } catch (error) {
      rethrow;
    }
  }

  @override
  Future<bool> isFavorite(String productId) async {
    try {
      return await _remoteDataSource.isFavorite(productId);
    } catch (error) {
      rethrow;
    }
  }

  @override
  Future<void> incrementViewCount(String productId) async {
    try {
      await _remoteDataSource.incrementViewCount(productId);
    } catch (error) {
      rethrow;
    }
  }

  @override
  Future<List<Product>> getViewHistory({
    int limit = 20,
  }) async {
    try {
      return await _remoteDataSource.getViewHistory(limit);
    } catch (error) {
      rethrow;
    }
  }

  @override
  Future<List<String>> getSearchSuggestions(String keyword) async {
    try {
      return await _remoteDataSource.getSearchSuggestions(keyword);
    } catch (error) {
      rethrow;
    }
  }

  @override
  Future<List<String>> getHotSearchKeywords({
    int limit = 10,
  }) async {
    try {
      return await _remoteDataSource.getHotSearchKeywords(limit);
    } catch (error) {
      rethrow;
    }
  }
}

/// 商品仓库Provider
final productRepositoryProvider = Provider<ProductRepository>((ref) {
  final remoteDataSource = ref.watch(productRemoteDataSourceProvider);
  return ProductRepositoryImpl(remoteDataSource);
});
