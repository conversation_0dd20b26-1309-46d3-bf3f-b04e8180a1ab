<template>
    <button
        :hover-start-time="hoverStartTime"
        :hover-stay-time="hoverStayTime"
        :form-type="formType"
        :open-type="openType"
        :app-parameter="appParameter"
        :hover-stop-propagation="hoverStopPropagation"
        :send-message-title="sendMessageTitle"
        :send-message-path="sendMessagePath"
        :lang="lang"
        :data-name="dataName"
        :session-from="sessionFrom"
        :send-message-img="sendMessageImg"
        :show-message-card="showMessageCard"
        @getphonenumber="getphonenumber"
        @getuserinfo="getuserinfo"
        @error="error"
        @opensetting="opensetting"
        @launchapp="launchapp"
        :hover-class="!disabled && !loading ? 'up-button--active' : ''"
        class="up-button up-reset-button"
        :style="[baseColor, nvueTextStyle, addStyle(customStyle)]"
        @click="clickHandler"
        :class="bemClass"
    >
        <template v-if="loading">
            <up-loading-icon
                :mode="loadingMode"
                :size="addUnit(loadingSize * 1.15)"
                :color="loadingColor"
            ></up-loading-icon>
            <text
                class="up-button__text up-button__loading-text"
                :style="[{ fontSize: textSize + 'px' }, nvueTextStyle]"
                >{{ loadingText != '' ? loadingText : text }}</text
            >
        </template>
        <template v-else>
            <up-icon
                v-if="icon"
                :name="icon"
                :color="iconColorCom"
                :size="addUnit(parseInt(textSize) * 1.35)"
                :customStyle="{ marginRight: '2px' }"
            ></up-icon>
            <slot>
                <text
                    class="up-button__text"
                    :style="[{ fontSize: textSize + 'px'}, nvueTextStyle]"
                    >
                    {{ text }}
                </text>
            </slot>
        </template>
    </button>
</template>

<script lang="uts">
import { buttonMixin } from "../../libs/mixin/button";
import { openType } from "../../libs/mixin/openType";
import { mpMixin } from '../../libs/mixin/mpMixin';
import { mixin } from '../../libs/mixin/mixin';
import { propsButton } from "./props";
import config from '../../libs/config/config';
import { addStyle, addUnit } from '../../libs/function/index';
import { throttle } from '../../libs/function/throttle';
/**
 * button 按钮
 * @<NAME_EMAIL> 2024
 * @description Button 按钮
 * @tutorial https://ijry.github.io/uview-plus/components/button.html
 *
 * @property {Boolean}			hairline				是否显示按钮的细边框 (默认 true )
 * @property {String}			type					按钮的预置样式，info，primary，error，warning，success (默认 'info' )
 * @property {String}			size					按钮尺寸，large，normal，mini （默认 normal）
 * @property {String}			shape					按钮形状，circle（两边为半圆），square（带圆角） （默认 'square' ）
 * @property {Boolean}			plain					按钮是否镂空，背景色透明 （默认 false）
 * @property {Boolean}			disabled				是否禁用 （默认 false）
 * @property {Boolean}			loading					按钮名称前是否带 loading 图标(App-nvue 平台，在 ios 上为雪花，Android上为圆圈) （默认 false）
 * @property {String}	        loadingText				加载中提示文字
 * @property {String}			loadingMode				加载状态图标类型 （默认 'spinner' ）
 * @property {Number}	        loadingSize				加载图标大小 （默认 15 ）
 * @property {String}			openType				开放能力，具体请看uniapp稳定关于button组件部分说明
 * @property {String}			formType				用于 <form> 组件，点击分别会触发 <form> 组件的 submit/reset 事件
 * @property {String}			appParameter			打开 APP 时，向 APP 传递的参数，open-type=launchApp时有效 （注：只微信小程序、QQ小程序有效）
 * @property {Boolean}			hoverStopPropagation	指定是否阻止本节点的祖先节点出现点击态，微信小程序有效（默认 true ）
 * @property {String}			lang					指定返回用户信息的语言，zh_CN 简体中文，zh_TW 繁体中文，en 英文（默认 en ）
 * @property {String}			sessionFrom				会话来源，openType="contact"时有效
 * @property {String}			sendMessageTitle		会话内消息卡片标题，openType="contact"时有效
 * @property {String}			sendMessagePath			会话内消息卡片点击跳转小程序路径，openType="contact"时有效
 * @property {String}			sendMessageImg			会话内消息卡片图片，openType="contact"时有效
 * @property {Boolean}			showMessageCard			是否显示会话内消息卡片，设置此参数为 true，用户进入客服会话会在右下角显示"可能要发送的小程序"提示，用户点击后可以快速发送小程序消息，openType="contact"时有效（默认false）
 * @property {String}			dataName				额外传参参数，用于小程序的data-xxx属性，通过target.dataset.name获取
 * @property {String | Number}	throttleTime			节流，一定时间内只能触发一次 （默认 0 )
 * @property {String | Number}	hoverStartTime			按住后多久出现点击态，单位毫秒 （默认 0 )
 * @property {String | Number}	hoverStayTime			手指松开后点击态保留时间，单位毫秒 （默认 200 )
 * @property {String | Number}	text					按钮文字，之所以通过props传入，是因为slot传入的话（注：nvue中无法控制文字的样式）
 * @property {String}			icon					按钮图标
 * @property {String}			iconColor				按钮图标颜色
 * @property {String}			color					按钮颜色，支持传入linear-gradient渐变色
 * @property {Object}			customStyle				定义需要用到的外部样式
 *
 * @event {Function}	click			非禁止并且非加载中，才能点击
 * @event {Function}	getphonenumber	open-type="getPhoneNumber"时有效
 * @event {Function}	getuserinfo		用户点击该按钮时，会返回获取到的用户信息，从返回参数的detail中获取到的值同uni.getUserInfo
 * @event {Function}	error			当使用开放能力时，发生错误的回调
 * @event {Function}	opensetting		在打开授权设置页并关闭后回调
 * @event {Function}	launchapp		打开 APP 成功的回调
 * @example <up-button>月落</up-button>
 */
export default {
    name: "up-button",
    // #ifdef MP
    mixins: [mpMixin, mixin, buttonMixin, openType, propsButton],
    // #endif
    // #ifndef MP
    mixins: [mpMixin, mixin, propsButton],
    // #endif
    computed: {
        // 生成bem风格的类名
        bemClass(): String {
            // this.bem为一个computed变量，在mixin中
			let ret = ""
            if (this.color == '') {
                ret = this.bem(
                    "button",
                    [this.type, this.shape, this.size] ,
                    [
						["disabled", this.disabled],
						["plain", this.plain],
						["hairline", this.hairline],
					]
                );
				// console.log(ret)
            } else {
                // 由于nvue的原因，在有color参数时，不需要传入type，否则会生成type相关的类型，影响最终的样式
                ret = this.bem(
                    "button",
                    [this.shape, this.size] ,
                    [
                    	["disabled", this.disabled],
                    	["plain", this.plain],
                    	["hairline", this.hairline],
                    ]
                );
            }
			return ret
        },
        loadingColor(): string {
            if (this.plain) {
                // 如果有设置color值，则用color值，否则使用type主题颜色
                return this.color != ''
                    ? this.color
                    : config.getString(`color.up-${this.type}`) as String;
            }
            if (this.type == "info") {
                return "#c9c9c9";
            }
            return "rgb(200, 200, 200)";
        },
        iconColorCom(): String {
            // 如果是镂空状态，设置了color就用color值，否则使用主题颜色，
            // up-icon的color能接受一个主题颜色的值
			if (this.iconColor != '') return this.iconColor;
			if (this.plain) {
                return this.color != '' ? this.color : this.type;
            } else {
                return "";
            }
        },
        baseColor(): UTSJSONObject {
            let style = {
				color: ''
			} as UTSJSONObject;
            if (this.color != '') {
                // 针对自定义了color颜色的情况，镂空状态下，就是用自定义的颜色
                style['color'] = this.plain ? this.color : "white";
                if (!this.plain) {
                    // 非镂空，背景色使用自定义的颜色
                    style["backgroundColor"] = this.color;
                }
                if (this.color.indexOf("gradient") !== -1) {
                    // 如果自定义的颜色为渐变色，不显示边框，以及通过backgroundImage设置渐变色
                    // weex文档说明可以写borderWidth的形式，为什么这里需要分开写？
                    // 因为weex是阿里巴巴为了部门业绩考核而做的你懂的东西，所以需要这么写才有效
                    style['borderTopWidth'] = 0;
                    style['borderRightWidth'] = 0;
                    style['borderBottomWidth'] = 0;
                    style['borderLeftWidth'] = 0;
                    if (!this.plain) {
                        style['backgroundImage'] = this.color;
                    }
                } else {
                    // 非渐变色，则设置边框相关的属性
                    style['borderColor'] = this.color;
                    style['borderWidth'] = "1px";
                    style['borderStyle'] = "solid";
                }
            }
            return style;
        },
        // nvue版本按钮的字体不会继承父组件的颜色，需要对每一个text组件进行单独的设置
        nvueTextStyle(): UTSJSONObject {
            let style = {} as UTSJSONObject;
            // 针对自定义了color颜色的情况，镂空状态下，就是用自定义的颜色
            if (this.type === "info") {
                style['color'] = "#323233";
            }
			// console.debug(this.color)
            if (this.color != '') {
                style['color'] = this.plain ? this.color : "white";
            }
            style['fontSize'] = this.textSize + "px";
            return style;
        },
        // 字体大小
        textSize(): String {
            let fontSize = '14px';
            if (this.size === "large") fontSize = '16px';
            if (this.size === "normal") fontSize = '14px';
            if (this.size === "small") fontSize = '12px';
            if (this.size === "mini") fontSize = '10px';
            return fontSize;
        },
    },
	emits: ['click', 'getphonenumber', 'getuserinfo',
		'error', 'opensetting', 'launchapp'],
    methods: {
        addUnit(val: any): any {
			return addUnit(val)
		},
		addStyle(val: any): any {
			return addStyle(val)
		},
        clickHandler(): void {
            // 非禁止并且非加载中，才能点击
            if (!this.disabled && !this.loading) {
				console.log('点击');
				// 进行节流控制，每throttle毫秒内，只在开始处执行
				throttle(() => {
					this.$emit("click");
				}, this.throttleTime);
            } else {
				console.log('禁止点击');
			}
        },
        // 下面为对接uniapp官方按钮开放能力事件回调的对接
        getphonenumber(res: UTSJSONObject): void {
            this.$emit("getphonenumber", res);
        },
        getuserinfo(res: UTSJSONObject): void {
            this.$emit("getuserinfo", res);
        },
        error(res: UTSJSONObject): void {
            this.$emit("error", res);
        },
        opensetting(res: UTSJSONObject): void {
            this.$emit("opensetting", res);
        },
        launchapp(res: UTSJSONObject): void {
            this.$emit("launchapp", res);
        },
    },
};
</script>

<style lang="scss" scoped>
@import "../../libs/css/components.scss";

$up-button-up-button-height: 40px !default;
$up-button-text-font-size: 15px !default;
$up-button-loading-text-font-size: 15px !default;
$up-button-loading-text-margin-left: 4px !default;
$up-button-large-width: 100% !default;
$up-button-large-height: 50px !default;
$up-button-normal-padding: 0 12px !default;
$up-button-large-padding: 0 15px !default;
$up-button-normal-font-size: 14px !default;
$up-button-small-min-width: 60px !default;
$up-button-small-height: 30px !default;
$up-button-small-padding: 0px 8px !default;
$up-button-mini-padding: 0px 8px !default;
$up-button-small-font-size: 12px !default;
$up-button-mini-height: 22px !default;
$up-button-mini-font-size: 10px !default;
$up-button-mini-min-width: 50px !default;
$up-button-disabled-opacity: 0.5 !default;
$up-button-info-color: #323233 !default;
$up-button-info-background-color: #fff !default;
$up-button-info-border-color: #ebedf0 !default;
$up-button-info-border-width: 1px !default;
$up-button-info-border-style: solid !default;
$up-button-success-color: #fff !default;
$up-button-success-background-color: $up-success !default;
$up-button-success-border-color: $up-button-success-background-color !default;
$up-button-success-border-width: 1px !default;
$up-button-success-border-style: solid !default;
$up-button-primary-color: #fff !default;
$up-button-primary-background-color: $up-primary !default;
$up-button-primary-border-color: $up-button-primary-background-color !default;
$up-button-primary-border-width: 1px !default;
$up-button-primary-border-style: solid !default;
$up-button-error-color: #fff !default;
$up-button-error-background-color: $up-error !default;
$up-button-error-border-color: $up-button-error-background-color !default;
$up-button-error-border-width: 1px !default;
$up-button-error-border-style: solid !default;
$up-button-warning-color: #fff !default;
$up-button-warning-background-color: $up-warning !default;
$up-button-warning-border-color: $up-button-warning-background-color !default;
$up-button-warning-border-width: 1px !default;
$up-button-warning-border-style: solid !default;
$up-button-block-width: 100% !default;
$up-button-circle-border-top-right-radius: 100px !default;
$up-button-circle-border-top-left-radius: 100px !default;
$up-button-circle-border-bottom-left-radius: 100px !default;
$up-button-circle-border-bottom-right-radius: 100px !default;
$up-button-square-border-top-right-radius: 3px !default;
$up-button-square-border-top-left-radius: 3px !default;
$up-button-square-border-bottom-left-radius: 3px !default;
$up-button-square-border-bottom-right-radius: 3px !default;
$up-button-icon-min-width: 4px !default;
$up-button-plain-background-color: #fff !default;
$up-button-hairline-border-width: 0.5px !default;
$up-button-active-opacity:0.75 !default;
$up-button-before-top:50% !default;
$up-button-before-left:50% !default;
$up-button-before-width:100% !default;
$up-button-before-height:100% !default;
$up-button-before-transform:translate(-50%, -50%) !default;
$up-button-before-opacity:0 !default;
$up-button-before-background-color:#000 !default;
$up-button-before-border-color:#000 !default;
$up-button-active-before-opacity:.15 !default;
$up-button-icon-margin-left:4px !default;
$up-button-plain-up-button-info-color:#000000 !default;;
$up-button-plain-up-button-success-color:$up-success !default;;
$up-button-plain-up-button-error-color:$up-error !default;;
$up-button-plain-up-button-warning-color:$up-error !default;;

.up-button {
    height: $up-button-up-button-height;
    position: relative;
	box-sizing: border-box;

    &__content {
        width: 100%;
        height: 100%;
        @include flex;
        align-items: center;
        justify-content: center;
        flex-direction: row;
    }

    &__text {
        font-size: $up-button-text-font-size;
		color: #fff;
    }

    &__loading-text {
        font-size: $up-button-loading-text-font-size;
        margin-left: $up-button-loading-text-margin-left;
    }

    &--large {
        width: $up-button-large-width;
        height: $up-button-large-height;
        padding: $up-button-large-padding;
    }

    &--normal {
        padding: $up-button-normal-padding;
        font-size: $up-button-normal-font-size;
    }

    &--small {
        min-width: $up-button-small-min-width;
        height: $up-button-small-height;
        padding: $up-button-small-padding;
        font-size: $up-button-small-font-size;
    }

    &--mini {
        height: $up-button-mini-height;
        font-size: $up-button-mini-font-size;
        min-width: $up-button-mini-min-width;
        padding: $up-button-mini-padding;
    }

    &--disabled {
        opacity: $up-button-disabled-opacity;
    }

	&--active {
		opacity: $up-button-active-opacity;
	}

    &--info {
        .up-button__text {
            color: $up-button-info-color;
        }
        background-color: $up-button-info-background-color;
        border-color: $up-button-info-border-color;
        border-width: $up-button-info-border-width;
        border-style: $up-button-info-border-style;
    }

    &--success {
        .up-button__text {
            color: $up-button-success-color;
        }
        background-color: $up-button-success-background-color;
        border-color: $up-button-success-border-color;
        border-width: $up-button-success-border-width;
        border-style: $up-button-success-border-style;
    }

    &--primary {
        .up-button__text {
            color: $up-button-primary-color;
        }
        background-color: $up-button-primary-background-color;
        border-color: $up-button-primary-border-color;
        border-width: $up-button-primary-border-width;
        border-style: $up-button-primary-border-style;
    }

    &--error {
        .up-button__text {
            color: $up-button-error-color;
        }
        background-color: $up-button-error-background-color;
        border-color: $up-button-error-border-color;
        border-width: $up-button-error-border-width;
        border-style: $up-button-error-border-style;
    }

    &--warning {
        .up-button__text {
            color: $up-button-warning-color;
        }
        background-color: $up-button-warning-background-color;
        border-color: $up-button-warning-border-color;
        border-width: $up-button-warning-border-width;
        border-style: $up-button-warning-border-style;
    }

    &--block {
        @include flex;
        width: $up-button-block-width;
    }

    &--circle {
        border-top-right-radius: $up-button-circle-border-top-right-radius;
        border-top-left-radius: $up-button-circle-border-top-left-radius;
        border-bottom-left-radius: $up-button-circle-border-bottom-left-radius;
        border-bottom-right-radius: $up-button-circle-border-bottom-right-radius;
    }

    &--square {
        border-bottom-left-radius: $up-button-square-border-top-right-radius;
        border-bottom-right-radius: $up-button-square-border-top-left-radius;
        border-top-left-radius: $up-button-square-border-bottom-left-radius;
        border-top-right-radius: $up-button-square-border-bottom-right-radius;
    }

    &__icon {
        min-width: $up-button-icon-min-width;
        // line-height: inherit !important;
        // vertical-align: top;
    }

    &--plain {
        background-color: $up-button-plain-background-color;
        &.up-button--primary {
            .up-button__text {
                color: $up-primary;
            }
		}
        &.up-button--info {
            .up-button__text {
			    color:$up-button-plain-up-button-info-color;
            }
		}
        &.up-button--success {
            .up-button__text {
			    color:$up-button-plain-up-button-success-color;
            }
		}
        &.up-button--error {
            .up-button__text {
			    color:$up-button-plain-up-button-error-color;
            }
		}
        &.up-button--warning {
            .up-button__text {
			    color:$up-button-plain-up-button-warning-color;
            }
		}
    }

    &--hairline {
        border-width: $up-button-hairline-border-width !important;
    }

    &:before {
		position: absolute;
		top:$up-button-before-top;
		left:$up-button-before-left;
		width:$up-button-before-width;
		height:$up-button-before-height;
		/* #ifndef UNI-APP-X */
		border: inherit;
		border-radius: inherit;
		/* #endif */
		transform:$up-button-before-transform;
		opacity:$up-button-before-opacity;
		// content: " ";
		background-color:$up-button-before-background-color;
		border-color:$up-button-before-border-color;
	}
	
	&--active {
		&:before {
			opacity: .15
		}
	}
	
	/* #ifndef UNI-APP-X */
	&__icon+&__text:not(:empty),
	/* #endif */
	&__loading-text {
		margin-left:$up-button-icon-margin-left;
	}
}

</style>
