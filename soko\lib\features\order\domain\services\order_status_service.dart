import 'package:soko/core/enums/app_enums.dart';

/// 订单状态服务
/// 负责订单状态的验证、流转和业务逻辑处理
class OrderStatusService {
  /// 获取订单状态的显示文本
  static String getStatusDisplayText(OrderStatus status) {
    switch (status) {
      case OrderStatus.pending:
        return '待付款';
      case OrderStatus.paid:
        return '已付款';
      case OrderStatus.shipped:
        return '已发货';
      case OrderStatus.delivered:
        return '已送达';
      case OrderStatus.completed:
        return '已完成';
      case OrderStatus.cancelled:
        return '已取消';
      case OrderStatus.refunded:
        return '已退款';
    }
  }

  /// 获取订单状态的描述信息
  static String getStatusDescription(OrderStatus status) {
    switch (status) {
      case OrderStatus.pending:
        return '订单已创建，等待付款';
      case OrderStatus.paid:
        return '订单已付款，等待发货';
      case OrderStatus.shipped:
        return '订单已发货，正在配送中';
      case OrderStatus.delivered:
        return '订单已送达，等待确认收货';
      case OrderStatus.completed:
        return '订单已完成';
      case OrderStatus.cancelled:
        return '订单已取消';
      case OrderStatus.refunded:
        return '订单已退款';
    }
  }

  /// 获取订单状态的颜色
  static String getStatusColor(OrderStatus status) {
    switch (status) {
      case OrderStatus.pending:
        return '#FF9800'; // 橙色
      case OrderStatus.paid:
        return '#2196F3'; // 蓝色
      case OrderStatus.shipped:
        return '#4CAF50'; // 绿色
      case OrderStatus.delivered:
        return '#4CAF50'; // 绿色
      case OrderStatus.completed:
        return '#4CAF50'; // 绿色
      case OrderStatus.cancelled:
        return '#9E9E9E'; // 灰色
      case OrderStatus.refunded:
        return '#9C27B0'; // 紫色
    }
  }

  /// 检查订单状态是否可以取消
  static bool canCancelOrder(OrderStatus status) {
    return status == OrderStatus.pending;
  }

  /// 检查订单状态是否可以支付
  static bool canPayOrder(OrderStatus status) {
    return status == OrderStatus.pending;
  }

  /// 检查订单状态是否可以确认收货
  static bool canConfirmReceived(OrderStatus status) {
    return status == OrderStatus.shipped || status == OrderStatus.delivered;
  }

  /// 检查订单状态是否可以申请退款
  static bool canRequestRefund(OrderStatus status) {
    return status == OrderStatus.completed;
  }

  /// 检查订单状态是否可以删除
  static bool canDeleteOrder(OrderStatus status) {
    return status == OrderStatus.cancelled || status == OrderStatus.refunded;
  }

  /// 检查订单状态是否可以查看物流
  static bool canViewTracking(OrderStatus status) {
    return status == OrderStatus.shipped || 
           status == OrderStatus.delivered || 
           status == OrderStatus.completed;
  }

  /// 获取订单的下一个可能状态
  static List<OrderStatus> getNextPossibleStatuses(OrderStatus currentStatus) {
    switch (currentStatus) {
      case OrderStatus.pending:
        return [OrderStatus.paid, OrderStatus.cancelled];
      case OrderStatus.paid:
        return [OrderStatus.shipped, OrderStatus.cancelled];
      case OrderStatus.shipped:
        return [OrderStatus.delivered, OrderStatus.completed];
      case OrderStatus.delivered:
        return [OrderStatus.completed];
      case OrderStatus.completed:
        return [OrderStatus.refunded];
      case OrderStatus.cancelled:
        return []; // 已取消的订单不能再变更状态
      case OrderStatus.refunded:
        return []; // 已退款的订单不能再变更状态
    }
  }

  /// 验证订单状态流转是否合法
  static bool isValidStatusTransition(OrderStatus from, OrderStatus to) {
    final possibleStatuses = getNextPossibleStatuses(from);
    return possibleStatuses.contains(to);
  }

  /// 获取订单状态流转的错误信息
  static String getStatusTransitionError(OrderStatus from, OrderStatus to) {
    if (isValidStatusTransition(from, to)) {
      return '';
    }

    switch (from) {
      case OrderStatus.pending:
        return '待付款订单只能支付或取消';
      case OrderStatus.paid:
        return '已付款订单只能发货或取消';
      case OrderStatus.shipped:
        return '已发货订单只能确认收货';
      case OrderStatus.delivered:
        return '已送达订单只能确认收货';
      case OrderStatus.completed:
        return '已完成订单只能申请退款';
      case OrderStatus.cancelled:
        return '已取消订单不能变更状态';
      case OrderStatus.refunded:
        return '已退款订单不能变更状态';
    }
  }

  /// 获取订单状态的优先级（用于排序）
  static int getStatusPriority(OrderStatus status) {
    switch (status) {
      case OrderStatus.pending:
        return 1; // 最高优先级，需要用户操作
      case OrderStatus.paid:
        return 2;
      case OrderStatus.shipped:
        return 3;
      case OrderStatus.delivered:
        return 4;
      case OrderStatus.completed:
        return 5;
      case OrderStatus.cancelled:
        return 6;
      case OrderStatus.refunded:
        return 7; // 最低优先级
    }
  }

  /// 检查订单状态是否为最终状态
  static bool isFinalStatus(OrderStatus status) {
    return status == OrderStatus.completed || 
           status == OrderStatus.cancelled || 
           status == OrderStatus.refunded;
  }

  /// 检查订单状态是否需要用户操作
  static bool requiresUserAction(OrderStatus status) {
    return status == OrderStatus.pending || 
           status == OrderStatus.shipped || 
           status == OrderStatus.delivered;
  }

  /// 获取订单状态的操作提示
  static String getActionHint(OrderStatus status) {
    switch (status) {
      case OrderStatus.pending:
        return '请尽快完成支付';
      case OrderStatus.paid:
        return '商家正在准备发货';
      case OrderStatus.shipped:
        return '商品正在配送中，请注意查收';
      case OrderStatus.delivered:
        return '商品已送达，请确认收货';
      case OrderStatus.completed:
        return '交易已完成，感谢您的购买';
      case OrderStatus.cancelled:
        return '订单已取消';
      case OrderStatus.refunded:
        return '订单已退款';
    }
  }

  /// 获取订单状态的进度百分比
  static double getStatusProgress(OrderStatus status) {
    switch (status) {
      case OrderStatus.pending:
        return 0.1; // 10%
      case OrderStatus.paid:
        return 0.3; // 30%
      case OrderStatus.shipped:
        return 0.6; // 60%
      case OrderStatus.delivered:
        return 0.8; // 80%
      case OrderStatus.completed:
        return 1; // 100%
      case OrderStatus.cancelled:
        return 0; // 0%
      case OrderStatus.refunded:
        return 0; // 0%
    }
  }

  /// 获取订单状态的图标
  static String getStatusIcon(OrderStatus status) {
    switch (status) {
      case OrderStatus.pending:
        return 'payment';
      case OrderStatus.paid:
        return 'check_circle';
      case OrderStatus.shipped:
        return 'local_shipping';
      case OrderStatus.delivered:
        return 'location_on';
      case OrderStatus.completed:
        return 'done_all';
      case OrderStatus.cancelled:
        return 'cancel';
      case OrderStatus.refunded:
        return 'money_off';
    }
  }
}
