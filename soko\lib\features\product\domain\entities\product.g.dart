// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'product.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

Product _$ProductFromJson(Map<String, dynamic> json) => Product(
      id: json['id'] as String,
      name: json['name'] as String,
      category: json['category'] as String,
      brand: json['brand'] as String?,
      acg: json['acg'] as String?,
      type: json['type'] as String?,
      size: json['size'] as String?,
      description: json['description'] as String?,
      files: (json['files'] as List<dynamic>?)
          ?.map((e) => ProductFile.fromJson(e as Map<String, dynamic>))
          .toList(),
      skus: (json['skus'] as List<dynamic>?)
          ?.map((e) => ProductSku.fromJson(e as Map<String, dynamic>))
          .toList(),
      minPrice: (json['minPrice'] as num).toDouble(),
      maxPrice: (json['maxPrice'] as num?)?.toDouble(),
      originalPrice: (json['originalPrice'] as num?)?.toDouble(),
      newable: json['newable'] as bool?,
      domesticFreight: (json['domesticFreight'] as num?)?.toDouble(),
      internationalFreight: (json['internationalFreight'] as num?)?.toDouble(),
      totalSales: (json['totalSales'] as num?)?.toInt(),
      salesType: json['salesType'] as String?,
      status: json['status'] as String,
      createTime: (json['createTime'] as num).toInt(),
      updateTime: (json['updateTime'] as num).toInt(),
    );

Map<String, dynamic> _$ProductToJson(Product instance) => <String, dynamic>{
      'id': instance.id,
      'name': instance.name,
      'category': instance.category,
      'brand': instance.brand,
      'acg': instance.acg,
      'type': instance.type,
      'size': instance.size,
      'description': instance.description,
      'files': instance.files,
      'skus': instance.skus,
      'minPrice': instance.minPrice,
      'maxPrice': instance.maxPrice,
      'originalPrice': instance.originalPrice,
      'newable': instance.newable,
      'domesticFreight': instance.domesticFreight,
      'internationalFreight': instance.internationalFreight,
      'totalSales': instance.totalSales,
      'salesType': instance.salesType,
      'status': instance.status,
      'createTime': instance.createTime,
      'updateTime': instance.updateTime,
    };

ProductFile _$ProductFileFromJson(Map<String, dynamic> json) => ProductFile(
      id: json['id'] as String,
      url: json['url'] as String,
      thumbnailUrl: json['thumbnailUrl'] as String?,
      sort: (json['sort'] as num).toInt(),
      isMain: json['isMain'] as bool,
    );

Map<String, dynamic> _$ProductFileToJson(ProductFile instance) =>
    <String, dynamic>{
      'id': instance.id,
      'url': instance.url,
      'thumbnailUrl': instance.thumbnailUrl,
      'sort': instance.sort,
      'isMain': instance.isMain,
    };

ProductSku _$ProductSkuFromJson(Map<String, dynamic> json) => ProductSku(
      id: json['id'] as String,
      name: json['name'] as String,
      price: (json['price'] as num).toDouble(),
      originalPrice: (json['originalPrice'] as num?)?.toDouble(),
      stock: (json['stock'] as num).toInt(),
      status: json['status'] as String,
      attributes: (json['attributes'] as Map<String, dynamic>?)?.map(
        (k, e) => MapEntry(k, e as String),
      ),
    );

Map<String, dynamic> _$ProductSkuToJson(ProductSku instance) =>
    <String, dynamic>{
      'id': instance.id,
      'name': instance.name,
      'price': instance.price,
      'originalPrice': instance.originalPrice,
      'stock': instance.stock,
      'status': instance.status,
      'attributes': instance.attributes,
    };
