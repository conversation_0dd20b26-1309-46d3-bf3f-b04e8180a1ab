import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

import 'package:soko/core/theme/app_colors.dart';
import 'package:soko/core/theme/app_text_styles.dart';

/// 底部导航栏项目
class BottomNavItem {

  const BottomNavItem({
    required this.label,
    required this.icon,
    this.activeIcon,
    required this.route,
    this.badgeCount,
  });
  final String label;
  final IconData icon;
  final IconData? activeIcon;
  final String route;
  final int? badgeCount;
}

/// 自定义底部导航栏
class CustomBottomNavigation extends StatelessWidget {

  const CustomBottomNavigation({
    super.key,
    required this.items,
    required this.currentIndex,
    required this.onTap,
    this.backgroundColor,
    this.selectedItemColor,
    this.unselectedItemColor,
    this.elevation,
  });
  final List<BottomNavItem> items;
  final int currentIndex;
  final ValueChanged<int> onTap;
  final Color? backgroundColor;
  final Color? selectedItemColor;
  final Color? unselectedItemColor;
  final double? elevation;

  @override
  Widget build(BuildContext context) {
    return Container(
      decoration: BoxDecoration(
        color: backgroundColor ?? Colors.white,
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.1),
            blurRadius: elevation ?? 8,
            offset: const Offset(0, -2),
          ),
        ],
      ),
      child: SafeArea(
        child: Container(
          height: 60.h,
          padding: EdgeInsets.symmetric(horizontal: 8.w),
          child: Row(
            children: items.asMap().entries.map((entry) {
              final index = entry.key;
              final item = entry.value;
              final isSelected = index == currentIndex;

              return Expanded(
                child: GestureDetector(
                  onTap: () => onTap(index),
                  behavior: HitTestBehavior.opaque,
                  child: Container(
                    padding: EdgeInsets.symmetric(vertical: 8.h),
                    child: Column(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        // 图标和徽章
                        Stack(
                          clipBehavior: Clip.none,
                          children: [
                            Icon(
                              isSelected ? (item.activeIcon ?? item.icon) : item.icon,
                              size: 24.w,
                              color: isSelected
                                  ? (selectedItemColor ?? AppColors.primary)
                                  : (unselectedItemColor ?? AppColors.textTertiary),
                            ),
                            // 徽章
                            if (item.badgeCount != null && item.badgeCount! > 0)
                              Positioned(
                                right: -6.w,
                                top: -6.h,
                                child: Container(
                                  min: 16.w,
                                  height: 16.h,
                                  padding: EdgeInsets.symmetric(horizontal: 4.w),
                                  decoration: BoxDecoration(
                                    color: AppColors.error,
                                    borderRadius: BorderRadius.circular(8.r),
                                  ),
                                  child: Center(
                                    child: Text(
                                      item.badgeCount! > 99 ? '99+' : item.badgeCount.toString(),
                                      style: TextStyle(
                                        fontSize: 10.sp,
                                        color: Colors.white,
                                        fontWeight: FontWeight.w500,
                                      ),
                                    ),
                                  ),
                                ),
                              ),
                          ],
                        ),
                        SizedBox(height: 4.h),
                        // 标签
                        Text(
                          item.label,
                          style: AppTextStyles.labelSmall.copyWith(
                            color: isSelected
                                ? (selectedItemColor ?? AppColors.primary)
                                : (unselectedItemColor ?? AppColors.textTertiary),
                            fontWeight: isSelected ? FontWeight.w600 : FontWeight.w400,
                          ),
                        ),
                      ],
                    ),
                  ),
                ),
              );
            }).toList(),
          ),
        ),
      ),
    );
  }
}

/// 浮动操作按钮底部导航栏
class CustomBottomNavigationWithFAB extends StatelessWidget {

  const CustomBottomNavigationWithFAB({
    super.key,
    required this.items,
    required this.currentIndex,
    required this.onTap,
    this.floatingActionButton,
    this.floatingActionButtonLocation,
    this.backgroundColor,
    this.selectedItemColor,
    this.unselectedItemColor,
  });
  final List<BottomNavItem> items;
  final int currentIndex;
  final ValueChanged<int> onTap;
  final Widget? floatingActionButton;
  final FloatingActionButtonLocation? floatingActionButtonLocation;
  final Color? backgroundColor;
  final Color? selectedItemColor;
  final Color? unselectedItemColor;

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      bottomNavigationBar: CustomBottomNavigation(
        items: items,
        currentIndex: currentIndex,
        onTap: onTap,
        backgroundColor: backgroundColor,
        selectedItemColor: selectedItemColor,
        unselectedItemColor: unselectedItemColor,
      ),
      floatingActionButton: floatingActionButton,
      floatingActionButtonLocation: floatingActionButtonLocation ?? FloatingActionButtonLocation.centerDocked,
    );
  }
}

/// 带凹槽的底部导航栏
class NotchedBottomNavigation extends StatelessWidget {

  const NotchedBottomNavigation({
    super.key,
    required this.items,
    required this.currentIndex,
    required this.onTap,
    this.backgroundColor,
    this.selectedItemColor,
    this.unselectedItemColor,
    this.notchMargin = 8.0,
  });
  final List<BottomNavItem> items;
  final int currentIndex;
  final ValueChanged<int> onTap;
  final Color? backgroundColor;
  final Color? selectedItemColor;
  final Color? unselectedItemColor;
  final double notchMargin;

  @override
  Widget build(BuildContext context) {
    return BottomAppBar(
      color: backgroundColor ?? Colors.white,
      notchMargin: notchMargin,
      shape: const CircularNotchedRectangle(),
      child: Container(
        height: 60.h,
        padding: EdgeInsets.symmetric(horizontal: 8.w),
        child: Row(
          children: _buildNavItems(),
        ),
      ),
    );
  }

  List<Widget> _buildNavItems() {
    final navItems = <Widget>[];
    final middleIndex = items.length ~/ 2;

    for (var i = 0; i < items.length; i++) {
      if (i == middleIndex) {
        // 在中间位置添加空白空间给FAB
        navItems.add(SizedBox(width: 80.w));
      }

      final item = items[i];
      final isSelected = i == currentIndex;

      navItems.add(
        Expanded(
          child: GestureDetector(
            onTap: () => onTap(i),
            behavior: HitTestBehavior.opaque,
            child: Container(
              padding: EdgeInsets.symmetric(vertical: 8.h),
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  Stack(
                    clipBehavior: Clip.none,
                    children: [
                      Icon(
                        isSelected ? (item.activeIcon ?? item.icon) : item.icon,
                        size: 24.w,
                        color: isSelected
                            ? (selectedItemColor ?? AppColors.primary)
                            : (unselectedItemColor ?? AppColors.textTertiary),
                      ),
                      if (item.badgeCount != null && item.badgeCount! > 0)
                        Positioned(
                          right: -6.w,
                          top: -6.h,
                          child: Container(
                            min: 16.w,
                            height: 16.h,
                            padding: EdgeInsets.symmetric(horizontal: 4.w),
                            decoration: BoxDecoration(
                              color: AppColors.error,
                              borderRadius: BorderRadius.circular(8.r),
                            ),
                            child: Center(
                              child: Text(
                                item.badgeCount! > 99 ? '99+' : item.badgeCount.toString(),
                                style: TextStyle(
                                  fontSize: 10.sp,
                                  color: Colors.white,
                                  fontWeight: FontWeight.w500,
                                ),
                              ),
                            ),
                          ),
                        ),
                    ],
                  ),
                  SizedBox(height: 4.h),
                  Text(
                    item.label,
                    style: AppTextStyles.labelSmall.copyWith(
                      color: isSelected
                          ? (selectedItemColor ?? AppColors.primary)
                          : (unselectedItemColor ?? AppColors.textTertiary),
                      fontWeight: isSelected ? FontWeight.w600 : FontWeight.w400,
                    ),
                  ),
                ],
              ),
            ),
          ),
        ),
      );
    }

    return navItems;
  }
}

/// 自定义浮动操作按钮
class CustomFloatingActionButton extends StatelessWidget {

  const CustomFloatingActionButton({
    super.key,
    this.onPressed,
    this.child,
    this.tooltip,
    this.backgroundColor,
    this.foregroundColor,
    this.elevation,
  });
  final VoidCallback? onPressed;
  final Widget? child;
  final String? tooltip;
  final Color? backgroundColor;
  final Color? foregroundColor;
  final double? elevation;

  @override
  Widget build(BuildContext context) {
    return FloatingActionButton(
      onPressed: onPressed,
      tooltip: tooltip,
      backgroundColor: backgroundColor ?? AppColors.primary,
      foregroundColor: foregroundColor ?? Colors.white,
      elevation: elevation ?? 6,
      child: child ??
          Icon(
            Icons.add,
            size: 28.w,
          ),
    );
  }
}
