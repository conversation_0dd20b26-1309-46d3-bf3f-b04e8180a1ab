import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:cached_network_image/cached_network_image.dart';

import 'package:soko/shared/presentation/widgets/custom_card.dart';
import 'package:soko/features/order/domain/entities/order_create_request.dart';

/// 订单创建商品信息组件
class OrderCreateItemsSection extends StatelessWidget {

  const OrderCreateItemsSection({
    super.key,
    required this.items,
  });
  final List<OrderCreateItem> items;

  @override
  Widget build(BuildContext context) {
    return CustomCard(
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // 标题
          Row(
            children: [
              Icon(
                Icons.shopping_cart,
                color: Colors.green,
                size: 20.w,
              ),
              SizedBox(width: 8.w),
              Text(
                '商品信息',
                style: TextStyle(
                  fontSize: 16.sp,
                  fontWeight: FontWeight.w600,
                ),
              ),
              const Spacer(),
              Text(
                '共${items.length}件商品',
                style: TextStyle(
                  fontSize: 12.sp,
                  color: Colors.grey[600],
                ),
              ),
            ],
          ),
          SizedBox(height: 16.h),

          // 商品列表
          if (items.isNotEmpty)
            ...items.map(_buildItemRow)
          else
            _buildEmptyState(),
        ],
      ),
    );
  }

  /// 构建商品行
  Widget _buildItemRow(OrderCreateItem item) {
    return Padding(
      padding: EdgeInsets.only(bottom: 16.h),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // 商品图片
          ClipRRect(
            borderRadius: BorderRadius.circular(8.r),
            child: item.productImage?.isNotEmpty == true
                ? CachedNetworkImage(
                    imageUrl: item.productImage!,
                    width: 60.w,
                    height: 60.w,
                    fit: BoxFit.cover,
                    placeholder: (context, url) => Container(
                      width: 60.w,
                      height: 60.w,
                      color: Colors.grey[200],
                      child: Icon(
                        Icons.image,
                        color: Colors.grey[400],
                        size: 24.w,
                      ),
                    ),
                    errorWidget: (context, url, error) => Container(
                      width: 60.w,
                      height: 60.w,
                      color: Colors.grey[200],
                      child: Icon(
                        Icons.broken_image,
                        color: Colors.grey[400],
                        size: 24.w,
                      ),
                    ),
                  )
                : Container(
                    width: 60.w,
                    height: 60.w,
                    color: Colors.grey[200],
                    child: Icon(
                      Icons.image,
                      color: Colors.grey[400],
                      size: 24.w,
                    ),
                  ),
          ),
          SizedBox(width: 12.w),

          // 商品信息
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                // 商品名称
                Text(
                  item.productName,
                  style: TextStyle(
                    fontSize: 14.sp,
                    fontWeight: FontWeight.w500,
                    color: Colors.black87,
                  ),
                  maxLines: 2,
                  overflow: TextOverflow.ellipsis,
                ),

                // SKU信息
                if (item.skuName?.isNotEmpty == true) ...[
                  SizedBox(height: 4.h),
                  Text(
                    item.skuName!,
                    style: TextStyle(
                      fontSize: 12.sp,
                      color: Colors.grey[600],
                    ),
                  ),
                ],

                SizedBox(height: 8.h),

                // 价格和数量
                Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    // 单价
                    Text(
                      '¥${item.price.toStringAsFixed(2)}',
                      style: TextStyle(
                        fontSize: 16.sp,
                        color: Colors.red,
                        fontWeight: FontWeight.w600,
                      ),
                    ),

                    // 数量
                    Text(
                      'x${item.quantity}',
                      style: TextStyle(
                        fontSize: 14.sp,
                        color: Colors.grey[600],
                      ),
                    ),
                  ],
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  /// 构建空状态
  Widget _buildEmptyState() {
    return Container(
      width: double.infinity,
      padding: EdgeInsets.all(32.w),
      child: Column(
        children: [
          Icon(
            Icons.shopping_cart_outlined,
            size: 48.w,
            color: Colors.grey[400],
          ),
          SizedBox(height: 8.h),
          Text(
            '暂无商品',
            style: TextStyle(
              fontSize: 14.sp,
              color: Colors.grey[600],
            ),
          ),
        ],
      ),
    );
  }
}
