import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

import 'package:soko/core/theme/app_colors.dart';
import 'package:soko/core/theme/app_text_styles.dart';
import 'package:soko/shared/presentation/widgets/loading_widget.dart';

/// 按钮类型枚举
enum ButtonType {
  primary,
  secondary,
  outline,
  text,
  danger,
}

/// 按钮大小枚举
enum ButtonSize {
  small,
  medium,
  large,
}

/// 自定义按钮组件
class CustomButton extends StatelessWidget {

  const CustomButton({
    super.key,
    required this.text,
    this.onPressed,
    this.type = ButtonType.primary,
    this.size = ButtonSize.medium,
    this.isLoading = false,
    this.isDisabled = false,
    this.icon,
    this.width,
    this.height,
    this.padding,
    this.borderRadius,
  });
  final String text;
  final VoidCallback? onPressed;
  final ButtonType type;
  final ButtonSize size;
  final bool isLoading;
  final bool isDisabled;
  final Widget? icon;
  final double? width;
  final double? height;
  final EdgeInsetsGeometry? padding;
  final BorderRadius? borderRadius;

  @override
  Widget build(BuildContext context) {
    final buttonStyle = _getButtonStyle();
    final textStyle = _getTextStyle();
    final buttonHeight = height ?? _getHeight();
    final buttonPadding = padding ?? _getPadding();

    return SizedBox(
      width: width,
      height: buttonHeight,
      child: ElevatedButton(
        onPressed: (isDisabled || isLoading) ? null : onPressed,
        style: buttonStyle,
        child: Padding(
          padding: buttonPadding,
          child: _buildButtonContent(textStyle),
        ),
      ),
    );
  }

  Widget _buildButtonContent(TextStyle textStyle) {
    if (isLoading) {
      return ButtonLoadingWidget(
        color: _getLoadingColor(),
        size: _getLoadingSize(),
      );
    }

    if (icon != null) {
      return Row(
        mainAxisSize: MainAxisSize.min,
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          icon!,
          SizedBox(width: 8.w),
          Text(text, style: textStyle),
        ],
      );
    }

    return Text(text, style: textStyle);
  }

  ButtonStyle _getButtonStyle() {
    final colors = _getColors();
    
    return ElevatedButton.styleFrom(
      backgroundColor: colors['background'],
      foregroundColor: colors['foreground'],
      disabledBackgroundColor: colors['disabledBackground'],
      disabledForegroundColor: colors['disabledForeground'],
      elevation: type == ButtonType.outline || type == ButtonType.text ? 0 : 2,
      shadowColor: Colors.black26,
      shape: RoundedRectangleBorder(
        borderRadius: borderRadius ?? BorderRadius.circular(_getBorderRadius()),
        side: _getBorderSide(),
      ),
      padding: EdgeInsets.zero,
    );
  }

  Map<String, Color?> _getColors() {
    switch (type) {
      case ButtonType.primary:
        return {
          'background': AppColors.primary,
          'foreground': Colors.white,
          'disabledBackground': AppColors.border,
          'disabledForeground': AppColors.textDisabled,
        };
      case ButtonType.secondary:
        return {
          'background': AppColors.secondary,
          'foreground': Colors.white,
          'disabledBackground': AppColors.border,
          'disabledForeground': AppColors.textDisabled,
        };
      case ButtonType.outline:
        return {
          'background': Colors.transparent,
          'foreground': AppColors.primary,
          'disabledBackground': Colors.transparent,
          'disabledForeground': AppColors.textDisabled,
        };
      case ButtonType.text:
        return {
          'background': Colors.transparent,
          'foreground': AppColors.primary,
          'disabledBackground': Colors.transparent,
          'disabledForeground': AppColors.textDisabled,
        };
      case ButtonType.danger:
        return {
          'background': AppColors.error,
          'foreground': Colors.white,
          'disabledBackground': AppColors.border,
          'disabledForeground': AppColors.textDisabled,
        };
    }
  }

  BorderSide _getBorderSide() {
    if (type == ButtonType.outline) {
      return BorderSide(
        color: isDisabled ? AppColors.border : AppColors.primary,
      );
    }
    return BorderSide.none;
  }

  TextStyle _getTextStyle() {
    final baseStyle = _getBaseTextStyle();
    final colors = _getColors();
    
    return baseStyle.copyWith(
      color: isDisabled ? colors['disabledForeground'] : colors['foreground'],
    );
  }

  TextStyle _getBaseTextStyle() {
    switch (size) {
      case ButtonSize.small:
        return AppTextStyles.labelSmall;
      case ButtonSize.medium:
        return AppTextStyles.labelMedium;
      case ButtonSize.large:
        return AppTextStyles.labelLarge;
    }
  }

  double _getHeight() {
    switch (size) {
      case ButtonSize.small:
        return 32.h;
      case ButtonSize.medium:
        return 44.h;
      case ButtonSize.large:
        return 52.h;
    }
  }

  EdgeInsetsGeometry _getPadding() {
    switch (size) {
      case ButtonSize.small:
        return EdgeInsets.symmetric(horizontal: 12.w);
      case ButtonSize.medium:
        return EdgeInsets.symmetric(horizontal: 16.w);
      case ButtonSize.large:
        return EdgeInsets.symmetric(horizontal: 20.w);
    }
  }

  double _getBorderRadius() {
    switch (size) {
      case ButtonSize.small:
        return 6.r;
      case ButtonSize.medium:
        return 8.r;
      case ButtonSize.large:
        return 10.r;
    }
  }

  Color _getLoadingColor() {
    final colors = _getColors();
    return colors['foreground'] ?? AppColors.primary;
  }

  double _getLoadingSize() {
    switch (size) {
      case ButtonSize.small:
        return 14.w;
      case ButtonSize.medium:
        return 16.w;
      case ButtonSize.large:
        return 18.w;
    }
  }
}

/// 主要按钮
class PrimaryButton extends StatelessWidget {

  const PrimaryButton({
    super.key,
    required this.text,
    this.onPressed,
    this.isLoading = false,
    this.isDisabled = false,
    this.icon,
    this.width,
    this.size = ButtonSize.medium,
  });
  final String text;
  final VoidCallback? onPressed;
  final bool isLoading;
  final bool isDisabled;
  final Widget? icon;
  final double? width;
  final ButtonSize size;

  @override
  Widget build(BuildContext context) {
    return CustomButton(
      text: text,
      onPressed: onPressed,
      size: size,
      isLoading: isLoading,
      isDisabled: isDisabled,
      icon: icon,
      width: width,
    );
  }
}

/// 次要按钮
class SecondaryButton extends StatelessWidget {

  const SecondaryButton({
    super.key,
    required this.text,
    this.onPressed,
    this.isLoading = false,
    this.isDisabled = false,
    this.icon,
    this.width,
    this.size = ButtonSize.medium,
  });
  final String text;
  final VoidCallback? onPressed;
  final bool isLoading;
  final bool isDisabled;
  final Widget? icon;
  final double? width;
  final ButtonSize size;

  @override
  Widget build(BuildContext context) {
    return CustomButton(
      text: text,
      onPressed: onPressed,
      type: ButtonType.secondary,
      size: size,
      isLoading: isLoading,
      isDisabled: isDisabled,
      icon: icon,
      width: width,
    );
  }
}

/// 轮廓按钮
class OutlineButton extends StatelessWidget {

  const OutlineButton({
    super.key,
    required this.text,
    this.onPressed,
    this.isLoading = false,
    this.isDisabled = false,
    this.icon,
    this.width,
    this.size = ButtonSize.medium,
  });
  final String text;
  final VoidCallback? onPressed;
  final bool isLoading;
  final bool isDisabled;
  final Widget? icon;
  final double? width;
  final ButtonSize size;

  @override
  Widget build(BuildContext context) {
    return CustomButton(
      text: text,
      onPressed: onPressed,
      type: ButtonType.outline,
      size: size,
      isLoading: isLoading,
      isDisabled: isDisabled,
      icon: icon,
      width: width,
    );
  }
}

/// 文本按钮
class TextButton extends StatelessWidget {

  const TextButton({
    super.key,
    required this.text,
    this.onPressed,
    this.isLoading = false,
    this.isDisabled = false,
    this.icon,
    this.width,
    this.size = ButtonSize.medium,
  });
  final String text;
  final VoidCallback? onPressed;
  final bool isLoading;
  final bool isDisabled;
  final Widget? icon;
  final double? width;
  final ButtonSize size;

  @override
  Widget build(BuildContext context) {
    return CustomButton(
      text: text,
      onPressed: onPressed,
      type: ButtonType.text,
      size: size,
      isLoading: isLoading,
      isDisabled: isDisabled,
      icon: icon,
      width: width,
    );
  }
}
