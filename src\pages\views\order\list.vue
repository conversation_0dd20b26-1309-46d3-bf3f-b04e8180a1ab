<template>
  <view class="flex flex-col min-h-screen bg-slate-50">
    <!-- 使用自定义NavBar组件 -->
    <NavBar
      title="我的订单"
      bgColor="#FFFFFF"
      color="#333333"
      :showBack="true"
      :fixed="true"
    />

    <!-- 搜索框 -->
    <view class="bg-white px-4 py-3 border-b border-slate-100">
      <view class="relative flex items-center max-w-lg mx-auto w-full">
        <view class="absolute left-4 text-gray-400">
          <van-icon name="search" size="16" color="#A3A3A3" />
        </view>
        <input
          type="text"
          v-model="searchQuery"
          placeholder="搜索订单号或商品名称"
          class="w-full bg-slate-100 rounded-full py-2 pl-10 pr-4 text-sm text-text-primary focus:outline-none focus:ring-2 focus:ring-primary/20"
          @confirm="filterOrders"
        />
      </view>
    </view>

    <!-- 订单状态标签 -->
    <scroll-view
      scroll-x
      class="bg-white border-b whitespace-nowrap"
      :show-scrollbar="false"
      :scroll-into-view="`tab-${activeTab}`"
      scroll-with-animation
    >
      <view class="flex px-4 h-12 items-center flex-nowrap">
        <view
          v-for="(tab, index) in tabs"
          :key="index"
          :id="`tab-${tab.value}`"
          :class="[
            'px-4 py-1.5 mx-1 rounded-full inline-block whitespace-nowrap transition-all duration-300',
            activeTab === tab.value
              ? 'bg-primary text-white shadow-sm'
              : 'text-text-secondary bg-slate-100'
          ]"
          @click="filterListByTab(tab.value)"
        >
          <text class="text-sm font-medium whitespace-nowrap">{{
            tab.label
          }}</text>
        </view>
      </view>
    </scroll-view>

    <!-- 主内容区域 -->
    <van-pull-refresh v-model="refreshLoading" @refresh="onRefresh">
      <view class="flex-1 p-4">
        <view class="max-w-2xl mx-auto w-full">
          <!-- 订单数量显示 -->
          <view class="mb-4 flex justify-between items-center">
            <text class="text-base font-medium text-text-primary">{{
              `共计${totalOrders} 个订单`
            }}</text>
            <picker
              :value="sortIndex"
              :range="sortOptions"
              range-key="label"
              @change="handleSortChange"
              class="h-8 rounded-full text-xs bg-white border border-gray-200 px-3 flex items-center shadow-sm"
            >
              <view class="flex items-center justify-between w-full gap-2">
                <text class="text-xs text-text-secondary">{{
                  sortOptions[sortIndex].label
                }}</text>
                <van-icon name="arrow-down" size="12" color="#666666" />
              </view>
            </picker>
          </view>

          <!-- 无订单状态 -->
          <view
            class="flex flex-col items-center justify-center py-10"
            v-if="!filteredOrders.length"
          >
            <view
              class="w-24 h-24 bg-slate-100 rounded-full flex items-center justify-center mb-6 shadow-inner"
            >
              <van-icon name="gift-o" size="36" color="#666666" />
            </view>
            <text class="text-xl font-semibold mb-2 text-text-primary"
              >暂无订单</text
            >
            <text class="text-text-secondary text-sm text-center mb-8 max-w-xs"
              >您还没有符合条件的订单，去探索一下吧</text
            >
            <view
              @click="goToShop"
              class="bg-gradient-to-r from-heroic to-villain text-white rounded-full px-8 py-3 shadow-lg shadow-villain/20 active:translate-y-0.5 transition-transform"
            >
              <text class="text-sm font-medium">去购物</text>
            </view>
          </view>

          <!-- 订单列表 -->
          <view v-else class="order-list-container">
            <view v-for="order in filteredOrders" :key="order.id" class="mb-3">
              <view
                class="bg-white rounded-xl overflow-hidden shadow-sm active:bg-slate-50 transition-colors order-item"
                @click="goToOrderDetail(order.id)"
              >
                <!-- 订单头部 -->
                <view class="p-4 pb-2">
                  <view class="flex justify-between items-start">
                    <view>
                      <text class="text-sm font-medium text-text-primary">{{
                        order.orderNo
                      }}</text>
                      <text class="text-xs text-text-secondary block mt-0.5">
                        {{
                          order.createdTime
                            ? moment(order.createdTime).format("YYYY-MM-DD")
                            : "-"
                        }}
                      </text>
                    </view>
                    <view :class="getStatusBadgeClass(order.orderStatus)">
                      <van-icon
                        :name="getStatusIconName(order.orderStatus)"
                        class="mr-1.5"
                        size="12"
                      />
                      <text class="text-xs font-medium">{{
                        getStatusText(order.orderStatus)
                      }}</text>
                    </view>
                  </view>
                </view>

                <view class="h-px bg-slate-100 mx-4"></view>

                <!-- 订单商品 -->
                <view class="p-4 pt-3">
                  <view class="space-y-3">
                    <!-- 订单商品项 -->
                    <view
                      v-for="item in order.orderItems"
                      :key="item.id"
                      class="flex gap-3"
                    >
                      <view
                        class="relative h-16 w-16 bg-slate-50 rounded-lg overflow-hidden flex-shrink-0 border border-slate-100"
                      >
                        <CacheImgs
                          :src="getFileUrl(item.fileUrl || item.productImage)"
                          :alt="item.productName"
                          class="product-image"
                          :lazy="true"
                        />
                      </view>
                      <view
                        class="flex-1 min-w-0 flex flex-col justify-between"
                      >
                        <view class="flex items-center justify-between">
                          <text
                            class="text-sm font-medium truncate block text-text-primary"
                            >{{ item.productName }}</text
                          >
                          <view
                            v-if="item.orderStatus === 'AFTER_SALE'"
                            class="inline-flex items-center ml-2 px-2 py-0.5 rounded-full bg-purple-100 text-purple-600 text-xs font-medium"
                          >
                            <van-icon name="replay" size="12" class="mr-1" />
                            售后中
                          </view>
                        </view>
                        <view class="flex justify-between items-center">
                          <text class="text-sm font-medium text-primary"
                            >¥{{ item.productPrice.toFixed(2) }}</text
                          >
                          <text class="text-xs text-text-secondary"
                            >×{{ item.quantity }}</text
                          >
                        </view>
                      </view>
                    </view>
                  </view>

                  <text
                    v-if="order.orderItems && order.orderItems.length > 1"
                    class="text-xs text-right text-text-secondary mt-2 block"
                  >
                    共 {{ order.orderItems.length }} 件商品
                  </text>

                  <!-- 订单底部 -->
                  <view
                    class="flex justify-between items-center mt-3 pt-3 border-t border-slate-100"
                  >
                    <view class="flex flex-col">
                      <text class="text-sm text-text-primary">
                        合计:
                        <text class="font-bold text-primary">
                          ¥{{
                            (
                              order.totalAmount +
                              order.domesticFreight +
                              order.internationalFreight
                            ).toFixed(2)
                          }}
                        </text>
                      </text>
                      <text
                        v-if="order.orderStatus === 'SHIPPED'"
                        class="text-xs text-red-500 mt-1"
                      >
                        预计{{
                          moment(order.orderTime)
                            .add(7, "days")
                            .format("YYYY-MM-DD")
                        }}自动收货
                      </text>
                    </view>
                    <view
                      :class="getActionButtonClass(order.orderStatus)"
                      @click.stop="handleActionButton(order)"
                    >
                      <text>{{ getActionButtonText(order.orderStatus) }}</text>
                    </view>
                  </view>
                </view>
              </view>
            </view>

            <!-- 加载状态 -->
            <view class="py-4">
              <view v-if="listLoading" class="flex justify-center items-center">
                <view class="loading-icon mr-2"></view>
                <text class="text-sm text-gray-500">加载中...</text>
              </view>
              <view v-else-if="finished" class="flex justify-center">
                <text class="text-sm text-gray-500">没有更多了</text>
              </view>
              <view v-else-if="loadError" class="flex justify-center">
                <text class="text-sm text-gray-500" @tap="loadList"
                  >加载失败，点击重试</text
                >
              </view>
            </view>
          </view>
        </view>
      </view>
    </van-pull-refresh>

    <!-- 支付失败弹窗 -->
    <PaymentFailedPopup
      :show="showPaymentFailedPopup"
      @close="showPaymentFailedPopup = false"
      @retry="retryPayment"
      @refresh="refreshPaymentStatus"
    />
  </view>
</template>

<script lang="ts" setup>
import CacheImgs from "@/components/CacheImgs.vue"
import {
  confirmReceiveOrder,
  deleteOrder,
  getOrderDetail,
  getOrderList,
  payOrder
} from "@/api/api.js"
import NavBar from "@/components/NavBar.vue"
import { onLoad, onReachBottom } from "@dcloudio/uni-app"
import moment from "moment"
import { reactive, ref } from "vue"
import PaymentFailedPopup from "@/components/PaymentFailedPopup.vue"

// 订单状态类型
type OrderStatus =
  | "UNPAID"
  | "PAID"
  | "SHIPPED"
  | "COMPLETED"
  | "GROUP"
  | "CANCELLED"
  | "AFTER_SALE"
  | "REFUND"

// 订单项类型
interface OrderItem {
  id: string
  orderId: string
  orderNo: string
  productId: string
  productSkuId: string
  productName: string
  productImage: string
  fileUrl?: string
  productPrice: number
  quantity: number
  totalPrice: number
  realPrice: number
  salesType: string
  isAfterSale?: boolean
  orderStatus?: string
}

// 订单记录类型
interface OrderRecord {
  id: string
  orderId: string
  orderNo: string
  oldStatus: string
  oldStatusDesc: string
  newStatus: string
  newStatusDesc: string
  mediaType: string
  mediaUrl: string
  description: string
  operateTime: string
  operatorId: string
  operatorName: string
}

// 订单类型
interface Order {
  id: string
  version: number
  creatorId: string
  creator: string
  createdTime: number
  modifierId: string
  modifier: string
  modifiedTime: number
  orderNo: string
  userId: string
  totalAmount: number
  payAmount: number
  domesticFreight: number
  internationalFreight: number
  orderTime: number
  discountAmount: number
  payType: string
  payTime: number
  shipTime: number
  receiveTime: number
  orderStatus: OrderStatus
  receiverName: string
  receiverPhone: string
  receiverProvince: string
  receiverCity: string
  receiverDistrict: string
  receiverAddress: string
  note: string
  trackingNo: string
  trackingCompany: string
  salesType: string
  status: string
  orderItems: OrderItem[]
  orderRecords: OrderRecord[]
}

// 排序选项
const sortOptions = [
  { value: "latest", label: "最新订单" },
  { value: "oldest", label: "最早订单" },
  { value: "price-high", label: "价格从高到低" },
  { value: "price-low", label: "价格从低到高" }
]

// 标签选项
const tabs = [
  { value: "", label: "全部" },
  { value: "UNPAID", label: "待付款" },
  { value: "PAID", label: "待发货" },
  { value: "SHIPPED", label: "待收货" },
  { value: "COMPLETED", label: "已完成" },
  { value: "CANCELLED", label: "已取消" }
  // { value: 'GROUP', label: '拼团中' },
]

// 响应式状态
const activeTab = ref("")
const isLoading = ref(true)
const filteredOrders = ref<Order[]>([])
const sortIndex = ref(0)
const searchQuery = ref("")
const pageNoParams = reactive({
  page: 0,
  size: 10
})
const totalOrders = ref(0)
const listLoading = ref(false)
const finished = ref(false)
const showPaymentFailedPopup = ref(false) // 控制支付失败弹窗的显示
const currentOrderIdForPopup = ref<string | null>(null) // 存储支付失败时的订单ID
const loadError = ref(false)
const isLoadingMore = ref(false) // 防止重复触发加载
const throttleTimer = ref<any>(null) // 节流定时器
const refreshLoading = ref(false)

onLoad((option) => {
  activeTab.value = option.status || ""
  loadList()
})

// 添加页面触底加载更多事件，使用节流防止频繁触发
onReachBottom(() => {
  if (throttleTimer.value !== null) return

  throttleTimer.value = setTimeout(() => {
    loadList()
    throttleTimer.value = null
  }, 200) // 200ms节流
})

const filterListByTab = (tabValue: string) => {
  activeTab.value = tabValue
  resetAndReload()
}

const loadList = () => {
  // 防止重复触发加载
  if (!finished.value && !listLoading.value && !isLoadingMore.value) {
    isLoadingMore.value = true
    listLoading.value = true
    pageNoParams.page++
    fetchOrders(pageNoParams)
  }
}

// 重置列表并重新加载
const resetAndReload = () => {
  // 重置分页参数
  pageNoParams.page = 0
  finished.value = false
  loadError.value = false
  filteredOrders.value = []
  isLoadingMore.value = false
  // 加载数据
  loadList()
}

// 刷新列表
const refreshList = () => {
  loadError.value = false
  finished.value = false
  pageNoParams.page = 0
  loadList()
}

async function fetchOrders(pageParams: { page: number; size: number }) {
  const params: {
    page: number
    size: number
    orderNo?: string
    name?: string
    orderStatus?: string
  } = { ...pageParams }

  // 定义搜索参数逻辑
  const addSearchParams = () => {
    if (/^ORD\d+/.test(searchQuery.value)) {
      params.orderNo = searchQuery.value
    } else {
      params.name = searchQuery.value
    }
  }

  // 定义状态筛选逻辑
  const addStatusFilter = () => {
    params.orderStatus = activeTab.value
  }

  // 添加搜索参数
  if (searchQuery.value) {
    addSearchParams()
  }

  // 添加状态筛选
  if (activeTab.value) {
    addStatusFilter()
  }

  try {
    // 获取订单列表数据
    const res = await getOrderList(params)
    const resData = res?.data

    if (resData && resData.records) {
      // 更新总订单数
      totalOrders.value = resData.total

      // 合并数据，如果是第一页则替换，否则追加
      if (params.page === 1) {
        filteredOrders.value = resData.records
      } else {
        // 使用setTimeout延迟渲染，减少卡顿
        setTimeout(() => {
          filteredOrders.value = [...filteredOrders.value, ...resData.records]
        }, 0)
      }

      // 判断是否已加载全部数据
      if (
        filteredOrders.value.length >= resData.total ||
        resData.records.length === 0
      ) {
        finished.value = true
      } else {
        finished.value = false
      }

      loadError.value = false
    } else {
      loadError.value = true
      uni.showToast({
        title: res?.message || "获取订单列表失败",
        icon: "none"
      })
    }
  } catch (error) {
    console.error("获取订单列表失败", error)
    loadError.value = true
    uni.showToast({
      title: "获取订单列表失败，请重试",
      icon: "none"
    })
  } finally {
    // 延迟重置加载状态，减少卡顿感
    setTimeout(() => {
      listLoading.value = false
      isLoadingMore.value = false
    }, 100)
  }
}

// 筛选订单
function filterOrders() {
  resetAndReload()
}

// 应用排序
function applySorting(sortIndexNumber: number) {
  const sortType = sortOptions[sortIndexNumber]?.value
  switch (sortType) {
    case "latest":
      filteredOrders.value.sort((a, b) => b.createdTime - a.createdTime)
      break
    case "oldest":
      filteredOrders.value.sort((a, b) => a.createdTime - b.createdTime)
      break
    case "price-high":
      filteredOrders.value.sort((a, b) => b.totalAmount - a.totalAmount)
      break
    case "price-low":
      filteredOrders.value.sort((a, b) => a.totalAmount - b.totalAmount)
      break
  }
}

// 状态样式
function getStatusBadgeClass(status: OrderStatus): string {
  switch (status) {
    case "UNPAID":
      return "flex items-center border rounded-full px-2.5 py-1 border-warning text-warning"
    case "PAID":
      return "flex items-center border rounded-full px-2.5 py-1 border-primary text-primary"
    case "SHIPPED":
      return "flex items-center border rounded-full px-2.5 py-1 border-accent text-accent"
    case "COMPLETED":
      return "flex items-center border rounded-full px-2.5 py-1 border-success text-success"
    case "CANCELLED":
      return "flex items-center border rounded-full px-2.5 py-1 border-destructive text-destructive"
    case "GROUP":
      return "flex items-center border rounded-full px-2.5 py-1 border-villain text-villain"
    case "AFTER_SALE":
    case "REFUND":
      return "flex items-center border rounded-full px-2.5 py-1 border-heroic text-heroic"
    default:
      return "flex items-center border rounded-full px-2.5 py-1 border-heroic text-heroic"
  }
}

// 状态图标名称
function getStatusIconName(status: OrderStatus): string {
  switch (status) {
    case "UNPAID":
      return "clock-o"
    case "PAID":
      return "gold-coin-o"
    case "SHIPPED":
      return "logistics"
    case "COMPLETED":
      return "passed"
    case "CANCELLED":
      return "close"
    case "GROUP":
      return "friends-o"
    case "AFTER_SALE":
    case "REFUND":
      return "todo-list-o"
    default:
      return "circle"
  }
}

// 状态文本
function getStatusText(status: OrderStatus): string {
  switch (status) {
    case "UNPAID":
      return "待付款"
    case "PAID":
      return "待发货"
    case "SHIPPED":
      return "待收货"
    case "COMPLETED":
      return "已完成"
    case "CANCELLED":
      return "已取消"
    case "GROUP":
      return "拼团中"
    case "AFTER_SALE":
    case "REFUND":
      return "售后/退货"
    default:
      return "未知状态"
  }
}

// 处理文件URL
function getFileUrl(url: string | undefined): string {
  if (!url) return "/static/placeholder.png"

  // 如果已经是完整URL，则直接返回
  if (url.startsWith("http://") || url.startsWith("https://")) {
    return url
  }

  // 如果是相对路径，添加域名或CDN前缀
  // 根据实际环境配置修改baseUrl
  const baseUrl = "" // 从环境配置或全局变量中获取
  return `${baseUrl}${url}`
}

// 操作按钮样式
function getActionButtonClass(status: OrderStatus): string {
  switch (status) {
    case "UNPAID":
      return "bg-primary text-white rounded-full px-4 py-1.5 text-sm font-medium shadow-sm active:translate-y-0.5 transition-transform"
    case "SHIPPED":
      return "bg-secondary text-white rounded-full px-4 py-1.5 text-sm font-medium shadow-sm active:translate-y-0.5 transition-transform"
    case "COMPLETED":
      return "bg-accent text-white rounded-full px-4 py-1.5 text-sm font-medium shadow-sm active:translate-y-0.5 transition-transform"
    case "GROUP":
      return "bg-villain text-white rounded-full px-4 py-1.5 text-sm font-medium shadow-sm active:translate-y-0.5 transition-transform"
    case "AFTER_SALE":
    case "REFUND":
      return "bg-heroic text-white rounded-full px-4 py-1.5 text-sm font-medium shadow-sm active:translate-y-0.5 transition-transform"
    case "CANCELLED":
      return "border border-gray-300 rounded-full px-4 py-1.5 text-sm font-medium text-text-secondary active:bg-slate-100 transition-colors"
    default:
      return "border border-gray-300 rounded-full px-4 py-1.5 text-sm font-medium text-text-secondary active:bg-slate-100 transition-colors"
  }
}

// 操作按钮文本
function getActionButtonText(status: OrderStatus): string {
  switch (status) {
    case "UNPAID":
      return "去付款"
    case "PAID":
      return "查看详情"
    case "SHIPPED":
      return "确认收货"
    case "COMPLETED":
      return "再次购买"
    case "CANCELLED":
      return "删除订单"
    case "GROUP":
      return "查看拼团"
    case "AFTER_SALE":
    case "REFUND":
      return "查看进度"
    default:
      return "查看详情"
  }
}

// 排序变更处理
function handleSortChange(e: any) {
  sortIndex.value = e.detail.value
  resetAndReload()
  // 应用本地排序
  applySorting(e.detail.value)
}
// 处理支付
async function handlePayment(order: Order) {
  try {
    // 创建支付宝订单
    const alipayOrder = await payOrder({
      orderId: order.id,
      payType: "ALIPAY"
    })
    const result = alipayOrder[1] // uni.request promisify后的结果在第二个元素

    if (result.code === 200) {
      const platform = uni.getSystemInfoSync().platform
      if (
        platform === "android" ||
        platform === "ios" ||
        platform === "app-plus"
      ) {
        try {
          // 设置支付宝沙盒环境 (如果需要)
          // var EnvUtils = plus.android.importClass(
          //   'com.alipay.sdk.app.EnvUtils'
          // ) as any;
          // EnvUtils.setEnv(EnvUtils.EnvEnum.SANDBOX);

          // 调用支付接口
          uni.requestPayment({
            provider: "alipay",
            orderInfo: result.data.trim(), //支付宝订单数据
            success: (e) => {
              console.log("支付成功返回数据:", e)
              // submitting.value = false; // 重置提交状态
              uni.showToast({
                icon: "success",
                title: "支付成功！"
              })

              // 支付成功后延迟跳转到结果页或刷新列表
              setTimeout(() => {
                // uni.redirectTo({
                //   url: `/pages/views/order/result?orderId=${order.id}&status=success`,
                // });
                // 重置分页参数
                pageNoParams.page = 0
                finished.value = false
                filteredOrders.value = []
                loadList()
              }, 1500)
            },
            fail: (e) => {
              console.log("支付失败或取消:", e)
              uni.showToast({
                title: "支付未完成",
                icon: "none",
                duration: 1500
              })
              // 直接跳转到订单详情页面
              setTimeout(() => {
                uni.redirectTo({
                  url: `/pages/views/order/detail?id=${order.id}`
                })
              }, 1500)
            }
          })
        } catch (error) {
          console.error("支付过程异常:", error)
          uni.showToast({
            title: "支付异常",
            icon: "none",
            duration: 1500
          })
          // 显示支付失败弹窗
          currentOrderIdForPopup.value = order.id
          showPaymentFailedPopup.value = true
          // submitting.value = false;
        }
      } else {
        // 非APP环境
        uni.showToast({
          title: "当前环境不支持支付",
          icon: "none"
        })
        currentOrderIdForPopup.value = order.id
        showPaymentFailedPopup.value = true
        // submitting.value = false;
      }
    } else {
      // 创建支付订单失败
      uni.showToast({
        title: result.message || "创建支付订单失败",
        icon: "none"
      })
      currentOrderIdForPopup.value = order.id
      showPaymentFailedPopup.value = true
      // submitting.value = false;
    }
  } catch (error) {
    console.error("支付处理失败", error)
    const errorMsg =
      error?.data?.message || error?.message || "支付处理失败，请重试"
    uni.showToast({
      title: errorMsg,
      icon: "none"
    })
    if (order && order.id) {
      currentOrderIdForPopup.value = order.id
      showPaymentFailedPopup.value = true
    }
    // submitting.value = false;
  }
}

// 支付失败弹窗的"重新支付"按钮事件
const retryPayment = () => {
  showPaymentFailedPopup.value = false
  // submitting.value = false; // 允许用户重新点击支付
  if (currentOrderIdForPopup.value) {
    const orderToRetry = filteredOrders.value.find(
      (o) => o.id === currentOrderIdForPopup.value
    )
    if (orderToRetry) {
      handlePayment(orderToRetry)
    } else {
      uni.showToast({ title: "找不到订单信息", icon: "none" })
    }
  } else {
    uni.showToast({ title: "无法获取订单信息", icon: "none" })
  }
}

// 支付失败弹窗的"刷新"按钮事件
const refreshPaymentStatus = () => {
  showPaymentFailedPopup.value = false
  if (currentOrderIdForPopup.value) {
    queryPaymentStatus(currentOrderIdForPopup.value)
  } else {
    uni.showToast({
      title: "无法获取订单信息",
      icon: "none"
    })
  }
}

// 查询支付状态（只查一次，不轮询）
const queryPaymentStatus = async (orderId: string) => {
  try {
    uni.showLoading({
      title: "查询支付结果...",
      mask: true
    })

    await new Promise((resolve) => setTimeout(resolve, 1000))

    const [statusError, statusRes] = await getOrderDetail(orderId)

    if (statusError) {
      console.error("查询订单状态出错:", statusError)
      throw new Error(statusError.message || "查询订单状态失败")
    }

    if (!statusRes) {
      console.error("查询订单状态响应异常:", statusRes)
      throw new Error("订单状态查询返回数据异常")
    }

    let orderData = statusRes.data?.data || statusRes.data

    if (!orderData) {
      console.error("查询返回的数据格式异常:", statusRes)
      throw new Error("订单数据格式异常")
    }
    console.log("查询订单状态结果:", orderData)

    if (
      orderData.payStatus === "PAID" ||
      orderData.orderStatus === "PAID" ||
      orderData.orderStatus === "WAIT_DELIVER" ||
      orderData.orderStatus === "PAID_SUCCESS" ||
      orderData.payStatus === "SUCCESS"
    ) {
      uni.hideLoading()
      uni.showToast({
        title: "支付成功",
        icon: "success"
      })
      setTimeout(() => {
        pageNoParams.page = 0
        finished.value = false
        filteredOrders.value = []
        loadList()
      }, 1500)
      return true
    }

    if (
      orderData.orderStatus === "CANCELLED" ||
      orderData.orderStatus === "CANCEL" ||
      orderData.payStatus === "CANCEL"
    ) {
      uni.hideLoading()
      uni.showToast({
        title: "订单已取消",
        icon: "none"
      })
      setTimeout(() => {
        pageNoParams.page = 0
        finished.value = false
        filteredOrders.value = []
        loadList()
      }, 1500)
      return false
    }

    // 仅查一次，未能确认支付结果时弹窗提示
    uni.hideLoading()
    uni.showModal({
      title: "支付提示",
      content: "支付结果未确认，请在订单页面查看最新状态",
      confirmText: "查看订单",
      cancelText: "返回",
      success: (res) => {
        if (res.confirm) {
          goToOrderDetail(orderId)
        } else {
          pageNoParams.page = 0
          finished.value = false
          filteredOrders.value = []
          loadList()
        }
      }
    })
    return false
  } catch (error) {
    console.error("查询支付状态异常", error)
    uni.hideLoading()
    uni.showToast({
      title: "查询支付结果异常",
      icon: "none"
    })
    uni.showModal({
      title: "支付提示",
      content: "支付过程中出现异常，请在订单页面确认结果",
      confirmText: "查看订单",
      cancelText: "返回",
      success: (res) => {
        if (res.confirm) {
          goToOrderDetail(orderId)
        } else {
          pageNoParams.page = 0
          finished.value = false
          filteredOrders.value = []
          loadList()
        }
      }
    })
    return false
  }
}

// 按钮处理
async function handleActionButton(order) {
  switch (order.orderStatus) {
    case "UNPAID":
      // 去支付
      handlePayment(order)
      break
    case "SHIPPED":
      // 确认收货
      confirmReceive(order)
      break
    case "CANCELLED":
      // 删除订单
      uni.showModal({
        title: "删除订单",
        content: "确定要删除此订单吗？删除后无法恢复。",
        success: async (res) => {
          if (res.confirm) {
            // 校验订单版本号是否存在且为数字
            if (typeof order.version !== "number") {
              uni.showToast({
                title: "订单版本信息缺失，无法删除订单",
                icon: "none"
              })
              return
            }

            isLoading.value = true
            uni.showLoading({ title: "正在删除..." })
            try {
              const response = await deleteOrder(order.id, order.version)
              uni.hideLoading()
              isLoading.value = false

              if (response && response.code === 200) {
                uni.showToast({
                  title: "订单已删除",
                  icon: "success"
                })
                // 刷新订单列表
                pageNoParams.page = 0
                finished.value = false
                filteredOrders.value = []
                loadList()
              } else {
                uni.showToast({
                  title: `操作失败: ${
                    (response && response.message) || "未知服务端错误"
                  }`,
                  icon: "none"
                })
              }
            } catch (e) {
              uni.hideLoading()
              isLoading.value = false
              const errorMessage =
                (e.data && e.data.msg) ||
                e.errMsg ||
                e.message ||
                "操作异常，请稍后重试"
              uni.showToast({
                title: errorMessage,
                icon: "none"
              })
            }
          }
        }
      })
      break
    case "COMPLETED":
      // 再次购买，跳转到相关商品页面
      rebuyProduct(order)
      break
    default:
      // 其他情况跳转到订单详情
      goToOrderDetail(order.id)
  }
}

// 跳转支付页面
function goToPayment(order: Order) {
  uni.navigateTo({
    url: `/pages/views/order/payment?orderId=${order.id}`
  })
}

// 确认收货
async function confirmReceive(order: Order) {
  uni.showModal({
    title: "确认收货",
    content: "确认已收到商品吗？",
    success: async (res) => {
      if (res.confirm) {
        try {
          isLoading.value = true
          // 调用API确认收货
          const [error, res] = await confirmReceiveOrder(
            order.id,
            order.version
          )

          if (error) throw error

          uni.showToast({
            title: "确认收货成功",
            icon: "success"
          })
          // 刷新订单列表
          // 重置分页参数
          pageNoParams.page = 0
          finished.value = false
          filteredOrders.value = []
          loadList()
        } catch (error) {
          console.error("确认收货失败", error)
          uni.showToast({
            title: "确认收货失败",
            icon: "none"
          })
        } finally {
          isLoading.value = false
        }
      }
    }
  })
}

// 再次购买
function rebuyProduct(order: Order) {
  // 假设第一个商品是主要商品
  if (order.orderItems && order.orderItems.length > 0) {
    const item = order.orderItems[0]
    uni.navigateTo({
      url: `/pages/views/product/detail?id=${item.productId}`
    })
  }
}

// 导航方法
function goBack() {
  uni.navigateBack()
}

function goToSearch() {
  uni.navigateTo({
    url: "/pages/views/search/index"
  })
}

function goToShop() {
  uni.switchTab({
    url: "/pages/views/shop/index"
  })
}

function goToOrderDetail(orderId: string) {
  uni.navigateTo({
    url: `/pages/views/order/detail?id=${orderId}`
  })
}

const onRefresh = () => {
  filterListByTab(activeTab.value)
  refreshLoading.value = false
}
</script>

<style>
/* 移除mt-nav-bar，因为NavBar组件内部已有占位元素 */
.whitespace-nowrap {
  white-space: nowrap;
}

.truncate {
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.grid-cols-2 {
  display: grid;
  grid-template-columns: 1fr 1fr;
}

.gap-4 {
  gap: 1rem;
}

.space-y-1 > view + view,
.space-y-1 > text + view {
  margin-top: 0.25rem;
}

.space-y-2 > view + view,
.space-y-2 > text + view {
  margin-top: 0.5rem;
}

.space-y-3 > view + view {
  margin-top: 0.75rem;
}

.space-y-4 > view + view {
  margin-top: 1rem;
}

.space-y-6 > view + view {
  margin-top: 1.5rem;
}

.space-x-4 > view + view {
  margin-left: 1rem;
}

.animate-pulse {
  animation: pulse 2s cubic-bezier(0.4, 0, 0.6, 1) infinite;
}

@keyframes pulse {
  0%,
  100% {
    opacity: 1;
  }
  50% {
    opacity: 0.7;
  }
}

.transition-transform {
  transition-property: transform;
  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
  transition-duration: 150ms;
}

.transition-colors {
  transition-property: background-color, border-color, color, fill, stroke;
  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
  transition-duration: 150ms;
}

.animate-spin {
  animation: spin 1s linear infinite;
}

@keyframes spin {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

.pb-safe {
  padding-bottom: env(safe-area-inset-bottom, 16px);
}

/* 订单列表滚动区域样式 */
.order-list-scroll {
  height: calc(100vh - 200px); /* 根据实际情况调整高度 */
  width: 100%;
}

/* 加载图标 */
.loading-icon {
  width: 20px;
  height: 20px;
  border: 2px solid #f3f3f3;
  border-top: 2px solid #3498db;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

/* 性能优化相关样式 */
/* 使用硬件加速 */
.order-item {
  will-change: transform;
  transform: translateZ(0);
  backface-visibility: hidden;
  perspective: 1000;
}

/* 减少重绘和回流 */
.product-image {
  width: 100%;
  height: 100%;
  object-fit: contain;
}

/* 优化滚动性能 */
.order-list-container {
  -webkit-overflow-scrolling: touch;
  overscroll-behavior: contain;
}
</style>
