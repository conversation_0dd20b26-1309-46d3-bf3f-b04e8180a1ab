import type {
  RecyclingOrderVO,
  CreateOrderRequest,
  OrderQueryParams,
  ApiResponse,
  PageResult
} from '@/types/recycle'

// 封装一个获取 http 实例的函数
const getHttpInstance = () => {
  if (!uni.$u || !uni.$u.http) {
    console.error('uView Plus HTTP 实例尚未初始化！')
    throw new Error('uView Plus HTTP instance is not initialized.')
  }
  return uni.$u.http
}

export const recyclingApi = {
  // 创建回收订单
  createOrder: (data: CreateOrderRequest): Promise<ApiResponse<string>> => {
    const http = getHttpInstance()
    return http.post('/recycling', data)
  },

  // 查询用户订单列表
  getUserOrders: (params: OrderQueryParams): Promise<ApiResponse<PageResult<RecyclingOrderVO>>> => {
    const http = getHttpInstance()
    return http.get('/recycling/list', { params })
  },

  // 获取订单详情
  getOrderDetail: (orderId: string): Promise<ApiResponse<RecyclingOrderVO>> => {
    const http = getHttpInstance()
    return http.get(`/recycling/${orderId}`)
  },

  // 取消订单
  cancelOrder: (orderId: string): Promise<ApiResponse<void>> => {
    const http = getHttpInstance()
    return http.post(`/recycling/cancel/${orderId}`)
  },

  // 确认寄送
  confirmShipment: (data: { orderId: string; shippingInfo: string }): Promise<ApiResponse<void>> => {
    const http = getHttpInstance()
    return http.post('/recycling/confirm-shipment', data)
  },

  // 完成退货
  completeReturn: (orderId: string): Promise<ApiResponse<void>> => {
    const http = getHttpInstance()
    return http.post(`/recycling/complete-return/${orderId}`)
  },

  // 上传图片文件
  uploadImage: (filePath: string): Promise<ApiResponse<{ id: string; url: string; thumbnailUrl: string }>> => {
    const http = getHttpInstance()
    return new Promise((resolve, reject) => {
      uni.uploadFile({
        url: `${http.config.baseURL}/file/upload`,
        filePath: filePath,
        name: 'file',
        header: {
          'Authorization': `Bearer ${uni.getStorageSync('token')}`
        },
        success: (uploadRes) => {
          try {
            const result = JSON.parse(uploadRes.data)
            if (result.success) {
              resolve(result)
            } else {
              reject(new Error(result.message))
            }
          } catch (error) {
            reject(error)
          }
        },
        fail: (error) => {
          reject(error)
        }
      })
    })
  },

  // 获取回收商品类别
  getRecycleCategories: (): Promise<ApiResponse<any[]>> => {
    const http = getHttpInstance()
    return http.get('/category/recycle')
  },

  // 获取回收配置
  getRecycleConfig: (): Promise<ApiResponse<any>> => {
    const http = getHttpInstance()
    return http.get('/system/recycle-config')
  }
}
