import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

import 'package:soko/core/theme/app_colors.dart';
import 'package:soko/core/theme/app_text_styles.dart';
import 'package:soko/shared/presentation/widgets/loading_widget.dart';
import 'package:soko/features/product/domain/entities/category.dart';
import 'package:soko/features/product/presentation/providers/category_provider.dart';

/// 分类导航组件
class CategoryNavigation extends ConsumerWidget {

  const CategoryNavigation({
    super.key,
    this.onCategorySelected,
    this.onAcgTypeSelected,
    this.showAcgCategories = true,
    this.showHotCategories = true,
  });
  final Function(ProductCategory category)? onCategorySelected;
  final Function(String acgType)? onAcgTypeSelected;
  final bool showAcgCategories;
  final bool showHotCategories;

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    return ColoredBox(
      color: Colors.white,
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // ACG分类
          if (showAcgCategories) _buildAcgCategories(ref),
          
          // 热门分类
          if (showHotCategories) _buildHotCategories(ref),
          
          // 全部分类
          _buildAllCategories(ref),
        ],
      ),
    );
  }

  /// 构建ACG分类
  Widget _buildAcgCategories(WidgetRef ref) {
    final acgCategories = ref.watch(acgCategoriesProvider);
    
    if (acgCategories.isEmpty) return const SizedBox.shrink();

    return Container(
      padding: EdgeInsets.all(16.w),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'ACG专区',
            style: AppTextStyles.bodyLarge.copyWith(
              fontWeight: FontWeight.w600,
            ),
          ),
          SizedBox(height: 12.h),
          GridView.builder(
            shrinkWrap: true,
            physics: const NeverScrollableScrollPhysics(),
            gridDelegate: SliverGridDelegateWithFixedCrossAxisCount(
              crossAxisCount: 2,
              childAspectRatio: 3,
              crossAxisSpacing: 12.w,
              mainAxisSpacing: 8.h,
            ),
            itemCount: acgCategories.length,
            itemBuilder: (context, index) {
              final category = acgCategories[index];
              return _buildAcgCategoryCard(category);
            },
          ),
        ],
      ),
    );
  }

  /// 构建ACG分类卡片
  Widget _buildAcgCategoryCard(ProductCategory category) {
    return GestureDetector(
      onTap: () {
        onAcgTypeSelected?.call(category.code);
        onCategorySelected?.call(category);
      },
      child: Container(
        decoration: BoxDecoration(
          gradient: LinearGradient(
            colors: [
              AppColors.primary.withOpacity(0.1),
              AppColors.primary.withOpacity(0.05),
            ],
            begin: Alignment.topLeft,
            end: Alignment.bottomRight,
          ),
          borderRadius: BorderRadius.circular(8.r),
          border: Border.all(
            color: AppColors.primary.withOpacity(0.2),
          ),
        ),
        child: Row(
          children: [
            // 图标
            Container(
              width: 40.w,
              alignment: Alignment.center,
              child: Text(
                category.icon ?? '📦',
                style: TextStyle(fontSize: 20.sp),
              ),
            ),
            // 分类信息
            Expanded(
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    category.name,
                    style: AppTextStyles.bodyMedium.copyWith(
                      fontWeight: FontWeight.w600,
                      color: AppColors.primary,
                    ),
                  ),
                  if (category.productCount != null) ...[
                    SizedBox(height: 2.h),
                    Text(
                      '${category.productCount}件商品',
                      style: AppTextStyles.caption.copyWith(
                        color: AppColors.textSecondary,
                      ),
                    ),
                  ],
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  /// 构建热门分类
  Widget _buildHotCategories(WidgetRef ref) {
    final hotCategoriesState = ref.watch(hotCategoriesProvider);
    
    if (hotCategoriesState.isLoading) {
      return Container(
        padding: EdgeInsets.all(16.w),
        child: const LoadingWidget(),
      );
    }

    if (hotCategoriesState.categories.isEmpty) {
      return const SizedBox.shrink();
    }

    return Container(
      padding: EdgeInsets.all(16.w),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            '热门分类',
            style: AppTextStyles.bodyLarge.copyWith(
              fontWeight: FontWeight.w600,
            ),
          ),
          SizedBox(height: 12.h),
          Wrap(
            spacing: 8.w,
            runSpacing: 8.h,
            children: hotCategoriesState.categories.map(_buildHotCategoryChip).toList(),
          ),
        ],
      ),
    );
  }

  /// 构建热门分类标签
  Widget _buildHotCategoryChip(ProductCategory category) {
    return GestureDetector(
      onTap: () => onCategorySelected?.call(category),
      child: Container(
        padding: EdgeInsets.symmetric(horizontal: 12.w, vertical: 6.h),
        decoration: BoxDecoration(
          color: AppColors.background,
          borderRadius: BorderRadius.circular(16.r),
          border: Border.all(color: AppColors.borderLight),
        ),
        child: Row(
          mainAxisSize: MainAxisSize.min,
          children: [
            if (category.icon != null) ...[
              Text(
                category.icon!,
                style: TextStyle(fontSize: 14.sp),
              ),
              SizedBox(width: 4.w),
            ],
            Text(
              category.name,
              style: AppTextStyles.bodySmall.copyWith(
                color: AppColors.textSecondary,
              ),
            ),
          ],
        ),
      ),
    );
  }

  /// 构建全部分类
  Widget _buildAllCategories(WidgetRef ref) {
    final categoryTreeState = ref.watch(categoryTreeProvider);
    
    if (categoryTreeState.isLoading) {
      return Container(
        padding: EdgeInsets.all(16.w),
        child: const LoadingWidget(),
      );
    }

    if (categoryTreeState.error != null) {
      return Container(
        padding: EdgeInsets.all(16.w),
        child: Text(
          '加载失败: ${categoryTreeState.error}',
          style: AppTextStyles.bodyMedium.copyWith(
            color: AppColors.error,
          ),
        ),
      );
    }

    return Container(
      padding: EdgeInsets.all(16.w),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            '全部分类',
            style: AppTextStyles.bodyLarge.copyWith(
              fontWeight: FontWeight.w600,
            ),
          ),
          SizedBox(height: 12.h),
          ...categoryTreeState.categories.map((category) {
            return _buildCategoryItem(category, 0);
          }),
        ],
      ),
    );
  }

  /// 构建分类项
  Widget _buildCategoryItem(ProductCategory category, int level) {
    return Column(
      children: [
        GestureDetector(
          onTap: () => onCategorySelected?.call(category),
          child: Container(
            padding: EdgeInsets.symmetric(vertical: 8.h),
            margin: EdgeInsets.only(left: level * 16.w),
            child: Row(
              children: [
                // 图标
                if (category.icon != null) ...[
                  Text(
                    category.icon!,
                    style: TextStyle(fontSize: 16.sp),
                  ),
                  SizedBox(width: 8.w),
                ],
                // 分类名称
                Expanded(
                  child: Text(
                    category.name,
                    style: AppTextStyles.bodyMedium.copyWith(
                      fontWeight: level == 0 ? FontWeight.w600 : FontWeight.normal,
                      color: level == 0 ? AppColors.textPrimary : AppColors.textSecondary,
                    ),
                  ),
                ),
                // 商品数量
                if (category.productCount != null) ...[
                  Text(
                    '${category.productCount}',
                    style: AppTextStyles.caption.copyWith(
                      color: AppColors.textTertiary,
                    ),
                  ),
                  SizedBox(width: 4.w),
                ],
                // 展开图标
                if (category.hasChildren)
                  Icon(
                    Icons.chevron_right,
                    size: 16.sp,
                    color: AppColors.textTertiary,
                  ),
              ],
            ),
          ),
        ),
        // 子分类
        if (category.hasChildren) ...[
          ...category.children!.map((child) {
            return _buildCategoryItem(child, level + 1);
          }),
        ],
      ],
    );
  }
}

/// 分类选择弹窗
class CategorySelectionDialog extends ConsumerStatefulWidget {

  const CategorySelectionDialog({
    super.key,
    this.selectedCategory,
    this.onCategorySelected,
  });
  final ProductCategory? selectedCategory;
  final Function(ProductCategory category)? onCategorySelected;

  @override
  ConsumerState<CategorySelectionDialog> createState() => _CategorySelectionDialogState();
}

class _CategorySelectionDialogState extends ConsumerState<CategorySelectionDialog> {
  ProductCategory? _selectedCategory;

  @override
  void initState() {
    super.initState();
    _selectedCategory = widget.selectedCategory;
  }

  @override
  Widget build(BuildContext context) {
    return Dialog(
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(12.r),
      ),
      child: Container(
        width: double.infinity,
        constraints: BoxConstraints(maxHeight: 600.h),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            // 标题栏
            Container(
              padding: EdgeInsets.all(16.w),
              decoration: const BoxDecoration(
                border: Border(
                  bottom: BorderSide(color: AppColors.borderLight),
                ),
              ),
              child: Row(
                children: [
                  Text(
                    '选择分类',
                    style: AppTextStyles.headlineSmall.copyWith(
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                  const Spacer(),
                  IconButton(
                    onPressed: () => Navigator.of(context).pop(),
                    icon: Icon(Icons.close, size: 20.sp),
                    padding: EdgeInsets.zero,
                    constraints: BoxConstraints(
                      minWidth: 32.w,
                      minHeight: 32.w,
                    ),
                  ),
                ],
              ),
            ),
            // 分类列表
            Expanded(
              child: SingleChildScrollView(
                child: CategoryNavigation(
                  showAcgCategories: false,
                  showHotCategories: false,
                  onCategorySelected: (category) {
                    setState(() {
                      _selectedCategory = category;
                    });
                  },
                ),
              ),
            ),
            // 底部按钮
            Container(
              padding: EdgeInsets.all(16.w),
              decoration: const BoxDecoration(
                border: Border(
                  top: BorderSide(color: AppColors.borderLight),
                ),
              ),
              child: Row(
                children: [
                  Expanded(
                    child: OutlinedButton(
                      onPressed: () {
                        setState(() {
                          _selectedCategory = null;
                        });
                      },
                      child: const Text('清除'),
                    ),
                  ),
                  SizedBox(width: 12.w),
                  Expanded(
                    child: ElevatedButton(
                      onPressed: () {
                        if (_selectedCategory != null) {
                          widget.onCategorySelected?.call(_selectedCategory!);
                        }
                        Navigator.of(context).pop();
                      },
                      child: const Text('确定'),
                    ),
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }
}
