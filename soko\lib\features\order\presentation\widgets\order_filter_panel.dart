import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

import 'package:soko/core/enums/app_enums.dart';

/// 订单筛选面板组件
class OrderFilterPanel extends StatefulWidget {
  const OrderFilterPanel({
    super.key,
    this.onFilterApplied,
  });

  final ValueChanged<Map<String, dynamic>>? onFilterApplied;

  @override
  State<OrderFilterPanel> createState() => _OrderFilterPanelState();
}

class _OrderFilterPanelState extends State<OrderFilterPanel> {
  // 筛选条件
  OrderStatus? _selectedStatus;
  PayType? _selectedPayType;
  DateTimeRange? _dateRange;
  double? _minAmount;
  double? _maxAmount;
  String? _selectedProvince;
  
  // 控制器
  final TextEditingController _minAmountController = TextEditingController();
  final TextEditingController _maxAmountController = TextEditingController();

  @override
  void dispose() {
    _minAmountController.dispose();
    _maxAmountController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.vertical(top: Radius.circular(20.r)),
      ),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          // 标题栏
          _buildHeader(),
          
          // 筛选内容
          Flexible(
            child: SingleChildScrollView(
              padding: EdgeInsets.all(16.w),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  // 订单状态
                  _buildStatusFilter(),
                  SizedBox(height: 20.h),
                  
                  // 支付方式
                  _buildPayTypeFilter(),
                  SizedBox(height: 20.h),
                  
                  // 时间范围
                  _buildDateRangeFilter(),
                  SizedBox(height: 20.h),
                  
                  // 金额范围
                  _buildAmountRangeFilter(),
                  SizedBox(height: 20.h),
                  
                  // 收货地区
                  _buildProvinceFilter(),
                  SizedBox(height: 32.h),
                ],
              ),
            ),
          ),
          
          // 底部按钮
          _buildBottomButtons(),
        ],
      ),
    );
  }

  /// 构建标题栏
  Widget _buildHeader() {
    return Container(
      padding: EdgeInsets.all(16.w),
      decoration: BoxDecoration(
        border: Border(
          bottom: BorderSide(color: Colors.grey[200]!),
        ),
      ),
      child: Row(
        children: [
          Text(
            '筛选条件',
            style: TextStyle(
              fontSize: 18.sp,
              fontWeight: FontWeight.w600,
            ),
          ),
          const Spacer(),
          GestureDetector(
            onTap: () => Navigator.of(context).pop(),
            child: Icon(
              Icons.close,
              size: 24.w,
              color: Colors.grey[600],
            ),
          ),
        ],
      ),
    );
  }

  /// 构建状态筛选
  Widget _buildStatusFilter() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        _buildSectionTitle('订单状态'),
        SizedBox(height: 12.h),
        Wrap(
          spacing: 8.w,
          runSpacing: 8.h,
          children: [
            _buildFilterChip('全部', _selectedStatus == null, () {
              setState(() => _selectedStatus = null);
            }),
            ...OrderStatus.values.map((status) => _buildFilterChip(
              _getStatusText(status),
              _selectedStatus == status,
              () => setState(() => _selectedStatus = status),
            )),
          ],
        ),
      ],
    );
  }

  /// 构建支付方式筛选
  Widget _buildPayTypeFilter() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        _buildSectionTitle('支付方式'),
        SizedBox(height: 12.h),
        Wrap(
          spacing: 8.w,
          runSpacing: 8.h,
          children: [
            _buildFilterChip('全部', _selectedPayType == null, () {
              setState(() => _selectedPayType = null);
            }),
            ...PayType.values.map((payType) => _buildFilterChip(
              _getPayTypeText(payType),
              _selectedPayType == payType,
              () => setState(() => _selectedPayType = payType),
            )),
          ],
        ),
      ],
    );
  }

  /// 构建时间范围筛选
  Widget _buildDateRangeFilter() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        _buildSectionTitle('下单时间'),
        SizedBox(height: 12.h),
        GestureDetector(
          onTap: _selectDateRange,
          child: Container(
            padding: EdgeInsets.all(12.w),
            decoration: BoxDecoration(
              border: Border.all(color: Colors.grey[300]!),
              borderRadius: BorderRadius.circular(8.r),
            ),
            child: Row(
              children: [
                Icon(
                  Icons.date_range,
                  size: 20.w,
                  color: Colors.grey[600],
                ),
                SizedBox(width: 8.w),
                Expanded(
                  child: Text(
                    _dateRange != null
                        ? '${_formatDate(_dateRange!.start)} - ${_formatDate(_dateRange!.end)}'
                        : '选择时间范围',
                    style: TextStyle(
                      fontSize: 14.sp,
                      color: _dateRange != null ? Colors.grey[800] : Colors.grey[500],
                    ),
                  ),
                ),
                if (_dateRange != null)
                  GestureDetector(
                    onTap: () => setState(() => _dateRange = null),
                    child: Icon(
                      Icons.clear,
                      size: 20.w,
                      color: Colors.grey[600],
                    ),
                  ),
              ],
            ),
          ),
        ),
      ],
    );
  }

  /// 构建金额范围筛选
  Widget _buildAmountRangeFilter() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        _buildSectionTitle('订单金额'),
        SizedBox(height: 12.h),
        Row(
          children: [
            Expanded(
              child: TextField(
                controller: _minAmountController,
                keyboardType: TextInputType.number,
                decoration: InputDecoration(
                  hintText: '最低金额',
                  prefixText: '¥ ',
                  border: OutlineInputBorder(
                    borderRadius: BorderRadius.circular(8.r),
                  ),
                  contentPadding: EdgeInsets.symmetric(horizontal: 12.w, vertical: 8.h),
                ),
                onChanged: (value) {
                  _minAmount = double.tryParse(value);
                },
              ),
            ),
            SizedBox(width: 16.w),
            Text(
              '至',
              style: TextStyle(
                fontSize: 14.sp,
                color: Colors.grey[600],
              ),
            ),
            SizedBox(width: 16.w),
            Expanded(
              child: TextField(
                controller: _maxAmountController,
                keyboardType: TextInputType.number,
                decoration: InputDecoration(
                  hintText: '最高金额',
                  prefixText: '¥ ',
                  border: OutlineInputBorder(
                    borderRadius: BorderRadius.circular(8.r),
                  ),
                  contentPadding: EdgeInsets.symmetric(horizontal: 12.w, vertical: 8.h),
                ),
                onChanged: (value) {
                  _maxAmount = double.tryParse(value);
                },
              ),
            ),
          ],
        ),
      ],
    );
  }

  /// 构建省份筛选
  Widget _buildProvinceFilter() {
    final provinces = ['北京', '上海', '广东', '浙江', '江苏', '山东', '河南', '四川'];
    
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        _buildSectionTitle('收货地区'),
        SizedBox(height: 12.h),
        Wrap(
          spacing: 8.w,
          runSpacing: 8.h,
          children: [
            _buildFilterChip('全部', _selectedProvince == null, () {
              setState(() => _selectedProvince = null);
            }),
            ...provinces.map((province) => _buildFilterChip(
              province,
              _selectedProvince == province,
              () => setState(() => _selectedProvince = province),
            )),
          ],
        ),
      ],
    );
  }

  /// 构建底部按钮
  Widget _buildBottomButtons() {
    return Container(
      padding: EdgeInsets.all(16.w),
      decoration: BoxDecoration(
        border: Border(
          top: BorderSide(color: Colors.grey[200]!),
        ),
      ),
      child: Row(
        children: [
          // 重置按钮
          Expanded(
            child: OutlinedButton(
              onPressed: _resetFilters,
              style: OutlinedButton.styleFrom(
                padding: EdgeInsets.symmetric(vertical: 12.h),
              ),
              child: const Text('重置'),
            ),
          ),
          SizedBox(width: 16.w),
          
          // 确定按钮
          Expanded(
            flex: 2,
            child: ElevatedButton(
              onPressed: _applyFilters,
              style: ElevatedButton.styleFrom(
                backgroundColor: Theme.of(context).primaryColor,
                foregroundColor: Colors.white,
                padding: EdgeInsets.symmetric(vertical: 12.h),
              ),
              child: const Text('确定'),
            ),
          ),
        ],
      ),
    );
  }

  /// 构建章节标题
  Widget _buildSectionTitle(String title) {
    return Text(
      title,
      style: TextStyle(
        fontSize: 16.sp,
        fontWeight: FontWeight.w600,
        color: Colors.grey[800],
      ),
    );
  }

  /// 构建筛选标签
  Widget _buildFilterChip(String label, bool isSelected, VoidCallback onTap) {
    return GestureDetector(
      onTap: onTap,
      child: Container(
        padding: EdgeInsets.symmetric(horizontal: 12.w, vertical: 6.h),
        decoration: BoxDecoration(
          color: isSelected ? Theme.of(context).primaryColor : Colors.grey[100],
          borderRadius: BorderRadius.circular(16.r),
          border: Border.all(
            color: isSelected ? Theme.of(context).primaryColor : Colors.grey[300]!,
          ),
        ),
        child: Text(
          label,
          style: TextStyle(
            fontSize: 12.sp,
            color: isSelected ? Colors.white : Colors.grey[700],
            fontWeight: isSelected ? FontWeight.w500 : FontWeight.normal,
          ),
        ),
      ),
    );
  }

  /// 选择时间范围
  void _selectDateRange() async {
    final picked = await showDateRangePicker(
      context: context,
      firstDate: DateTime.now().subtract(const Duration(days: 365)),
      lastDate: DateTime.now(),
      initialDateRange: _dateRange,
    );
    
    if (picked != null) {
      setState(() => _dateRange = picked);
    }
  }

  /// 重置筛选条件
  void _resetFilters() {
    setState(() {
      _selectedStatus = null;
      _selectedPayType = null;
      _dateRange = null;
      _minAmount = null;
      _maxAmount = null;
      _selectedProvince = null;
      _minAmountController.clear();
      _maxAmountController.clear();
    });
  }

  /// 应用筛选条件
  void _applyFilters() {
    final filters = <String, dynamic>{};
    
    if (_selectedStatus != null) {
      filters['status'] = _selectedStatus;
    }
    if (_selectedPayType != null) {
      filters['payType'] = _selectedPayType;
    }
    if (_dateRange != null) {
      filters['startDate'] = _dateRange!.start.millisecondsSinceEpoch;
      filters['endDate'] = _dateRange!.end.millisecondsSinceEpoch;
    }
    if (_minAmount != null) {
      filters['minAmount'] = _minAmount;
    }
    if (_maxAmount != null) {
      filters['maxAmount'] = _maxAmount;
    }
    if (_selectedProvince != null) {
      filters['province'] = _selectedProvince;
    }
    
    widget.onFilterApplied?.call(filters);
    Navigator.of(context).pop();
  }

  /// 获取状态文本
  String _getStatusText(OrderStatus status) {
    switch (status) {
      case OrderStatus.pending:
        return '待付款';
      case OrderStatus.paid:
        return '待发货';
      case OrderStatus.shipped:
        return '已发货';
      case OrderStatus.completed:
        return '已完成';
      case OrderStatus.cancelled:
        return '已取消';
      default:
        return '未知';
    }
  }

  /// 获取支付方式文本
  String _getPayTypeText(PayType payType) {
    switch (payType) {
      case PayType.wechat:
        return '微信支付';
      case PayType.alipay:
        return '支付宝';
      case PayType.bankCard:
        return '银行卡';
      default:
        return '其他';
    }
  }

  /// 格式化日期
  String _formatDate(DateTime date) {
    return '${date.year}-${date.month.toString().padLeft(2, '0')}-${date.day.toString().padLeft(2, '0')}';
  }
}
