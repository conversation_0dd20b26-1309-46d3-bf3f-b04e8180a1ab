import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:go_router/go_router.dart';

import 'package:soko/shared/presentation/widgets/custom_app_bar.dart';
import 'package:soko/shared/presentation/widgets/loading_widget.dart';
import 'package:soko/shared/presentation/widgets/empty_state_widget.dart';
import 'package:soko/shared/presentation/widgets/error_retry_widget.dart';
import 'package:soko/features/recycle/presentation/providers/recycle_order_list_provider.dart';
import 'package:soko/features/recycle/presentation/widgets/recycle_order_card.dart';
import 'package:soko/features/recycle/presentation/widgets/recycle_order_filter_bar.dart';
import 'package:soko/core/router/app_routes.dart';

/// 回收订单列表页面
class RecycleOrderListPage extends ConsumerStatefulWidget {
  const RecycleOrderListPage({super.key});

  @override
  ConsumerState<RecycleOrderListPage> createState() => _RecycleOrderListPageState();
}

class _RecycleOrderListPageState extends ConsumerState<RecycleOrderListPage>
    with TickerProviderStateMixin {
  late TabController _tabController;
  late ScrollController _scrollController;

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 6, vsync: this);
    _scrollController = ScrollController();
    
    // 监听滚动事件，实现分页加载
    _scrollController.addListener(_onScroll);
    
    // 页面初始化时加载数据
    WidgetsBinding.instance.addPostFrameCallback((_) {
      ref.read(recycleOrderListProvider.notifier).loadOrders();
    });
  }

  @override
  void dispose() {
    _tabController.dispose();
    _scrollController.dispose();
    super.dispose();
  }

  void _onScroll() {
    if (_scrollController.position.pixels >=
        _scrollController.position.maxScrollExtent - 200) {
      // 距离底部200px时开始加载更多
      ref.read(recycleOrderListProvider.notifier).loadMoreOrders();
    }
  }

  @override
  Widget build(BuildContext context) {
    final orderListState = ref.watch(recycleOrderListProvider);

    return Scaffold(
      appBar: const CustomAppBar(
        title: '我的回收订单',
        showBackButton: true,
      ),
      body: Column(
        children: [
          // 状态筛选标签栏
          Container(
            color: Colors.white,
            child: TabBar(
              controller: _tabController,
              isScrollable: true,
              labelColor: Theme.of(context).primaryColor,
              unselectedLabelColor: Colors.grey[600],
              indicatorColor: Theme.of(context).primaryColor,
              indicatorSize: TabBarIndicatorSize.label,
              labelStyle: TextStyle(
                fontSize: 14.sp,
                fontWeight: FontWeight.w500,
              ),
              unselectedLabelStyle: TextStyle(
                fontSize: 14.sp,
                fontWeight: FontWeight.normal,
              ),
              onTap: (index) => _onTabChanged(index),
              tabs: const [
                Tab(text: '全部'),
                Tab(text: '待审核'),
                Tab(text: '已报价'),
                Tab(text: '已寄送'),
                Tab(text: '已完成'),
                Tab(text: '已取消'),
              ],
            ),
          ),
          
          // 筛选工具栏
          RecycleOrderFilterBar(
            onFilterChanged: (filters) {
              ref.read(recycleOrderListProvider.notifier).applyFilters(filters);
            },
          ),
          
          // 订单列表内容
          Expanded(
            child: RefreshIndicator(
              onRefresh: () => ref.read(recycleOrderListProvider.notifier).refreshOrders(),
              child: orderListState.when(
                idle: () => const Center(child: Text('请下拉刷新')),
                loading: () => const LoadingWidget(),
                success: (data) => _buildOrderList(data),
                error: (error) => ErrorRetryWidget(
                  message: error,
                  onRetry: () => ref.read(recycleOrderListProvider.notifier).loadOrders(),
                ),
              ),
            ),
          ),
        ],
      ),
      
      // 浮动操作按钮 - 创建新订单
      floatingActionButton: FloatingActionButton.extended(
        onPressed: _navigateToCreateOrder,
        icon: const Icon(Icons.add),
        label: const Text('新建回收订单'),
        backgroundColor: Theme.of(context).primaryColor,
      ),
    );
  }

  /// 构建订单列表
  Widget _buildOrderList(RecycleOrderListData data) {
    if (data.orders.isEmpty) {
      return EmptyStateWidget(
        icon: Icons.recycling,
        title: '暂无回收订单',
        subtitle: '您还没有创建任何回收订单',
        actionText: '立即创建',
        onAction: _navigateToCreateOrder,
      );
    }

    return ListView.builder(
      controller: _scrollController,
      padding: EdgeInsets.all(16.w),
      itemCount: data.orders.length + (data.hasMore ? 1 : 0),
      itemBuilder: (context, index) {
        if (index == data.orders.length) {
          // 加载更多指示器
          return _buildLoadMoreIndicator(data.isLoadingMore);
        }

        final order = data.orders[index];
        return Padding(
          padding: EdgeInsets.only(bottom: 12.h),
          child: RecycleOrderCard(
            order: order,
            onTap: () => _navigateToOrderDetail(order.id),
            onCancel: order.canCancel ? () => _showCancelDialog(order.id) : null,
            onConfirmShipment: order.canConfirmShipment 
                ? () => _navigateToShipping(order.id) 
                : null,
          ),
        );
      },
    );
  }

  /// 构建加载更多指示器
  Widget _buildLoadMoreIndicator(bool isLoading) {
    return Container(
      padding: EdgeInsets.symmetric(vertical: 16.h),
      alignment: Alignment.center,
      child: isLoading
          ? Row(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                SizedBox(
                  width: 16.w,
                  height: 16.w,
                  child: const CircularProgressIndicator(strokeWidth: 2),
                ),
                SizedBox(width: 8.w),
                Text(
                  '加载中...',
                  style: TextStyle(
                    fontSize: 12.sp,
                    color: Colors.grey[600],
                  ),
                ),
              ],
            )
          : Text(
              '没有更多数据了',
              style: TextStyle(
                fontSize: 12.sp,
                color: Colors.grey[600],
              ),
            ),
    );
  }

  /// 标签页切换
  void _onTabChanged(int index) {
    String? status;
    switch (index) {
      case 0:
        status = null; // 全部
        break;
      case 1:
        status = 'PENDING_APPROVAL'; // 待审核
        break;
      case 2:
        status = 'PRICE_QUOTED'; // 已报价
        break;
      case 3:
        status = 'SHIPPING_CONFIRMED'; // 已寄送
        break;
      case 4:
        status = 'COMPLETED'; // 已完成
        break;
      case 5:
        status = 'CANCELLED'; // 已取消
        break;
    }
    
    ref.read(recycleOrderListProvider.notifier).filterByStatus(status);
  }

  /// 显示取消订单对话框
  void _showCancelDialog(String orderId) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('取消订单'),
        content: const Text('确定要取消这个回收订单吗？取消后无法恢复。'),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('取消'),
          ),
          TextButton(
            onPressed: () {
              Navigator.of(context).pop();
              ref.read(recycleOrderListProvider.notifier).cancelOrder(orderId);
            },
            child: const Text('确定'),
          ),
        ],
      ),
    );
  }

  /// 导航到创建订单页面
  void _navigateToCreateOrder() {
    context.push(AppRoutes.createRecycleOrder);
  }

  /// 导航到订单详情页面
  void _navigateToOrderDetail(String orderId) {
    context.push(AppRoutes.recycleOrderDetailPath(orderId));
  }

  /// 导航到物流页面
  void _navigateToShipping(String orderId) {
    context.push('${AppRoutes.recycleShipping}?orderId=$orderId');
  }
}
