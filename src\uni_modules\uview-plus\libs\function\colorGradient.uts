/**
 * 求两个颜色之间的渐变值
 * @param {string} startColor 开始的颜色
 * @param {string} endColor 结束的颜色
 * @param {number} step 颜色等分的份额
 * */
export function colorGradient(startColor = 'rgb(0, 0, 0)',
	endColor = 'rgb(255, 255, 255)', step = 10): Array<string> {
	// console.log('startColor', startColor)
	let startColorRgb = hexToRgb(startColor, false);
	let startRGB: number[] = [];
	if (startColorRgb instanceof Array) {
		startRGB = startColorRgb as number[] // 转换为rgb数组模式
		const startR:number = startRGB[0]
		const startG:number = startRGB[1]
		const startB:number = startRGB[2]
		
		const endRGB:number[]= hexToRgb(endColor, false) as number[]
		const endR:number = endRGB[0]
		const endG:number = endRGB[1]
		const endB:number = endRGB[2]
		
		const sR = (endR - startR) / step // 总差值
		const sG = (endG - startG) / step
		const sB = (endB - startB) / step
		
		const colorArr: Array<string> = []
		for (let i = 0; i < step; i++) {
		  // 计算每一步的hex值
			let sr: string = JSON.stringify(Math.round((sR * i + startR)))
			let sg: string = JSON.stringify(Math.round((sG * i + startG)))
			let sb: string = JSON.stringify(Math.round((sB * i + startB)))
		  let hex = rgbToHex(`rgb(${sr},${sg},${sb})`)
		  // 确保第一个颜色值为startColor的值
		  if (i === 0) hex = rgbToHex(startColor)
		  // 确保最后一个颜色值为endColor的值
		  if (i === step - 1) hex = rgbToHex(endColor)
		  colorArr.push(hex)
		}
		return colorArr
	}
	return []
}

// 将hex表示方式转换为rgb表示方式(这里返回rgb数组模式)
export function hexToRgb(sColor: string, str = true): any {
    const reg = /^#([0-9a-fA-f]{3}|[0-9a-fA-f]{6})$/
    sColor = sColor.toLowerCase()
    if (sColor != '' && sColor != null && reg.test(sColor)
		) {
		// 16进制转换为rgb数组
        if (sColor.length === 4) {
            let sColorNew = '#'
            for (let i = 1; i < 4; i += 1) {
                sColorNew += sColor.slice(i, i + 1).concat(sColor.slice(i, i + 1))
            }
            sColor = sColorNew
        }
        // 处理六位的颜色值
        const sColorChange: number[] = []
        for (let i = 1; i < 7; i += 2) {
			// console.log('###',sColor.slice(i, i + 2), '###')
			// console.log(parseInt(`0x${sColor.slice(i, i + 2)}`))
            sColorChange.push(parseInt(`0x${sColor.slice(i, i + 2)}`))
        }
        if (str == false) {
			// console.log('strtrue', sColorChange)
            return sColorChange
        }
		let sc0 = JSON.stringify(sColorChange[0])
		let sc1 = JSON.stringify(sColorChange[1])
		let sc2 = JSON.stringify(sColorChange[2])
        return `rgb(${sc0},${sc1},${sc2})`
    }
	// rgb字符串转换为rgb数组
	if (/^(rgb|RGB)/.test(sColor)) {
        const arr: string[] = sColor.replace(/(?:\(|\)|rgb|RGB)*/g, '').split(',')
		let arrNumber: number[] = []
		arr.forEach(val => {
			arrNumber.push(parseInt(val))
		})
		return arrNumber
    }
    return sColor
}

// 将rgb表示方式转换为hex表示方式
export function rgbToHex(rgb: string): string {
    const _this = rgb
    const reg = /^#([0-9a-fA-f]{3}|[0-9a-fA-f]{6})$/
    if (/^(rgb|RGB)/.test(_this)) {
        const aColor: string[] = _this.replace(/(?:\(|\)|rgb|RGB)*/g, '').split(',')
        let strHex = '#'
        for (let i = 0; i < aColor.length; i++) {
            let hex = parseInt(aColor[i]).toString(16)
            hex = (hex).length == 1 ? `${0}${hex}` : hex // 保证每个rgb的值为2位
            if (hex === '0') {
                hex += hex
            }
            strHex += hex
        }
        if (strHex.length !== 7) {
            strHex = _this
        }
        return strHex
    } else if (reg.test(_this)) {
        // const aNum = _this.replace(/#/, '').split('')
        // if (aNum.length === 6) {
        //     return _this
        // } if (aNum.length === 3) {
        //     let numHex = '#'
        //     for (let i = 0; i < aNum.length; i += 1) {
        //         numHex += (aNum[i] + aNum[i])
        //     }
        //     return numHex
        // }
    }
	return _this
}

// /**
// * JS颜色十六进制转换为rgb或rgba,返回的格式为 rgba（255，255，255，0.5）字符串
// * sHex为传入的十六进制的色值
// * alpha为rgba的透明度
// */
// export function colorToRgba(color, alpha): string {
//     color = rgbToHex(color)
//     // 十六进制颜色值的正则表达式
//     const reg = /^#([0-9a-fA-f]{3}|[0-9a-fA-f]{6})$/
//     /* 16进制颜色转为RGB格式 */
//     let sColor = String(color).toLowerCase()
//     if (sColor && reg.test(sColor)) {
//         if (sColor.length === 4) {
//             let sColorNew = '#'
//             for (let i = 1; i < 4; i += 1) {
//                 sColorNew += sColor.slice(i, i + 1).concat(sColor.slice(i, i + 1))
//             }
//             sColor = sColorNew
//         }
//         // 处理六位的颜色值
//         const sColorChange = []
//         for (let i = 1; i < 7; i += 2) {
//             sColorChange.push(parseInt(`0x${sColor.slice(i, i + 2)}`))
//         }
//         // return sColorChange.join(',')
//         return `rgba(${sColorChange.join(',')},${alpha})`
//     }

//     return sColor
// }

export default {
    colorGradient,
    hexToRgb,
    rgbToHex,
    // colorToRgba
}
