include: package:very_good_analysis/analysis_options.yaml

analyzer:
  exclude:
    - "**/*.g.dart"
    - "**/*.freezed.dart"
    - "**/*.gr.dart"
    - "build/**"
    - "lib/generated/**"
  
  language:
    strict-casts: true
    strict-inference: true
    strict-raw-types: true

linter:
  rules:
    # 禁用一些过于严格的规则
    public_member_api_docs: false
    lines_longer_than_80_chars: false
    prefer_relative_imports: false
    
    # 启用额外的规则
    always_use_package_imports: true
    avoid_print: true
    avoid_unnecessary_containers: true
    avoid_web_libraries_in_flutter: true
    no_logic_in_create_state: true
    prefer_const_constructors: true
    prefer_const_declarations: true
    prefer_const_literals_to_create_immutables: true
    sized_box_for_whitespace: true
    sort_child_properties_last: true
    use_build_context_synchronously: true
    use_full_hex_values_for_flutter_colors: true
    use_key_in_widget_constructors: true
