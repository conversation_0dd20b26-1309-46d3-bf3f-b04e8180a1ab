import 'package:flutter_riverpod/flutter_riverpod.dart';

import 'package:soko/features/product/domain/entities/product.dart';
import 'package:soko/features/product/data/datasources/product_api_service.dart';

/// 商品推荐状态管理
class ProductRecommendationNotifier extends StateNotifier<AsyncValue<Map<String, List<Product>>>> {
  ProductRecommendationNotifier(this._apiService) : super(const AsyncValue.loading());

  final ProductApiService _apiService;

  /// 加载所有推荐数据
  Future<void> loadRecommendations() async {
    state = const AsyncValue.loading();

    try {
      final results = await Future.wait([
        _apiService.getRecommendedProducts(limit: 20),
        _apiService.getPopularProducts(limit: 20),
        _apiService.getLatestProducts(limit: 20),
      ]);

      final recommendationData = {
        'recommended': results[0],
        'popular': results[1],
        'latest': results[2],
      };

      state = AsyncValue.data(recommendationData);
    } catch (error, stackTrace) {
      state = AsyncValue.error(error, stackTrace);
    }
  }

  /// 刷新推荐数据
  Future<void> refreshRecommendations() async {
    await loadRecommendations();
  }

  /// 获取个性推荐商品
  List<Product> get recommendedProducts {
    return state.value?['recommended'] ?? [];
  }

  /// 获取热门商品
  List<Product> get popularProducts {
    return state.value?['popular'] ?? [];
  }

  /// 获取最新商品
  List<Product> get latestProducts {
    return state.value?['latest'] ?? [];
  }
}

/// 商品推荐Provider
final productRecommendationProvider = StateNotifierProvider<ProductRecommendationNotifier, AsyncValue<Map<String, List<Product>>>>((ref) {
  final apiService = ref.watch(productApiServiceProvider);
  return ProductRecommendationNotifier(apiService);
});

/// 个性推荐商品Provider
final recommendedProductsProvider = FutureProvider<List<Product>>((ref) async {
  final apiService = ref.watch(productApiServiceProvider);
  return apiService.getRecommendedProducts(limit: 20);
});

/// 热门商品Provider
final popularProductsProvider = FutureProvider<List<Product>>((ref) async {
  final apiService = ref.watch(productApiServiceProvider);
  return apiService.getPopularProducts(limit: 20);
});

/// 最新商品Provider
final latestProductsProvider = FutureProvider<List<Product>>((ref) async {
  final apiService = ref.watch(productApiServiceProvider);
  return apiService.getLatestProducts(limit: 20);
});

/// 分类推荐商品Provider
final categoryRecommendationProvider = FutureProvider.family<List<Product>, String>((ref, categoryId) async {
  final apiService = ref.watch(productApiServiceProvider);
  return apiService.getRecommendedProducts(categoryId: categoryId, limit: 10);
});

/// 相似商品推荐Provider
final similarProductsProvider = FutureProvider.family<List<Product>, String>((ref, productId) async {
  final apiService = ref.watch(productApiServiceProvider);
  return apiService.getSimilarProducts(productId, limit: 10);
});
