import { defineMixin } from '../../libs/vue'
import defProps from './backtop'
let crtProp = defProps['backtop'] as UTSJSONObject
export const propsBacktop = defineMixin({
    props: {
        // 返回顶部的形状，circle-圆形，square-方形
        mode: {
            type: String,
            default: crtProp['mode']
        },
        // 自定义图标
        icon: {
            type: String,
            default: crtProp['icon']
        },
        // 提示文字
        text: {
            type: String,
            default: crtProp['text']
        },
        // 返回顶部滚动时间
        duration: {
            type: [String, Number],
            default: crtProp['duration']
        },
        // 滚动距离
        scrollTop: {
            type: [String, Number],
            default: crtProp['scrollTop']
        },
        // 距离顶部多少距离显示，单位px
        top: {
            type: [String, Number],
            default: crtProp['top']
        },
        // 返回顶部按钮到底部的距离，单位px
        bottom: {
            type: [String, Number],
            default: crtProp['bottom']
        },
        // 返回顶部按钮到右边的距离，单位px
        right: {
            type: [String, Number],
            default: crtProp['right']
        },
        // 层级
        zIndex: {
            type: [String, Number],
            default: crtProp['zIndex']
        },
        // 图标的样式，对象形式
        iconStyle: {
            type: Object,
            default: crtProp['iconStyle']
        }
    }
})
