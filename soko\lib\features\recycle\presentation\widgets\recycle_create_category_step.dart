import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

import 'package:soko/shared/presentation/widgets/loading_widget.dart';
import 'package:soko/features/recycle/domain/entities/recycle_models.dart';
import 'package:soko/features/recycle/presentation/providers/recycle_create_provider.dart';

/// 回收订单创建 - 分类选择步骤
class RecycleCreateCategoryStep extends ConsumerStatefulWidget {

  const RecycleCreateCategoryStep({
    super.key,
    this.selectedCategoryId,
    required this.onCategorySelected,
  });
  final String? selectedCategoryId;
  final ValueChanged<String> onCategorySelected;

  @override
  ConsumerState<RecycleCreateCategoryStep> createState() => _RecycleCreateCategoryStepState();
}

class _RecycleCreateCategoryStepState extends ConsumerState<RecycleCreateCategoryStep> {
  @override
  void initState() {
    super.initState();
    // 加载分类数据
    WidgetsBinding.instance.addPostFrameCallback((_) {
      ref.read(recycleCreateProvider.notifier).loadCategories();
    });
  }

  @override
  Widget build(BuildContext context) {
    final createState = ref.watch(recycleCreateProvider);

    return Padding(
      padding: EdgeInsets.all(16.w),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // 步骤标题
          Text(
            '选择商品分类',
            style: TextStyle(
              fontSize: 24.sp,
              fontWeight: FontWeight.w600,
              color: Colors.black87,
            ),
          ),
          SizedBox(height: 8.h),
          Text(
            '请选择您要回收的商品类型',
            style: TextStyle(
              fontSize: 16.sp,
              color: Colors.grey[600],
            ),
          ),
          SizedBox(height: 24.h),

          // 分类列表
          Expanded(
            child: createState.categoriesState.when(
              idle: () => const SizedBox.shrink(),
              loading: () => const LoadingWidget(),
              success: _buildCategoriesList,
              error: _buildErrorWidget,
            ),
          ),
        ],
      ),
    );
  }

  /// 构建分类列表
  Widget _buildCategoriesList(List<CategoryItem> categories) {
    return GridView.builder(
      gridDelegate: SliverGridDelegateWithFixedCrossAxisCount(
        crossAxisCount: 2,
        crossAxisSpacing: 16.w,
        mainAxisSpacing: 16.h,
        childAspectRatio: 1.2,
      ),
      itemCount: categories.length,
      itemBuilder: (context, index) {
        final category = categories[index];
        return _buildCategoryCard(category);
      },
    );
  }

  /// 构建分类卡片
  Widget _buildCategoryCard(CategoryItem category) {
    final isSelected = widget.selectedCategoryId == category.id;

    return InkWell(
      onTap: () => widget.onCategorySelected(category.id),
      borderRadius: BorderRadius.circular(12.r),
      child: Container(
        padding: EdgeInsets.all(16.w),
        decoration: BoxDecoration(
          color: isSelected 
              ? Colors.blue.withValues(alpha: 0.1)
              : Colors.white,
          borderRadius: BorderRadius.circular(12.r),
          border: Border.all(
            color: isSelected 
                ? Colors.blue 
                : Colors.grey.withValues(alpha: 0.3),
            width: isSelected ? 2 : 1,
          ),
          boxShadow: [
            BoxShadow(
              color: Colors.black.withValues(alpha: 0.05),
              blurRadius: 8,
              offset: const Offset(0, 2),
            ),
          ],
        ),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            // 分类图标
            Container(
              width: 60.w,
              height: 60.w,
              decoration: BoxDecoration(
                color: _getCategoryColor(category.name).withValues(alpha: 0.1),
                borderRadius: BorderRadius.circular(30.r),
              ),
              child: Icon(
                _getCategoryIcon(category.name),
                color: _getCategoryColor(category.name),
                size: 30.w,
              ),
            ),
            SizedBox(height: 12.h),

            // 分类名称
            Text(
              category.name,
              style: TextStyle(
                fontSize: 16.sp,
                fontWeight: FontWeight.w600,
                color: isSelected ? Colors.blue : Colors.black87,
              ),
              textAlign: TextAlign.center,
            ),
            SizedBox(height: 4.h),

            // 分类描述
            if (category.description?.isNotEmpty == true)
              Text(
                category.description!,
                style: TextStyle(
                  fontSize: 12.sp,
                  color: Colors.grey[600],
                ),
                textAlign: TextAlign.center,
                maxLines: 2,
                overflow: TextOverflow.ellipsis,
              ),

            // 选中指示器
            if (isSelected) ...[
              SizedBox(height: 8.h),
              Icon(
                Icons.check_circle,
                color: Colors.blue,
                size: 20.w,
              ),
            ],
          ],
        ),
      ),
    );
  }

  /// 构建错误组件
  Widget _buildErrorWidget(String error) {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.error_outline,
            size: 64.w,
            color: Colors.red[300],
          ),
          SizedBox(height: 16.h),
          Text(
            '加载失败',
            style: TextStyle(
              fontSize: 18.sp,
              fontWeight: FontWeight.w600,
              color: Colors.black87,
            ),
          ),
          SizedBox(height: 8.h),
          Text(
            error,
            style: TextStyle(
              fontSize: 14.sp,
              color: Colors.grey[600],
            ),
            textAlign: TextAlign.center,
          ),
          SizedBox(height: 24.h),
          ElevatedButton(
            onPressed: () {
              ref.read(recycleCreateProvider.notifier).loadCategories();
            },
            child: const Text('重试'),
          ),
        ],
      ),
    );
  }

  /// 获取分类图标
  IconData _getCategoryIcon(String categoryName) {
    switch (categoryName.toLowerCase()) {
      case '手机':
      case 'phone':
        return Icons.smartphone;
      case '笔记本':
      case 'laptop':
        return Icons.laptop;
      case '平板':
      case 'tablet':
        return Icons.tablet;
      case '手表':
      case 'watch':
        return Icons.watch;
      case '相机':
      case 'camera':
        return Icons.camera_alt;
      case '游戏机':
      case 'game':
        return Icons.games;
      case '耳机':
      case 'headphone':
        return Icons.headphones;
      case '其他':
      case 'other':
        return Icons.more_horiz;
      default:
        return Icons.devices;
    }
  }

  /// 获取分类颜色
  Color _getCategoryColor(String categoryName) {
    switch (categoryName.toLowerCase()) {
      case '手机':
      case 'phone':
        return Colors.blue;
      case '笔记本':
      case 'laptop':
        return Colors.green;
      case '平板':
      case 'tablet':
        return Colors.orange;
      case '手表':
      case 'watch':
        return Colors.purple;
      case '相机':
      case 'camera':
        return Colors.red;
      case '游戏机':
      case 'game':
        return Colors.indigo;
      case '耳机':
      case 'headphone':
        return Colors.teal;
      case '其他':
      case 'other':
        return Colors.grey;
      default:
        return Colors.blueGrey;
    }
  }
}
