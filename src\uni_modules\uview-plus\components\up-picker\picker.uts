/*
 * <AUTHOR> jry
 * @Description  :
 * @version      : 4.0
 * @Date         : 2024-09-12 15:25:27
 * @LastAuthor   : jry
 * @lastTime     : 2024-09-12 15:25:27
 * @FilePath     : /uview-plus/libs/config/props/picker
 */
export default {
    // picker
    picker: {
        show: false,
		popupMode: 'bottom',
        showToolbar: true,
        title: '',
        columns: [] as Array<Array<any>>,
        loading: false,
        itemHeight: 44,
        cancelText: '取消',
        confirmText: '确定',
        cancelColor: '#909193',
        confirmColor: '#3c9cff',
        visibleItemCount: 5,
        keyName: 'text',
        closeOnClickOverlay: false,
        defaultIndex: [] as number[],
		immediateChange: true
    }
} as UTSJSONObject
