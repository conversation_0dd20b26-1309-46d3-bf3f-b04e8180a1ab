<template>
	<text
	    class="up-link"
	    @tap.stop="openLink"
	    :style="[linkStyle, addStyle(customStyle)]"
	>{{text}}</text>
</template>

<script>
	import { propsLink } from './props';
	import { mpMixin } from '../../libs/mixin/mpMixin';
	import { mixin } from '../../libs/mixin/mixin';
	import { addStyle, addUnit, getPx, toast } from '../../libs/function/index';
	/**
	 * link 超链接
	 * @<NAME_EMAIL> 2024
	 * @description 该组件为超链接组件，在不同平台有不同表现形式：在APP平台会通过plus环境打开内置浏览器，在小程序中把链接复制到粘贴板，同时提示信息，在H5中通过window.open打开链接。
	 * @tutorial https://ijry.github.io/uview-plus/components/link.html
	 * @property {String}			color		文字颜色 （默认 color['up-primary'] ）
	 * @property {String ｜ Number}	fontSize	字体大小，单位px （默认 15 ）
	 * @property {Boolean}			underLine	是否显示下划线 （默认 false ）
	 * @property {String}			href		跳转的链接，要带上http(s)
	 * @property {String}			mpTips		各个小程序平台把链接复制到粘贴板后的提示语（默认“链接已复制，请在浏览器打开”）
	 * @property {String}			lineColor	下划线颜色，默认同color参数颜色 
	 * @property {String}			text		超链接的问题，不使用slot形式传入，是因为nvue下无法修改颜色 
	 * @property {Object}			customStyle	定义需要用到的外部样式
	 * 
	 * @example <up-link href="http://www.uviewui.com">蜀道难，难于上青天</up-link>
	 */
	export default {
		name: "up-link",
		mixins: [mpMixin, mixin, propsLink],
		computed: {
			linkStyle(): any{
				const style = {
					color: this.color,
					fontSize: addUnit(this.fontSize),
					// line-height设置为比字体大小多2px
					lineHeight: addUnit(parseInt(getPx(this.fontSize)) + 2),
					textDecoration: this.underLine ? 'underline' : 'none'
				}
				// if (this.underLine) {
				// 	style.borderBottomColor = this.lineColor || this.color
				// 	style.borderBottomWidth = '1px'
				// }
				return style
			}
		},
		emits: ["click"],
		methods: {
			addStyle(style: any): any {
				return addStyle(style)
			},
			openLink(): void {
				// #ifdef APP
				// todo
				//plus.runtime.openURL(this.href)
				// #endif
				// #ifdef H5
				window.open(this.href)
				// #endif
				// #ifdef MP
				// todo
				// uni.setClipboardData({
				// 	data: this.href,
				// 	success: () => {
				// 		uni.hideToast();
				// 		this.$nextTick(() => {
				// 			toast(this.mpTips);
				// 		})
				// 	}
				// });
				// #endif
				this.$emit('click')
			}
		}
	}
</script>

<style lang="scss" scoped>
	@import "../../libs/css/components.scss";
	$up-link-line-height:1 !default;

	.up-link {
		line-height: $up-link-line-height;
		@include flex;
		flex-wrap: wrap;
		flex: 1;
	}
</style>
