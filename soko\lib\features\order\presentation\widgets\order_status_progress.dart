import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

import 'package:soko/core/enums/app_enums.dart';
import 'package:soko/core/utils/color_utils.dart';
import 'package:soko/features/order/domain/entities/order.dart';
import 'package:soko/features/order/presentation/providers/order_status_provider.dart';

/// 订单状态进度组件
class OrderStatusProgress extends ConsumerWidget {

  const OrderStatusProgress({
    super.key,
    required this.order,
    this.showDescription = true,
    this.showActionHint = true,
  });
  final Order order;
  final bool showDescription;
  final bool showActionHint;

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final statusInfo = ref.watch(orderStatusFromOrderProvider(order));

    return Container(
      padding: EdgeInsets.all(16.w),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(12.r),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.05),
            blurRadius: 8,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // 状态标题和图标
          Row(
            children: [
              Container(
                width: 40.w,
                height: 40.w,
                decoration: BoxDecoration(
                  color: ColorUtils.hexToColor(statusInfo.color).withValues(alpha: 0.1),
                  borderRadius: BorderRadius.circular(20.r),
                ),
                child: Icon(
                  _getIconData(statusInfo.icon),
                  color: ColorUtils.hexToColor(statusInfo.color),
                  size: 20.w,
                ),
              ),
              SizedBox(width: 12.w),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      statusInfo.displayText,
                      style: TextStyle(
                        fontSize: 16.sp,
                        fontWeight: FontWeight.w600,
                        color: Colors.black87,
                      ),
                    ),
                    if (showDescription) ...[
                      SizedBox(height: 4.h),
                      Text(
                        statusInfo.description,
                        style: TextStyle(
                          fontSize: 12.sp,
                          color: Colors.grey[600],
                        ),
                      ),
                    ],
                  ],
                ),
              ),
            ],
          ),

          SizedBox(height: 16.h),

          // 进度条
          _buildProgressBar(statusInfo),

          // 操作提示
          if (showActionHint && statusInfo.actionHint.isNotEmpty) ...[
            SizedBox(height: 12.h),
            Container(
              width: double.infinity,
              padding: EdgeInsets.symmetric(horizontal: 12.w, vertical: 8.h),
              decoration: BoxDecoration(
                color: statusInfo.requiresUserAction 
                    ? Colors.orange.withValues(alpha: 0.1)
                    : Colors.blue.withValues(alpha: 0.1),
                borderRadius: BorderRadius.circular(8.r),
              ),
              child: Row(
                children: [
                  Icon(
                    statusInfo.requiresUserAction 
                        ? Icons.info_outline 
                        : Icons.check_circle_outline,
                    color: statusInfo.requiresUserAction 
                        ? Colors.orange 
                        : Colors.blue,
                    size: 16.w,
                  ),
                  SizedBox(width: 8.w),
                  Expanded(
                    child: Text(
                      statusInfo.actionHint,
                      style: TextStyle(
                        fontSize: 12.sp,
                        color: statusInfo.requiresUserAction 
                            ? Colors.orange[700] 
                            : Colors.blue[700],
                      ),
                    ),
                  ),
                ],
              ),
            ),
          ],
        ],
      ),
    );
  }

  /// 构建进度条
  Widget _buildProgressBar(OrderStatusInfo statusInfo) {
    return Column(
      children: [
        // 进度条
        Row(
          children: [
            Expanded(
              child: LinearProgressIndicator(
                value: statusInfo.progress,
                backgroundColor: Colors.grey[300],
                valueColor: AlwaysStoppedAnimation<Color>(
                  ColorUtils.hexToColor(statusInfo.color),
                ),
                minHeight: 4.h,
              ),
            ),
            SizedBox(width: 8.w),
            Text(
              '${(statusInfo.progress * 100).toInt()}%',
              style: TextStyle(
                fontSize: 12.sp,
                color: Colors.grey[600],
                fontWeight: FontWeight.w500,
              ),
            ),
          ],
        ),

        SizedBox(height: 8.h),

        // 状态步骤
        _buildStatusSteps(statusInfo),
      ],
    );
  }

  /// 构建状态步骤
  Widget _buildStatusSteps(OrderStatusInfo statusInfo) {
    final allStatuses = [
      OrderStatus.pending,
      OrderStatus.paid,
      OrderStatus.shipped,
      OrderStatus.completed,
    ];

    return Row(
      mainAxisAlignment: MainAxisAlignment.spaceBetween,
      children: allStatuses.map((status) {
        final isActive = _isStatusActive(status, statusInfo.status);
        final isCompleted = _isStatusCompleted(status, statusInfo.status);
        
        return Column(
          children: [
            Container(
              width: 8.w,
              height: 8.w,
              decoration: BoxDecoration(
                color: isCompleted 
                    ? ColorUtils.hexToColor(statusInfo.color)
                    : isActive 
                        ? ColorUtils.hexToColor(statusInfo.color).withValues(alpha: 0.5)
                        : Colors.grey[300],
                borderRadius: BorderRadius.circular(4.r),
              ),
            ),
            SizedBox(height: 4.h),
            Text(
              _getStatusStepText(status),
              style: TextStyle(
                fontSize: 10.sp,
                color: isCompleted || isActive 
                    ? Colors.black87 
                    : Colors.grey[500],
                fontWeight: isActive ? FontWeight.w500 : FontWeight.normal,
              ),
            ),
          ],
        );
      }).toList(),
    );
  }

  /// 判断状态是否激活
  bool _isStatusActive(OrderStatus stepStatus, OrderStatus currentStatus) {
    return stepStatus == currentStatus;
  }

  /// 判断状态是否已完成
  bool _isStatusCompleted(OrderStatus stepStatus, OrderStatus currentStatus) {
    final stepPriority = _getStatusStepPriority(stepStatus);
    final currentPriority = _getStatusStepPriority(currentStatus);
    return stepPriority < currentPriority;
  }

  /// 获取状态步骤优先级
  int _getStatusStepPriority(OrderStatus status) {
    switch (status) {
      case OrderStatus.pending:
        return 1;
      case OrderStatus.paid:
        return 2;
      case OrderStatus.shipped:
        return 3;
      case OrderStatus.completed:
        return 4;
      default:
        return 0;
    }
  }

  /// 获取状态步骤文本
  String _getStatusStepText(OrderStatus status) {
    switch (status) {
      case OrderStatus.pending:
        return '下单';
      case OrderStatus.paid:
        return '付款';
      case OrderStatus.shipped:
        return '发货';
      case OrderStatus.completed:
        return '完成';
      default:
        return '';
    }
  }

  /// 获取图标数据
  IconData _getIconData(String iconName) {
    switch (iconName) {
      case 'payment':
        return Icons.payment;
      case 'check_circle':
        return Icons.check_circle;
      case 'local_shipping':
        return Icons.local_shipping;
      case 'location_on':
        return Icons.location_on;
      case 'done_all':
        return Icons.done_all;
      case 'cancel':
        return Icons.cancel;
      case 'money_off':
        return Icons.money_off;
      default:
        return Icons.info;
    }
  }
}
