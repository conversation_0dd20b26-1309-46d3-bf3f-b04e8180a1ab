<script setup>
import { ref } from "vue"
import store from "@/store"
import { onLaunch, onShow } from "@dcloudio/uni-app"

const onShowHandler = function () {
  const userInfoStr = uni.getStorageSync("userInfo")
  if (userInfoStr) {
    store.dispatch("connectWebSocket")
  }
}

const onLaunchHandler = function () {
  uni.getSystemInfo({
    success: (res) => {
      uni.setStorageSync("statusBarHeight", res.statusBarHeight)
    }
  })
}

// 注册生命周期钩子
onShow(() => {
  onShowHandler()
})

onLaunch(() => {
  onLaunchHandler()
})
</script>

<style lang="scss">
/*每个页面公共css */
// @import "uview-plus/index.scss";
@import "@/uni_modules/uview-plus/index.scss";
/* 引入tailwind - 已在main.ts中引入 */
/* 推荐：在main.ts中引入而不是在这里，避免样式冲突 */

/* 全局背景色设置 */
page {
  background-color: #ffffff;
}

/* 设置全局文本和背景颜色 */
uni-page-body {
  background-color: #ffffff;
  line-height: 1.5;
  font-family: Figtree, PingFang SC, HarmonyOS Sans SC, 微软雅黑, system-ui,
    -apple-system, Segoe UI, Roboto, Helvetica Neue, Arial, Noto Sans,
    Liberation Sans, sans-serif, "Apple Color Emoji", "Segoe UI Emoji",
    Segoe UI Symbol, "Noto Color Emoji";
}

/* 全局状态栏适配 */
/* 适用于没有使用自定义导航栏的页面 */
.page-container {
  box-sizing: border-box;
  padding-top: var(--status-bar-height);
}

/* 适用于使用了自定义导航栏的页面 */
.custom-nav-page {
  box-sizing: border-box;
  padding-top: 0;
}

/* 安全区域适配 */
.safe-area-bottom {
  padding-bottom: constant(safe-area-inset-bottom); /* iOS 11.2以下 */
  padding-bottom: env(safe-area-inset-bottom); /* iOS 11.2以上 */
}

.safe-area-top {
  padding-top: constant(safe-area-inset-top); /* iOS 11.2以下 */
  padding-top: env(safe-area-inset-top); /* iOS 11.2以上 */
}

/* 状态栏高度变量，兼容方案 */
.status-bar-height {
  height: var(--status-bar-height);
}

/* 状态栏占位 */
.status-bar-placeholder {
  width: 100%;
  height: var(--status-bar-height);
}

/* 页面内容区域自适应状态栏 */
.page-content {
  padding-top: var(--status-bar-height);
}

/* 针对固定顶部元素的状态栏适配 */
.fixed-top {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  z-index: 100;
}

.fixed-top-safe {
  position: fixed;
  top: var(--status-bar-height);
  left: 0;
  right: 0;
  z-index: 100;
}

/* 用于常规页面内的顶部安全区域适配 */
.pt-status-bar {
  padding-top: var(--status-bar-height);
}

/* 确保页面内容不被状态栏遮挡 */
.safe-content {
  margin-top: var(--status-bar-height);
}
</style>
