import Big from "big.js"

export function coutPriceByCouponsList(options) {
  const { product, couponsList } = options
  console.log(options, "options")
  // 按照优先级排序：折扣券 > 满减券 > 直减券
  const sortedCoupons = couponsList.sort((a, b) => {
    const priority = { 折扣券: 1, 满减券: 2, 直减券: 3 }
    return priority[a.type] - priority[b.type]
  })

  // 检查NOT_SAME_TYPE规则
  const typeCount = {}
  for (const coupon of sortedCoupons) {
    if (coupon.stackRule === "NOT_SAME_TYPE") {
      if (typeCount[coupon.type]) {
        uni.showToast({
          title: "存在不能使用多张相同类型的券",
          icon: "none"
        })
        return false
      }
      typeCount[coupon.type] = true
    }
  }

  let newProduct = { ...product, productPrice: new Big(product.productPrice) }
  for (const coupon of sortedCoupons) {
    switch (coupon.type) {
      case "折扣券":
        newProduct.productPrice = newProduct.productPrice.times(
          new Big(coupon.value)
        )
        break
      case "满减券":
        const totalPrice = newProduct.productPrice.times(
          new Big(newProduct.quantity)
        )
        if (totalPrice.gt(new Big(coupon.threshold))) {
          newProduct.productPrice = totalPrice
            .minus(new Big(coupon.value))
            .div(new Big(newProduct.quantity))
        } else {
          uni.showToast({
            title: "未达到满减券门槛",
            icon: "none"
          })
          return false
        }
        break
      case "直减券":
        newProduct.productPrice = newProduct.productPrice
          .times(new Big(newProduct.quantity))
          .minus(new Big(coupon.value))
          .div(new Big(newProduct.quantity))
        break
      default:
        break
    }
  }
  return { ...newProduct, productPrice: Number(newProduct.productPrice) }
}

export function coutDiscountAmount(productList) {
  if (!Array.isArray(productList)) {
    return 0
  }

  let discount = new Big(0) // 使用Big.js进行高精度计算
  for (const product of productList) {
    const { productPrice, quantity, price } = product
    console.log(productPrice, quantity, price, 99999)
    const priceDiff = new Big(price).minus(new Big(productPrice))
    const discountAmount = priceDiff.times(quantity)
    discount = discount.plus(discountAmount)
  }
  return Number(discount.toFixed(2)) // 最后转换为数字
}
