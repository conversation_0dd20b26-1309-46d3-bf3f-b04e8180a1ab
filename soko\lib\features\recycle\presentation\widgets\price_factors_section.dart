import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

import 'package:soko/features/recycle/domain/entities/recycle_models.dart';

/// 价格影响因素组件
class PriceFactorsSection extends StatelessWidget {
  const PriceFactorsSection({
    super.key,
    required this.factors,
  });

  final List<PriceFactor> factors;

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: EdgeInsets.all(16.w),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(12.r),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.05),
            blurRadius: 8,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // 标题
          Row(
            children: [
              Icon(
                Icons.analytics,
                size: 20.w,
                color: Theme.of(context).primaryColor,
              ),
              SizedBox(width: 8.w),
              Text(
                '价格影响因素',
                style: TextStyle(
                  fontSize: 16.sp,
                  fontWeight: FontWeight.w600,
                ),
              ),
            ],
          ),
          SizedBox(height: 16.h),
          
          // 因素列表
          ...factors.map((factor) => _buildFactorItem(context, factor)),
        ],
      ),
    );
  }

  /// 构建单个因素项
  Widget _buildFactorItem(BuildContext context, PriceFactor factor) {
    final impactColor = _getImpactColor(factor.impactType);
    final impactIcon = _getImpactIcon(factor.impactType);
    
    return Padding(
      padding: EdgeInsets.only(bottom: 12.h),
      child: Row(
        children: [
          // 影响图标
          Container(
            width: 32.w,
            height: 32.w,
            decoration: BoxDecoration(
              color: impactColor.withValues(alpha: 0.1),
              borderRadius: BorderRadius.circular(8.r),
            ),
            child: Icon(
              impactIcon,
              size: 16.w,
              color: impactColor,
            ),
          ),
          SizedBox(width: 12.w),
          
          // 因素信息
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Row(
                  children: [
                    Expanded(
                      child: Text(
                        factor.name,
                        style: TextStyle(
                          fontSize: 14.sp,
                          fontWeight: FontWeight.w500,
                          color: Colors.grey[800],
                        ),
                      ),
                    ),
                    Text(
                      _getImpactText(factor.impact),
                      style: TextStyle(
                        fontSize: 12.sp,
                        fontWeight: FontWeight.w600,
                        color: impactColor,
                      ),
                    ),
                  ],
                ),
                SizedBox(height: 4.h),
                Text(
                  factor.description,
                  style: TextStyle(
                    fontSize: 12.sp,
                    color: Colors.grey[600],
                    height: 1.3,
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  /// 获取影响类型对应的颜色
  Color _getImpactColor(PriceImpactType type) {
    switch (type) {
      case PriceImpactType.positive:
        return Colors.green;
      case PriceImpactType.negative:
        return Colors.red;
      case PriceImpactType.neutral:
        return Colors.grey;
    }
  }

  /// 获取影响类型对应的图标
  IconData _getImpactIcon(PriceImpactType type) {
    switch (type) {
      case PriceImpactType.positive:
        return Icons.trending_up;
      case PriceImpactType.negative:
        return Icons.trending_down;
      case PriceImpactType.neutral:
        return Icons.trending_flat;
    }
  }

  /// 获取影响程度文本
  String _getImpactText(double impact) {
    final percentage = (impact.abs() * 100).toInt();
    final sign = impact > 0 ? '+' : (impact < 0 ? '-' : '');
    return '$sign$percentage%';
  }
}
