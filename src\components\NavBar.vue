<template>
  <view class="custom-nav" :class="{ fixed: fixed }" :style="[navBarStyle]">
    <!-- 状态栏占位 -->
    <view class="status-bar" :style="{ height: statusBarHeight + 'px' }"></view>

    <!-- 导航栏内容 -->
    <view class="nav-content">
      <view class="left-area" @click="handleLeftClick">
        <slot name="left">
          <van-icon
            v-if="showBack"
            name="arrow-left"
            size="20"
            color="#303133"
          />
        </slot>
      </view>

      <view class="title-area" v-if="showTitle">
        <slot name="title">
          <text class="nav-title">{{ title }}</text>
        </slot>
      </view>

      <view
        v-else
        class="flex-1 rounded-full bg-white border-2 border-gray-500 h-11 px-4 flex items-center shadow-sm mx-4"
        @click="goToSearchPage"
      >
        <van-icon name="search" size="20" class="text-gray-400 mr-2" />
        <text class="text-sm text-gray-400">搜索商品</text>
      </view>

      <view class="right-area" v-if="showRight">
        <slot name="right"></slot>
      </view>
    </view>

    <!-- 通知栏 -->
    <view
      v-if="showNoticeBarNotice.showMessage"
      class="notice-bar-wrapper"
      :style="{ top: navHeight + 'px' }"
    >
      <up-notice-bar
        duration="10000"
        :text="showNoticeBarNotice.message"
        mode="link"
        :url="`/pages/views/noticesCenter/noticesCenter?id=${showNoticeBarNotice.id}`"
      ></up-notice-bar>
    </view>
  </view>
  <!-- 占位元素，防止内容被遮挡 -->
  <view
    v-if="fixed"
    class="placeholder"
    :style="{ height: navHeight + 'px' }"
  ></view>
</template>

<script>
import store from "@/store"
export default {
  name: "NavBar",
  props: {
    title: {
      type: String,
      default: ""
    },
    bgColor: {
      type: String,
      default: "#FFFFFF"
    },
    color: {
      type: String,
      default: "#303133"
    },
    showBack: {
      type: Boolean,
      default: true
    },
    fixed: {
      type: Boolean,
      default: true
    },
    showTitle: {
      type: Boolean,
      default: true
    },
    showRight: {
      type: Boolean,
      default: true
    },
    goToSearchPage: {
      type: Function,
      default: () => {}
    }
  },
  data() {
    return {
      statusBarHeight: 0,
      navBarHeight: 56, // 导航栏默认高度
      searchValue: ""
    }
  },
  computed: {
    showNoticeBarNotice() {
      return store.state.$warningMessage
    },

    navBarStyle() {
      return {
        backgroundColor: this.bgColor,
        color: this.color
      }
    },
    navHeight() {
      return this.statusBarHeight + this.navBarHeight
    }
  },
  created() {
    // 获取状态栏高度
    this.getStatusBarHeight()
  },
  methods: {
    getStatusBarHeight() {
      const sysInfo = uni.getSystemInfoSync()
      this.statusBarHeight = sysInfo.statusBarHeight || 0
    },
    handleLeftClick() {
      this.$emit("leftClick")
      if (this.showBack) {
        uni.navigateBack({
          delta: 1,
          fail() {
            uni.switchTab({
              url: "/pages/views/home/<USER>"
            })
          }
        })
      }
    }
  }
}
</script>

<style lang="scss" scoped>
.custom-nav {
  width: 100%;
  border-bottom: 1px solid #f0f0f0;

  &.fixed {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    z-index: 999;
  }

  .notice-bar-wrapper {
    position: absolute;
    left: 0;
    right: 0;
  }

  .status-bar {
    width: 100%;
  }

  .nav-content {
    position: relative;
    display: flex;
    align-items: center;
    height: 56px;
    padding: 0 15px;
    justify-content: space-between;

    .left-area {
      position: static;
      height: 100%;
      display: flex;
      align-items: center;
      flex-shrink: 0;
    }

    .title-area {
      flex: 1;
      text-align: center;
      min-width: 0;

      .nav-title {
        font-size: 16px;
        font-weight: 500;
      }
    }

    .right-area {
      position: static;
      height: 100%;
      display: flex;
      align-items: center;
      flex-shrink: 0;
      margin-left: 10px;
    }
  }
}

.placeholder {
  width: 100%;
}
</style>
