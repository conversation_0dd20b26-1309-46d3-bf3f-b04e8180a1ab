// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'coupon.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

Coupon _$CouponFromJson(Map<String, dynamic> json) => Coupon(
      id: json['id'] as String,
      name: json['name'] as String,
      description: json['description'] as String?,
      type: $enumDecode(_$CouponTypeEnumMap, json['type']),
      value: (json['value'] as num).toDouble(),
      minAmount: (json['minAmount'] as num?)?.toDouble(),
      maxDiscount: (json['maxDiscount'] as num?)?.toDouble(),
      validFrom: (json['validFrom'] as num).toInt(),
      validTo: (json['validTo'] as num).toInt(),
      status: $enumDecode(_$CouponStatusEnumMap, json['status']),
      usageLimit: (json['usageLimit'] as num?)?.toInt(),
      usedCount: (json['usedCount'] as num).toInt(),
      categoryIds: (json['categoryIds'] as List<dynamic>?)
          ?.map((e) => e as String)
          .toList(),
      productIds: (json['productIds'] as List<dynamic>?)
          ?.map((e) => e as String)
          .toList(),
      excludeProductIds: (json['excludeProductIds'] as List<dynamic>?)
          ?.map((e) => e as String)
          .toList(),
    );

Map<String, dynamic> _$CouponToJson(Coupon instance) => <String, dynamic>{
      'id': instance.id,
      'name': instance.name,
      'description': instance.description,
      'type': _$CouponTypeEnumMap[instance.type]!,
      'value': instance.value,
      'minAmount': instance.minAmount,
      'maxDiscount': instance.maxDiscount,
      'validFrom': instance.validFrom,
      'validTo': instance.validTo,
      'status': _$CouponStatusEnumMap[instance.status]!,
      'usageLimit': instance.usageLimit,
      'usedCount': instance.usedCount,
      'categoryIds': instance.categoryIds,
      'productIds': instance.productIds,
      'excludeProductIds': instance.excludeProductIds,
    };

const _$CouponTypeEnumMap = {
  CouponType.discount: 'DISCOUNT',
  CouponType.amount: 'AMOUNT',
  CouponType.shipping: 'SHIPPING',
};

const _$CouponStatusEnumMap = {
  CouponStatus.available: 'AVAILABLE',
  CouponStatus.used: 'USED',
  CouponStatus.expired: 'EXPIRED',
  CouponStatus.disabled: 'DISABLED',
};

MemberDiscount _$MemberDiscountFromJson(Map<String, dynamic> json) =>
    MemberDiscount(
      level: $enumDecode(_$MemberLevelEnumMap, json['level']),
      discountRate: (json['discountRate'] as num).toDouble(),
      freeShippingThreshold:
          (json['freeShippingThreshold'] as num?)?.toDouble(),
      pointsMultiplier: (json['pointsMultiplier'] as num).toDouble(),
    );

Map<String, dynamic> _$MemberDiscountToJson(MemberDiscount instance) =>
    <String, dynamic>{
      'level': _$MemberLevelEnumMap[instance.level]!,
      'discountRate': instance.discountRate,
      'freeShippingThreshold': instance.freeShippingThreshold,
      'pointsMultiplier': instance.pointsMultiplier,
    };

const _$MemberLevelEnumMap = {
  MemberLevel.bronze: 'BRONZE',
  MemberLevel.silver: 'SILVER',
  MemberLevel.gold: 'GOLD',
  MemberLevel.platinum: 'PLATINUM',
  MemberLevel.diamond: 'DIAMOND',
};
