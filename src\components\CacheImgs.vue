<template>
  <view class="w-full h-full">
    <image
      v-if="!loading"
      :src="localPath"
      :mode="mode"
      class="w-full h-full object-cover"
      :class="className"
    />
    <view v-else class="loading-indicator">
      <!-- 加载动画 -->
    </view>
  </view>
</template>

<script>
import { ref, onMounted } from "vue"

export default {
  name: "CacheImgs",
  props: {
    src: {
      type: String,
      required: true
    },
    mode: {
      type: String,
      default: "aspectFill"
    },
    className: String,
    style: Object
  },
  setup(props) {
    const localPath = ref("")
    const loading = ref(true)

    // 检查本地缓存（优化版）
    const checkCache = async (url) => {
      try {
        const cacheKey = `cache_img_${url}`
        const cacheInfo = uni.getStorageSync(cacheKey)
        if (!cacheInfo) return null
        const { path, timestamp } = cacheInfo
        // 检查缓存是否过期（7天有效期）
        if (Date.now() - timestamp >= 7 * 24 * 60 * 60 * 1000) {
          // 异步清除过期缓存
          uni.removeSavedFile({ filePath: path })
          uni.removeStorageSync(cacheKey)
          return null
        }

        // 优化：仅检查文件是否存在，不获取完整文件列表
        try {
          const fileInfo = await uni.getSavedFileInfo({ filePath: path })
          return fileInfo ? path : null
        } catch {
          return null
        }
      } catch (error) {
        console.error("检查缓存失败:", error)
        return null
      }
    }

    const downloadImage = async (url) => {
      try {
        const { tempFilePath, statusCode } = await uni.downloadFile({
          url,
          timeout: 15000
        })

        if (statusCode !== 200) {
          throw new Error(`下载失败，状态码: ${statusCode}`)
        }

        return new Promise((resolve, reject) => {
          uni.saveFile({
            tempFilePath,
            success: (res) => {
              const cacheKey = `cache_img_${url}`
              const cacheInfo = {
                path: res.savedFilePath,
                timestamp: Date.now()
              }
              console.log("图片保存成功", cacheKey, cacheInfo)
              uni.setStorageSync(cacheKey, cacheInfo)
              resolve(res.savedFilePath)
            },
            fail: reject
          })
        })
      } catch (error) {
        console.error("图片下载失败:", error)
        throw error // 重新抛出错误以便上层处理
      }
    }

    onMounted(async () => {
      try {
        loading.value = true
        const cached = await checkCache(props.src)
        localPath.value = cached || (await downloadImage(props.src))
      } catch (error) {
        localPath.value = props.src
      } finally {
        loading.value = false
      }
    })

    return {
      localPath,
      loading
    }
  }
}
</script>
<style></style>
