<template>
	<view
	    :style="[style]"
	    class="up-status-bar"
	>
		<slot />
	</view>
</template>

<script>
	import { propsStatusBar } from './props';
	import { mpMixin } from '../../libs/mixin/mpMixin';
	import { mixin } from '../../libs/mixin/mixin';
	import { addUnit, addStyle, deepMerge, sys } from '../../libs/function/index';
	/**
	 * StatbusBar 状态栏占位
	 * @<NAME_EMAIL>
	 * @description 本组件主要用于状态填充，比如在自定导航栏的时候，它会自动适配一个恰当的状态栏高度。
	 * @tutorial https://uview-plus.jiangruyi.com/components/statusBar.html
	 * @property {String}			bgColor			背景色 (默认 'transparent' )
	 * @property {String | Object}	customStyle		自定义样式 
	 * @example <up-status-bar></up-status-bar>
	 */
	export default {
		name: 'up-status-bar',
		mixins: [mpMixin, mixin, propsStatusBar],
		data() {
			return {
			}
		},
		computed: {
			style(): any {
				const style = {}
				// 状态栏高度，由于某些安卓和微信开发工具无法识别css的顶部状态栏变量，所以使用js获取的方式
				style['height'] = addUnit(uni.getSystemInfoSync().statusBarHeight, 'px')
				style['backgroundColor'] = this.bgColor
				return deepMerge(style, addStyle(this.customStyle))
			}
		},
	}
</script>

<style lang="scss" scoped>
	.up-status-bar {
		width: 100%;
	}
</style>
