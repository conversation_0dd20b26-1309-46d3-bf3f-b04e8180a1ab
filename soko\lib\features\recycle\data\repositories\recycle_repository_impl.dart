import 'package:flutter_riverpod/flutter_riverpod.dart';

import 'package:soko/features/recycle/domain/entities/recycle_order.dart';
import 'package:soko/features/recycle/domain/entities/recycle_models.dart';
import 'package:soko/features/recycle/domain/repositories/recycle_repository.dart';
import 'package:soko/features/recycle/data/datasources/recycle_remote_datasource.dart';
import 'package:soko/core/models/paginated_data.dart';

/// 回收业务仓库实现
class RecycleRepositoryImpl implements RecycleRepository {
  const RecycleRepositoryImpl(this._remoteDataSource);

  final RecycleRemoteDataSource _remoteDataSource;

  @override
  Future<PaginatedData<RecycleOrder>> getRecycleOrders({
    int page = 1,
    int pageSize = 20,
    String? status,
    String? category,
    String? keyword,
    DateTime? startDate,
    DateTime? endDate,
  }) async {
    try {
      return await _remoteDataSource.getRecycleOrders(
        page: page,
        pageSize: pageSize,
        status: status,
        category: category,
        keyword: keyword,
        startDate: startDate,
        endDate: endDate,
      );
    } catch (error) {
      rethrow;
    }
  }

  @override
  Future<RecycleOrder> getRecycleOrderDetail(String orderId) async {
    try {
      return await _remoteDataSource.getRecycleOrderDetail(orderId);
    } catch (error) {
      rethrow;
    }
  }

  @override
  Future<RecycleOrder> createRecycleOrder(CreateRecycleOrderRequest request) async {
    try {
      return await _remoteDataSource.createRecycleOrder(request);
    } catch (error) {
      rethrow;
    }
  }

  @override
  Future<void> cancelRecycleOrder(String orderId) async {
    try {
      await _remoteDataSource.cancelRecycleOrder(orderId);
    } catch (error) {
      rethrow;
    }
  }

  @override
  Future<void> confirmShipment(String orderId, Map<String, dynamic> shippingInfo) async {
    try {
      await _remoteDataSource.confirmShipment(orderId, shippingInfo);
    } catch (error) {
      rethrow;
    }
  }

  @override
  Future<List<CategoryItem>> getRecycleCategories() async {
    try {
      return await _remoteDataSource.getRecycleCategories();
    } catch (error) {
      rethrow;
    }
  }

  @override
  Future<Map<String, dynamic>> getRecycleStats() async {
    try {
      return await _remoteDataSource.getRecycleStats();
    } catch (error) {
      rethrow;
    }
  }

  @override
  Future<List<Map<String, dynamic>>> getRecycleProcess() async {
    try {
      return await _remoteDataSource.getRecycleProcess();
    } catch (error) {
      rethrow;
    }
  }

  @override
  Future<List<RecycleOrder>> getRecentOrders({int limit = 5}) async {
    try {
      return await _remoteDataSource.getRecentOrders(limit: limit);
    } catch (error) {
      rethrow;
    }
  }
}

/// 回收仓库Provider
final recycleRepositoryProvider = Provider<RecycleRepository>((ref) {
  final remoteDataSource = ref.watch(recycleRemoteDataSourceProvider);
  return RecycleRepositoryImpl(remoteDataSource);
});
