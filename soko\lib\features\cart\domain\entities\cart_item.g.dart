// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'cart_item.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

CartItem _$CartItemFromJson(Map<String, dynamic> json) => CartItem(
      id: json['id'] as String,
      userId: json['userId'] as String,
      productId: json['productId'] as String,
      productName: json['productName'] as String,
      productImage: json['productImage'] as String?,
      skuId: json['skuId'] as String?,
      skuName: json['skuName'] as String?,
      price: (json['price'] as num).toDouble(),
      originalPrice: (json['originalPrice'] as num?)?.toDouble(),
      quantity: (json['quantity'] as num).toInt(),
      maxQuantity: (json['maxQuantity'] as num?)?.toInt(),
      selected: json['selected'] as bool,
      available: json['available'] as bool,
      createTime: (json['createTime'] as num).toInt(),
      updateTime: (json['updateTime'] as num).toInt(),
    );

Map<String, dynamic> _$CartItemToJson(CartItem instance) => <String, dynamic>{
      'id': instance.id,
      'userId': instance.userId,
      'productId': instance.productId,
      'productName': instance.productName,
      'productImage': instance.productImage,
      'skuId': instance.skuId,
      'skuName': instance.skuName,
      'price': instance.price,
      'originalPrice': instance.originalPrice,
      'quantity': instance.quantity,
      'maxQuantity': instance.maxQuantity,
      'selected': instance.selected,
      'available': instance.available,
      'createTime': instance.createTime,
      'updateTime': instance.updateTime,
    };

AddToCartRequest _$AddToCartRequestFromJson(Map<String, dynamic> json) =>
    AddToCartRequest(
      productId: json['productId'] as String,
      skuId: json['skuId'] as String?,
      quantity: (json['quantity'] as num).toInt(),
    );

Map<String, dynamic> _$AddToCartRequestToJson(AddToCartRequest instance) =>
    <String, dynamic>{
      'productId': instance.productId,
      'skuId': instance.skuId,
      'quantity': instance.quantity,
    };

UpdateCartItemRequest _$UpdateCartItemRequestFromJson(
        Map<String, dynamic> json) =>
    UpdateCartItemRequest(
      quantity: (json['quantity'] as num?)?.toInt(),
      selected: json['selected'] as bool?,
    );

Map<String, dynamic> _$UpdateCartItemRequestToJson(
        UpdateCartItemRequest instance) =>
    <String, dynamic>{
      'quantity': instance.quantity,
      'selected': instance.selected,
    };

CartSummary _$CartSummaryFromJson(Map<String, dynamic> json) => CartSummary(
      totalItems: (json['totalItems'] as num).toInt(),
      selectedItems: (json['selectedItems'] as num).toInt(),
      totalAmount: (json['totalAmount'] as num).toDouble(),
      totalOriginalAmount: (json['totalOriginalAmount'] as num?)?.toDouble(),
      totalDiscount: (json['totalDiscount'] as num).toDouble(),
      freight: (json['freight'] as num?)?.toDouble(),
      finalAmount: (json['finalAmount'] as num).toDouble(),
    );

Map<String, dynamic> _$CartSummaryToJson(CartSummary instance) =>
    <String, dynamic>{
      'totalItems': instance.totalItems,
      'selectedItems': instance.selectedItems,
      'totalAmount': instance.totalAmount,
      'totalOriginalAmount': instance.totalOriginalAmount,
      'totalDiscount': instance.totalDiscount,
      'freight': instance.freight,
      'finalAmount': instance.finalAmount,
    };
