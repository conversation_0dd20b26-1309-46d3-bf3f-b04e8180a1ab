<template>
  <view class="page min-h-screen bg-gray-50">
    <NavBar title="联系客服" :showBack="true"></NavBar>

    <div class="contact-content p-4">
      <view class="contact-card">
        <view class="contact-item">
          <view class="contact-item-title">
            <van-icon name="phone-circle-o" size="20" />
            <text>客服电话</text>
          </view>
          <view class="contact-item-content">
            <text class="phone-number">************</text>
            <view class="item-button">
              <up-button size="mini" type="primary" @click="callPhone"
                >拨打电话</up-button
              >
            </view>
          </view>
          <view class="contact-hint">周一至周日 9:00-22:00</view>
        </view>

        <view class="contact-item">
          <view class="contact-item-title">
            <van-icon name="service-o" size="20" />
            <text>在线客服</text>
          </view>
          <view class="contact-item-content">
            <text>工作时间：9:00-22:00</text>
            <view class="item-button">
              <up-button size="mini" type="primary" @click="onlineService"
                >立即咨询</up-button
              >
            </view>
          </view>
        </view>
      </view>

      <view class="faq-section">
        <view class="faq-title">常见问题</view>
        <view class="faq-list">
          <view
            class="faq-item"
            v-for="(item, index) in faqList"
            :key="index"
            @click="showFaqDetail(item)"
          >
            <text>{{ item.question }}</text>
            <van-icon name="arrow" size="16" color="#999" />
          </view>
        </view>
      </view>
    </div>
  </view>
</template>

<script setup lang="ts">
import { ref } from 'vue'
import NavBar from '@/components/NavBar.vue'
// 引入组件
import '@/components/falcon/FaIcon.vue'

// 常见问题列表
const faqList = ref([
  {
    question: '如何修改收货地址？',
    answer: '进入"我的"-"服务中心"-"收货地址"，点击地址右侧编辑按钮进行修改。',
  },
  {
    question: '下单后多久发货？',
    answer:
      '正常情况下，现货付款成功后48小时内发货，预售商品付款成功后15天左右发货，官方新品预定以官方实际出货时间为准，节假日可能会延迟，请以实际通知为准。',
  },
  {
    question: '如何申请退款？',
    answer:
      '确认收货后，进入订单详情页面，点击"申请退款"按钮，按提示操作即可。由于海淘中古商品的特殊性，暂不支持用户下单后取消订单，敬请谅解！',
  },
])

// 拨打电话
const callPhone = () => {
  uni.makePhoneCall({
    phoneNumber: '4001234567',
  })
}

// 在线客服
const onlineService = () => {
  if (typeof plus === 'undefined') {
    uni.showToast({
      title: '当前环境不支持相关操作',
      icon: 'none',
    })
    return
  }
  let sweixin = null
  plus.share.getServices(
    (res) => {
      sweixin = res.find((i) => i.id === ('weixin' as any))
      console.log(sweixin)

      if (sweixin) {
        sweixin.openCustomerServiceChat(
          {
            corpid: '你的企业ID', // 企业ID
            url: '你的客服链接地址', // 客服链接地址
          },
          (suc) => {
            console.log('success', JSON.stringify(suc))
          },
          (err) => {
            console.log('error', JSON.stringify(err))
          }
        )
      } else {
        uni.showToast({
          title: '当前环境不支持微信操作',
          icon: 'none',
        })
      }
    },
    function (err) {
      uni.showToast({
        title: '获取服务失败，不支持该操作。' + JSON.stringify(err),
        icon: 'none',
      })
    }
  )
}

// 发送邮件
const sendEmail = () => {
  // 复制邮箱地址
  uni.setClipboardData({
    data: '<EMAIL>',
    success: () => {
      uni.showToast({
        title: '邮箱已复制',
        icon: 'success',
      })
    },
  })
}

// 显示常见问题详情
const showFaqDetail = (item) => {
  uni.showModal({
    title: item.question,
    content: item.answer,
    showCancel: false,
  })
}
</script>

<style scoped lang="scss">
.contact {
  min-height: 100vh;
  background-color: #f5f5f5;
}

.contact-content {
  padding: 15px;
}

.contact-title {
  font-size: 18px;
  font-weight: bold;
  margin-bottom: 20px;
}

.contact-card {
  background-color: #fff;
  border-radius: 10px;
  padding: 15px;
  margin-bottom: 20px;
}

.contact-item {
  border-bottom: 1px solid #f0f0f0;
  padding: 15px 0;
}

.contact-item:last-child {
  border-bottom: none;
  padding-bottom: 0;
}

.contact-item-title {
  display: flex;
  align-items: center;
  margin-bottom: 15px;
}

.contact-item-title text {
  font-size: 16px;
  font-weight: bold;
  margin-left: 8px;
}

.contact-item-content {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 8px;
  .item-button {
    justify-content: end;
    width: 25vw;
  }
}

.phone-number {
  font-size: 16px;
  color: #333;
  font-weight: bold;
}

.contact-hint {
  font-size: 12px;
  color: #999;
}

.faq-section {
  background-color: #fff;
  border-radius: 10px;
  padding: 15px;
}

.faq-title {
  font-size: 16px;
  font-weight: bold;
  margin-bottom: 15px;
}

.faq-list {
}

.faq-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px 0;
  border-bottom: 1px solid #f0f0f0;
}

.faq-item:last-child {
  border-bottom: none;
}

.faq-item text {
  font-size: 14px;
  color: #333;
}
</style>
