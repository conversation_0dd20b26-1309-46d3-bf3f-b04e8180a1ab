<script setup lang="ts">
import NavBar from '@/components/NavBar.vue'
</script>

<template>
  <view class="page min-h-screen bg-white">
    <NavBar title="隐私政策" :showBack="true"></NavBar>
    
    <view class="privacy-content">
      <view class="privacy-title">隐私政策</view>
      <view class="privacy-section">
        <view class="privacy-section-title">1. 信息收集</view>
        <view class="privacy-text">
          我们收集的信息包括您提供给我们的信息，如创建账户时提供的姓名、电子邮件地址、电话号码等。我们也会自动收集某些信息，如您的IP地址、浏览器类型、操作系统、访问时间等。
        </view>
      </view>
      
      <view class="privacy-section">
        <view class="privacy-section-title">2. 信息使用</view>
        <view class="privacy-text">
          我们使用收集的信息来提供、维护和改进我们的服务，如处理您的交易、发送订单确认和提供客户支持。我们还使用这些信息来改进和个性化您的体验，以及开发新的功能和服务。
        </view>
      </view>
      
      <view class="privacy-section">
        <view class="privacy-section-title">3. 信息共享</view>
        <view class="privacy-text">
          我们不会出售或出租您的个人信息给任何第三方用于营销目的。我们可能会与我们的服务提供商共享信息，以帮助我们提供服务，如支付处理、数据分析、电子邮件发送等。这些公司有权使用您的个人信息，但仅限于为我们提供服务所需的范围。
        </view>
      </view>
      
      <view class="privacy-section">
        <view class="privacy-section-title">4. 数据安全</view>
        <view class="privacy-text">
          我们采取合理的安全措施来保护您的个人信息不被未经授权的访问、使用或披露。这些措施包括加密、物理访问控制以及安全的数据存储。
        </view>
      </view>
      
      <view class="privacy-section">
        <view class="privacy-section-title">5. 您的权利</view>
        <view class="privacy-text">
          您有权访问、更正或删除您的个人信息。您也可以选择退出我们的营销通信。如果您有任何关于我们如何处理您的个人信息的问题，请联系我们的客户服务团队。
        </view>
      </view>
      
      <view class="privacy-section">
        <view class="privacy-section-title">6. 政策更新</view>
        <view class="privacy-text">
          我们可能会不时更新这一隐私政策。我们会在本页面上发布任何变更，并在重大变更时通过电子邮件或应用内通知告知您。
        </view>
      </view>
      
      <view class="privacy-footer">
        最后更新日期：2023年12月1日
      </view>
    </view>
  </view>
</template>

<style scoped lang="scss">
.privacy-content {
  padding: 16px;
  background-color: #ffffff;
  min-height: 100%;
  
  .privacy-title {
    font-size: 20px;
    font-weight: bold;
    margin-bottom: 20px;
    color: #303133;
    text-align: center;
  }
  
  .privacy-section {
    margin-bottom: 20px;
    
    .privacy-section-title {
      font-size: 16px;
      font-weight: 600;
      margin-bottom: 8px;
      color: #303133;
    }
    
    .privacy-text {
      font-size: 14px;
      line-height: 1.6;
      color: #606266;
    }
  }
  
  .privacy-footer {
    font-size: 12px;
    color: #909399;
    text-align: center;
    margin-top: 30px;
    padding-bottom: 20px;
  }
}
</style>
