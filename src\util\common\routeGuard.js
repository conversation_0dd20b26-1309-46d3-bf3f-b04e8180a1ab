import store from '@/store'

// 需要登录才能访问的页面路径列表
const requireLoginPages = [
  '/pages/views/cart/cart',         // 购物车页面
  '/pages/views/order/orders',      // 订单列表
  '/pages/views/order/confirm',     // 订单确认
  '/pages/views/order/checkout'     // 结算页面
]

/**
 * 路由拦截，检查是否需要登录
 * @param {Object} options - 路由配置
 * @param {Function} resolve - 决定是否继续路由跳转
 */
export function routeIntercept(options, resolve) {
  // 提取当前跳转的路由路径
  const url = options.url.split('?')[0]
  
  // 检查当前页面是否需要登录
  const needLogin = requireLoginPages.some(page => url.includes(page))
  
  if (needLogin) {
    // 检查登录状态
    const isLoggedIn = store.getters.isLoggedIn
    
    if (!isLoggedIn) {
      // 未登录，显示提示
      uni.showToast({
        title: '请先登录',
        icon: 'none',
        duration: 1500
      })
      
      // 跳转到登录页
      setTimeout(() => {
        uni.navigateTo({
          url: '/pages/views/login/login'
        })
      }, 1500)
      
      // 阻止原本的路由跳转
      resolve(false)
      return
    }
  }
  
  // 允许继续路由跳转
  resolve(true)
}

// 初始化路由拦截
export function initRouteIntercept() {
  // 设置全局的路由拦截器
  uni.$u.routeIntercept = routeIntercept
} 