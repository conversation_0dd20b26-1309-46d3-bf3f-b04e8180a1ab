import 'package:flutter_riverpod/flutter_riverpod.dart';

import 'package:soko/core/enums/app_enums.dart';
import 'package:soko/core/models/query_params.dart';
import 'package:soko/core/state/base_state.dart';
import 'package:soko/features/order/domain/entities/order.dart';
import 'package:soko/features/order/domain/usecases/get_orders_usecase.dart';

/// 订单状态管理器
class OrderNotifier extends StateNotifier<PageState<Order>> {

  OrderNotifier(this._getOrdersUseCase) : super(PageState.initial());
  final GetOrdersUseCase _getOrdersUseCase;

  /// 加载订单列表
  Future<void> loadOrders({
    OrderStatus? status,
    bool refresh = false,
  }) async {
    if (refresh) {
      state = PageState.initial();
    }

    if (state.isLoading) return;

    state = state.copyWith(isLoading: true, error: null);

    try {
      final queryParams = QueryParams(
        page: refresh ? 1 : state.currentPage + 1,
        pageSize: 20,
        filters: status != null ? {'status': status.name} : null,
      );

      final result = await _getOrdersUseCase(queryParams);

      result.fold(
        (failure) {
          state = state.copyWith(
            isLoading: false,
            error: failure.message,
          );
        },
        (paginatedData) {
          final newItems = refresh 
              ? paginatedData.data 
              : [...state.items, ...paginatedData.data];

          state = state.copyWith(
            items: newItems,
            isLoading: false,
            hasMore: paginatedData.hasMore,
            currentPage: paginatedData.currentPage,
            totalPages: paginatedData.totalPages,
            totalCount: paginatedData.totalCount,
            error: null,
          );
        },
      );
    } catch (e) {
      state = state.copyWith(
        isLoading: false,
        error: e.toString(),
      );
    }
  }

  /// 刷新订单列表
  Future<void> refreshOrders({OrderStatus? status}) async {
    await loadOrders(status: status, refresh: true);
  }

  /// 加载更多订单
  Future<void> loadMoreOrders({OrderStatus? status}) async {
    if (state.canLoadMore) {
      await loadOrders(status: status);
    }
  }

  /// 清空订单列表
  void clearOrders() {
    state = PageState.initial();
  }
}

/// 订单状态管理器提供者
final orderProvider = StateNotifierProvider<OrderNotifier, PageState<Order>>((ref) {
  final getOrdersUseCase = ref.read(getOrdersUseCaseProvider);
  return OrderNotifier(getOrdersUseCase);
});

/// 按状态筛选的订单提供者
final ordersByStatusProvider = Provider.family<List<Order>, OrderStatus?>((ref, status) {
  final orderState = ref.watch(orderProvider);
  if (status == null) {
    return orderState.items;
  }
  return orderState.items.where((order) => order.status == status).toList();
});

/// 订单统计提供者
final orderStatsProvider = Provider<Map<OrderStatus, int>>((ref) {
  final orders = ref.watch(orderProvider).items;
  final stats = <OrderStatus, int>{};
  
  for (final status in OrderStatus.values) {
    stats[status] = orders.where((order) => order.status == status).length;
  }
  
  return stats;
});
