<template>
  <view class="flex flex-col min-h-screen bg-slate-50 custom-nav-page">
    <!-- 自定义导航栏 -->
    <NavBar title="申请退货" bgColor="#FFFFFF" :showBack="true" />

    <!-- 主内容区域 -->
    <view class="flex-1 p-4 space-y-4 pb-8">
      <!-- 商品选择区 -->
      <view
        v-if="orderItems.length > 0"
        class="bg-white rounded-xl shadow-base p-4"
      >
        <text class="text-sm font-semibold text-text-primary block mb-3"
          >请选择需要申请售后的商品</text
        >
        <view class="space-y-3">
          <view
            v-for="(item, index) in orderItems"
            :key="item.id"
            class="flex gap-3 p-3 rounded-xl border-2"
            :class="{
              'border-primary bg-primary bg-opacity-5': activeItemId === item.id,
              'border-transparent': activeItemId !== item.id,
            }"
            @click="setActiveItem(item.id)"
          >
            <!-- 商品图片 -->
            <view
              class="relative h-20 w-20 bg-white rounded-xl overflow-hidden flex-shrink-0 border border-slate-100"
            >
              <CacheImgs
                :src="item.image || '/static/placeholder.png'"
                :alt="item.name"
                class="w-full h-full object-contain"
              />
            </view>
            <!-- 商品信息 -->
            <view class="flex-1 min-w-0">
              <text
                class="text-sm font-medium text-text-primary block truncate"
                >{{ item.name || "未知商品" }}</text
              >
              <view class="flex justify-between items-center mt-1">
                <text class="text-sm text-primary font-semibold"
                  >¥{{ item.price ? formatPrice(item.price) : "0.00" }}</text
                >
                <text class="text-xs text-text-secondary"
                  >x{{ item.quantity }}</text
                >
              </view>
              <view v-if="item.appliedRefundAmount && parseFloat(item.appliedRefundAmount) > 0" class="mt-1">
                  <text class="text-xs text-green-600">已申请退款: ¥{{formatPrice(item.appliedRefundAmount)}}</text>
              </view>
            </view>
          </view>
        </view>
      </view>

      <!-- 当前选中商品的售后信息填写区 -->
      <view v-if="activeItem" class="space-y-4">
        <!-- 退款金额 -->
        <view class="bg-white rounded-xl shadow-base p-4">
          <view class="flex items-center justify-between mb-3">
            <text class="text-sm font-semibold text-text-primary">退款金额 ({{ activeItem.name }})</text>
            <text class="text-xs text-text-secondary">
              最高可退: ¥{{ formatPrice(activeItemMaxRefund) }}
            </text>
          </view>
          <view class="relative">
            <view
              class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none"
            >
              <text class="text-primary font-medium">¥</text>
            </view>
            <input
              type="digit"
              :value="activeItem.appliedRefundAmount"
              class="w-full h-10 pl-8 pr-3 bg-slate-50 rounded-xl text-sm text-text-primary placeholder-text-secondary focus:outline-none focus:ring-2 focus:ring-primary"
              placeholder="请输入退款金额"
              @input="handleRefundAmountInput"
              :ref="el => refundAmountInputRefs[activeItem!.id] = el as HTMLInputElement"
            />
          </view>
          <view v-if="activeItem.refundAmountError" class="mt-2">
            <text class="text-xs text-destructive">{{ activeItem.refundAmountError }}</text>
          </view>
          <view v-else class="mt-2 flex justify-between">
            <text class="text-xs text-text-secondary">默认为商品总金额</text>
            <text
              class="text-xs text-primary cursor-pointer"
              @click="resetToSelectedTotal"
              >重置为商品金额</text
            >
          </view>
        </view>

        <!-- 问题描述 -->
        <view class="bg-white rounded-xl shadow-base p-4">
          <text class="text-sm font-semibold text-text-primary block mb-3"
            >问题描述 ({{ activeItem.name }})</text
          >
          <view>
            <textarea
              v-model="activeItem.returnDescription"
              class="block h-28 px-3 py-2 bg-slate-50 rounded-xl text-sm text-text-primary placeholder-text-secondary resize-none focus:outline-none focus:ring-2 focus:ring-primary"
              style="width: 100%; box-sizing: border-box"
              placeholder="请详细描述商品问题，便于售后处理（必填，10字以上）"
              maxlength="200"
            ></textarea>
          </view>
          <text class="text-xs text-text-secondary mt-1 block text-right"
            >{{ activeItem.returnDescription?.length || 0 }}/200</text
          >
        </view>

        <!-- 图片上传 -->
        <view class="bg-white rounded-xl shadow-base p-4">
          <text class="text-sm font-semibold text-text-primary block mb-3"
            >上传凭证 ({{ activeItem.name }})</text
          >
          <text class="text-xs text-text-secondary mb-4 block"
            >请上传商品问题照片，最多4张，支持jpg/png，单张不超过2MB</text
          >
          <view class="grid grid-cols-4 gap-3">
            <view
              v-for="(image, index) in activeItem.returnImages"
              :key="index"
              class="relative aspect-square bg-slate-50 rounded-lg overflow-hidden group"
            >
              <CacheImgs
                :src="image"
                class="w-full h-full object-cover"
                mode="aspectFill"
              />
              <view
                class="absolute top-1 right-1 w-7 h-7 bg-black bg-opacity-50 rounded-full flex items-center justify-center z-10"
                @click="removeImage(index)"
                style="min-width: 44px; min-height: 44px"
              >
                <van-icon name="cross" size="16" color="#FFFFFF" />
              </view>
            </view>
            <view
              v-if="activeItem.returnImages && activeItem.returnImages.length < 4"
              class="aspect-square bg-slate-50 rounded-lg flex items-center justify-center border-2 border-dashed border-slate-200 cursor-pointer min-h-[44px] min-w-[44px]"
              @click="chooseImage"
            >
              <van-icon name="photograph" size="28" color="#94A3B8" />
            </view>
          </view>
        </view>
      </view>

      <!-- 退货原因 -->
      <view class="bg-white rounded-xl shadow-base p-4">
        <view class="flex items-center mb-2">
          <text class="text-sm font-semibold text-text-primary mr-2"
            >退货原因</text
          >
        </view>
        <view class="flex items-center mt-2">
          <view
            class="w-6 h-6 rounded-full border-2 border-primary flex items-center justify-center mr-3 bg-white"
          >
            <view class="w-3.5 h-3.5 rounded-full bg-primary" />
          </view>
          <text class="text-sm text-text-primary">99天无理由退货</text>
        </view>
        <text class="text-xs text-text-secondary mt-2 block"
          >本商品支持99天无理由退货，售后更安心</text
        >
      </view>

      <!-- 退货地址 -->
      <view class="bg-white rounded-xl shadow-base p-4">
        <text class="text-sm font-semibold text-text-primary block mb-3"
          >退货地址</text
        >
        <view class="bg-slate-50 p-4 rounded-xl flex items-start gap-3">
          <van-icon
            name="location-o"
            class="flex-shrink-0 mt-0.5"
            size="18"
            color="#666666"
          />
          <view class="flex-1 min-w-0">
            <view class="flex items-center flex-wrap mb-1">
              <text class="text-sm font-medium text-text-primary mr-2">{{
                returnAddress.name
              }}</text>
              <text class="text-sm text-text-secondary">{{
                returnAddress.phone
              }}</text>
            </view>
            <text class="text-xs text-text-secondary block leading-relaxed">{{
              returnAddress.fullAddress
            }}</text>
          </view>
        </view>
      </view>
    </view>

    <!-- 底部提交按钮 -->
    <view
      class="fixed bottom-0 left-0 right-0 px-4 py-1 bg-white border-t border-slate-100 pb-safe z-50 flex items-center"
      style="min-height: 44px"
    >
      <view
        class="w-full py-1 bg-gradient-to-r from-primary to-neon text-white rounded-full flex items-center justify-center shadow-base text-[13px] font-medium active:opacity-90 min-h-[28px]"
        :class="{ 'opacity-50': !isFormValid }"
        @click="submitReturn"
        style="min-width: 44px; min-height: 28px"
      >
        <text class="font-medium">提交申请</text>
      </view>
    </view>
  </view>
</template>

<script lang="ts" setup>
import CacheImgs from "@/components/CacheImgs.vue";
import { ref, computed, type Ref, watch, nextTick } from "vue";
import { onShow } from "@dcloudio/uni-app";
import NavBar from "@/components/NavBar.vue";
import { Icon as VanIcon } from "vant";
import {
  getOrderDetail,
  submitReturnRequest,
  uploadFile,
  getSystemMap,
} from "@/api/api";

// 退货原因选项
const returnReasons = [{ label: "99天无理由退货", value: "NO_REASON" }];

// 表单数据
const orderId = ref("");
const orderItems = ref<PlaygroundOrderItem[]>([]); // 所有订单商品项, 现在扩展了结构
const activeItemId = ref<number | null>(null); // 当前激活进行售后填写的商品ID
const selectedReason = ref("NO_REASON"); // 退货原因保持全局
const description = ref("");
const images = ref<string[]>([]);
const refundAmount = ref(""); // 退款金额
const refundAmountError = ref(""); // 退款金额错误信息
const maxRefundAmount = ref(0); // 最高可退金额（订单总额减去运费）
const refundAmountInputRefs = ref<Record<number, HTMLInputElement | null>>({});

interface ReturnAddress {
  name: string;
  phone: string;
  fullAddress: string;
}
const returnAddress = ref<ReturnAddress>({
  name: "",
  phone: "",
  fullAddress: "",
});

// 扩展后的订单项接口
interface PlaygroundOrderItem {
  id: number;
  name: string;
  price: string; // 或 number, 接口中通常用string，使用时解析
  quantity: number;
  image: string;
  productId: string; // 或 number
  skuId: string; // 或 number
  specification: string; // JSON
  // 新增的售后相关字段
  returnDescription?: string; // 设置为可选，因为初始可能没有
  returnImages?: string[];    // 设置为可选
  appliedRefundAmount?: string; // 用户为该商品输入的退款金额, 可选
  refundAmountError?: string; // 该商品退款金额的错误信息, 可选
  originalPrice: number; // 用于计算该商品最高可退, 必填
}

// 当前激活的商品项
const activeItem = computed(() => {
  if (!activeItemId.value) return null;
  return orderItems.value.find(item => item.id === activeItemId.value) || null;
});

// 当前激活商品的最大可退金额
const activeItemMaxRefund = computed(() => {
  if (!activeItem.value) return 0;
  // 简单处理：单个商品最高可退就是其自身价格 * 数量
  // 如果有更复杂的订单级别折扣分摊，这里需要调整
  return activeItem.value.originalPrice * activeItem.value.quantity;
});

// 表单验证: 至少有一个商品填写了完整的售后信息
const isFormValid = computed(() => {
  return orderItems.value.some(item =>
    item.appliedRefundAmount &&
    parseFloat(item.appliedRefundAmount) > 0 &&
    !item.refundAmountError &&
    item.returnDescription &&
    item.returnDescription.length >= 10 &&
    item.returnImages &&
    item.returnImages.length > 0
  );
});

// 设置当前操作的商品
function setActiveItem(itemId: number) {
  // 先找到要操作的商品，确保它存在
  const current = orderItems.value.find(i => i.id === itemId);
  if (!current) {
    console.error("未找到指定ID的商品:", itemId);
    return;
  }
  
  // 确保该商品的所有售后相关字段都已初始化
  if (current.appliedRefundAmount === undefined) {
    // 仅在首次激活时设置默认退款金额
    current.appliedRefundAmount = (current.originalPrice * current.quantity).toFixed(2);
  }
  
  // 确保所有字段都有初始值
  current.returnDescription = current.returnDescription || "";
  current.returnImages = current.returnImages || [];
  current.refundAmountError = current.refundAmountError || "";
  
  // 最后再设置activeItemId，确保其他初始化操作先完成
  activeItemId.value = itemId;
  
  // 激活时也校验一次退款金额
  nextTick(() => {
    validateRefundAmount();
  });
}

// 验证退款金额 - 针对当前激活的商品
function validateRefundAmount() {
  if (!activeItem.value) return;

  const item = activeItem.value;
  const amount = parseFloat(item.appliedRefundAmount);
  const maxRefund = item.originalPrice * item.quantity;

  if (isNaN(amount)) {
    item.refundAmountError = "请输入有效金额";
    return;
  }

  if (amount <= 0) {
    item.refundAmountError = "退款金额必须大于0";
    return;
  }

  if (amount > maxRefund) {
    item.refundAmountError = `退款金额不能超过该商品的最高可退金额 ¥${formatPrice(
      maxRefund
    )}`;
    return;
  }

  item.refundAmountError = "";
}

// 重置为所选商品总金额 - 针对当前激活的商品
function resetToSelectedTotal() {
  if (!activeItem.value) return;
  const item = activeItem.value;
  item.appliedRefundAmount = (item.originalPrice * item.quantity).toFixed(2);
  validateRefundAmount();
}

/**
 * 选择图片并上传 - 针对当前激活的商品
 */
const chooseImage = async () => {
  if (!activeItem.value?.id) return;
  // 安全获取当前商品ID和可上传的图片数量
  const activeId = activeItem.value.id;
  const currentImagesLength = activeItem.value.returnImages?.length || 0;
  const remainingSlots = 4 - currentImagesLength;
  
  if (remainingSlots <= 0) return;

  try {
    // 使用ID而不是直接引用对象
    const res = await new Promise<UniApp.ChooseImageSuccessCallbackResult>(
      (resolve, reject) => {
        uni.chooseImage({
          count: remainingSlots,
          sizeType: ["compressed"],
          sourceType: ["album", "camera"],
          success: resolve,
          fail: reject,
        });
      }
    );
    
    if (res.tempFilePaths && res.tempFilePaths.length > 0) {
      let pathsToUpload: string[] = [];
      if (typeof res.tempFilePaths === 'string') {
        pathsToUpload = [res.tempFilePaths];
      } else {
        pathsToUpload = res.tempFilePaths;
      }
      
      if (pathsToUpload.length > 0) {
        // 传递商品ID，而不是依赖activeItem的实时状态
        await uploadImages(pathsToUpload, activeId);
      }
    }
  } catch (error) {
    uni.showToast({ title: "选择图片失败", icon: "none" });
  }
};

/**
 * 上传图片到服务器，返回图片url数组
 * @param filePaths 要上传的文件路径数组
 * @param targetItemId 指定要更新的商品ID，优先级高于activeItem
 */
const uploadImages = async (filePaths: string[], targetItemId?: number): Promise<void> => {
  if (!filePaths.length) return;
  
  // 使用传入的itemId或从activeItem获取
  const itemId = targetItemId || activeItem.value?.id;
  if (!itemId) return;

  uni.showLoading({ title: "上传中..." });
  try {
    const uploaded: string[] = [];
    for (const filePath of filePaths) {
      try {
        const res = await uploadFile(filePath);
        if (res && res.success !== false && res.data && res.data.url) {
          uploaded.push(res.data.url);
        } else {
          uni.showToast({
            title: res?.message || "图片上传失败",
            icon: "none",
          });
        }
      } catch (err: any) {
        uni.showToast({ title: err?.message || "图片上传失败", icon: "none" });
      }
    }
    
    // 上传完成后，找到对应商品并更新
    const itemToUpdate = orderItems.value.find(item => item.id === itemId);
    if (itemToUpdate) {
      // 直接复制一份新数组，确保Vue能检测到变更
      const currentImages = [...(itemToUpdate.returnImages || [])];
      itemToUpdate.returnImages = [...currentImages, ...uploaded];
      
      // 如果这是当前活动的商品，确保UI也更新
      if (activeItem.value && activeItem.value.id === itemId) {
        // 这里不需要额外操作，因为activeItem是computed，会自动更新
        // 但确保检查一下，防止上传过程中activeItem已经变化
        console.log("已更新活动商品的图片", itemToUpdate.returnImages.length);
      }
    }
  } finally {
    uni.hideLoading();
  }
};

// 移除图片 - 针对当前激活的商品
function removeImage(index: number) {
  if (!activeItem.value || !activeItem.value.returnImages) return;
  activeItem.value.returnImages.splice(index, 1);
}

// 提交退货申请
async function submitReturn() {
  // 收集所有已填写并有效的售后商品
  const itemsToSubmit = orderItems.value.filter(item =>
    item.appliedRefundAmount &&
    parseFloat(item.appliedRefundAmount) > 0 &&
    !item.refundAmountError &&
    item.returnDescription &&
    item.returnDescription.length >= 10 &&
    item.returnImages &&
    item.returnImages.length > 0
  );

  if (itemsToSubmit.length === 0) {
    uni.showToast({
      title: "请至少为一个商品填写完整的售后信息",
      icon: "none",
    });
    return;
  }

  // 计算总退款金额
  const totalAppliedRefundAmount = itemsToSubmit.reduce((sum, item) => {
    // 确保 appliedRefundAmount 是有效的数字字符串
    const amount = parseFloat(item.appliedRefundAmount || "0");
    return sum + amount;
  }, 0);

  uni.showLoading({ title: "提交中..." });
  try {
    // 构建售后商品明细列表
    const afterSaleItems = itemsToSubmit.map((item) => ({
      afterSaleType: "RETURN_REFUND", // 新增：售后类型
      orderItemId: String(item.id),
      productId: String(item.productId),
      productSkuId: String(item.skuId || ""),
      productName: item.name || "未知商品",
      fileUrl: item.image || "/static/placeholder.png",
      specification: item.specification,
      returnQuantity: item.quantity, // 假设整件商品退货
      returnAmount: parseFloat(item.appliedRefundAmount || "0"), // 使用用户为该商品申请的金额
      images: item.returnImages?.join(",") || "", // 新增：单个商品的图片凭证
      reason: selectedReason.value, // 新增：单个商品的退货原因
      description: item.returnDescription?.substring(0, 500) || "", // 新增：单个商品的描述
    }));

    const afterSaleData = {
      orderId: orderId.value,
      amount: Number(totalAppliedRefundAmount.toFixed(2)), // 售后总金额
      items: afterSaleItems,
    };

    // 调用申请售后接口
    const [err, result] = await submitReturnRequest(afterSaleData);
    if (err || (result && result.code !== 0 && result.code !== 200)) {
      throw new Error(err?.message || result?.msg || "提交失败");
    }

    uni.showToast({
      title: "申请已提交",
      icon: "success",
    });

    // 延迟返回订单详情页
    setTimeout(() => {
      // 跳转到订单详情页
      uni.redirectTo({
        url: `/pages/views/order/detail?id=${orderId.value}`,
        fail: (err) => {
          console.error("导航失败", err);
          // 导航失败时退回上一页
          uni.navigateBack();
        },
      });
    }, 1500);
  } catch (error: any) {
    uni.showToast({
      title: error.message || "提交失败，请重试",
      icon: "none",
    });
  } finally {
    uni.hideLoading();
  }
}

// 格式化价格
function formatPrice(price: string | number): string {
  if (typeof price === "string") {
    price = parseFloat(price) || 0;
  }
  return price.toFixed(2).replace(/\B(?=(\d{3})+(?!\d))/g, ",");
}

// 获取订单商品信息
async function fetchOrderItems() {
  try {
    const [err, result] = await getOrderDetail(orderId.value);
    if (err || !result) {
      throw new Error("获取订单信息失败");
    }

    const orderData = result;
    if (!orderData || !orderData.orderItems) {
      throw new Error("订单数据不完整");
    }

    // 计算最高可退金额 = 订单支付总额 - 运费
    const totalAmount = parseFloat(
      orderData.payAmount || orderData.totalAmount || 0
    );
    const domesticFreight = parseFloat(orderData.domesticFreight || 0);
    const internationalFreight = parseFloat(
      orderData.internationalFreight || 0
    );
    const totalShipping = domesticFreight + internationalFreight;

    // 设置最高可退金额（确保不小于0）
    maxRefundAmount.value = Math.max(0, totalAmount - totalShipping);

    // 处理订单商品项，并初始化售后相关字段
    orderItems.value = orderData.orderItems.map((item: any) => {
      const originalPrice = parseFloat(item.productPrice) || 0;
      return {
        id: item.id,
        name: item.productName || "未命名商品",
        price: item.productPrice, // 这个price是单价
        quantity: item.quantity,
        image: item.fileUrl || "/static/placeholder.png",
        productId: item.productId,
        skuId: item.productSkuId,
        specification: item.specification,
        // 初始化售后字段 - 确保每个字段都有默认值且类型正确
        returnDescription: "",
        returnImages: [], // 确保是数组
        appliedRefundAmount: "", // 初始为空字符串
        refundAmountError: "",
        originalPrice: originalPrice, // 存储原始单价用于计算
      };
    });

    // 如果URL中有itemId参数，预激活该商品
    if (itemId.value && orderItems.value.length > 0) {
      const targetItem = orderItems.value.find(
        (item) => String(item.id) === String(itemId.value)
      );
      if (targetItem) {
        setActiveItem(targetItem.id); // 激活商品并进行初始化
      }
    } else if (orderItems.value.length > 0) {
      // 默认激活第一个商品
      setActiveItem(orderItems.value[0].id);
    }
  } catch (error: any) {
    uni.showToast({
      title: error.message || "获取商品信息失败",
      icon: "none",
    });
    setTimeout(() => {
      uni.navigateBack();
    }, 1500);
  }
}

/**
 * 获取退货地址（系统参数）
 */
const fetchReturnAddress = async () => {
  try {
    const res = await getSystemMap("return_info");
    // 假设返回格式为 { data: { name, phone, fullAddress } } 或 data为对象
    if (res && res.data) {
      // 兼容后端返回数组或对象
      const info = Array.isArray(res.data) ? res.data[0] : res.data;
      if (info) {
        returnAddress.value = {
          name: info.name || "",
          phone: info.phone || "",
          fullAddress: info.fullAddress || info.address || "",
        };
      }
    }
  } catch (error) {
    // 可选：toast提示
  }
};

// 用于接收URL中可能的itemId参数
const itemId = ref("");

const isOrderLoaded = ref(false);

onShow(async () => {
  if (!isOrderLoaded.value) {
    // 获取路由参数
    const pages = getCurrentPages();
    const currentPage = pages[pages.length - 1];
    // @ts-ignore
    if (currentPage && currentPage.$page && currentPage.$page.options) {
      // @ts-ignore
      orderId.value = currentPage.$page.options.orderId || "";
      // @ts-ignore
      itemId.value = currentPage.$page.options.itemId || "";
    }

    await fetchReturnAddress();

    if (orderId.value) {
      await fetchOrderItems();
      isOrderLoaded.value = true;
    } else {
      uni.showToast({
        title: "参数错误",
        icon: "none",
      });
      setTimeout(() => {
        uni.navigateBack();
      }, 1500);
    }
  }
});

// 处理退款金额输入，同时保持光标位置
function handleRefundAmountInput(e: any) {
  if (!activeItem.value?.id) return;
  
  // 获取当前商品ID，而不是保存引用
  const itemId = activeItem.value.id;
  
  // 每次都从orderItems中获取最新的商品对象
  const item = orderItems.value.find(i => i.id === itemId);
  if (!item) return;
  
  const target = e.target;
  const value = e.detail.value || "";
  const cursorPos = target.selectionEnd || 0;

  const filteredValue = filterNumericInput(value);

  if (filteredValue === value) {
    item.appliedRefundAmount = filteredValue;
    validateRefundAmount(); // 验证当前活动商品的金额
    return;
  }

  const lengthDiff = value.length - filteredValue.length;
  const newCursorPos = Math.max(0, cursorPos - lengthDiff);

  // 直接更新原始数组中的对象
  item.appliedRefundAmount = filteredValue;
  validateRefundAmount(); // 验证当前活动商品的金额

  nextTick(() => {
    const inputRef = refundAmountInputRefs.value[itemId];
    if (inputRef) {
      inputRef.focus();
      inputRef.setSelectionRange(newCursorPos, newCursorPos);
    }
  });
}

// 过滤输入，只允许数字和一个小数点
function filterNumericInput(value: string): string {
  // 移除非数字和小数点字符
  let result = value.replace(/[^\d.]/g, "");

  // 确保只有一个小数点
  const dotIndex = result.indexOf(".");
  if (dotIndex !== -1) {
    // 如果有小数点，确保后面只有两位小数
    const intPart = result.substring(0, dotIndex);
    const decimalPart = result
      .substring(dotIndex + 1)
      .replace(/\./g, "")
      .slice(0, 2);
    result = intPart + "." + decimalPart;
  }

  return result;
}
</script>

<style>
.custom-nav-page {
  padding-bottom: calc(env(safe-area-inset-bottom) + 80px);
}
</style>
