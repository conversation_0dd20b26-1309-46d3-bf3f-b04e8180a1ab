import { getPublicKey } from '@/api/api';

// publicKey.js - 公钥管理工具类
const CACHE_KEY = 'publickey';

/**
 * 公钥管理工具类
 * 负责处理公钥的缓存和获取
 */
class PublicKeyManager {
  constructor() {
    this.publicKey = null;
  }

  /**
   * 获取公钥
   * 先从缓存获取，如果没有再调用接口
   * @returns {Promise<string>} 公钥字符串
   */
  async getPublicKey() {
    // 如果内存中已有公钥，直接返回
    if (this.publicKey) {
      return this.publicKey;
    }

    // 尝试从缓存中获取
    const cachedKey = uni.getStorageSync(CACHE_KEY);
    if (cachedKey) {
      this.publicKey = cachedKey;
      return cachedKey;
    }

    // 从接口获取
    return await this.fetchPublicKeyFromApi();
  }

  /**
   * 从API获取公钥 (优化版)
   * 使用 async/await 提高可读性
   * @returns {Promise<string>} 公钥字符串
   */
  async fetchPublicKeyFromApi() {
    try {
      // 等待 getPublicKey 函数返回结果
      const response = await getPublicKey();

      if (response.code === 200) {
        const storageInfo = uni.getStorageSync(CACHE_KEY);
        if (storageInfo != null) {
          return storageInfo;
        }
      }

      // 如果头和体都没有找到公钥
      throw new Error('响应中不包含公钥');
    } catch (error) {
      // 捕获请求过程或处理过程中的任何错误
      console.error('获取公钥时出错:', error); // 打印详细错误
      const errorMessage = error.errMsg || error.message || '未知网络错误';
      // 抛出统一格式的错误，方便上层调用者处理
      throw new Error(`公钥请求失败：${errorMessage}`);
    }
  }
}

// 创建单例
const publicKeyManager = new PublicKeyManager();

export default publicKeyManager;
