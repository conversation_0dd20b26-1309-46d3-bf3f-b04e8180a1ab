<template>
  <view class="page bg-gray-50 min-h-screen pb-safe">
    <NavBar
      :title="isEditMode ? '编辑收货地址' : '新增收货地址'"
      :showBack="true"
    ></NavBar>

    <!-- 表单区域 -->
    <view class="form-container mt-3 bg-white px-4 rounded-t-xl">
      <!-- 姓名输入 -->
      <view class="input-item py-3.5 border-b border-gray-100">
        <text class="input-label text-base text-text-primary mb-2.5"
          >收货人</text
        >
        <view
          class="region-value flex justify-between items-center mt-2.5 px-3 py-2 bg-gray-50 rounded-lg"
        >
          <up-input
            v-model="addressForm.name"
            placeholder="请填写收货人姓名"
            clearable
            :border="false"
            inputClass="bg-gray-50"
          />
        </view>
      </view>

      <!-- 手机号码 -->
      <view class="input-item py-3.5 border-b border-gray-100">
        <text class="input-label text-base text-text-primary mb-2.5"
          >手机号码</text
        >
        <view
          class="region-value flex justify-between items-center mt-2.5 px-3 py-2 bg-gray-50 rounded-lg"
        >
          <up-input
            v-model="addressForm.phone"
            type="number"
            placeholder="请填写收货人手机号"
            maxlength="11"
            clearable
            :border="false"
            inputClass="bg-gray-50"
          />
        </view>
      </view>

      <!-- 所在地区 -->
      <view
        class="input-item region-item py-3.5 border-b border-gray-100"
        @click="showAreaSelector"
      >
        <text class="input-label text-base text-text-primary mb-2.5"
          >所在地区</text
        >
        <view
          class="region-value flex justify-between items-center mt-2.5 px-3 py-2 bg-gray-50 rounded-lg"
        >
          <text
            class="region-text flex-1 text-sm mr-2"
            :class="{
              'text-gray-400': !regionText,
              'text-text-primary': regionText
            }"
            >{{ regionText || "请选择所在地区" }}</text
          >
          <van-icon name="arrow" size="16" color="#c0c4cc" />
        </view>
      </view>

      <!-- 详细地址 -->
      <view
        class="input-item address-detail-item py-3.5 border-b border-gray-100"
      >
        <text class="input-label text-base text-text-primary mb-2.5"
          >详细地址</text
        >
        <up-textarea
          v-model="addressForm.detail"
          placeholder="街道、门牌号等"
          :border="false"
          :height="100"
          count
          maxlength="100"
          class="bg-gray-50 p-3 rounded-lg"
        />
      </view>

      <!-- 设为默认地址 -->
      <view
        class="default-address-section flex justify-between items-center py-4"
      >
        <view class="default-content">
          <view class="default-title text-base text-text-primary"
            >设为默认地址</view
          >
          <view class="default-desc text-xs text-text-secondary mt-1"
            >每次下单会默认选择该地址</view
          >
        </view>
        <up-switch
          v-model="defaultSwitchValue"
          :active-color="secondaryColor"
          size="24"
          :disabled="isFirstAddress || isOnlyAddress"
        />
      </view>

      <!-- 删除按钮 -->
      <view
        class="delete-area pt-4 pb-5 border-t border-gray-100"
        v-if="isEditMode"
      >
        <up-button text="删除收货地址" type="error" @click="confirmDelete" />
      </view>
    </view>

    <!-- 底部保存按钮 -->
    <view
      class="save-button fixed left-0 right-0 bottom-0 bg-white px-4 flex items-center h-16 pb-safe border-t border-gray-100 shadow-sm"
    >
      <up-button
        text="保存"
        type="primary"
        :custom-style="saveButtonStyle"
        @click="saveAddress"
      />
    </view>

    <!-- 使用 up-picker 组件实现地区选择 -->
    <up-picker
      ref="areaPicker"
      :show="showAreaPicker"
      :columns="areaColumns"
      @confirm="confirmAreaSelect"
      @change="areaPickerChange"
      @cancel="cancelAreaSelect"
      title="请选择地区"
      value-key="id"
      label-key="text"
    />
  </view>
</template>

<script setup lang="ts">
import { ref, reactive, computed, nextTick, onMounted, watch } from "vue"
import { useStore } from "vuex"
import { onLoad } from "@dcloudio/uni-app"
import NavBar from "@/components/NavBar.vue"
import {
  getAddressById,
  saveAddress as saveAddressApi,
  updateAddress,
  deleteAddress,
  getAddressList
} from "@/api/api"

import areaData from "@/common/area"

import cityData from "@/common/city"

import provinceData from "@/common/province"

// 定义地区数据类型
interface AreaItem {
  label: string
  value: string // 保留 value 以防万一，但当前代码似乎没用
  // 假设 areaData 的结构返回的对象包含 label
}

// 定义Picker组件的类型
interface PickerInstance {
  setColumnValues: (columnIndex: number, values: any[]) => Promise<void> // 假设返回 Promise
}

const store = useStore()
// 使用设计规范定义的颜色
const primaryColor = "#EF4444" // 奥特曼红
const secondaryColor = "#10B981" // 骑士绿
const accentColor = "#3B82F6" // 战队蓝

// 表单数据
const addressForm = reactive({
  id: "",
  name: "",
  phone: "",
  province: "",
  city: "",
  district: "",
  detail: "",
  iDefault: false
})

// 是否为编辑模式
const isEditMode = ref(false)
// 是否为用户的第一个地址
const isFirstAddress = ref(false)
// 是否为用户的唯一地址
const isOnlyAddress = ref(false)

// 地区选择器相关
const showAreaPicker = ref(false)
const areaPicker = ref<PickerInstance | null>(null)
const areaColumns = ref<any[][]>([[], [], []]) // 初始化为空，使用对象数组

// 省市区数据 (原始数据)
const provinceLabels = ref<string[]>([]) // 只存 label 用于查找和显示

// 选中项的索引
const selectedProvinceIndex = ref(0) // 基于 provinceLabels 的索引
const selectedCityIndex = ref(0) // 基于当前城市列表的索引

// 计算显示的地区文本
const regionText = computed(() => {
  if (addressForm.province && addressForm.city && addressForm.district) {
    return `${addressForm.province} ${addressForm.city} ${addressForm.district}`
  }
  return ""
})

// --- 数据加载与处理 ---

// 初始化省份列表 (从 provinceData 生成基础列表)
const initializeProvinceList = () => {
  try {
    if (provinceData && Array.isArray(provinceData)) {
      provinceLabels.value = provinceData.map((province: any) => province.label)
      // 不在此处设置 areaColumns[0]，因为 showAreaSelector 会处理
      return true
    } else {
      provinceLabels.value = []
      return false
    }
  } catch (error) {
    provinceLabels.value = []
    return false
  }
}

// 监听 iDefault 变化
watch(
  () => addressForm.iDefault,
  (newVal) => {
    // 移除调试日志
  }
)

// 使用计算属性处理开关值
const defaultSwitchValue = computed({
  get: () => {
    // 确保转换为布尔类型
    // 移除调试日志
    return Boolean(addressForm.iDefault)
  },
  set: (val) => {
    // 如果是唯一地址，则不允许修改默认状态
    if (isOnlyAddress.value) {
      // 移除调试日志
      addressForm.iDefault = true
      return
    }

    // 移除调试日志
    addressForm.iDefault = val
  }
})

// 获取城市列表 (根据省份label)
const getCities = (provinceLabel: string): AreaItem[] => {
  // 找到省份在 provinceData 中的索引
  const provinceIndex = provinceData.findIndex(
    (p: any) => p.label === provinceLabel
  )
  if (provinceIndex === -1) {
    return []
  }

  // 获取该省份的城市数组
  const cities = cityData[provinceIndex] || []

  // 假设 cityData[provinceIndex] 的项有 label 属性
  return cities.map((city: any, index: number) => {
    if (typeof city.label === "undefined") {
      console.warn(
        `getCities: City item at index ${index} for province "${provinceLabel}" is missing 'label'. Item:`,
        city
      )
    }
    return {
      label: city.label || `未知城市${index}`, // 提供回退
      value: city.value || index.toString() // 保留 value
    }
  })
}

// 获取区县列表 (根据省份label和城市在当前列表中的索引)
const getDistricts = (
  provinceLabel: string,
  cityListIndex: number
): AreaItem[] => {
  const provinceIndex = provinceData.findIndex(
    (p: any) => p.label === provinceLabel
  )
  if (provinceIndex === -1) {
    return []
  }

  // 获取该省份对应的整个区县数据
  const provinceAreaData = areaData[provinceIndex] // Get the array/object for the province
  if (!Array.isArray(provinceAreaData)) {
    // 如果省份的地区数据不是一个数组，则无法按城市索引访问
    return []
  }

  // *** 安全地访问该城市索引对应的区县列表 ***
  if (cityListIndex >= 0 && cityListIndex < provinceAreaData.length) {
    const districts = provinceAreaData[cityListIndex]
    // 检查获取到的地区数据是否确实是一个数组
    if (!Array.isArray(districts)) {
      return [] // 如果不是数组，返回空列表
    }

    // 映射地区数据
    return districts.map((district: any, index: number) => {
      if (typeof district.label === "undefined") {
        console.warn(
          `getDistricts: District item at index ${index} for province "${provinceLabel}", city index ${cityListIndex} is missing 'label'. Item:`,
          district
        )
      }
      return {
        label: district.label || `未知区域${index}`, // 提供回退
        value: district.value || index.toString() // 保留 value
      }
    })
  } else {
    // cityListIndex 超出范围，意味着 areaData 中没有这个城市的数据
    console.warn(
      `getDistricts: cityListIndex ${cityListIndex} is out of bounds for provinceAreaData (length ${provinceAreaData.length}). Returning empty district list.`
    )
    return [] // 返回空列表
  }
}

// --- 生命周期与事件处理 ---

onLoad((options) => {
  // 初始化省份数据
  const provincesInitialized = initializeProvinceList()

  if (options && options.id) {
    isEditMode.value = true
    if (provincesInitialized) {
      fetchAddressDetail(options.id) // fetchAddressDetail 内部处理列数据

      // 检查是否有地址列表参数传入
      if (options.isOnly === "true") {
        // 如果列表页传入了isOnly参数，直接使用
        isOnlyAddress.value = true
        addressForm.iDefault = true
      } else if (options.addressCount) {
        // 如果传入了地址总数，根据总数判断是否为唯一地址
        const addressCount = parseInt(options.addressCount)
        if (addressCount === 1) {
          isOnlyAddress.value = true
          addressForm.iDefault = true
        }
      } else {
        // 如果没有参数传入，才调用接口检查（兼容其他入口）
        checkIsOnlyAddress(options.id)
      }
    } else {
      uni.showToast({ title: "省份数据加载失败", icon: "none" })
    }
  } else {
    // 新增模式，检查是否为第一个地址
    checkIsFirstAddress()
  }
})

// 页面加载后打印当前状态
onMounted(() => {
  // 移除调试日志

  // 确保UI上的开关状态是最新的
  nextTick(() => {
    // 移除调试日志
    // 强制刷新开关状态
    if (defaultSwitchValue.value !== Boolean(addressForm.iDefault)) {
      // 移除调试日志
      // 触发一次计算属性的set，确保视图更新
      setTimeout(() => {
        addressForm.iDefault = Boolean(addressForm.iDefault)
      }, 100)
    }
  })
})

// 检查是否为用户的唯一地址（编辑模式下）
const checkIsOnlyAddress = async (currentAddressId: string) => {
  try {
    const res = await getAddressList()
    const addressList = res.data || []
    // 如果总共只有1个地址，则认为是唯一地址
    if (addressList.length === 1) {
      isOnlyAddress.value = true
      // 强制设置为默认地址
      addressForm.iDefault = true
      // 移除调试日志
    } else if (addressList.length > 1) {
      // 有多个地址，检查当前编辑的地址是否为默认地址
      const currentAddress = addressList.find(
        (addr: any) => addr.id === currentAddressId
      )
      if (
        currentAddress &&
        (currentAddress.idefault || currentAddress.iDefault)
      ) {
        // 如果当前地址已经是默认地址，检查是否还有其他非默认地址
        const hasOtherNonDefaultAddress = addressList.some(
          (addr: any) =>
            addr.id !== currentAddressId && !(addr.idefault || addr.iDefault)
        )

        if (!hasOtherNonDefaultAddress) {
          // 如果没有其他非默认地址，则当前默认地址不允许更改
          isOnlyAddress.value = true
          addressForm.iDefault = true
          // 移除调试日志
        }
      }
    }
  } catch (error) {
    console.error("获取地址列表失败", error)
  }
}

// 检查是否为第一个地址
const checkIsFirstAddress = async () => {
  try {
    const res = await getAddressList()
    // 如果地址列表为空，则设置为第一个地址
    if (!res.data || !res.data.length) {
      isFirstAddress.value = true
      isOnlyAddress.value = true // 第一个地址也是唯一地址
      // 设置为默认地址
      addressForm.iDefault = true
      // 移除调试日志
    }
  } catch (error) {
    console.error("获取地址列表失败", error)
  }
}

// 获取地址详情并准备选择器列
const fetchAddressDetail = async (id: string) => {
  try {
    uni.showLoading({ title: "加载中..." })
    const res = await getAddressById(id)
    uni.hideLoading()

    if (res.code === 200) {
      const addressData = res.data

      // 先复制基本数据，不包含 iDefault 和 idefault
      const { iDefault, idefault, ...otherData } = addressData
      Object.assign(addressForm, otherData)

      // 处理后端可能返回的两种格式的默认地址字段
      // 后端返回的是 idefault (全小写)
      // 移除调试日志

      if (idefault !== undefined) {
        // 移除调试日志
        // 强制转换为布尔值
        if (typeof idefault === "string") {
          addressForm.iDefault = idefault.toLowerCase() === "true"
        } else {
          addressForm.iDefault = !!idefault
        }
      } else if (iDefault !== undefined) {
        // 以防万一后端改用了驼峰命名
        // 移除调试日志
        if (typeof iDefault === "string") {
          addressForm.iDefault = iDefault.toLowerCase() === "true"
        } else {
          addressForm.iDefault = !!iDefault
        }
      }

      // 移除调试日志

      // --- 关键: 查找编辑时的省市区索引 ---
      const pIndex = provinceLabels.value.findIndex(
        (p) => p === addressData.province
      )
      if (pIndex !== -1) {
        selectedProvinceIndex.value = pIndex // 设置省份索引
        const cities = getCities(addressData.province) // 获取该省城市
        const cIndex = cities.findIndex((c) => c.label === addressData.city) // 查找城市索引
        if (cIndex !== -1) {
          selectedCityIndex.value = cIndex // 设置城市索引

          // 注意：getDistricts 需要的是 cityListIndex (即 cIndex)
          const districts = getDistricts(addressData.province, cIndex)

          // --- 准备 Picker 列数据 ---
          areaColumns.value = [
            provinceLabels.value.map((label, index) => ({
              id: index.toString(),
              text: label
            })),
            cities.map((c, index) => ({
              id: index.toString(), // 使用城市列表的索引作为id
              text: c.label
            })),
            districts.map((d, index) => ({
              id: index.toString(), // 使用区县列表的索引作为id
              text: d.label
            }))
          ]
        } else {
          // 可以考虑只加载省份和城市，让用户重新选择区县
          areaColumns.value = [
            provinceLabels.value.map((label, index) => ({
              id: index.toString(),
              text: label
            })),
            cities.map((c, index) => ({ id: index.toString(), text: c.label })),
            [] // 区县为空
          ]
        }
      } else {
        uni.showToast({ title: "地址数据有误", icon: "none" })
      }
      // --- 结束 ---
    } else {
      uni.showToast({
        title: res?.message || "获取地址信息失败",
        icon: "none"
      })
    }
  } catch (error) {
    uni.hideLoading()
    console.error("获取地址详情失败", error)
    uni.showToast({ title: "获取地址信息失败", icon: "none" })
  }
}

// 显示区域选择器
const showAreaSelector = async () => {
  // 确保省份数据已加载
  if (provinceLabels.value.length === 0) {
    if (!initializeProvinceList()) {
      uni.showToast({ title: "省份数据加载失败", icon: "none" })
      return
    }
  }

  // 如果 areaColumns 还没有初始化（例如新增地址时首次点击）
  // 或者需要基于当前 selection 刷新 (编辑时可能需要，但 fetch 已处理)
  // 这里我们总是基于当前 selectedProvinceIndex 和 selectedCityIndex 重新生成列
  // 保证打开时显示的是当前状态对应的列表

  const currentProvinceLabel = provinceLabels.value[selectedProvinceIndex.value]
  if (!currentProvinceLabel) {
    return // 暂时返回，避免错误
  }

  const cities = getCities(currentProvinceLabel)
  let districts: AreaItem[] = []
  if (cities.length > 0) {
    // 确保 selectedCityIndex 在 cities 的有效范围内
    if (
      selectedCityIndex.value < 0 ||
      selectedCityIndex.value >= cities.length
    ) {
      selectedCityIndex.value = 0 // 重置为0
    }
    districts = getDistricts(currentProvinceLabel, selectedCityIndex.value)
  } else {
    selectedCityIndex.value = 0 // 没有城市，索引也重置
  }

  // 更新列数据，确保每项都有 id 和 text
  areaColumns.value = [
    provinceLabels.value.map((label, index) => ({
      id: index.toString(),
      text: label
    })),
    cities.map((c, index) => ({
      id: index.toString(), // 使用在 cities 数组中的索引作为 id
      text: c.label
    })),
    districts.map((d, index) => ({
      id: index.toString(), // 使用在 districts 数组中的索引作为 id
      text: d.label
    }))
  ]

  // 需要在 nextTick 后显示，确保 DOM 更新和 ref 可用
  await nextTick()
  showAreaPicker.value = true
}

// 地区选择器变化处理
const areaPickerChange = async (e: any) => {
  console.log("Picker change event:", e)
  const { columnIndex, value, index, picker = areaPicker.value } = e // 获取列索引、当前选中值(可能不准)、当前选中索引、picker实例

  // 确保 picker 实例有效
  if (!picker) {
    console.error("Picker instance not found during change event.")
    return
  }

  // 增加延迟，等待 picker 内部状态稳定 (可选的调试手段)
  await new Promise((resolve) => setTimeout(resolve, 50))

  try {
    if (columnIndex === 0) {
      // 省份变化
      selectedProvinceIndex.value = index // 更新省份索引
      const currentProvinceLabel = provinceLabels.value[index] // 获取新省份名称
      if (!currentProvinceLabel) {
        console.error(`Cannot get province label for index ${index}`)
        return
      }
      console.log(
        `Province changed to: "${currentProvinceLabel}" (index ${index})`
      )

      // 获取新省份的城市列表
      const cities = getCities(currentProvinceLabel)
      const cityObjects = cities.map((c, idx) => ({
        id: idx.toString(),
        text: c.label
      }))
      console.log("New cities to set:", JSON.parse(JSON.stringify(cityObjects)))

      // 重置城市索引并更新城市列
      selectedCityIndex.value = 0
      console.log("Setting column 1 (cities)")
      await picker.setColumnValues(1, cityObjects)
      console.log("Column 1 (cities) set.")

      // 获取并更新区县列 (基于第一个城市)
      let districtObjects: any[] = []
      if (cities.length > 0) {
        const districts = getDistricts(currentProvinceLabel, 0) // cityIndex 传 0
        districtObjects = districts.map((d, idx) => ({
          id: idx.toString(),
          text: d.label
        }))
      }
      console.log(
        "New districts to set (for first city):",
        JSON.parse(JSON.stringify(districtObjects))
      )
      console.log("Setting column 2 (districts)")
      await picker.setColumnValues(2, districtObjects)
      console.log("Column 2 (districts) set.")
    } else if (columnIndex === 1) {
      // 城市变化
      selectedCityIndex.value = index // 更新城市索引 (相对于当前城市列表)
      const currentProvinceLabel =
        provinceLabels.value[selectedProvinceIndex.value] // 获取当前省份
      if (!currentProvinceLabel) {
        console.error(
          `Cannot get province label for saved index ${selectedProvinceIndex.value}`
        )
        return
      }
      console.log(
        `City changed. Current province: "${currentProvinceLabel}". New city index in list: ${index}`
      )

      // 使用新的城市索引 (index) 获取区县列表
      // ****************************************************************
      // 重点: 调用 getDistricts 时，第二个参数是城市在列表中的索引 (number)
      // ****************************************************************
      const districts = getDistricts(currentProvinceLabel, index)
      const districtObjects = districts.map((d, idx) => ({
        id: idx.toString(),
        text: d.label
      }))
      console.log(
        "New districts to set:",
        JSON.parse(JSON.stringify(districtObjects))
      )

      // 更新区县列
      console.log("Setting column 2 (districts)")
      await picker.setColumnValues(2, districtObjects)
      console.log("Column 2 (districts) set.")
    }
    // columnIndex === 2 (区县变化) 不需要联动更新其他列，所以不处理
  } catch (error) {
    console.error("Error handling picker change:", error)
    uni.showToast({ title: "更新地区列表失败", icon: "none" })
  }
}

// 取消区域选择
const cancelAreaSelect = () => {
  showAreaPicker.value = false
}

// 确认区域选择
const confirmAreaSelect = (e: any) => {
  console.log("Picker confirm event:", e)
  // uview-plus 的 confirm 事件的 value 是选中项的对象数组 {id, text}
  const { value } = e

  // 检查返回的数据结构是否符合预期
  if (
    !value ||
    !Array.isArray(value) ||
    value.length < 3 ||
    !value[0] ||
    !value[1] ||
    !value[2]
  ) {
    console.error("Confirm selection data is invalid:", value)
    // 尝试从 picker 实例获取当前值 (如果组件支持)
    // const currentValues = areaPicker.value?.getValues(); // 假设有这个方法
    // if(currentValues && currentValues.length >= 3) { ... } else { error }
    return uni.showToast({ title: "请选择完整的省市区", icon: "none" })
  }

  // 更新表单数据，使用对象的 text 属性
  addressForm.province = value[0]?.text || ""
  addressForm.city = value[1]?.text || ""
  addressForm.district = value[2]?.text || ""

  // 更新保存的索引，以便下次打开时默认选中
  const pIndex = provinceLabels.value.findIndex(
    (p) => p === addressForm.province
  )
  if (pIndex !== -1) selectedProvinceIndex.value = pIndex
  // 注意：城市和区县的索引是在 getCities/getDistricts 内部确定的，
  // 确认时只拿到了 text，如果需要精确回显，可能需要在 confirm 时也查找索引，
  // 或者依赖 showAreaSelector 打开时根据 text 重新计算。目前依赖 showAreaSelector。

  showAreaPicker.value = false
  uni.showToast({ title: "地区选择完成", icon: "success", duration: 1500 })
}

// 保存地址
const saveAddress = async () => {
  // 表单验证
  if (!addressForm.name.trim())
    return uni.showToast({ title: "请填写收货人姓名", icon: "none" })
  if (!addressForm.phone.trim())
    return uni.showToast({ title: "请填写手机号码", icon: "none" })
  if (!/^1[3-9]\d{9}$/.test(addressForm.phone))
    return uni.showToast({ title: "请填写正确的手机号码", icon: "none" })
  if (!addressForm.province)
    return uni.showToast({ title: "请选择所在地区", icon: "none" })
  if (!addressForm.detail.trim())
    return uni.showToast({ title: "请填写详细地址", icon: "none" })

  try {
    uni.showLoading({ title: "保存中..." })
    // 确保 userId 存在
    const userId = store.state?.$userInfo?.id
    if (!userId) {
      uni.hideLoading()
      uni.showToast({ title: "无法获取用户信息，请重新登录", icon: "none" })
      return
    }

    // 确保使用计算属性的当前值
    let currentDefaultValue = defaultSwitchValue.value

    // 如果是唯一地址或第一个地址，强制设置为默认地址
    if (isOnlyAddress.value || isFirstAddress.value) {
      currentDefaultValue = true
      // 移除调试日志
    }

    // 构造请求数据，确保 iDefault 字段类型正确
    const addressData = {
      ...addressForm,
      userId,
      // 使用后端期望的字段名 idefault (全小写)
      idefault: currentDefaultValue,
      // 删除前端使用的字段，避免发送多余数据
      iDefault: undefined
    }

    // 根据编辑状态调用不同的API
    let res
    if (isEditMode.value) {
      // 编辑模式：调用更新接口
      res = await updateAddress(addressData)
    } else {
      // 新增模式：调用保存接口
      res = await saveAddressApi(addressData)
    }

    uni.hideLoading()
    if (res && res.code === 200) {
      uni.showToast({ title: "保存成功", icon: "success", duration: 1000 }) // 缩短提示时间
      // 移除 setTimeout，立即返回
      uni.navigateBack()
    } else {
      uni.showToast({ title: res?.message || "保存失败", icon: "none" })
    }
  } catch (error) {
    uni.hideLoading()
    console.error("保存地址失败", error)
    uni.showToast({ title: "保存失败，请稍后重试", icon: "none" })
  }
}

// 确认删除
const confirmDelete = () => {
  if (!addressForm.id) {
    uni.showToast({ title: "无法删除，地址ID不存在", icon: "none" })
    return
  }
  uni.showModal({
    title: "提示",
    content: "确定要删除该地址吗？",
    confirmColor: accentColor,
    success: async (res) => {
      if (res.confirm) {
        try {
          uni.showLoading({ title: "删除中..." })
          const result = await deleteAddress({
            id: addressForm.id,
            status: "D",
            version: 0 // 你可能需要根据实际情况传递 version
          })
          uni.hideLoading()
          if (result && result.code === 200) {
            uni.showToast({
              title: "删除成功",
              icon: "success",
              duration: 1000
            }) // 缩短提示时间
            // 移除 setTimeout，立即返回
            uni.navigateBack()
          } else {
            uni.showToast({
              title: result?.message || "删除失败",
              icon: "none"
            })
          }
        } catch (error) {
          uni.hideLoading()
          console.error("删除地址失败", error)
          uni.showToast({ title: "删除失败，请稍后重试", icon: "none" })
        }
      }
    }
  })
}

// 添加按钮样式
const saveButtonStyle = {
  height: "44px",
  fontSize: "16px",
  width: "100%",
  borderRadius: "22px", // 全圆角按钮
  background: "linear-gradient(to right, #10B981, #34D399)", // 从次要色到浅绿色的渐变
  fontWeight: "500",
  boxShadow: "0 4px 12px rgba(16, 185, 129, 0.4)",
  border: "none",
  color: "#ffffff" // 白色文字
}
</script>

<style lang="scss" scoped>
/* 所有的颜色和间距已在模板中使用 Tailwind 类处理 */

:deep(.up-input__content),
:deep(.up-textarea__content) {
  background-color: transparent !important;
  border: none !important;
  padding: 0 !important;
}

:deep(.up-input__input),
:deep(.up-textarea__textarea) {
  height: auto; /* 让输入框自适应高度 */
  min-height: 20px;
  font-size: 14px;
  color: #333;
  background-color: #f9fafb !important; /* bg-gray-50 */
  border-radius: 8px;
}

:deep(.up-input__input) {
  padding: 0 !important;
  height: 32px !important; /* 固定输入框高度 */
  line-height: 32px !important;
}

:deep(.up-textarea__textarea) {
  margin-top: 10px; /* textarea 与 label 间距 */
  padding: 12px !important;
}

:deep(.up-textarea__count) {
  bottom: 4px !important; /* 调整计数器位置 */
  right: 12px !important;
  color: #666666; /* text-text-secondary */
}

:deep(.up-button) {
  border-radius: 22px; /* 圆角按钮 */
  height: 44px;
}

:deep(.up-button--primary) {
  background-color: #10b981; /* secondaryColor */
  border-color: #10b981;
}

:deep(.up-button--error) {
  background-color: #fff;
  border: 1px solid #ef4444; /* primaryColor */
  color: #ef4444; /* primaryColor */
}

:deep(.up-input__placeholder),
:deep(.up-textarea__placeholder) {
  color: #c0c4cc !important;
}

.input-bg {
  /* 处理内部元素与容器的关系 */
  margin-top: 4px;
}
</style>
